{"RootPath": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS", "ProjectFileName": "Basseet_SYS.vbproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Add_Update_User.Designer.vb"}, {"SourceFile": "Add_Update_User.vb"}, {"SourceFile": "buy_form\\frmBuyOrder.Designer.vb"}, {"SourceFile": "buy_form\\frmBuyOrder.vb"}, {"SourceFile": "CAT_TYPE FORMS\\FRM_DEP_CAT.Designer.vb"}, {"SourceFile": "CAT_TYPE FORMS\\FRM_DEP_CAT.vb"}, {"SourceFile": "datasets\\DataSet1.Designer.vb"}, {"SourceFile": "datasets\\DataSet1.vb"}, {"SourceFile": "FRM_CUST_SUP\\frm_bestCust.Designer.vb"}, {"SourceFile": "FRM_CUST_SUP\\frm_bestCust.vb"}, {"SourceFile": "frm_log_cashier.Designer.vb"}, {"SourceFile": "frm_log_cashier.vb"}, {"SourceFile": "frm_users.Designer.vb"}, {"SourceFile": "frm_users.vb"}, {"SourceFile": "money\\frm_istlam.Designer.vb"}, {"SourceFile": "money\\frm_istlam.vb"}, {"SourceFile": "money\\frm_taslim.Designer.vb"}, {"SourceFile": "money\\frm_taslim.vb"}, {"SourceFile": "RES_TOOLS\\frm_whats.Designer.vb"}, {"SourceFile": "RES_TOOLS\\frm_whats.vb"}, {"SourceFile": "SALES_FORMS\\frmOrderType.Designer.vb"}, {"SourceFile": "SALES_FORMS\\frmOrderType.vb"}, {"SourceFile": "FRM_CUST_SUP\\frmCustomers.Designer.vb"}, {"SourceFile": "FRM_CUST_SUP\\frmCustomers.vb"}, {"SourceFile": "FRM_HOME.vb"}, {"SourceFile": "FRM_HOME.Designer.vb"}, {"SourceFile": "GENERAL_MOD\\CLS_CON.vb"}, {"SourceFile": "GENERAL_MOD\\documentR.vb"}, {"SourceFile": "GENERAL_MOD\\Module1.vb"}, {"SourceFile": "GENERAL_MOD\\Mod_gen.vb"}, {"SourceFile": "GENERAL_MOD\\PRT_MOD.vb"}, {"SourceFile": "GENERAL_MOD\\Qr_Mod.vb"}, {"SourceFile": "GENERAL_MOD\\receiptr.vb"}, {"SourceFile": "money\\frm_paid.Designer.vb"}, {"SourceFile": "money\\frm_paid.vb"}, {"SourceFile": "My Project\\AssemblyInfo.vb"}, {"SourceFile": "My Project\\Application.Designer.vb"}, {"SourceFile": "My Project\\Resources.Designer.vb"}, {"SourceFile": "My Project\\Settings.Designer.vb"}, {"SourceFile": "PRODUCTS\\frm_add_update_containt.Designer.vb"}, {"SourceFile": "PRODUCTS\\frm_add_update_containt.vb"}, {"SourceFile": "PRODUCTS\\FRM_ADD_UPDATE_PROD.Designer.vb"}, {"SourceFile": "PRODUCTS\\FRM_ADD_UPDATE_PROD.vb"}, {"SourceFile": "PRODUCTS\\frm_manage_product.Designer.vb"}, {"SourceFile": "PRODUCTS\\frm_manage_product.vb"}, {"SourceFile": "RES_TOOLS\\frmSettings.Designer.vb"}, {"SourceFile": "RES_TOOLS\\frmSettings.vb"}, {"SourceFile": "RES_TOOLS\\frm_select_table.Designer.vb"}, {"SourceFile": "RES_TOOLS\\frm_select_table.vb"}, {"SourceFile": "RES_TOOLS\\FRM_TABELS.Designer.vb"}, {"SourceFile": "RES_TOOLS\\FRM_TABELS.vb"}, {"SourceFile": "SALES_FORMS\\Frm_Mange_Sales.Designer.vb"}, {"SourceFile": "SALES_FORMS\\Frm_Mange_Sales.vb"}, {"SourceFile": "SALES_FORMS\\FRM_POS.Designer.vb"}, {"SourceFile": "SALES_FORMS\\FRM_POS.vb"}, {"SourceFile": "SALES_FORMS\\frm_qty.Designer.vb"}, {"SourceFile": "SALES_FORMS\\frm_qty.vb"}, {"SourceFile": "SALES_FORMS\\FRM_SALES_BINFIT.Designer.vb"}, {"SourceFile": "SALES_FORMS\\FRM_SALES_BINFIT.vb"}, {"SourceFile": "SALES_FORMS\\frm_show_inv.Designer.vb"}, {"SourceFile": "SALES_FORMS\\frm_show_inv.vb"}, {"SourceFile": "signToXML.Designer.vb"}, {"SourceFile": "signToXML.vb"}, {"SourceFile": "STYLE_FORMAT\\CLS_FORMAT.vb"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.vb"}], "References": [{"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\AxShockwaveFlashObjects.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\BouncyCastle.Crypto.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\DevComponents.DotNetBar2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.Bars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.Compat.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.DataVisualization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.Editor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.Service.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.VSDesign.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\FastReports\\FastReport .NET Trial\\Demo New\\FastReport.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Greensoft.TlvLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\IKVM.OpenJDK.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\IKVM.OpenJDK.Text.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\IKVM.OpenJDK.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\IKVM.OpenJDK.XML.API.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\SignXML_V2\\SignXML\\SignXML\\bin\\Debug\\IKVM.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\Microsoft.Bcl.AsyncInterfaces.8.0.0\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\Newtonsoft.Json.12.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\Newton.JsonMediaTypeFormatter.1.0.6\\lib\\net451\\Newtonsoft.Json.MediaTypeFormatter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\NJsonSchema.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Nuget.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Pkcs11Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\QRCoder.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\RestSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\saxon-he-10.9.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\DotNetSeleniumExtras.WaitHelpers.3.11.0\\lib\\net45\\SeleniumExtras.WaitHelpers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\Microsoft.AspNet.WebApi.Client.5.2.7\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.WebRequest.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Runtime.CompilerServices.Unsafe.6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Text.Encodings.Web.8.0.0\\lib\\net462\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Text.Json.8.0.5\\lib\\net462\\System.Text.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\System.ValueTuple.4.5.0\\lib\\net47\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.DataVisualization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\TheClock.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\Selenium.WebDriver.4.31.0\\lib\\netstandard2.0\\WebDriver.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Zatca.EInvoice.SDK.Contracts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Zatca.EInvoice.SDK.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\ZXing.Net.0.16.10\\lib\\net48\\zxing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\AI\\Basseet_SYS -ok\\packages\\ZXing.Net.0.16.10\\lib\\net48\\zxing.presentation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\AI\\Basseet_SYS -ok\\Basseet_SYS\\bin\\Debug\\Basseet_SYS.exe", "OutputItemRelativePath": "Basseet_SYS.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}