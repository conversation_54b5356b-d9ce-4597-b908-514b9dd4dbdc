﻿Imports System.Data.SqlClient
Imports Net.Pkcs11Interop.Common

Public Class Add_Update_User
    Dim connx As New CLS_CON
    Public Sub InsertNewRowIn_User_Tbl()
        Dim CmdInsert As New SqlCommand
        With CmdInsert
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into User_Tbl (UserName ,  UserPass ,  FullName ,  UserRole )values( @UserName ,  @UserPass ,  @FullName ,  @UserRole )"
            .Parameters.Clear()
            .Parameters.AddWithValue("@UserName", SqlDbType.VarChar).Value = TxtUserName.Text
            .Parameters.AddWithValue("@UserPass", SqlDbType.VarChar).Value = TxtUserPass.Text
            .Parameters.AddWithValue("@FullName", SqlDbType.VarChar).Value = TxtFullName.Text
            .Parameters.AddWithValue("@UserRole", SqlDbType.VarChar).Value = CmbUserRole.Text
        End With
        Try
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdInsert.ExecuteNonQuery()
            connx.Con.Close()
            MsgBox("تم إضافة المستخدم بنجاح", MsgBoxStyle.Information, "حفظ")
            CmdInsert = Nothing
        Catch ex As Exception
            connx.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If TxtUserName.Text = vbNullString Or TxtUserPass.Text = vbNullString Or TxtFullName.Text = vbNullString Or CmbUserRole.Text = vbNullString Then
            MessageBox.Show(" عفواً ، قم بتعبئة كل الحقول", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)

            If TxtUserName.Text = vbNullString Then TxtUserName.Focus()
            If TxtUserPass.Text = vbNullString Then TxtUserPass.Focus()
            If TxtFullName.Text = vbNullString Then TxtFullName.Focus()
            If CmbUserRole.Text = vbNullString Then CmbUserRole.Focus()
            Exit Sub
        End If
        InsertNewRowIn_User_Tbl()
        InsertNewRowIn_User_Cash_Tbl()

        Dim st As String = "اضافة مستخدم جديد "
        LogFunc(Str_UserName, st)
        TxtUserName.Text = ""
        TxtUserPass.Text = ""
        TxtFullName.Text = ""
        CmbUserRole.Text = ""
        TxtUserName.Focus()
    End Sub

    Public Sub InsertNewRowIn_User_Cash_Tbl()
        Dim CmdInsert As New SqlCommand
        With CmdInsert
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into User_Cash_Tbl (UserName , FullName , Total_Cash , Status)values( @UserName ,  @FullName ,  @Total_Cash ,  @Status)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@UserName", SqlDbType.VarChar).Value = TxtUserName.Text
            .Parameters.AddWithValue("@FullName", SqlDbType.VarChar).Value = TxtFullName.Text
            .Parameters.AddWithValue("@Total_Cash", SqlDbType.Decimal).Value = 0.00
            .Parameters.AddWithValue("@Status", SqlDbType.VarChar).Value = "close"
        End With
        Try
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdInsert.ExecuteNonQuery()
            connx.Con.Close()

            CmdInsert = Nothing
        Catch ex As Exception
            connx.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Public Sub UpdateRowIn_User_Tbl(ByVal TxtUserName As String, ByVal TxtUserPass As String, ByVal TxtFullName As String, ByVal TxtUserRole As String, ByVal TxtUser_IDW As Int32)
        Dim CmdUpdate As New SqlCommand
        With CmdUpdate
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update User_Tbl Set UserName = @UserName , UserPass = @UserPass , FullName = @FullName , UserRole = @UserRole Where User_ID = @User_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@UserName", SqlDbType.VarChar).Value = TxtUserName
            .Parameters.AddWithValue("@UserPass", SqlDbType.VarChar).Value = TxtUserPass
            .Parameters.AddWithValue("@FullName", SqlDbType.VarChar).Value = TxtFullName
            .Parameters.AddWithValue("@UserRole", SqlDbType.VarChar).Value = TxtUserRole
            .Parameters.AddWithValue("@User_ID", SqlDbType.Int).Value = TxtUser_IDW
        End With
        Try
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate.ExecuteNonQuery()
            connx.Con.Close()
            MsgBox("تم تحديث السجل بنجاح", MsgBoxStyle.Information, "تحديث")
            CmdUpdate = Nothing
        Catch ex As Exception
            connx.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub
    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If TxtUserName.Text = vbNullString Or TxtUserPass.Text = vbNullString Or TxtFullName.Text = vbNullString Or CmbUserRole.Text = vbNullString Then
            MessageBox.Show(" عفواً ، قم بتعبئة كل الحقول", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)

            If TxtUserName.Text = vbNullString Then TxtUserName.Focus()
            If TxtUserPass.Text = vbNullString Then TxtUserPass.Focus()
            If TxtFullName.Text = vbNullString Then TxtFullName.Focus()
            If CmbUserRole.Text = vbNullString Then CmbUserRole.Focus()
            Exit Sub
        End If

        UpdateRowIn_User_Tbl(TxtUserName.Text, TxtUserPass.Text, TxtFullName.Text, CmbUserRole.Text, TxtUser_ID.Text)
        'InsertNewRowIn_User_Tbl()
        TxtUserName.Text = ""
        TxtUserPass.Text = ""
        TxtFullName.Text = ""
        CmbUserRole.Text = ""
        TxtUserName.Focus()
        Me.Close()
        With frm_users
            .LoadUsers()
        End With
    End Sub

    Public Sub ClearText()
        Me.TxtUser_ID.Text = "Auto"
        Me.TxtUserName.Text = ""
        Me.TxtUserPass.Text = ""
        Me.TxtFullName.Text = ""
        Me.CmbUserRole.Text = ""
        Me.TxtUserName.Focus()
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs) Handles BtnClear.Click
        ClearText()
    End Sub

    Private Sub Label6_Click(sender As Object, e As EventArgs) Handles Label6.Click
        Me.Close()
    End Sub
End Class