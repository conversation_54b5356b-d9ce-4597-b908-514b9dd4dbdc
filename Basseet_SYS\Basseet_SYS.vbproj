﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{21B4FF18-47EF-418F-B9AF-7D5BB7B0FDA1}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Basseet_SYS.My.MyApplication</StartupObject>
    <RootNamespace>Basseet_SYS</RootNamespace>
    <AssemblyName>Basseet_SYS</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Basseet_SYS.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Basseet_SYS.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AxShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AxShockwaveFlashObjects.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2, Version=7.3.0.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="FastReport">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Bars">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.Bars.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Compat">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.Compat.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.DataVisualization">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Editor">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.Editor.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Service">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.Service.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.VSDesign">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.VSDesign.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Web">
      <HintPath>C:\Program Files (x86)\FastReports\FastReport .NET Trial\Demo New\FastReport.Web.dll</HintPath>
    </Reference>
    <Reference Include="Greensoft.TlvLib, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Greensoft.TlvLib.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Core, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\IKVM.OpenJDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Text, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\IKVM.OpenJDK.Text.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Util, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\IKVM.OpenJDK.Util.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.XML.API, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\IKVM.OpenJDK.XML.API.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.Runtime, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SignXML_V2\SignXML\SignXML\bin\Debug\IKVM.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.MediaTypeFormatter, Version=1.0.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Newton.JsonMediaTypeFormatter.1.0.6\lib\net451\Newtonsoft.Json.MediaTypeFormatter.dll</HintPath>
    </Reference>
    <Reference Include="NJsonSchema, Version=10.7.2.0, Culture=neutral, PublicKeyToken=c2f9c3bdfae56102, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\NJsonSchema.dll</HintPath>
    </Reference>
    <Reference Include="Nuget, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Nuget.dll</HintPath>
    </Reference>
    <Reference Include="Pkcs11Interop, Version=5.1.2.0, Culture=neutral, PublicKeyToken=c10e9c2d8c006d2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Pkcs11Interop.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder, Version=1.3.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.15.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="saxon-he-10.9, Version=10.9.0.0, Culture=neutral, PublicKeyToken=e1fdd002d5083fe6, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\saxon-he-10.9.dll</HintPath>
    </Reference>
    <Reference Include="SeleniumExtras.WaitHelpers, Version=3.11.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DotNetSeleniumExtras.WaitHelpers.3.11.0\lib\net45\SeleniumExtras.WaitHelpers.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="TheClock, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\TheClock.dll</HintPath>
    </Reference>
    <Reference Include="WebDriver, Version=4.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Selenium.WebDriver.4.31.0\lib\netstandard2.0\WebDriver.dll</HintPath>
    </Reference>
    <Reference Include="Zatca.EInvoice.SDK, Version=3.3.3.0, Culture=neutral, PublicKeyToken=cf541c52f43eaadd, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Zatca.EInvoice.SDK.dll</HintPath>
    </Reference>
    <Reference Include="Zatca.EInvoice.SDK.Contracts, Version=3.3.3.0, Culture=neutral, PublicKeyToken=cf541c52f43eaadd, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Zatca.EInvoice.SDK.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="zxing, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net48\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net48\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Add_Update_User.Designer.vb">
      <DependentUpon>Add_Update_User.vb</DependentUpon>
    </Compile>
    <Compile Include="Add_Update_User.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="buy_form\frmBuyOrder.Designer.vb">
      <DependentUpon>frmBuyOrder.vb</DependentUpon>
    </Compile>
    <Compile Include="buy_form\frmBuyOrder.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CAT_TYPE FORMS\FRM_DEP_CAT.Designer.vb">
      <DependentUpon>FRM_DEP_CAT.vb</DependentUpon>
    </Compile>
    <Compile Include="CAT_TYPE FORMS\FRM_DEP_CAT.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="datasets\DataSet1.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </Compile>
    <Compile Include="datasets\DataSet1.vb">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </Compile>
    <Compile Include="FRM_CUST_SUP\frm_bestCust.Designer.vb">
      <DependentUpon>frm_bestCust.vb</DependentUpon>
    </Compile>
    <Compile Include="FRM_CUST_SUP\frm_bestCust.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frm_log_cashier.Designer.vb">
      <DependentUpon>frm_log_cashier.vb</DependentUpon>
    </Compile>
    <Compile Include="frm_log_cashier.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frm_users.Designer.vb">
      <DependentUpon>frm_users.vb</DependentUpon>
    </Compile>
    <Compile Include="frm_users.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="money\frm_istlam.Designer.vb">
      <DependentUpon>frm_istlam.vb</DependentUpon>
    </Compile>
    <Compile Include="money\frm_istlam.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="money\frm_taslim.Designer.vb">
      <DependentUpon>frm_taslim.vb</DependentUpon>
    </Compile>
    <Compile Include="money\frm_taslim.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RES_TOOLS\frm_whats.Designer.vb">
      <DependentUpon>frm_whats.vb</DependentUpon>
    </Compile>
    <Compile Include="RES_TOOLS\frm_whats.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\frmOrderType.Designer.vb">
      <DependentUpon>frmOrderType.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\frmOrderType.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRM_CUST_SUP\frmCustomers.Designer.vb">
      <DependentUpon>frmCustomers.vb</DependentUpon>
    </Compile>
    <Compile Include="FRM_CUST_SUP\frmCustomers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRM_HOME.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FRM_HOME.Designer.vb">
      <DependentUpon>FRM_HOME.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GENERAL_MOD\CLS_CON.vb" />
    <Compile Include="GENERAL_MOD\documentR.vb" />
    <Compile Include="GENERAL_MOD\Module1.vb" />
    <Compile Include="GENERAL_MOD\Mod_gen.vb" />
    <Compile Include="GENERAL_MOD\PRT_MOD.vb" />
    <Compile Include="GENERAL_MOD\Qr_Mod.vb" />
    <Compile Include="GENERAL_MOD\receiptr.vb" />
    <Compile Include="money\frm_paid.Designer.vb">
      <DependentUpon>frm_paid.vb</DependentUpon>
    </Compile>
    <Compile Include="money\frm_paid.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="PRODUCTS\frm_add_update_containt.Designer.vb">
      <DependentUpon>frm_add_update_containt.vb</DependentUpon>
    </Compile>
    <Compile Include="PRODUCTS\frm_add_update_containt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PRODUCTS\FRM_ADD_UPDATE_PROD.Designer.vb">
      <DependentUpon>FRM_ADD_UPDATE_PROD.vb</DependentUpon>
    </Compile>
    <Compile Include="PRODUCTS\FRM_ADD_UPDATE_PROD.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PRODUCTS\frm_manage_product.Designer.vb">
      <DependentUpon>frm_manage_product.vb</DependentUpon>
    </Compile>
    <Compile Include="PRODUCTS\frm_manage_product.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RES_TOOLS\frmSettings.Designer.vb">
      <DependentUpon>frmSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="RES_TOOLS\frmSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RES_TOOLS\frm_select_table.Designer.vb">
      <DependentUpon>frm_select_table.vb</DependentUpon>
    </Compile>
    <Compile Include="RES_TOOLS\frm_select_table.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RES_TOOLS\FRM_TABELS.Designer.vb">
      <DependentUpon>FRM_TABELS.vb</DependentUpon>
    </Compile>
    <Compile Include="RES_TOOLS\FRM_TABELS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\Frm_Mange_Sales.Designer.vb">
      <DependentUpon>Frm_Mange_Sales.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\Frm_Mange_Sales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\FRM_POS.Designer.vb">
      <DependentUpon>FRM_POS.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\FRM_POS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\frm_qty.Designer.vb">
      <DependentUpon>frm_qty.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\frm_qty.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\FRM_SALES_BINFIT.Designer.vb">
      <DependentUpon>FRM_SALES_BINFIT.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\FRM_SALES_BINFIT.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SALES_FORMS\frm_show_inv.Designer.vb">
      <DependentUpon>frm_show_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="SALES_FORMS\frm_show_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="signToXML.Designer.vb">
      <DependentUpon>signToXML.vb</DependentUpon>
    </Compile>
    <Compile Include="signToXML.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STYLE_FORMAT\CLS_FORMAT.vb" />
    <Compile Include="TABLE_MANAGEMENT\frm_table_management.Designer.vb">
      <DependentUpon>frm_table_management.vb</DependentUpon>
    </Compile>
    <Compile Include="TABLE_MANAGEMENT\frm_table_management.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TABLE_MANAGEMENT\frm_table_test.Designer.vb">
      <DependentUpon>frm_table_test.vb</DependentUpon>
    </Compile>
    <Compile Include="TABLE_MANAGEMENT\frm_table_test.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Add_Update_User.resx">
      <DependentUpon>Add_Update_User.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="buy_form\frmBuyOrder.resx">
      <DependentUpon>frmBuyOrder.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CAT_TYPE FORMS\FRM_DEP_CAT.resx">
      <DependentUpon>FRM_DEP_CAT.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FRM_CUST_SUP\frm_bestCust.resx">
      <DependentUpon>frm_bestCust.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frm_log_cashier.resx">
      <DependentUpon>frm_log_cashier.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frm_users.resx">
      <DependentUpon>frm_users.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="money\frm_istlam.resx">
      <DependentUpon>frm_istlam.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="money\frm_taslim.resx">
      <DependentUpon>frm_taslim.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RES_TOOLS\frm_whats.resx">
      <DependentUpon>frm_whats.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\frmOrderType.resx">
      <DependentUpon>frmOrderType.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRM_CUST_SUP\frmCustomers.resx">
      <DependentUpon>frmCustomers.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FRM_HOME.resx">
      <DependentUpon>FRM_HOME.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="money\frm_paid.resx">
      <DependentUpon>frm_paid.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PRODUCTS\frm_add_update_containt.resx">
      <DependentUpon>frm_add_update_containt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PRODUCTS\FRM_ADD_UPDATE_PROD.resx">
      <DependentUpon>FRM_ADD_UPDATE_PROD.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PRODUCTS\frm_manage_product.resx">
      <DependentUpon>frm_manage_product.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RES_TOOLS\frmSettings.resx">
      <DependentUpon>frmSettings.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RES_TOOLS\frm_select_table.resx">
      <DependentUpon>frm_select_table.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RES_TOOLS\FRM_TABELS.resx">
      <DependentUpon>FRM_TABELS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\Frm_Mange_Sales.resx">
      <DependentUpon>Frm_Mange_Sales.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\FRM_POS.resx">
      <DependentUpon>FRM_POS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\frm_qty.resx">
      <DependentUpon>frm_qty.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\FRM_SALES_BINFIT.resx">
      <DependentUpon>FRM_SALES_BINFIT.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SALES_FORMS\frm_show_inv.resx">
      <DependentUpon>frm_show_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="signToXML.resx">
      <DependentUpon>signToXML.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TABLE_MANAGEMENT\frm_table_management.resx">
      <DependentUpon>frm_table_management.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="datasets\DataSet1.xsc">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="datasets\DataSet1.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet1.Designer.vb</LastGenOutput>
    </None>
    <None Include="datasets\DataSet1.xss">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\icons8_add_32px.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\save-322.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\save-32_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\Apps-Zoom-In-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\18-06-2022 08-16-59 م.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\Actions-application-exit-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\printer-321.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\exit-2-32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\alert.wav" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\croak.wav" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\suc.wav" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\when.wav" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\Pen-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\111.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\bundle-48x48x32b.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\18-06-2022 08-15-58 م.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\Settings-5-icon1.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="EGY_RECIPT\" />
    <Folder Include="print_report\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\log.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\7764.jpg_wh860.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\5559852.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\log.bmp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\money.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\money_transfer_64px.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\1420853324_Look.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\18-06-2022 08-17-29 م.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\images %2816%29.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Import Project="..\packages\Selenium.WebDriver.4.31.0\build\Selenium.WebDriver.targets" Condition="Exists('..\packages\Selenium.WebDriver.4.31.0\build\Selenium.WebDriver.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Selenium.WebDriver.4.31.0\build\Selenium.WebDriver.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Selenium.WebDriver.4.31.0\build\Selenium.WebDriver.targets'))" />
  </Target>
</Project>