﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FRM_DEP_CAT
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.Tab_DEP_CAT = New DevComponents.DotNetBar.TabControl()
        Me.TabControlPanel2 = New DevComponents.DotNetBar.TabControlPanel()
        Me.GroupPanel2 = New DevComponents.DotNetBar.Controls.GroupPanel()
        Me.BTN_REF = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.BtnNewCat = New System.Windows.Forms.Button()
        Me.TxtCat_ID = New System.Windows.Forms.TextBox()
        Me.BtnSaveCat = New System.Windows.Forms.Button()
        Me.BtnEditCat = New System.Windows.Forms.Button()
        Me.btnColor = New System.Windows.Forms.Button()
        Me.BtnDeleteCat = New System.Windows.Forms.Button()
        Me.btnSelectColor = New System.Windows.Forms.Button()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.CmbDep = New System.Windows.Forms.ComboBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TxtCatName = New System.Windows.Forms.TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.DgvCat = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn2 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn3 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Tab_CAT = New DevComponents.DotNetBar.TabItem(Me.components)
        Me.TabControlPanel1 = New DevComponents.DotNetBar.TabControlPanel()
        Me.Dgv_Dep = New System.Windows.Forms.DataGridView()
        Me.Column1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column2 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column3 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column4 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupPanel1 = New DevComponents.DotNetBar.Controls.GroupPanel()
        Me.Txt_ID = New System.Windows.Forms.TextBox()
        Me.BTN_CLOSE = New System.Windows.Forms.Button()
        Me.BtnEdit = New System.Windows.Forms.Button()
        Me.BtnPrint = New System.Windows.Forms.Button()
        Me.PanelEx2 = New DevComponents.DotNetBar.PanelEx()
        Me.BtnDelete = New System.Windows.Forms.Button()
        Me.PanelEx3 = New DevComponents.DotNetBar.PanelEx()
        Me.TXT_DepName = New System.Windows.Forms.TextBox()
        Me.BtnSave = New System.Windows.Forms.Button()
        Me.CmbPrinter = New System.Windows.Forms.ComboBox()
        Me.BtnNew = New System.Windows.Forms.Button()
        Me.Check_active = New System.Windows.Forms.CheckBox()
        Me.PanelEx4 = New DevComponents.DotNetBar.PanelEx()
        Me.Tab_DEP = New DevComponents.DotNetBar.TabItem(Me.components)
        Me.ColorDialog1 = New System.Windows.Forms.ColorDialog()
        CType(Me.Tab_DEP_CAT, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Tab_DEP_CAT.SuspendLayout()
        Me.TabControlPanel2.SuspendLayout()
        Me.GroupPanel2.SuspendLayout()
        CType(Me.DgvCat, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabControlPanel1.SuspendLayout()
        CType(Me.Dgv_Dep, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Tab_DEP_CAT
        '
        Me.Tab_DEP_CAT.CanReorderTabs = True
        Me.Tab_DEP_CAT.Controls.Add(Me.TabControlPanel1)
        Me.Tab_DEP_CAT.Controls.Add(Me.TabControlPanel2)
        Me.Tab_DEP_CAT.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Tab_DEP_CAT.Location = New System.Drawing.Point(0, 0)
        Me.Tab_DEP_CAT.Name = "Tab_DEP_CAT"
        Me.Tab_DEP_CAT.SelectedTabFont = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Bold)
        Me.Tab_DEP_CAT.SelectedTabIndex = 0
        Me.Tab_DEP_CAT.Size = New System.Drawing.Size(1229, 593)
        Me.Tab_DEP_CAT.TabIndex = 2
        Me.Tab_DEP_CAT.TabLayoutType = DevComponents.DotNetBar.eTabLayoutType.FixedWithNavigationBox
        Me.Tab_DEP_CAT.Tabs.Add(Me.Tab_DEP)
        Me.Tab_DEP_CAT.Tabs.Add(Me.Tab_CAT)
        Me.Tab_DEP_CAT.Text = "TabControl1"
        '
        'TabControlPanel2
        '
        Me.TabControlPanel2.Controls.Add(Me.GroupPanel2)
        Me.TabControlPanel2.Controls.Add(Me.DgvCat)
        Me.TabControlPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControlPanel2.Location = New System.Drawing.Point(0, 30)
        Me.TabControlPanel2.Name = "TabControlPanel2"
        Me.TabControlPanel2.Padding = New System.Windows.Forms.Padding(1)
        Me.TabControlPanel2.Size = New System.Drawing.Size(1229, 563)
        Me.TabControlPanel2.Style.BackColor1.Color = System.Drawing.Color.FromArgb(CType(CType(142, Byte), Integer), CType(CType(179, Byte), Integer), CType(CType(231, Byte), Integer))
        Me.TabControlPanel2.Style.BackColor2.Color = System.Drawing.Color.FromArgb(CType(CType(223, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(254, Byte), Integer))
        Me.TabControlPanel2.Style.Border = DevComponents.DotNetBar.eBorderType.SingleLine
        Me.TabControlPanel2.Style.BorderColor.Color = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(97, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.TabControlPanel2.Style.BorderSide = CType(((DevComponents.DotNetBar.eBorderSide.Left Or DevComponents.DotNetBar.eBorderSide.Right) _
            Or DevComponents.DotNetBar.eBorderSide.Bottom), DevComponents.DotNetBar.eBorderSide)
        Me.TabControlPanel2.Style.GradientAngle = 90
        Me.TabControlPanel2.TabIndex = 2
        Me.TabControlPanel2.TabItem = Me.Tab_CAT
        '
        'GroupPanel2
        '
        Me.GroupPanel2.CanvasColor = System.Drawing.SystemColors.Control
        Me.GroupPanel2.ColorSchemeStyle = DevComponents.DotNetBar.eDotNetBarStyle.Office2007
        Me.GroupPanel2.Controls.Add(Me.BTN_REF)
        Me.GroupPanel2.Controls.Add(Me.Button1)
        Me.GroupPanel2.Controls.Add(Me.BtnNewCat)
        Me.GroupPanel2.Controls.Add(Me.TxtCat_ID)
        Me.GroupPanel2.Controls.Add(Me.BtnSaveCat)
        Me.GroupPanel2.Controls.Add(Me.BtnEditCat)
        Me.GroupPanel2.Controls.Add(Me.btnColor)
        Me.GroupPanel2.Controls.Add(Me.BtnDeleteCat)
        Me.GroupPanel2.Controls.Add(Me.btnSelectColor)
        Me.GroupPanel2.Controls.Add(Me.Label5)
        Me.GroupPanel2.Controls.Add(Me.CmbDep)
        Me.GroupPanel2.Controls.Add(Me.Label4)
        Me.GroupPanel2.Controls.Add(Me.TxtCatName)
        Me.GroupPanel2.Controls.Add(Me.Label3)
        Me.GroupPanel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.GroupPanel2.Location = New System.Drawing.Point(1, 1)
        Me.GroupPanel2.Name = "GroupPanel2"
        Me.GroupPanel2.Size = New System.Drawing.Size(1227, 135)
        '
        '
        '
        Me.GroupPanel2.Style.BackColor2SchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2
        Me.GroupPanel2.Style.BackColorGradientAngle = 90
        Me.GroupPanel2.Style.BackColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground
        Me.GroupPanel2.Style.BorderBottom = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel2.Style.BorderBottomWidth = 1
        Me.GroupPanel2.Style.BorderColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder
        Me.GroupPanel2.Style.BorderLeft = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel2.Style.BorderLeftWidth = 1
        Me.GroupPanel2.Style.BorderRight = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel2.Style.BorderRightWidth = 1
        Me.GroupPanel2.Style.BorderTop = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel2.Style.BorderTopWidth = 1
        Me.GroupPanel2.Style.CornerDiameter = 4
        Me.GroupPanel2.Style.CornerType = DevComponents.DotNetBar.eCornerType.Rounded
        Me.GroupPanel2.Style.TextAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Center
        Me.GroupPanel2.Style.TextColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText
        Me.GroupPanel2.Style.TextLineAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Near
        Me.GroupPanel2.TabIndex = 65
        '
        'BTN_REF
        '
        Me.BTN_REF.Location = New System.Drawing.Point(334, 103)
        Me.BTN_REF.Name = "BTN_REF"
        Me.BTN_REF.Size = New System.Drawing.Size(108, 23)
        Me.BTN_REF.TabIndex = 66
        Me.BTN_REF.Text = "تحديث"
        Me.BTN_REF.UseVisualStyleBackColor = True
        Me.BTN_REF.Visible = False
        '
        'Button1
        '
        Me.Button1.BackColor = System.Drawing.Color.White
        Me.Button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button1.ForeColor = System.Drawing.Color.Black
        Me.Button1.Image = Global.Basseet_SYS.My.Resources.Resources.exit_2_32
        Me.Button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button1.Location = New System.Drawing.Point(334, 12)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(108, 77)
        Me.Button1.TabIndex = 65
        Me.Button1.Text = "غلق"
        Me.Button1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button1.UseVisualStyleBackColor = False
        '
        'BtnNewCat
        '
        Me.BtnNewCat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BtnNewCat.BackColor = System.Drawing.Color.White
        Me.BtnNewCat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnNewCat.ForeColor = System.Drawing.Color.Black
        Me.BtnNewCat.Image = Global.Basseet_SYS.My.Resources.Resources.icons8_add_32px
        Me.BtnNewCat.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnNewCat.Location = New System.Drawing.Point(558, 12)
        Me.BtnNewCat.Name = "BtnNewCat"
        Me.BtnNewCat.Size = New System.Drawing.Size(104, 36)
        Me.BtnNewCat.TabIndex = 60
        Me.BtnNewCat.Text = "جديد"
        Me.BtnNewCat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnNewCat.UseVisualStyleBackColor = False
        '
        'TxtCat_ID
        '
        Me.TxtCat_ID.Location = New System.Drawing.Point(448, 96)
        Me.TxtCat_ID.Name = "TxtCat_ID"
        Me.TxtCat_ID.Size = New System.Drawing.Size(104, 26)
        Me.TxtCat_ID.TabIndex = 64
        Me.TxtCat_ID.Visible = False
        '
        'BtnSaveCat
        '
        Me.BtnSaveCat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BtnSaveCat.BackColor = System.Drawing.Color.White
        Me.BtnSaveCat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnSaveCat.ForeColor = System.Drawing.Color.Black
        Me.BtnSaveCat.Image = Global.Basseet_SYS.My.Resources.Resources.save_322
        Me.BtnSaveCat.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnSaveCat.Location = New System.Drawing.Point(448, 12)
        Me.BtnSaveCat.Name = "BtnSaveCat"
        Me.BtnSaveCat.Size = New System.Drawing.Size(104, 36)
        Me.BtnSaveCat.TabIndex = 59
        Me.BtnSaveCat.Text = "حفظ"
        Me.BtnSaveCat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnSaveCat.UseVisualStyleBackColor = False
        '
        'BtnEditCat
        '
        Me.BtnEditCat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BtnEditCat.BackColor = System.Drawing.Color.White
        Me.BtnEditCat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnEditCat.ForeColor = System.Drawing.Color.Black
        Me.BtnEditCat.Image = Global.Basseet_SYS.My.Resources.Resources._18_06_2022_08_16_59_م
        Me.BtnEditCat.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnEditCat.Location = New System.Drawing.Point(558, 54)
        Me.BtnEditCat.Name = "BtnEditCat"
        Me.BtnEditCat.Size = New System.Drawing.Size(104, 36)
        Me.BtnEditCat.TabIndex = 61
        Me.BtnEditCat.Text = "تعديل"
        Me.BtnEditCat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnEditCat.UseVisualStyleBackColor = False
        '
        'btnColor
        '
        Me.btnColor.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnColor.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.btnColor.FlatAppearance.BorderSize = 0
        Me.btnColor.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnColor.Font = New System.Drawing.Font("Segoe UI", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnColor.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(174, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.btnColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnColor.Location = New System.Drawing.Point(558, 96)
        Me.btnColor.Name = "btnColor"
        Me.btnColor.Size = New System.Drawing.Size(104, 27)
        Me.btnColor.TabIndex = 58
        Me.btnColor.UseVisualStyleBackColor = False
        '
        'BtnDeleteCat
        '
        Me.BtnDeleteCat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BtnDeleteCat.BackColor = System.Drawing.Color.White
        Me.BtnDeleteCat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnDeleteCat.ForeColor = System.Drawing.Color.Black
        Me.BtnDeleteCat.Image = Global.Basseet_SYS.My.Resources.Resources.Actions_application_exit_icon
        Me.BtnDeleteCat.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnDeleteCat.Location = New System.Drawing.Point(448, 53)
        Me.BtnDeleteCat.Name = "BtnDeleteCat"
        Me.BtnDeleteCat.Size = New System.Drawing.Size(104, 36)
        Me.BtnDeleteCat.TabIndex = 62
        Me.BtnDeleteCat.Text = "حذف"
        Me.BtnDeleteCat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnDeleteCat.UseVisualStyleBackColor = False
        '
        'btnSelectColor
        '
        Me.btnSelectColor.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSelectColor.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(174, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.btnSelectColor.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnSelectColor.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSelectColor.ForeColor = System.Drawing.Color.White
        Me.btnSelectColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectColor.Location = New System.Drawing.Point(678, 90)
        Me.btnSelectColor.Name = "btnSelectColor"
        Me.btnSelectColor.Size = New System.Drawing.Size(415, 33)
        Me.btnSelectColor.TabIndex = 57
        Me.btnSelectColor.Text = "اختار اللون"
        Me.btnSelectColor.UseVisualStyleBackColor = False
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label5.ForeColor = System.Drawing.Color.Black
        Me.Label5.Location = New System.Drawing.Point(1099, 12)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(108, 27)
        Me.Label5.TabIndex = 54
        Me.Label5.Text = "اسم التصنيف"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'CmbDep
        '
        Me.CmbDep.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CmbDep.FormattingEnabled = True
        Me.CmbDep.Location = New System.Drawing.Point(678, 53)
        Me.CmbDep.Name = "CmbDep"
        Me.CmbDep.Size = New System.Drawing.Size(415, 26)
        Me.CmbDep.TabIndex = 56
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label4.ForeColor = System.Drawing.Color.Black
        Me.Label4.Location = New System.Drawing.Point(1099, 54)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(108, 27)
        Me.Label4.TabIndex = 53
        Me.Label4.Text = "اسم القسم"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TxtCatName
        '
        Me.TxtCatName.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TxtCatName.BackColor = System.Drawing.Color.White
        Me.TxtCatName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.TxtCatName.ForeColor = System.Drawing.Color.Black
        Me.TxtCatName.Location = New System.Drawing.Point(678, 12)
        Me.TxtCatName.Name = "TxtCatName"
        Me.TxtCatName.Size = New System.Drawing.Size(415, 26)
        Me.TxtCatName.TabIndex = 55
        Me.TxtCatName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label3.ForeColor = System.Drawing.Color.Black
        Me.Label3.Location = New System.Drawing.Point(1099, 93)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(108, 27)
        Me.Label3.TabIndex = 52
        Me.Label3.Text = "اللون"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'DgvCat
        '
        Me.DgvCat.AllowUserToAddRows = False
        Me.DgvCat.AllowUserToDeleteRows = False
        Me.DgvCat.BackgroundColor = System.Drawing.Color.White
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle3.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DgvCat.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.DgvCat.ColumnHeadersHeight = 28
        Me.DgvCat.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.DgvCat.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1, Me.DataGridViewTextBoxColumn2, Me.DataGridViewTextBoxColumn3})
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle4.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.Silver
        DataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DgvCat.DefaultCellStyle = DataGridViewCellStyle4
        Me.DgvCat.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.DgvCat.EnableHeadersVisualStyles = False
        Me.DgvCat.Location = New System.Drawing.Point(1, 142)
        Me.DgvCat.Name = "DgvCat"
        Me.DgvCat.ReadOnly = True
        Me.DgvCat.RowHeadersVisible = False
        Me.DgvCat.RowTemplate.Height = 27
        Me.DgvCat.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DgvCat.Size = New System.Drawing.Size(1227, 420)
        Me.DgvCat.TabIndex = 63
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.HeaderText = "رقم التصنيف"
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        Me.DataGridViewTextBoxColumn1.Width = 130
        '
        'DataGridViewTextBoxColumn2
        '
        Me.DataGridViewTextBoxColumn2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn2.HeaderText = "اسم التصنيف"
        Me.DataGridViewTextBoxColumn2.Name = "DataGridViewTextBoxColumn2"
        Me.DataGridViewTextBoxColumn2.ReadOnly = True
        '
        'DataGridViewTextBoxColumn3
        '
        Me.DataGridViewTextBoxColumn3.HeaderText = "اسم القسم"
        Me.DataGridViewTextBoxColumn3.Name = "DataGridViewTextBoxColumn3"
        Me.DataGridViewTextBoxColumn3.ReadOnly = True
        Me.DataGridViewTextBoxColumn3.Width = 400
        '
        'Tab_CAT
        '
        Me.Tab_CAT.AttachedControl = Me.TabControlPanel2
        Me.Tab_CAT.Name = "Tab_CAT"
        Me.Tab_CAT.Text = "التصنيفات"
        '
        'TabControlPanel1
        '
        Me.TabControlPanel1.Controls.Add(Me.Dgv_Dep)
        Me.TabControlPanel1.Controls.Add(Me.GroupPanel1)
        Me.TabControlPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControlPanel1.Location = New System.Drawing.Point(0, 30)
        Me.TabControlPanel1.Name = "TabControlPanel1"
        Me.TabControlPanel1.Padding = New System.Windows.Forms.Padding(1)
        Me.TabControlPanel1.Size = New System.Drawing.Size(1229, 563)
        Me.TabControlPanel1.Style.BackColor1.Color = System.Drawing.Color.FromArgb(CType(CType(142, Byte), Integer), CType(CType(179, Byte), Integer), CType(CType(231, Byte), Integer))
        Me.TabControlPanel1.Style.BackColor2.Color = System.Drawing.Color.FromArgb(CType(CType(223, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(254, Byte), Integer))
        Me.TabControlPanel1.Style.Border = DevComponents.DotNetBar.eBorderType.SingleLine
        Me.TabControlPanel1.Style.BorderColor.Color = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(97, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.TabControlPanel1.Style.BorderSide = CType(((DevComponents.DotNetBar.eBorderSide.Left Or DevComponents.DotNetBar.eBorderSide.Right) _
            Or DevComponents.DotNetBar.eBorderSide.Bottom), DevComponents.DotNetBar.eBorderSide)
        Me.TabControlPanel1.Style.GradientAngle = 90
        Me.TabControlPanel1.TabIndex = 1
        Me.TabControlPanel1.TabItem = Me.Tab_DEP
        '
        'Dgv_Dep
        '
        Me.Dgv_Dep.AllowUserToAddRows = False
        Me.Dgv_Dep.AllowUserToDeleteRows = False
        Me.Dgv_Dep.BackgroundColor = System.Drawing.Color.White
        Me.Dgv_Dep.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle1.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Dep.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.Dgv_Dep.ColumnHeadersHeight = 28
        Me.Dgv_Dep.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Dgv_Dep.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.Column1, Me.Column2, Me.Column3, Me.Column4})
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.Silver
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv_Dep.DefaultCellStyle = DataGridViewCellStyle2
        Me.Dgv_Dep.Dock = System.Windows.Forms.DockStyle.Top
        Me.Dgv_Dep.EnableHeadersVisualStyles = False
        Me.Dgv_Dep.Location = New System.Drawing.Point(1, 158)
        Me.Dgv_Dep.Name = "Dgv_Dep"
        Me.Dgv_Dep.ReadOnly = True
        Me.Dgv_Dep.RowHeadersVisible = False
        Me.Dgv_Dep.RowTemplate.Height = 27
        Me.Dgv_Dep.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv_Dep.Size = New System.Drawing.Size(1227, 401)
        Me.Dgv_Dep.TabIndex = 42
        '
        'Column1
        '
        Me.Column1.HeaderText = "رقم القسم"
        Me.Column1.Name = "Column1"
        Me.Column1.ReadOnly = True
        Me.Column1.Visible = False
        Me.Column1.Width = 130
        '
        'Column2
        '
        Me.Column2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.Column2.HeaderText = "اسم القسم"
        Me.Column2.Name = "Column2"
        Me.Column2.ReadOnly = True
        '
        'Column3
        '
        Me.Column3.HeaderText = "اسم الطابعة الافتراضية"
        Me.Column3.Name = "Column3"
        Me.Column3.ReadOnly = True
        Me.Column3.Width = 400
        '
        'Column4
        '
        Me.Column4.HeaderText = "الحالة"
        Me.Column4.Name = "Column4"
        Me.Column4.ReadOnly = True
        Me.Column4.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Column4.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable
        '
        'GroupPanel1
        '
        Me.GroupPanel1.CanvasColor = System.Drawing.SystemColors.Control
        Me.GroupPanel1.ColorSchemeStyle = DevComponents.DotNetBar.eDotNetBarStyle.Office2007
        Me.GroupPanel1.Controls.Add(Me.Txt_ID)
        Me.GroupPanel1.Controls.Add(Me.BTN_CLOSE)
        Me.GroupPanel1.Controls.Add(Me.BtnEdit)
        Me.GroupPanel1.Controls.Add(Me.BtnPrint)
        Me.GroupPanel1.Controls.Add(Me.PanelEx2)
        Me.GroupPanel1.Controls.Add(Me.BtnDelete)
        Me.GroupPanel1.Controls.Add(Me.PanelEx3)
        Me.GroupPanel1.Controls.Add(Me.TXT_DepName)
        Me.GroupPanel1.Controls.Add(Me.BtnSave)
        Me.GroupPanel1.Controls.Add(Me.CmbPrinter)
        Me.GroupPanel1.Controls.Add(Me.BtnNew)
        Me.GroupPanel1.Controls.Add(Me.Check_active)
        Me.GroupPanel1.Controls.Add(Me.PanelEx4)
        Me.GroupPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.GroupPanel1.Location = New System.Drawing.Point(1, 1)
        Me.GroupPanel1.Name = "GroupPanel1"
        Me.GroupPanel1.Size = New System.Drawing.Size(1227, 157)
        '
        '
        '
        Me.GroupPanel1.Style.BackColor2SchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2
        Me.GroupPanel1.Style.BackColorGradientAngle = 90
        Me.GroupPanel1.Style.BackColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground
        Me.GroupPanel1.Style.BorderBottom = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel1.Style.BorderBottomWidth = 1
        Me.GroupPanel1.Style.BorderColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder
        Me.GroupPanel1.Style.BorderLeft = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel1.Style.BorderLeftWidth = 1
        Me.GroupPanel1.Style.BorderRight = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel1.Style.BorderRightWidth = 1
        Me.GroupPanel1.Style.BorderTop = DevComponents.DotNetBar.eStyleBorderType.Solid
        Me.GroupPanel1.Style.BorderTopWidth = 1
        Me.GroupPanel1.Style.CornerDiameter = 4
        Me.GroupPanel1.Style.CornerType = DevComponents.DotNetBar.eCornerType.Rounded
        Me.GroupPanel1.Style.TextAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Center
        Me.GroupPanel1.Style.TextColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText
        Me.GroupPanel1.Style.TextLineAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Near
        Me.GroupPanel1.TabIndex = 41
        '
        'Txt_ID
        '
        Me.Txt_ID.Location = New System.Drawing.Point(716, 117)
        Me.Txt_ID.Name = "Txt_ID"
        Me.Txt_ID.Size = New System.Drawing.Size(194, 26)
        Me.Txt_ID.TabIndex = 42
        Me.Txt_ID.Visible = False
        '
        'BTN_CLOSE
        '
        Me.BTN_CLOSE.BackColor = System.Drawing.Color.White
        Me.BTN_CLOSE.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BTN_CLOSE.ForeColor = System.Drawing.Color.Black
        Me.BTN_CLOSE.Image = Global.Basseet_SYS.My.Resources.Resources.exit_2_32
        Me.BTN_CLOSE.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BTN_CLOSE.Location = New System.Drawing.Point(119, 63)
        Me.BTN_CLOSE.Name = "BTN_CLOSE"
        Me.BTN_CLOSE.Size = New System.Drawing.Size(108, 43)
        Me.BTN_CLOSE.TabIndex = 41
        Me.BTN_CLOSE.Text = "غلق"
        Me.BTN_CLOSE.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BTN_CLOSE.UseVisualStyleBackColor = False
        '
        'BtnEdit
        '
        Me.BtnEdit.BackColor = System.Drawing.Color.White
        Me.BtnEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnEdit.ForeColor = System.Drawing.Color.Black
        Me.BtnEdit.Image = Global.Basseet_SYS.My.Resources.Resources._18_06_2022_08_16_59_م
        Me.BtnEdit.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnEdit.Location = New System.Drawing.Point(119, 15)
        Me.BtnEdit.Name = "BtnEdit"
        Me.BtnEdit.Size = New System.Drawing.Size(108, 43)
        Me.BtnEdit.TabIndex = 38
        Me.BtnEdit.Text = "تعديل"
        Me.BtnEdit.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnEdit.UseVisualStyleBackColor = False
        '
        'BtnPrint
        '
        Me.BtnPrint.BackColor = System.Drawing.Color.White
        Me.BtnPrint.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnPrint.ForeColor = System.Drawing.Color.Black
        Me.BtnPrint.Image = Global.Basseet_SYS.My.Resources.Resources.printer_321
        Me.BtnPrint.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnPrint.Location = New System.Drawing.Point(233, 64)
        Me.BtnPrint.Name = "BtnPrint"
        Me.BtnPrint.Size = New System.Drawing.Size(104, 43)
        Me.BtnPrint.TabIndex = 40
        Me.BtnPrint.Text = "طباعة"
        Me.BtnPrint.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnPrint.UseVisualStyleBackColor = False
        '
        'PanelEx2
        '
        Me.PanelEx2.CanvasColor = System.Drawing.SystemColors.Control
        Me.PanelEx2.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PanelEx2.Location = New System.Drawing.Point(1041, 32)
        Me.PanelEx2.Name = "PanelEx2"
        Me.PanelEx2.Size = New System.Drawing.Size(163, 26)
        Me.PanelEx2.Style.Alignment = System.Drawing.StringAlignment.Center
        Me.PanelEx2.Style.BackColor1.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground
        Me.PanelEx2.Style.BackColor2.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2
        Me.PanelEx2.Style.Border = DevComponents.DotNetBar.eBorderType.SingleLine
        Me.PanelEx2.Style.BorderColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder
        Me.PanelEx2.Style.ForeColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText
        Me.PanelEx2.Style.GradientAngle = 90
        Me.PanelEx2.TabIndex = 0
        Me.PanelEx2.Text = "اسم القسم"
        '
        'BtnDelete
        '
        Me.BtnDelete.BackColor = System.Drawing.Color.White
        Me.BtnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnDelete.ForeColor = System.Drawing.Color.Black
        Me.BtnDelete.Image = Global.Basseet_SYS.My.Resources.Resources.Actions_application_exit_icon
        Me.BtnDelete.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnDelete.Location = New System.Drawing.Point(340, 63)
        Me.BtnDelete.Name = "BtnDelete"
        Me.BtnDelete.Size = New System.Drawing.Size(104, 43)
        Me.BtnDelete.TabIndex = 39
        Me.BtnDelete.Text = "حذف"
        Me.BtnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnDelete.UseVisualStyleBackColor = False
        '
        'PanelEx3
        '
        Me.PanelEx3.CanvasColor = System.Drawing.SystemColors.Control
        Me.PanelEx3.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PanelEx3.Location = New System.Drawing.Point(1041, 73)
        Me.PanelEx3.Name = "PanelEx3"
        Me.PanelEx3.Size = New System.Drawing.Size(163, 27)
        Me.PanelEx3.Style.Alignment = System.Drawing.StringAlignment.Center
        Me.PanelEx3.Style.BackColor1.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground
        Me.PanelEx3.Style.BackColor2.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2
        Me.PanelEx3.Style.Border = DevComponents.DotNetBar.eBorderType.SingleLine
        Me.PanelEx3.Style.BorderColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder
        Me.PanelEx3.Style.ForeColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText
        Me.PanelEx3.Style.GradientAngle = 90
        Me.PanelEx3.TabIndex = 1
        Me.PanelEx3.Text = "اختار الطابعة"
        '
        'TXT_DepName
        '
        Me.TXT_DepName.Location = New System.Drawing.Point(716, 32)
        Me.TXT_DepName.Name = "TXT_DepName"
        Me.TXT_DepName.Size = New System.Drawing.Size(254, 26)
        Me.TXT_DepName.TabIndex = 2
        '
        'BtnSave
        '
        Me.BtnSave.BackColor = System.Drawing.Color.White
        Me.BtnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnSave.ForeColor = System.Drawing.Color.Black
        Me.BtnSave.Image = Global.Basseet_SYS.My.Resources.Resources.save_32_blue
        Me.BtnSave.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnSave.Location = New System.Drawing.Point(233, 17)
        Me.BtnSave.Name = "BtnSave"
        Me.BtnSave.Size = New System.Drawing.Size(104, 43)
        Me.BtnSave.TabIndex = 36
        Me.BtnSave.Text = "حفظ"
        Me.BtnSave.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnSave.UseVisualStyleBackColor = False
        '
        'CmbPrinter
        '
        Me.CmbPrinter.FormattingEnabled = True
        Me.CmbPrinter.Location = New System.Drawing.Point(716, 74)
        Me.CmbPrinter.Name = "CmbPrinter"
        Me.CmbPrinter.Size = New System.Drawing.Size(254, 26)
        Me.CmbPrinter.TabIndex = 3
        '
        'BtnNew
        '
        Me.BtnNew.BackColor = System.Drawing.Color.White
        Me.BtnNew.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.BtnNew.ForeColor = System.Drawing.Color.Black
        Me.BtnNew.Image = Global.Basseet_SYS.My.Resources.Resources.Apps_Zoom_In_icon
        Me.BtnNew.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.BtnNew.Location = New System.Drawing.Point(340, 17)
        Me.BtnNew.Name = "BtnNew"
        Me.BtnNew.Size = New System.Drawing.Size(104, 43)
        Me.BtnNew.TabIndex = 37
        Me.BtnNew.Text = "جديد"
        Me.BtnNew.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.BtnNew.UseVisualStyleBackColor = False
        '
        'Check_active
        '
        Me.Check_active.AutoSize = True
        Me.Check_active.Checked = True
        Me.Check_active.CheckState = System.Windows.Forms.CheckState.Checked
        Me.Check_active.Location = New System.Drawing.Point(916, 119)
        Me.Check_active.Name = "Check_active"
        Me.Check_active.Size = New System.Drawing.Size(54, 22)
        Me.Check_active.TabIndex = 4
        Me.Check_active.Text = "فعال"
        Me.Check_active.UseVisualStyleBackColor = True
        '
        'PanelEx4
        '
        Me.PanelEx4.CanvasColor = System.Drawing.SystemColors.Control
        Me.PanelEx4.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PanelEx4.Location = New System.Drawing.Point(1041, 115)
        Me.PanelEx4.Name = "PanelEx4"
        Me.PanelEx4.Size = New System.Drawing.Size(163, 26)
        Me.PanelEx4.Style.Alignment = System.Drawing.StringAlignment.Center
        Me.PanelEx4.Style.BackColor1.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground
        Me.PanelEx4.Style.BackColor2.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2
        Me.PanelEx4.Style.Border = DevComponents.DotNetBar.eBorderType.SingleLine
        Me.PanelEx4.Style.BorderColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder
        Me.PanelEx4.Style.ForeColor.ColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText
        Me.PanelEx4.Style.GradientAngle = 90
        Me.PanelEx4.TabIndex = 5
        Me.PanelEx4.Text = "حالة القسم"
        '
        'Tab_DEP
        '
        Me.Tab_DEP.AttachedControl = Me.TabControlPanel1
        Me.Tab_DEP.Name = "Tab_DEP"
        Me.Tab_DEP.Text = "الاقسام"
        '
        'FRM_DEP_CAT
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1229, 593)
        Me.Controls.Add(Me.Tab_DEP_CAT)
        Me.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.Name = "FRM_DEP_CAT"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Tag = "الاقسام"
        Me.Text = "الاقسام"
        CType(Me.Tab_DEP_CAT, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Tab_DEP_CAT.ResumeLayout(False)
        Me.TabControlPanel2.ResumeLayout(False)
        Me.GroupPanel2.ResumeLayout(False)
        Me.GroupPanel2.PerformLayout()
        CType(Me.DgvCat, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabControlPanel1.ResumeLayout(False)
        CType(Me.Dgv_Dep, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupPanel1.ResumeLayout(False)
        Me.GroupPanel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Tab_DEP_CAT As DevComponents.DotNetBar.TabControl
    Friend WithEvents TabControlPanel1 As DevComponents.DotNetBar.TabControlPanel
    Friend WithEvents Tab_DEP As DevComponents.DotNetBar.TabItem
    Friend WithEvents PanelEx3 As DevComponents.DotNetBar.PanelEx
    Friend WithEvents PanelEx2 As DevComponents.DotNetBar.PanelEx
    Friend WithEvents TXT_DepName As TextBox
    Friend WithEvents PanelEx4 As DevComponents.DotNetBar.PanelEx
    Friend WithEvents Check_active As CheckBox
    Friend WithEvents CmbPrinter As ComboBox
    Friend WithEvents GroupPanel1 As DevComponents.DotNetBar.Controls.GroupPanel
    Friend WithEvents BtnEdit As Button
    Friend WithEvents BtnPrint As Button
    Friend WithEvents BtnDelete As Button
    Friend WithEvents BtnSave As Button
    Friend WithEvents BtnNew As Button
    Friend WithEvents Dgv_Dep As DataGridView
    Friend WithEvents Column1 As DataGridViewTextBoxColumn
    Friend WithEvents Column2 As DataGridViewTextBoxColumn
    Friend WithEvents Column3 As DataGridViewTextBoxColumn
    Friend WithEvents Column4 As DataGridViewTextBoxColumn
    Friend WithEvents BTN_CLOSE As Button
    Friend WithEvents TabControlPanel2 As DevComponents.DotNetBar.TabControlPanel
    Friend WithEvents Tab_CAT As DevComponents.DotNetBar.TabItem
    Friend WithEvents Txt_ID As TextBox
    Friend WithEvents TxtCat_ID As TextBox
    Friend WithEvents DgvCat As DataGridView
    Friend WithEvents DataGridViewTextBoxColumn1 As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn2 As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn3 As DataGridViewTextBoxColumn
    Friend WithEvents btnColor As Button
    Friend WithEvents btnSelectColor As Button
    Friend WithEvents CmbDep As ComboBox
    Friend WithEvents TxtCatName As TextBox
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents GroupPanel2 As DevComponents.DotNetBar.Controls.GroupPanel
    Friend WithEvents Button1 As Button
    Friend WithEvents ColorDialog1 As ColorDialog
    Friend WithEvents BtnNewCat As Button
    Friend WithEvents BtnSaveCat As Button
    Friend WithEvents BtnEditCat As Button
    Friend WithEvents BtnDeleteCat As Button
    Friend WithEvents BTN_REF As Button
End Class
