﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports System.Management '
Imports System.IO
Public Class FRM_DEP_CAT
    DIM CONNX As New CLS_CON
Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
         If Txt_DepName.Text = vbNullString Or cmbPrinter.Text = vbNullString Then
            MessageBox.Show("رجاء ، قم بتعبئة الكل", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Insert_Dep_tbl()
        Load_Dep()
        ClearText()
         My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
End Sub
     Public Sub Insert_Dep_tbl()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into Deptbl ( DepName,PrinterName,status)values(@DepName,@PrinterName,@status)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DepName", SqlDbType.VarChar).Value = Txt_DepName.Text
            .Parameters.AddWithValue("@PrinterName", SqlDbType.VarChar).Value = cmbPrinter.Text
            .Parameters.AddWithValue("@status", SqlDbType.Bit).Value = Check_active.CheckState
        End With
        If CONNX.Con.State = 1 Then CONNX.Con.Close()
        CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX.Con.Close()
        MsgBox("تم إضافة القسم بنجاح", MsgBoxStyle.Information, "حفظ")
Cmd = Nothing
End Sub
Public Sub ClearText()
Txt_DepName.Text = ""
cmbPrinter.Text = ""
        Txt_DepName.Focus()
        BtnSave.Enabled = True
        BtnEdit.Enabled = False
        BtnDelete.Enabled = False
        BtnNew.Enabled = False
PrintersCombo()
cmbPrinter.SelectedIndex = -1
    End Sub


Private Sub PrintersCombo()
    Try
        Dim i As Integer
        Dim pkInstalledPrinters As String

        ' تفريغ العناصر الحالية في القائمة
        cmbPrinter.Items.Clear()

            ' إضافة الطابعات المثبتة محلياً
            For i = 0 To PrinterSettings.InstalledPrinters.Count - 1
                pkInstalledPrinters = PrinterSettings.InstalledPrinters.Item(i)
                cmbPrinter.Items.Add(pkInstalledPrinters)
            Next
            ' إضافة طابعات الشبكة باستخدام WMI
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_Printer WHERE Network = TRUE")
        For Each printer As ManagementObject In searcher.Get()
            cmbPrinter.Items.Add(printer("Name").ToString())
        Next

    Catch ex As Exception
        MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.[Error])
    End Try
End Sub


    Private Sub FRM_CAT_DEP_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ClearText()
        Load_Dep()
        Load_Dep_Tbl()
        Load_Data_Cat()
        BtnNew.Enabled = True
    End Sub

    Private Sub BTN_CLOSE_Click(sender As Object, e As EventArgs) Handles BTN_CLOSE.Click
        Me.Close()
        Me.Dispose
        FRM_HOME.ButtonX1.Enabled = True
    End Sub
     Public Sub Load_Dep()
        Dgv_Dep.Rows.Clear()
        If connx.Con.State = 1 Then  connx.Con.Close()
        connx.Con.Open()
        connx.Cmd = New SqlCommand("Select * from DepTbl", connx.Con)
        connx.rdr =  connx.Cmd.ExecuteReader
        While  connx.rdr.Read
            Dim MY_Status As String = ""
            If  connx.rdr("status") = True Then
                MY_Status = "فعالة"
            Else
                MY_Status = "غير فعالة"
            End If
            Dgv_Dep.Rows.Add( connx.rdr("DepID").ToString,  connx.rdr("DepName").ToString,  connx.rdr("PrinterName").ToString, MY_Status)
        End While
         connx.rdr.Close()
         connx.Con.Close()
    End Sub

    Private Sub Dgv_Dep_Click(sender As Object, e As EventArgs) Handles Dgv_Dep.Click
              Txt_ID.Text = Dgv_Dep.CurrentRow.Cells(0).Value
        Txt_DepName.Text = Dgv_Dep.CurrentRow.Cells(1).Value
        cmbPrinter.Text = Dgv_Dep.CurrentRow.Cells(2).Value
        If Dgv_Dep.CurrentRow.Cells(3).Value = "فعالة" Then
            Check_active.Checked = True
        Else
            Check_active.Checked = False
        End If
        BtnSave.Enabled = False
        BtnEdit.Enabled = True
        BtnDelete.Enabled = True
        BtnNew.Enabled = True
    End Sub
     Public Sub Update_Dep_tbl()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Deptbl Set DepName = @DepName,PrinterName = @PrinterName,Status = @Status Where DepID = @DepID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DepName", SqlDbType.VarChar).Value = Txt_DepName.Text
            .Parameters.AddWithValue("@PrinterName", SqlDbType.VarChar).Value = cmbPrinter.Text
            .Parameters.AddWithValue("@Status", SqlDbType.Decimal).Value = Check_active.CheckState
            .Parameters.AddWithValue("@DepID", SqlDbType.Int).Value = Txt_ID.Text
        End With
        If CONNX.Con.State = 1 Then CONNX.Con.Close()
         CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
         CONNX.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
        Cmd = Nothing
    End Sub
    Private Sub BtnEdit_Click(sender As Object, e As EventArgs) Handles BtnEdit.Click
              If Txt_DepName.Text = vbNullString Or cmbPrinter.Text = vbNullString Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Update_Dep_tbl()
        ClearText()
        Load_Dep()
    End Sub

    Private Sub Button1_MouseHover(sender As Object, e As EventArgs) Handles Button1.MouseHover
        Button1.BackColor=Color.LimeGreen

    End Sub

    Private Sub Button1_MouseLeave(sender As Object, e As EventArgs) Handles Button1.MouseLeave
         Button1.BackColor=Color.White
    End Sub
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&التصنيفات&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
    Public Sub Load_Dep_Tbl()
        connx.dt = New DataTable
        CmbDep.DataSource = Nothing
        CmbDep.Items.Clear()
        CmbDep.Text = vbNullString
        connx.dt.Clear()
        connx.Cmd = New SqlCommand("Select DepID,DepName From DepTbl ", connx.Con) '
        Try
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            connx.dt.Load(connx.Cmd.ExecuteReader)
            connx.Con.Close()
            connx.Cmd = Nothing
        Catch ex As Exception
            MessageBox.Show(ex.Message)
            connx.Con.Close()
        End Try
        If connx.dt.Rows.Count <> 0 Then
            With CmbDep
                .DataSource = connx.dt
                .DisplayMember = "DepName"
                .ValueMember = "DepID"
            End With
        End If
        If CmbDep.Items.Count > 0 Then CmbDep.SelectedIndex = -1
    End Sub
    '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    Public Sub Load_Data_Cat()
        DgvCat.Rows.Clear()
        If CONNX.Con.State = 1 Then CONNX.Con.Close()
        CONNX.Con.Open()
        Dim cmd As New SqlCommand(" SELECT  dbo.Cat_Tbl.Cat_ID, dbo.Cat_Tbl.CatName, dbo.Cat_Tbl.CatColor, dbo.Cat_Tbl.depID, dbo.depTBL.depID AS Expr1, dbo.depTBL.depName FROM dbo.Cat_Tbl INNER JOIn    dbo.depTBL ON dbo.Cat_Tbl.depID = dbo.depTBL.depID", CONNX.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            DgvCat.Rows.Add(dr("Cat_ID").ToString, dr("CatName").ToString, dr("DepName").ToString)
        End While
        dr.Close()
        CONNX.Con.Close()
    End Sub
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
     Public Sub InsertCat()
        Dim CmdInsert As New SqlCommand
        With CmdInsert
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into Cat_Tbl (CatName,DepID,CatColor)values( @CatName,@DepID,@CatColor)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CatName", SqlDbType.VarChar).Value = TxtCatName.Text
            .Parameters.AddWithValue("@DepID", SqlDbType.Int).Value = CmbDep.SelectedValue
            .Parameters.AddWithValue("@CatColor", SqlDbType.VarChar).Value = btnColor.BackColor.ToArgb()
        End With
        Try
            If CONNX.Con.State = 1 Then CONNX.Con.Close()
            CONNX.Con.Open()
            CmdInsert.ExecuteNonQuery()
            CONNX.Con.Close()
            MsgBox("تمن الاضافة بنجاح", MsgBoxStyle.Information, "حفظ")
            CmdInsert = Nothing
        Catch ex As Exception
            CONNX.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        End Try
    End Sub
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
      Public Sub ClearCat()

        TxtCatName.Text = ""
        CmbDep.Text = ""
        TxtCatName.Focus()
        BtnSaveCat.Enabled = True
        BtnEditCat.Enabled = False
        BtnDeleteCat.Enabled = False
        BtnNewCat.Enabled = False
    End Sub
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
    
                        
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
    Private Sub btnSelectColor_Click(sender As Object, e As EventArgs) Handles btnSelectColor.Click
        Try
            ' Show the color dialog.
            Dim result As DialogResult = ColorDialog1.ShowDialog()
            ' See if user pressed ok.
            If result = DialogResult.OK Then
                ' Set form background to the selected color.
                Me.btnColor.BackColor = ColorDialog1.Color
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnSaveCat_Click(sender As Object, e As EventArgs) Handles BtnSaveCat.Click
        If TxtCatName.Text = vbNullString Or CmbDep.Text = vbNullString Then
            MessageBox.Show("رجاء ، قم بتعبئة الكل", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
     End If
        InsertCat()
        ClearCat()
        Load_Data_Cat()
    End Sub

    Private Sub BtnNewCat_Click(sender As Object, e As EventArgs) Handles BtnNewCat.Click
         ClearCat()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.CLOSE
 Me.Dispose
     FRM_HOME.ButtonX1.Enabled=TRUE
    End Sub
        Public Sub Delete_Dep_tbl(ByVal dgv_Dep_tbl As DataGridView)
        Dim Position As Integer = dgv_Dep_tbl.CurrentRow.Index
        Dim ID_Position As Integer = dgv_Dep_tbl.Rows(Position).Cells(0).Value
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Delete  From Deptbl Where DepID = @DepID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DepID", SqlDbType.Int).Value = ID_Position
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم حذف السجل.", MsgBoxStyle.Information, "حذف")
        Cmd = Nothing
    End Sub
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        Delete_Dep_tbl(Dgv_Dep)
         Load_Dep()
    End Sub
    
    Public Sub UpdateCat()
        Dim CmdUpdate As New SqlCommand
        With CmdUpdate
            .connection = connx.con
            .CommandType = CommandType.Text
            .CommandText = "Update Cat_Tbl Set CatName = @CatName,DepID=@DepID,CatColor=@CatColor Where Cat_ID = @Cat_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CatName", SqlDbType.VarChar).Value = TxtCatName.Text
            .Parameters.AddWithValue("@DepID", SqlDbType.Int).Value = CmbDep.SelectedValue
            .Parameters.AddWithValue("@CatColor", SqlDbType.VarChar).Value = btnColor.BackColor.ToArgb()
            .Parameters.AddWithValue("@Cat_ID", SqlDbType.Int).Value = TxtCat_ID.Text
        End With
        Try
            If connx.con.State = 1 Then connx.con.Close()
            connx.con.Open()
            CmdUpdate.ExecuteNonQuery()
            connx.con.Close()
            MsgBox("تم تحديث السجل بنجاح", MsgBoxStyle.Information, "تحديث")
            CmdUpdate = Nothing
        Catch ex As Exception
            connx.con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If connx.con.State = connectionState.Open Then connx.con.Close()
        End Try
    End Sub

    Private Sub BtnEditCat_Click(sender As Object, e As EventArgs) Handles BtnEditCat.Click
         UpdateCat()
             Load_Data_Cat()
    End Sub

    Private Sub DgvCat_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvCat.CellClick
                       TxtCat_ID.Text = DgvCat.CurrentRow.Cells(0).Value
        TxtCatName.Text = DgvCat.CurrentRow.Cells(1).Value
        CmbDep.Text = DgvCat.CurrentRow.Cells(2).Value
        BtnSaveCat.Enabled = False
        BtnEditCat.Enabled = True
        BtnDeleteCat.Enabled = True
        BtnNewCat.Enabled = True
    
    End Sub
    '^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        Public Sub Delete_Cat_Tbl(ByVal dgv_Cat_Tbl As DataGridView)
        Dim Position As Integer = dgv_Cat_Tbl.CurrentRow.Index
        Dim ID_Position As Integer = dgv_Cat_Tbl.Rows(Position).Cells(0).Value
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Delete  From Cat_Tbl Where Cat_ID = @Cat_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Cat_ID", SqlDbType.Int).Value = ID_Position
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم حذف السجل.", MsgBoxStyle.Information, "حذف")
        Cmd = Nothing
    End Sub
    '****************************************************************************************************************
    Private Sub BtnDeleteCat_Click(sender As Object, e As EventArgs) Handles BtnDeleteCat.Click
           If TxtCat_ID.Text = "" Then
            MsgBox("يجب احتيار سجل لحذفه")
            Exit Sub
        End If
        If MessageBox.Show("هل أنت متأكد من مواصلة عملية الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
            Exit Sub
        Else
            Delete_Cat_Tbl(DgvCat)
        End If
        ClearCat()
        Load_Data_Cat()
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearText()


    End Sub

End Class