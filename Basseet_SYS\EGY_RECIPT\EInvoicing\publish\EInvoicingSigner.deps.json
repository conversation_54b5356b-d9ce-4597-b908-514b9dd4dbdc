{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {}, ".NETCoreApp,Version=v3.1/win-x64": {"EInvoicingSigner/1.0.0": {"dependencies": {"BouncyCastle": "*******", "Newtonsoft.Json": "12.0.3", "Pkcs11Interop": "5.1.1", "System.Security.Cryptography.Pkcs": "5.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "3.1.9"}, "runtime": {"EInvoicingSigner.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/3.1.9": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "10.0.5.0", "fileVersion": "4.700.20.47203"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "4.700.20.47203"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "4.0.15.0", "fileVersion": "4.700.20.47203"}, "System.Collections.Immutable.dll": {"assemblyVersion": "1.2.5.0", "fileVersion": "4.700.20.47203"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "4.3.1.0", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.20.47203"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.6.0", "fileVersion": "4.700.20.47203"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.20.47203"}, "System.Private.Uri.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "4.700.20.47203"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "4.700.20.47203"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.700.20.47203"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.700.20.47203"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "1.4.5.0", "fileVersion": "4.700.20.47203"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.20.47203"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.6.0", "fileVersion": "4.700.20.47203"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.InteropServices.WindowsRuntime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.20.47203"}, "System.Runtime.Loader.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.700.20.47203"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "4.1.5.0", "fileVersion": "4.700.20.47203"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.WindowsRuntime.UI.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Runtime.WindowsRuntime.dll": {"assemblyVersion": "4.0.15.0", "fileVersion": "4.700.20.47203"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "4.3.2.0", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "4.6.5.0", "fileVersion": "4.700.20.47203"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.3.1.0", "fileVersion": "4.700.20.47203"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47203"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "4.700.20.47203"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.12.25830.2"}, "SOS_README.md": {"fileVersion": "0.0.0.0"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.47201"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.17134.12"}, "clrcompression.dll": {"fileVersion": "4.700.20.47203"}, "clretwrc.dll": {"fileVersion": "4.700.20.47201"}, "clrjit.dll": {"fileVersion": "4.700.20.47201"}, "coreclr.dll": {"fileVersion": "4.700.20.47201"}, "dbgshim.dll": {"fileVersion": "4.700.20.47201"}, "hostfxr.dll": {"fileVersion": "3.100.920.47301"}, "hostpolicy.dll": {"fileVersion": "3.100.920.47301"}, "mscordaccore.dll": {"fileVersion": "4.700.20.47201"}, "mscordaccore_amd64_amd64_4.700.20.47201.dll": {"fileVersion": "4.700.20.47201"}, "mscordbi.dll": {"fileVersion": "4.700.20.47201"}, "mscorrc.debug.dll": {"fileVersion": "4.700.20.47201"}, "mscorrc.dll": {"fileVersion": "4.700.20.47201"}, "ucrtbase.dll": {"fileVersion": "10.0.17134.12"}}}, "BouncyCastle/*******": {"runtime": {"lib/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "1.8.20052.1"}}}, "Newtonsoft.Json/12.0.3": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "12.0.3.23909"}}}, "Pkcs11Interop/5.1.1": {"runtime": {"lib/netstandard2.0/Pkcs11Interop.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Formats.Asn1/5.0.0": {"runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}, "runtime": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.Pkcs/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}}}, "libraries": {"EInvoicingSigner/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/3.1.9": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "BouncyCastle/*******": {"type": "package", "serviceable": true, "sha512": "sha512-rkqpuKmJkdcfTMBkIj1b5nMdBAWKwAyh+I/BQYeqqSD2jkIlhwc9qBNKmSbnpmmm5c29qHZGLpvBSTNWvDLtQA==", "path": "bouncycastle/*******", "hashPath": "bouncycastle.*******.nupkg.sha512"}, "Newtonsoft.Json/12.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6mgjfnRB4jKMlzHSl+VD+oUc1IebOZabkbyWj2RiTgWwYPPuaK1H97G1sHqGwPlS5npiF5Q0OrxN1wni2n5QWg==", "path": "newtonsoft.json/12.0.3", "hashPath": "newtonsoft.json.12.0.3.nupkg.sha512"}, "Pkcs11Interop/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-YDj3v4LN318ToSLeg+5NSJUhH2rpmUGsQN/XL87rMVkq5iZnaV759c3unFJR+W83hGV72I8aa6v+eI8jnstFlg==", "path": "pkcs11interop/5.1.1", "hashPath": "pkcs11interop.5.1.1.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9TPLGjBCGKmNvG8pjwPeuYy0SMVmGZRwlTZvyPHDbYv/DRkoeumJdfumaaDNQzVGMEmbWtg07zUpSW9q70IlDQ==", "path": "system.security.cryptography.pkcs/5.0.0", "hashPath": "system.security.cryptography.pkcs.5.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}