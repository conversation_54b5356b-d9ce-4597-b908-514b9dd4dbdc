{"info": {"_postman_id": "046c4513-dbed-4a9f-a44a-1e41f24a10dd", "name": "Egyptian eInvoicing SDK R3.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "11816"}, "item": [{"name": "Common", "item": [{"name": "1. <PERSON><PERSON> as Taxpayer System", "event": [{"listen": "test", "script": {"exec": ["\r", "pm.test(\"Status code is 200\", function () {\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"generatedAccessToken\", jsonData.access_token);\r", "});\r", "\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{clientId}}", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}, {"key": "scope", "value": "InvoicingAPI", "type": "text"}]}, "url": {"raw": "{{idSrvBaseUrl}}/connect/token", "host": ["{{idSrvBaseUrl}}"], "path": ["connect", "token"]}}, "response": []}, {"name": "2. Get Document Types", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"documentTypeCodeName\": \"creditupdated\",\r\n    \"namePrimaryLang\": \"CreditEn\",\r\n    \"nameSecondaryLang\": \"CreditAr\",\r\n    \"descriptionPrimaryLang\": \"Des-creditEn\",\r\n    \"descriptionSecondaryLang\": \"Des-creditAr\",\r\n    \"activeFrom\": \"2020-06-09T07:38:54.836Z\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documenttypes", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documenttypes"]}}, "response": []}, {"name": "3. Get Document Type", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "", "type": "text", "value": "", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documenttypes/:documentTypeID", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documenttypes", ":documentTypeID"], "variable": [{"key": "documentTypeID", "value": ""}]}}, "response": []}, {"name": "4. Get Document Type Version", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documenttypes/:documentTypeID/versions/:documentTypeVersionID", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documenttypes", ":documentTypeID", "versions", ":documentTypeVersionID"], "variable": [{"key": "documentTypeID", "value": ""}, {"key": "documentTypeVersionID", "value": ""}]}}, "response": []}, {"name": "5. Get Notifications", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/notifications/taxpayer?pageSize=10&pageNo=1&dateFrom=&dateTo=&type=&language=en&status=&channel=", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "notifications", "taxpayer"], "query": [{"key": "pageSize", "value": "10"}, {"key": "pageNo", "value": "1"}, {"key": "dateFrom", "value": ""}, {"key": "dateTo", "value": ""}, {"key": "type", "value": ""}, {"key": "language", "value": "en"}, {"key": "status", "value": ""}, {"key": "channel", "value": ""}]}}, "response": []}, {"name": "6. Create EGS Code Usage", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"items\": [\r\n        {\r\n            \"codeType\": \"EGS\",\r\n            \"parentCode\": \"10000051\",\r\n            \"itemCode\": \"EG-*********-5598542\",\r\n            \"codeName\": \"Test Code\",\r\n            \"codeNameAr\": \"كود تجريبي\",\r\n            \"activeFrom\": \"2021-02-20T00:00:00.000\",\r\n            \"activeTo\": \"2021-04-22T00:00:00.000\",\r\n            \"description\": \"Description of code\",\r\n            \"descriptionAr\": \"وصٝ الكود بالعربي\",\r\n            \"requestReason\": \"Request reason text\"\r\n        },\r\n        {\r\n            \"codeType\": \"EGS\",\r\n            \"parentCode\": \"10000051\",\r\n            \"itemCode\": \"EG-*********-5598543\",\r\n            \"codeName\": \"Test Code 1\",\r\n            \"codeNameAr\": \"1 كود تجريبي\",\r\n            \"activeFrom\": \"2021-02-20T00:00:00.000\",\r\n            \"activeTo\": \"2021-04-22T00:00:00.000\",\r\n            \"description\": \"Description of code 1\",\r\n            \"descriptionAr\": \" 1 وصٝ الكود بالعربي\",\r\n            \"requestReason\": \"Request reason text\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/requests/codes", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", "requests", "codes"]}}, "response": []}, {"name": "7. Search my EGS code usage requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "en", "type": "text"}], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/requests/my?Active=true&Status=Approved&Ps=10&Pn=1&OrderDirections=Descending", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", "requests", "my"], "query": [{"key": "ItemCode", "value": "EG-100027962-1208617", "disabled": true}, {"key": "CodeName", "value": "Code Name", "disabled": true}, {"key": "CodeDescription", "value": "Perf Test Desc", "disabled": true}, {"key": "ParentLevelName", "value": "EGS Level 4 Code", "disabled": true}, {"key": "ParentItemCode", "value": "100000", "disabled": true}, {"key": "ActiveFrom", "value": "2021-03-21T00:00:00Z", "disabled": true}, {"key": "ActiveTo", "value": "2021-05-21T23:59:00Z", "disabled": true}, {"key": "Active", "value": "true"}, {"key": "Status", "value": "Approved"}, {"key": "Ps", "value": "10"}, {"key": "Pn", "value": "1"}, {"key": "OrderDirections", "value": "Descending"}, {"key": "RequestType", "value": "New", "disabled": true}]}}, "response": []}, {"name": "8. Request Code Reuse", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"codetype\": \"EGS\",\n            \"itemCode\": \"EG-*********-5598542\",\n            \"comment\": \"create code usage reason\"\n        },\n        {\n            \"codetype\": \"EGS\",\n            \"itemCode\": \"EG-100000053-10015\",\n            \"comment\": \"create code usage reason\"\n        }\n    ]\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/requests/codeusages", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", "requests", "codeusages"]}}, "response": []}, {"name": "9. Search Published Codes", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept-Language", "value": "en", "type": "text"}], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/:codeType/codes?ParentLevelName=GPC Level 4 Code - Brick&OnlyActive=true&ActiveFrom=2019-01-01T00:00:00Z&Ps=10&Pn=1&OrdDir=Descending&CodeTypeLevelNumber=5", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", ":codeType", "codes"], "query": [{"key": "CodeLookupValue", "value": "6222007147906", "disabled": true}, {"key": "ParentCodeLookupValue", "value": "10002443", "disabled": true}, {"key": "CodeID", "value": "1127443", "disabled": true}, {"key": "CodeName", "value": "CERAMICA INNOVA", "disabled": true}, {"key": "CodeDescription", "value": "Code Description", "disabled": true}, {"key": "TaxpayerRIN", "value": "RIN Of Taxpayer", "disabled": true}, {"key": "ParentCodeID", "value": "4767", "disabled": true}, {"key": "ParentLevelName", "value": "GPC Level 4 Code - Brick"}, {"key": "OnlyActive", "value": "true"}, {"key": "ActiveFrom", "value": "2019-01-01T00:00:00Z"}, {"key": "ActiveTo", "value": "2022-01-01T00:00:00Z", "disabled": true}, {"key": "Ps", "value": "10"}, {"key": "Pn", "value": "1"}, {"key": "OrdDir", "value": "Descending"}, {"key": "CodeTypeLevelNumber", "value": "5"}], "variable": [{"key": "codeType", "value": "GS1"}]}}, "response": []}, {"name": "10. Get Code Details By Item Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/:codeType/codes/:itemCode", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", ":codeType", "codes", ":itemCode"], "variable": [{"key": "codeType", "value": "EGS"}, {"key": "itemCode", "value": ""}]}}, "response": []}, {"name": "11. Update EGS Code Usage", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"itemCode\": \"EG-*********-ABCD\",\n    \"codeName\": \"ABCD Updated\",\n    \"codeNameAr\": \"ABCD Updated Arabic\",\n    \"activeFrom\": \"2021-04-06T00:00:00Z\",\n    \"activeTo\": \"2021-06-30T23:59:59Z\",\n    \"description\": \"Description of code 123\",\n    \"descriptionAr\": \" 2314 وصٝ الكود بالعربي\",\n    \"parentCode\": \"10000051\",\n    \"requestReason\": \"Request reason text Updated\",\n    \"linkedCode\": \"EG-728466198-Yes\"\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/requests/codes/:codeUsageRequestId", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", "requests", "codes", ":codeUsageRequestId"], "variable": [{"key": "codeUsageRequestId", "value": ""}]}}, "response": []}, {"name": "12. Update Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"codeDescriptionPrimaryLang\": \"Dh108108 Updated\",\n    \"codeDescriptionSecondaryLang\": \"Dh108108 Updated\",\n    \"activeTo\": null,\n    \"linkedCode\": \"\"\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/codetypes/:codeType/codes/:itemCode", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "codetypes", ":codeType", "codes", ":itemCode"], "variable": [{"key": "codeType", "value": "EGS"}, {"key": "itemCode", "value": ""}]}}, "response": []}]}, {"name": "eInvoicing", "item": [{"name": "Submit Regular Documents", "item": [{"name": "JSON Format", "item": [{"name": "1. Submit Invoice", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"0\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Issuer Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Egypt\",\r\n                    \"regionCity\": \"Mufazat al Ismlyah\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Receiver\"\r\n            },\r\n            \"documentType\": \"I\",\r\n            \"documentTypeVersion\": \"0.9\",\r\n            \"dateTimeIssued\": \"2021-02-07T02:04:45Z\",\r\n            \"taxpayerActivityCode\": \"4620\",\r\n            \"internalID\": \"IID1\",\r\n            \"purchaseOrderReference\": \"P-233-A6375\",\r\n            \"purchaseOrderDescription\": \"purchase Order description\",\r\n            \"salesOrderReference\": \"1231\",\r\n            \"salesOrderDescription\": \"Sales Order description\",\r\n            \"proformaInvoiceNumber\": \"SomeValue\",\r\n            \"payment\": {\r\n                \"bankName\": \"SomeValue\",\r\n                \"bankAddress\": \"SomeValue\",\r\n                \"bankAccountNo\": \"SomeValue\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"SomeValue\",\r\n                \"packaging\": \"SomeValue\",\r\n                \"dateValidity\": \"2020-09-28T09:30:10Z\",\r\n                \"exportPort\": \"SomeValue\",\r\n                \"grossWeight\": 10.50,\r\n                \"netWeight\": 20.50,\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 1,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": ************.00,\r\n                    \"total\": ************.00,\r\n                    \"valueDifference\": 0.00,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": ************,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": ************.00\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V001\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 1,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": ************.00,\r\n                    \"total\": ************.00,\r\n                    \"valueDifference\": 0.00,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": ************,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": ************.00\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V001\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 1,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": ************.00,\r\n                    \"total\": ************.00,\r\n                    \"valueDifference\": 0.00,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": ************,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": ************.00\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V001\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 1,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": ************.00,\r\n                    \"total\": ************.00,\r\n                    \"valueDifference\": 0.00,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": ************,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": ************.00\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V001\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 1,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": ************.00,\r\n                    \"total\": ************.00,\r\n                    \"valueDifference\": 0.00,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": ************,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": ************.00\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V001\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 0,\r\n            \"totalSalesAmount\": 555555555555.00,\r\n            \"netAmount\": 555555555555.00,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 0\r\n                }\r\n            ],\r\n            \"totalAmount\": 555555555555.00,\r\n            \"extraDiscountAmount\": 0,\r\n            \"totalItemsDiscountAmount\": 0,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Debit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"1\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Issuer Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Egypt\",\r\n                    \"regionCity\": \"Mufazat al Ismlyah\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Receiver\"\r\n            },\r\n            \"documentType\": \"D\",\r\n            \"documentTypeVersion\": \"0.9\",\r\n            \"dateTimeIssued\": \"2021-02-08T23:59:59Z\",\r\n            \"taxpayerActivityCode\": \"4620\",\r\n            \"internalID\": \"IID1\",\r\n            \"purchaseOrderReference\": \"P-233-A6375\",\r\n            \"purchaseOrderDescription\": \"purchase Order description\",\r\n            \"salesOrderReference\": \"1231\",\r\n            \"salesOrderDescription\": \"Sales Order description\",\r\n            \"proformaInvoiceNumber\": \"SomeValue\",\r\n              \"references\": [\r\n                \"5Z40TP7SXAKADVH8WX71PXNE10\"\r\n            ],\r\n            \"payment\": {\r\n                \"bankName\": \"SomeValue\",\r\n                \"bankAddress\": \"SomeValue\",\r\n                \"bankAccountNo\": \"SomeValue\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"SomeValue\",\r\n                \"packaging\": \"SomeValue\",\r\n                \"dateValidity\": \"2020-09-28T09:30:10Z\",\r\n                \"exportPort\": \"SomeValue\",\r\n                \"countryOfOrigin\": \"EG\",\r\n                \"grossWeight\": 10.50,\r\n                \"netWeight\": 20.50,\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 5,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": 947.00,\r\n                    \"total\": 2969.89,\r\n                    \"valueDifference\": 7.00,\r\n                    \"totalTaxableFees\": 817.42,\r\n                    \"netTotal\": 880.71,\r\n                    \"itemsDiscount\": 5.00,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EUR\",\r\n                        \"amountEGP\": 189.40,\r\n                        \"amountSold\": 10.00,\r\n                        \"currencyExchangeRate\": 18.94\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 7,\r\n                        \"amount\": 66.29\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 272.07,\r\n                            \"subType\": \"T1\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T2\",\r\n                            \"amount\": 208.22,\r\n                            \"subType\": \"T2\",\r\n                            \"rate\": 12\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T3\",\r\n                            \"amount\": 30.00,\r\n                            \"subType\": \"T3\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T4\",\r\n                            \"amount\": 43.79,\r\n                            \"subType\": \"T4\",\r\n                            \"rate\": 5.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T5\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T5\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T6\",\r\n                            \"amount\": 60.00,\r\n                            \"subType\": \"T6\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T7\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T7\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T8\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T8\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T9\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T9\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T10\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T10\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T11\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T11\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T12\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T12\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T13\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T13\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T14\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T14\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T15\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T15\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T16\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T16\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T17\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T17\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T18\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T18\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T19\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T19\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T20\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T20\",\r\n                            \"rate\": 10.00\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer2\",\r\n                    \"itemType\": \"GPC\",\r\n                    \"itemCode\": \"********\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 7,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": 662.90,\r\n                    \"total\": 2226.61,\r\n                    \"valueDifference\": 6.00,\r\n                    \"totalTaxableFees\": 621.51,\r\n                    \"netTotal\": 652.90,\r\n                    \"itemsDiscount\": 9.00,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EUR\",\r\n                        \"amountEGP\": 94.70,\r\n                        \"amountSold\": 5.00,\r\n                        \"currencyExchangeRate\": 18.94\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 10.00\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 205.47,\r\n                            \"subType\": \"T1\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T2\",\r\n                            \"amount\": 157.25,\r\n                            \"subType\": \"T2\",\r\n                            \"rate\": 12\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T3\",\r\n                            \"amount\": 30.00,\r\n                            \"subType\": \"T3\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T4\",\r\n                            \"amount\": 32.20,\r\n                            \"subType\": \"T4\",\r\n                            \"rate\": 5.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T5\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T5\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T6\",\r\n                            \"amount\": 60.00,\r\n                            \"subType\": \"T6\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T7\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T7\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T8\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T8\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T9\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T9\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T10\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T10\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T11\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T11\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T12\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T12\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T13\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T13\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T14\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T14\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T15\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T15\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T16\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T16\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T17\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T17\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T18\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T18\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T19\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T19\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T20\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T20\",\r\n                            \"rate\": 10.00\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 76.29,\r\n            \"totalSalesAmount\": 1609.90,\r\n            \"netAmount\": 1533.61,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 477.54\r\n                },\r\n                {\r\n                    \"taxType\": \"T2\",\r\n                    \"amount\": 365.47\r\n                },\r\n                {\r\n                    \"taxType\": \"T3\",\r\n                    \"amount\": 60.00\r\n                },\r\n                {\r\n                    \"taxType\": \"T4\",\r\n                    \"amount\": 75.99\r\n                },\r\n                {\r\n                    \"taxType\": \"T5\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T6\",\r\n                    \"amount\": 120.00\r\n                },\r\n                {\r\n                    \"taxType\": \"T7\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T8\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T9\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T10\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T11\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T12\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T13\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T14\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T15\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T16\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T17\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T18\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T19\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T20\",\r\n                    \"amount\": 153.36\r\n                }\r\n            ],\r\n            \"totalAmount\": 5191.50,\r\n            \"extraDiscountAmount\": 5.00,\r\n            \"totalItemsDiscountAmount\": 14.00,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Credit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"1\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Issuer Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Egypt\",\r\n                    \"regionCity\": \"Mufazat al Ismlyah\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"68030\",\r\n                    \"floor\": \"1\",\r\n                    \"room\": \"123\",\r\n                    \"landmark\": \"7660 Melody Trail\",\r\n                    \"additionalInformation\": \"beside Townhall\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Receiver\"\r\n            },\r\n            \"documentType\": \"C\",\r\n            \"documentTypeVersion\": \"0.9\",\r\n            \"dateTimeIssued\": \"2020-10-29T23:59:59Z\",\r\n            \"taxpayerActivityCode\": \"4620\",\r\n            \"internalID\": \"IID1\",\r\n            \"purchaseOrderReference\": \"P-233-A6375\",\r\n            \"purchaseOrderDescription\": \"purchase Order description\",\r\n            \"salesOrderReference\": \"1231\",\r\n            \"salesOrderDescription\": \"Sales Order description\",\r\n            \"proformaInvoiceNumber\": \"SomeValue\",\r\n              \"references\": [\r\n                \"5Z40TP7SXAKADVH8WX71PXNE10\"\r\n            ],\r\n            \"payment\": {\r\n                \"bankName\": \"SomeValue\",\r\n                \"bankAddress\": \"SomeValue\",\r\n                \"bankAccountNo\": \"SomeValue\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"SomeValue\",\r\n                \"packaging\": \"SomeValue\",\r\n                \"dateValidity\": \"2020-09-28T09:30:10Z\",\r\n                \"exportPort\": \"SomeValue\",\r\n                \"countryOfOrigin\": \"EG\",\r\n                \"grossWeight\": 10.50,\r\n                \"netWeight\": 20.50,\r\n                \"terms\": \"SomeValue\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"EGS\",\r\n                    \"itemCode\": \"EG-*********-123456\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 5,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": 947.00,\r\n                    \"total\": 2969.89,\r\n                    \"valueDifference\": 7.00,\r\n                    \"totalTaxableFees\": 817.42,\r\n                    \"netTotal\": 880.71,\r\n                    \"itemsDiscount\": 5.00,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EUR\",\r\n                        \"amountEGP\": 189.40,\r\n                        \"amountSold\": 10.00,\r\n                        \"currencyExchangeRate\": 18.94\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 7,\r\n                        \"amount\": 66.29\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 272.07,\r\n                            \"subType\": \"T1\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T2\",\r\n                            \"amount\": 208.22,\r\n                            \"subType\": \"T2\",\r\n                            \"rate\": 12\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T3\",\r\n                            \"amount\": 30.00,\r\n                            \"subType\": \"T3\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T4\",\r\n                            \"amount\": 43.79,\r\n                            \"subType\": \"T4\",\r\n                            \"rate\": 5.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T5\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T5\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T6\",\r\n                            \"amount\": 60.00,\r\n                            \"subType\": \"T6\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T7\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T7\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T8\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T8\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T9\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T9\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T10\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T10\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T11\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T11\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T12\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T12\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T13\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T13\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T14\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T14\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T15\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T15\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T16\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T16\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T17\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T17\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T18\",\r\n                            \"amount\": 123.30,\r\n                            \"subType\": \"T18\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T19\",\r\n                            \"amount\": 105.69,\r\n                            \"subType\": \"T19\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T20\",\r\n                            \"amount\": 88.07,\r\n                            \"subType\": \"T20\",\r\n                            \"rate\": 10.00\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    \"description\": \"Computer2\",\r\n                    \"itemType\": \"GPC\",\r\n                    \"itemCode\": \"********\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 7,\r\n                    \"internalCode\": \"IC0\",\r\n                    \"salesTotal\": 662.90,\r\n                    \"total\": 2226.61,\r\n                    \"valueDifference\": 6.00,\r\n                    \"totalTaxableFees\": 621.51,\r\n                    \"netTotal\": 652.90,\r\n                    \"itemsDiscount\": 9.00,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EUR\",\r\n                        \"amountEGP\": 94.70,\r\n                        \"amountSold\": 5.00,\r\n                        \"currencyExchangeRate\": 18.94\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 10.00\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 205.47,\r\n                            \"subType\": \"T1\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T2\",\r\n                            \"amount\": 157.25,\r\n                            \"subType\": \"T2\",\r\n                            \"rate\": 12\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T3\",\r\n                            \"amount\": 30.00,\r\n                            \"subType\": \"T3\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T4\",\r\n                            \"amount\": 32.20,\r\n                            \"subType\": \"T4\",\r\n                            \"rate\": 5.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T5\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T5\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T6\",\r\n                            \"amount\": 60.00,\r\n                            \"subType\": \"T6\",\r\n                            \"rate\": 0.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T7\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T7\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T8\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T8\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T9\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T9\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T10\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T10\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T11\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T11\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T12\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T12\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T13\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T13\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T14\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T14\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T15\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T15\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T16\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T16\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T17\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T17\",\r\n                            \"rate\": 10.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T18\",\r\n                            \"amount\": 91.41,\r\n                            \"subType\": \"T18\",\r\n                            \"rate\": 14.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T19\",\r\n                            \"amount\": 78.35,\r\n                            \"subType\": \"T19\",\r\n                            \"rate\": 12.00\r\n                        },\r\n                        {\r\n                            \"taxType\": \"T20\",\r\n                            \"amount\": 65.29,\r\n                            \"subType\": \"T20\",\r\n                            \"rate\": 10.00\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 76.29,\r\n            \"totalSalesAmount\": 1609.90,\r\n            \"netAmount\": 1533.61,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 477.54\r\n                },\r\n                {\r\n                    \"taxType\": \"T2\",\r\n                    \"amount\": 365.47\r\n                },\r\n                {\r\n                    \"taxType\": \"T3\",\r\n                    \"amount\": 60.00\r\n                },\r\n                {\r\n                    \"taxType\": \"T4\",\r\n                    \"amount\": 75.99\r\n                },\r\n                {\r\n                    \"taxType\": \"T5\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T6\",\r\n                    \"amount\": 120.00\r\n                },\r\n                {\r\n                    \"taxType\": \"T7\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T8\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T9\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T10\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T11\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T12\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T13\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T14\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T15\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T16\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T17\",\r\n                    \"amount\": 153.36\r\n                },\r\n                {\r\n                    \"taxType\": \"T18\",\r\n                    \"amount\": 214.71\r\n                },\r\n                {\r\n                    \"taxType\": \"T19\",\r\n                    \"amount\": 184.04\r\n                },\r\n                {\r\n                    \"taxType\": \"T20\",\r\n                    \"amount\": 153.36\r\n                }\r\n            ],\r\n            \"totalAmount\": 5191.50,\r\n            \"extraDiscountAmount\": 5.00,\r\n            \"totalItemsDiscountAmount\": 14.00,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}]}, {"name": "XML Format", "item": [{"name": "1. Submit Invoice", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<submission>\r\n    <documents>\r\n        <document>\r\n            <issuer>\r\n                <type>B</type>\r\n                <id>*********</id>\r\n                <name>الشركة المصدرة</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 40</buildingNumber>\r\n                    <room>123</room>\r\n                    <floor>16</floor>\r\n                    <street>15 احمد فخرى</street>\r\n                    <landmark>مستشفى حسبو</landmark>\r\n                    <additionalInformation>بجانب مستشفى حسبو</additionalInformation>\r\n                    <governate>القاهرة</governate>\r\n                    <regionCity>مدينة نصر</regionCity>\r\n                    <postalCode>098607</postalCode>\r\n                    <country>EG</country>\r\n                    <branchID>0</branchID>\r\n                </address>\r\n            </issuer>\r\n            <receiver>\r\n                <type>B</type>\r\n                <id>200000640</id>\r\n                <name>receiver</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 15</buildingNumber>\r\n                    <room>110</room>\r\n                    <floor>8</floor>\r\n                    <street>16 حوش عيسى</street>\r\n                    <landmark>جامع الرحمة</landmark>\r\n                    <additionalInformation>بجانب جامع الرحمة</additionalInformation>\r\n                    <governate>البحيرة</governate>\r\n                    <regionCity>دمنهور</regionCity>\r\n                    <postalCode>098661</postalCode>\r\n                    <country>EG</country>\r\n                </address>\r\n            </receiver>\r\n            <documentType>I</documentType>\r\n            <documentTypeVersion>0.9</documentTypeVersion>\r\n            <dateTimeIssued>2021-02-01T00:30:22</dateTimeIssued>\r\n            <taxpayerActivityCode>4620</taxpayerActivityCode>\r\n            <internalID>ID1</internalID>\r\n            <purchaseOrderReference>1230</purchaseOrderReference>\r\n            <purchaseOrderDescription>وصف طلب الدفع</purchaseOrderDescription>\r\n            <salesOrderReference>1452</salesOrderReference>\r\n            <salesOrderDescription>وصف بيع الطلب</salesOrderDescription>\r\n            <proformaInvoiceNumber>1485</proformaInvoiceNumber>\r\n          \r\n            <payment>\r\n                <bankName>البنك الاهلى</bankName>\r\n                <bankAddress>15 شارع النصر</bankAddress>\r\n                <bankAccountNo>1234567</bankAccountNo>\r\n                <bankAccountIBAN></bankAccountIBAN>\r\n                <swiftCode></swiftCode>\r\n                <terms>لا يمكن التزوير</terms>\r\n            </payment>\r\n            <delivery>\r\n                <approach>جوى</approach>\r\n                <packaging>تغليف</packaging>\r\n                <dateValidity>2020-06-29T17:30:22Z</dateValidity>\r\n                <exportPort>ميناء اسكندرية</exportPort>\r\n                <countryOfOrigin>LS</countryOfOrigin>\r\n                <grossWeight>123.45</grossWeight>\r\n                <netWeight>122.87</netWeight>\r\n                <terms>لا يمكن الفتح</terms>\r\n            </delivery>\r\n            <invoiceLines>\r\n                <invoiceLine>\r\n                    <description>حاسب الى</description>\r\n                    <itemType>GS1</itemType>\r\n                    <itemCode>********</itemCode>\r\n                    <unitType>EA</unitType>\r\n                    <quantity>100.12134</quantity>\r\n                    <unitValue>\r\n                        <currencySold>EGP</currencySold>\r\n                        <amountEGP>100</amountEGP>\r\n                    </unitValue>\r\n                 \r\n                    <salesTotal>10012.134</salesTotal>\r\n                    <discount>\r\n                        <rate>0</rate>\r\n                        <amount>0</amount>\r\n                    </discount>\r\n                    <taxableItems>\r\n                        <taxableItem>\r\n                            <taxType>T1</taxType>\r\n                            <amount>0.0</amount>\r\n                            <subType>V001</subType>\r\n                            <rate>0.00</rate>\r\n                        </taxableItem>\r\n                       \r\n                    </taxableItems>\r\n                    <internalCode>IC0</internalCode>\r\n                    <itemsDiscount>0</itemsDiscount>\r\n                    <netTotal>10012.134</netTotal>\r\n                    <totalTaxableFees>0.00</totalTaxableFees>\r\n                    <valueDifference>0</valueDifference>\r\n                    <total>10012.134</total>\r\n                </invoiceLine>\r\n            </invoiceLines>\r\n            <totalSalesAmount>10012.134</totalSalesAmount>\r\n            <totalDiscountAmount>0.0</totalDiscountAmount>\r\n            <netAmount>10012.134</netAmount>\r\n            <taxTotals>\r\n                <taxTotal>\r\n                    <taxType>T1</taxType>\r\n                    <amount>0.00</amount>\r\n                </taxTotal>\r\n            </taxTotals>\r\n            <totalAmount>10012.134</totalAmount>\r\n            <totalItemsDiscountAmount>0.00</totalItemsDiscountAmount>\r\n            <extraDiscountAmount>0.00</extraDiscountAmount>\r\n            <signatures>\r\n                     <signature>\r\n                    <signatureType>I</signatureType>\r\n                    <value></value>\r\n                </signature>\r\n            </signatures>\r\n          \r\n        </document>\r\n    </documents>\r\n</submission>\r\n"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}, "description": "Remember that you can include multiple documents within the same submission."}, "response": []}, {"name": "1. Submit Debit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<submission>\r\n    <documents>\r\n        <document>\r\n            <issuer>\r\n                <type>B</type>\r\n                <id>*********</id>\r\n                <name>الشركة المصدرة</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 40</buildingNumber>\r\n                    <room>123</room>\r\n                    <floor>16</floor>\r\n                    <street>15 احمد فخرى</street>\r\n                    <landmark>مستشفى حسبو</landmark>\r\n                    <additionalInformation>بجانب مستشفى حسبو</additionalInformation>\r\n                    <governate>القاهرة</governate>\r\n                    <regionCity>مدينة نصر</regionCity>\r\n                    <postalCode>098607</postalCode>\r\n                    <country>EG</country>\r\n                    <branchID>1</branchID>\r\n                </address>\r\n            </issuer>\r\n            <receiver>\r\n                <type>B</type>\r\n                <id>730913562</id>\r\n                <name>المستلم</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 15</buildingNumber>\r\n                    <room>110</room>\r\n                    <floor>8</floor>\r\n                    <street>16 حوش عيسى</street>\r\n                    <landmark>جامع الرحمة</landmark>\r\n                    <additionalInformation>بجانب جامع الرحمة</additionalInformation>\r\n                    <governate>البحيرة</governate>\r\n                    <regionCity>دمنهور</regionCity>\r\n                    <postalCode>098661</postalCode>\r\n                    <country>EG</country>\r\n                </address>\r\n            </receiver>\r\n            <documentType>D</documentType>\r\n            <documentTypeVersion>0.9</documentTypeVersion>\r\n            <dateTimeIssued>2020-10-29T17:30:22Z</dateTimeIssued>\r\n            <taxpayerActivityCode>4620</taxpayerActivityCode>\r\n            <internalID>IID0</internalID>\r\n            <purchaseOrderReference>1230</purchaseOrderReference>\r\n            <purchaseOrderDescription>وصف طلب الدفع</purchaseOrderDescription>\r\n            <salesOrderReference>1452</salesOrderReference>\r\n            <salesOrderDescription>وصف بيع الطلب</salesOrderDescription>\r\n            <proformaInvoiceNumber>1485</proformaInvoiceNumber>\r\n            <references>\r\n                <reference>439W9MSDRECAS6HXA9W4PXNE10</reference>\r\n            </references>\r\n            <payment>\r\n                <bankName>البنك الاهلى</bankName>\r\n                <bankAddress>15 شارع النصر</bankAddress>\r\n                <bankAccountNo>1234567</bankAccountNo>\r\n                <bankAccountIBAN />\r\n                <swiftCode />\r\n                <terms>لا يمكن التزوير</terms>\r\n            </payment>\r\n            <delivery>\r\n                <approach>جوى</approach>\r\n                <packaging>تغليف</packaging>\r\n                <dateValidity>2020-06-22T17:30:22Z</dateValidity>\r\n                <exportPort>ميناء اسكندرية</exportPort>\r\n                <countryOfOrigin>LS</countryOfOrigin>\r\n                <grossWeight>123.45</grossWeight>\r\n                <netWeight>122.87</netWeight>\r\n                <terms>لا يمكن الفتح</terms>\r\n            </delivery>\r\n            <invoiceLines>\r\n                <invoiceLine>\r\n                    <description>حاسب الى</description>\r\n                    <itemType>GS1</itemType>\r\n                    <itemCode>********</itemCode>\r\n                    <unitType>EA</unitType>\r\n                    <quantity>5</quantity>\r\n                    <unitValue>\r\n                        <currencySold>EUR</currencySold>\r\n                        <amountSold>10.00</amountSold>\r\n                        <amountEGP>189.40</amountEGP>\r\n                        <currencyExchangeRate>18.94</currencyExchangeRate>\r\n                    </unitValue>\r\n                    <salesTotal>947.00</salesTotal>\r\n                    <discount>\r\n                        <rate>7</rate>\r\n                        <amount>66.29</amount>\r\n                    </discount>\r\n                    <taxableItems>\r\n                        <taxableItem>\r\n                            <taxType>T1</taxType>\r\n                            <amount>272.07</amount>\r\n                            <subType>V001</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T2</taxType>\r\n                            <amount>208.22</amount>\r\n                            <subType>T2-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T3</taxType>\r\n                            <amount>30.00</amount>\r\n                            <subType>T3-Sub</subType>\r\n                            <rate>0</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T4</taxType>\r\n                            <amount>43.79</amount>\r\n                            <subType>W001</subType>\r\n                            <rate>5</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T5</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T5-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T6</taxType>\r\n                            <amount>60.00</amount>\r\n                            <subType>T6-Sub</subType>\r\n                            <rate>0</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T7</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T7-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T8</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T8-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T9</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T9-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T10</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T10-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T11</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T11-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T12</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T12-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T13</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T13-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T14</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T14-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T15</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T15-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T16</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T16-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T17</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T17-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T18</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T18-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T19</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T19-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T20</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T20-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                    </taxableItems>\r\n                    <internalCode>IC0</internalCode>\r\n                    <itemsDiscount>5.00</itemsDiscount>\r\n                    <netTotal>880.71</netTotal>\r\n                    <totalTaxableFees>817.42</totalTaxableFees>\r\n                    <valueDifference>7.00</valueDifference>\r\n                    <total>2969.89</total>\r\n                </invoiceLine>\r\n            </invoiceLines>\r\n            <totalSalesAmount>947.00</totalSalesAmount>\r\n            <totalDiscountAmount>66.29</totalDiscountAmount>\r\n            <netAmount>880.71</netAmount>\r\n            <taxTotals>\r\n                <taxTotal>\r\n                    <taxType>T1</taxType>\r\n                    <amount>272.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T2</taxType>\r\n                    <amount>208.22</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T3</taxType>\r\n                    <amount>30.00</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T4</taxType>\r\n                    <amount>43.79</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T5</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T6</taxType>\r\n                    <amount>60.00</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T7</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T8</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T9</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T10</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T11</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T12</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T13</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T14</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T15</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T16</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T17</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T18</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T19</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T20</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n            </taxTotals>\r\n            <totalAmount>2964.89</totalAmount>\r\n            <totalItemsDiscountAmount>5.00</totalItemsDiscountAmount>\r\n            <extraDiscountAmount>5.00</extraDiscountAmount>\r\n            <signatures>\r\n                <signature>\r\n                    <signatureType>I</signatureType>\r\n                    <value>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</value>\r\n                </signature>\r\n            </signatures>\r\n        </document>\r\n    </documents>\r\n</submission>"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Credit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Authorization", "value": "Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "body": {"mode": "raw", "raw": "<submission>\r\n    <documents>\r\n        <document>\r\n            <issuer>\r\n                <type>B</type>\r\n                <id>*********</id>\r\n                <name>الشركة المصدرة</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 40</buildingNumber>\r\n                    <room>123</room>\r\n                    <floor>16</floor>\r\n                    <street>15 احمد فخرى</street>\r\n                    <landmark>مستشفى حسبو</landmark>\r\n                    <additionalInformation>بجانب مستشفى حسبو</additionalInformation>\r\n                    <governate>القاهرة</governate>\r\n                    <regionCity>مدينة نصر</regionCity>\r\n                    <postalCode>098607</postalCode>\r\n                    <country>EG</country>\r\n                    <branchID>1</branchID>\r\n                </address>\r\n            </issuer>\r\n            <receiver>\r\n                <type>B</type>\r\n                <id>730913562</id>\r\n                <name>المستلم</name>\r\n                <address>\r\n                    <buildingNumber>عمارة 15</buildingNumber>\r\n                    <room>110</room>\r\n                    <floor>8</floor>\r\n                    <street>16 حوش عيسى</street>\r\n                    <landmark>جامع الرحمة</landmark>\r\n                    <additionalInformation>بجانب جامع الرحمة</additionalInformation>\r\n                    <governate>البحيرة</governate>\r\n                    <regionCity>دمنهور</regionCity>\r\n                    <postalCode>098661</postalCode>\r\n                    <country>EG</country>\r\n                </address>\r\n            </receiver>\r\n            <documentType>C</documentType>\r\n            <documentTypeVersion>0.9</documentTypeVersion>\r\n            <dateTimeIssued>2020-10-29T17:30:22Z</dateTimeIssued>\r\n            <taxpayerActivityCode>4620</taxpayerActivityCode>\r\n            <internalID>IID0</internalID>\r\n            <purchaseOrderReference>1230</purchaseOrderReference>\r\n            <purchaseOrderDescription>وصف طلب الدفع</purchaseOrderDescription>\r\n            <salesOrderReference>1452</salesOrderReference>\r\n            <salesOrderDescription>وصف بيع الطلب</salesOrderDescription>\r\n            <proformaInvoiceNumber>1485</proformaInvoiceNumber>\r\n            <references>\r\n                <reference>439W9MSDRECAS6HXA9W4PXNE10</reference>\r\n            </references>\r\n            <payment>\r\n                <bankName>البنك الاهلى</bankName>\r\n                <bankAddress>15 شارع النصر</bankAddress>\r\n                <bankAccountNo>1234567</bankAccountNo>\r\n                <bankAccountIBAN />\r\n                <swiftCode />\r\n                <terms>لا يمكن التزوير</terms>\r\n            </payment>\r\n            <delivery>\r\n                <approach>جوى</approach>\r\n                <packaging>تغليف</packaging>\r\n                <dateValidity>2020-06-22T17:30:22Z</dateValidity>\r\n                <exportPort>ميناء اسكندرية</exportPort>\r\n                <countryOfOrigin>LS</countryOfOrigin>\r\n                <grossWeight>123.45</grossWeight>\r\n                <netWeight>122.87</netWeight>\r\n                <terms>لا يمكن الفتح</terms>\r\n            </delivery>\r\n            <invoiceLines>\r\n                <invoiceLine>\r\n                    <description>حاسب الى</description>\r\n                    <itemType>GS1</itemType>\r\n                    <itemCode>********</itemCode>\r\n                    <unitType>EA</unitType>\r\n                    <quantity>5</quantity>\r\n                    <unitValue>\r\n                        <currencySold>EUR</currencySold>\r\n                        <amountSold>10.00</amountSold>\r\n                        <amountEGP>189.40</amountEGP>\r\n                        <currencyExchangeRate>18.94</currencyExchangeRate>\r\n                    </unitValue>\r\n                    <salesTotal>947.00</salesTotal>\r\n                    <discount>\r\n                        <rate>7</rate>\r\n                        <amount>66.29</amount>\r\n                    </discount>\r\n                    <taxableItems>\r\n                        <taxableItem>\r\n                            <taxType>T1</taxType>\r\n                            <amount>272.07</amount>\r\n                            <subType>V001</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T2</taxType>\r\n                            <amount>208.22</amount>\r\n                            <subType>T2-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T3</taxType>\r\n                            <amount>30.00</amount>\r\n                            <subType>T3-Sub</subType>\r\n                            <rate>0</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T4</taxType>\r\n                            <amount>43.79</amount>\r\n                            <subType>W001</subType>\r\n                            <rate>5</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T5</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T5-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T6</taxType>\r\n                            <amount>60.00</amount>\r\n                            <subType>T6-Sub</subType>\r\n                            <rate>0</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T7</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T7-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T8</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T8-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T9</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T9-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T10</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T10-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T11</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T11-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T12</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T12-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T13</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T13-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T14</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T14-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T15</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T15-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T16</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T16-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T17</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T17-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T18</taxType>\r\n                            <amount>123.30</amount>\r\n                            <subType>T18-Sub</subType>\r\n                            <rate>14</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T19</taxType>\r\n                            <amount>105.69</amount>\r\n                            <subType>T19-Sub</subType>\r\n                            <rate>12</rate>\r\n                        </taxableItem>\r\n                        <taxableItem>\r\n                            <taxType>T20</taxType>\r\n                            <amount>88.07</amount>\r\n                            <subType>T20-Sub</subType>\r\n                            <rate>10</rate>\r\n                        </taxableItem>\r\n                    </taxableItems>\r\n                    <internalCode>IC0</internalCode>\r\n                    <itemsDiscount>5.00</itemsDiscount>\r\n                    <netTotal>880.71</netTotal>\r\n                    <totalTaxableFees>817.42</totalTaxableFees>\r\n                    <valueDifference>7.00</valueDifference>\r\n                    <total>2969.89</total>\r\n                </invoiceLine>\r\n            </invoiceLines>\r\n            <totalSalesAmount>947.00</totalSalesAmount>\r\n            <totalDiscountAmount>66.29</totalDiscountAmount>\r\n            <netAmount>880.71</netAmount>\r\n            <taxTotals>\r\n                <taxTotal>\r\n                    <taxType>T1</taxType>\r\n                    <amount>272.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T2</taxType>\r\n                    <amount>208.22</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T3</taxType>\r\n                    <amount>30.00</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T4</taxType>\r\n                    <amount>43.79</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T5</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T6</taxType>\r\n                    <amount>60.00</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T7</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T8</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T9</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T10</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T11</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T12</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T13</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T14</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T15</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T16</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T17</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T18</taxType>\r\n                    <amount>123.30</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T19</taxType>\r\n                    <amount>105.69</amount>\r\n                </taxTotal>\r\n                <taxTotal>\r\n                    <taxType>T20</taxType>\r\n                    <amount>88.07</amount>\r\n                </taxTotal>\r\n            </taxTotals>\r\n            <totalAmount>2964.89</totalAmount>\r\n            <totalItemsDiscountAmount>5.00</totalItemsDiscountAmount>\r\n            <extraDiscountAmount>5.00</extraDiscountAmount>\r\n            <signatures>\r\n                <signature>\r\n                    <signatureType>I</signatureType>\r\n                    <value>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</value>\r\n                </signature>\r\n            </signatures>\r\n        </document>\r\n    </documents>\r\n</submission>"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}]}]}, {"name": "Submit Export Documents", "item": [{"name": "JSON Format", "item": [{"name": "1. Submit Export Invoice", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"0\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"IE\",\r\n                    \"governate\": \"Leinster\",\r\n                    \"regionCity\": \"Dublin\",\r\n                    \"street\": \"Carmanhall and Leopardstown\",\r\n                    \"buildingNumber\": \"One Microsoft Place\",\r\n                    \"postalCode\": \"D18 P521\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"F\",\r\n                \"id\": \"*********0\",\r\n                \"name\": \"Receiver Company\"\r\n            },\r\n            \"documentType\": \"EI\",\r\n            \"documentTypeVersion\": \"1.0\",\r\n            \"dateTimeIssued\": \"2023-01-31T15:00:00Z\",\r\n            \"taxpayerActivityCode\": \"1910\",\r\n            \"serviceDeliveryDate\": \"2023-11-19\",\r\n            \"internalID\": \"Simple1234\",\r\n            \"purchaseOrderReference\": \"\",\r\n            \"purchaseOrderDescription\": \"\",\r\n            \"salesOrderReference\": \"\",\r\n            \"salesOrderDescription\": \"\",\r\n            \"proformaInvoiceNumber\": \"\",\r\n            \"payment\": {\r\n                \"bankName\": \"\",\r\n                \"bankAddress\": \"\",\r\n                \"bankAccountNo\": \"\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"\",\r\n                \"packaging\": \"\",\r\n                \"dateValidity\": \"\",\r\n                \"exportPort\": \"\",\r\n                \"countryOfOrigin\": \"\",\r\n                \"grossWeight\": 0,\r\n                \"netWeight\": 0,\r\n                \"terms\": \"\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"*************\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 25,\r\n                    \"weightUnitType\": \"EA\",\r\n                    \"weightQuantity\": 21,\r\n                    \"internalCode\": \"\",\r\n                    \"salesTotal\": 250,\r\n                    \"total\": 250,\r\n                    \"valueDifference\": 0,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": 250,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": 10\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V009\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 0,\r\n            \"totalSalesAmount\": 250,\r\n            \"netAmount\": 250,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 0\r\n                }\r\n            ],\r\n            \"totalAmount\": 250,\r\n            \"extraDiscountAmount\": 0,\r\n            \"totalItemsDiscountAmount\": 0,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Export Debit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"0\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"IE\",\r\n                    \"governate\": \"Leinster\",\r\n                    \"regionCity\": \"Dublin\",\r\n                    \"street\": \"Carmanhall and Leopardstown\",\r\n                    \"buildingNumber\": \"One Microsoft Place\",\r\n                    \"postalCode\": \"D18 P521\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"F\",\r\n                \"id\": \"*********0\",\r\n                \"name\": \"Receiver Company\"\r\n            },\r\n            \"documentType\": \"ED\",\r\n            \"documentTypeVersion\": \"1.0\",\r\n            \"dateTimeIssued\": \"2023-01-31T15:00:00Z\",\r\n            \"taxpayerActivityCode\": \"1910\",\r\n            \"serviceDeliveryDate\": \"2023-11-19\",\r\n            \"internalID\": \"Simple1234\",\r\n            \"references\" : [\"90HGMKPEVN0CNAT5Q4GF96RG10\"],\r\n            \"purchaseOrderReference\": \"\",\r\n            \"purchaseOrderDescription\": \"\",\r\n            \"salesOrderReference\": \"\",\r\n            \"salesOrderDescription\": \"\",\r\n            \"proformaInvoiceNumber\": \"\",\r\n            \"payment\": {\r\n                \"bankName\": \"\",\r\n                \"bankAddress\": \"\",\r\n                \"bankAccountNo\": \"\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"\",\r\n                \"packaging\": \"\",\r\n                \"dateValidity\": \"\",\r\n                \"exportPort\": \"\",\r\n                \"countryOfOrigin\": \"\",\r\n                \"grossWeight\": 0,\r\n                \"netWeight\": 0,\r\n                \"terms\": \"\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"*************\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 25,\r\n                    \"weightUnitType\": \"EA\",\r\n                    \"weightQuantity\": 21,\r\n                    \"internalCode\": \"\",\r\n                    \"salesTotal\": 250,\r\n                    \"total\": 250,\r\n                    \"valueDifference\": 0,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": 250,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": 10\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V009\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 0,\r\n            \"totalSalesAmount\": 250,\r\n            \"netAmount\": 250,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 0\r\n                }\r\n            ],\r\n            \"totalAmount\": 250,\r\n            \"extraDiscountAmount\": 0,\r\n            \"totalItemsDiscountAmount\": 0,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Export Credit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"0\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"IE\",\r\n                    \"governate\": \"Leinster\",\r\n                    \"regionCity\": \"Dublin\",\r\n                    \"street\": \"Carmanhall and Leopardstown\",\r\n                    \"buildingNumber\": \"One Microsoft Place\",\r\n                    \"postalCode\": \"D18 P521\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"F\",\r\n                \"id\": \"*********0\",\r\n                \"name\": \"Receiver Company\"\r\n            },\r\n            \"documentType\": \"EC\",\r\n            \"documentTypeVersion\": \"1.0\",\r\n            \"dateTimeIssued\": \"2023-01-31T15:00:00Z\",\r\n            \"taxpayerActivityCode\": \"1910\",\r\n            \"serviceDeliveryDate\": \"2023-11-19\",\r\n            \"internalID\": \"Simple1234\",\r\n            \"references\" : [\"90HGMKPEVN0CNAT5Q4GF96RG10\"],\r\n            \"purchaseOrderReference\": \"\",\r\n            \"purchaseOrderDescription\": \"\",\r\n            \"salesOrderReference\": \"\",\r\n            \"salesOrderDescription\": \"\",\r\n            \"proformaInvoiceNumber\": \"\",\r\n            \"payment\": {\r\n                \"bankName\": \"\",\r\n                \"bankAddress\": \"\",\r\n                \"bankAccountNo\": \"\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"\",\r\n                \"packaging\": \"\",\r\n                \"dateValidity\": \"\",\r\n                \"exportPort\": \"\",\r\n                \"countryOfOrigin\": \"\",\r\n                \"grossWeight\": 0,\r\n                \"netWeight\": 0,\r\n                \"terms\": \"\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"*************\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 25,\r\n                    \"weightUnitType\": \"EA\",\r\n                    \"weightQuantity\": 21,\r\n                    \"internalCode\": \"\",\r\n                    \"salesTotal\": 250,\r\n                    \"total\": 250,\r\n                    \"valueDifference\": 0,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": 250,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": 10\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V009\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 0,\r\n            \"totalSalesAmount\": 250,\r\n            \"netAmount\": 250,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 0\r\n                }\r\n            ],\r\n            \"totalAmount\": 250,\r\n            \"extraDiscountAmount\": 0,\r\n            \"totalItemsDiscountAmount\": 0,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}]}, {"name": "XML Format", "item": [{"name": "1. Submit Export Invoice", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<submission>\r\n   <documents>\r\n      <document>\r\n         <issuer>\r\n            <type>B</type>\r\n            <id>*********</id>\r\n            <name>Issuer Company</name>\r\n            <address>\r\n               <buildingNumber>Bulid 2</buildingNumber>\r\n               <room>123</room>\r\n               <floor>16</floor>\r\n               <street>15</street>\r\n               <landmark>87</landmark>\r\n               <additionalInformation>Info</additionalInformation>\r\n               <governate>Cairo</governate>\r\n               <regionCity>Nasr City</regionCity>\r\n               <postalCode>098607</postalCode>\r\n               <country>EG</country>\r\n               <branchID>0</branchID>\r\n            </address>\r\n         </issuer>\r\n         <receiver>\r\n            <type>F</type>\r\n            <id>*********0</id>\r\n            <name>receiver</name>\r\n            <address>\r\n               <country>IE</country>\r\n               <governate>Leinster</governate>\r\n               <regionCity>Dublin</regionCity>\r\n               <street>Carmanhall and Leopardstown</street>                                       \r\n               <buildingNumber>One Microsoft Place</buildingNumber>\r\n               <postalCode>D18 P521</postalCode>\r\n               <floor></floor>\r\n               <room></room>\r\n               <landmark></landmark>\r\n               <additionalInformation></additionalInformation>\r\n            </address>\r\n         </receiver>\r\n         <documentType>EI</documentType>\r\n         <documentTypeVersion>1.0</documentTypeVersion>\r\n         <dateTimeIssued>2023-01-31T15:00:00Z</dateTimeIssued>\r\n         <serviceDeliveryDate>2023-01-21</serviceDeliveryDate>\r\n         <taxpayerActivityCode>1910</taxpayerActivityCode>\r\n         <internalID>Simple</internalID>\r\n         <purchaseOrderReference>120</purchaseOrderReference>\r\n         <purchaseOrderDescription>DESC</purchaseOrderDescription>\r\n         <salesOrderReference>1452</salesOrderReference>\r\n         <salesOrderDescription>Sales DESC</salesOrderDescription>\r\n         <proformaInvoiceNumber>1485</proformaInvoiceNumber>\r\n         <payment>\r\n            <bankName>AlAhly Bank</bankName>\r\n            <bankAddress>Street 1</bankAddress>\r\n            <bankAccountNo>1234567</bankAccountNo>\r\n            <bankAccountIBAN />\r\n            <swiftCode />\r\n            <terms />\r\n         </payment>\r\n         <delivery>\r\n            <approach />\r\n            <packaging />\r\n            <dateValidity />\r\n            <exportPort />\r\n            <countryOfOrigin />\r\n            <grossWeight>123.45</grossWeight>\r\n            <netWeight>122.87</netWeight>\r\n            <terms />\r\n         </delivery>\r\n         <invoiceLines>\r\n            <invoiceLine>\r\n               <description>Computer1</description>\r\n               <itemType>GS1</itemType>\r\n               <itemCode>*************</itemCode>\r\n               <unitType>EA</unitType>\r\n               <quantity>25</quantity>\r\n               <weightUnitType>EA</weightUnitType>\r\n               <weightQuantity>26</weightQuantity>\r\n               <unitValue>\r\n                  <currencySold>EGP</currencySold>\r\n                  <amountEGP>10</amountEGP>\r\n               </unitValue>\r\n               <salesTotal>250</salesTotal>\r\n               <discount>\r\n                  <rate>0</rate>\r\n                  <amount>0</amount>\r\n               </discount>\r\n               <taxableItems>\r\n                  <taxableItem>\r\n                     <taxType>T1</taxType>\r\n                     <amount>0.0</amount>\r\n                     <subType>V001</subType>\r\n                     <rate>0.00</rate>\r\n                  </taxableItem>\r\n               </taxableItems>\r\n               <internalCode>IC0</internalCode>\r\n               <itemsDiscount>0</itemsDiscount>\r\n               <netTotal>250</netTotal>\r\n               <totalTaxableFees>0.00</totalTaxableFees>\r\n               <valueDifference>0</valueDifference>\r\n               <total>250</total>\r\n            </invoiceLine>\r\n         </invoiceLines>\r\n         <totalSalesAmount>250</totalSalesAmount>\r\n         <totalDiscountAmount>0.0</totalDiscountAmount>\r\n         <netAmount>250</netAmount>\r\n         <taxTotals>\r\n            <taxTotal>\r\n               <taxType>T1</taxType>\r\n               <amount>0.00</amount>\r\n            </taxTotal>\r\n         </taxTotals>\r\n         <totalAmount>250</totalAmount>\r\n         <totalItemsDiscountAmount>0.00</totalItemsDiscountAmount>\r\n         <extraDiscountAmount>0.00</extraDiscountAmount>\r\n         <signatures>\r\n            <signature>\r\n               <signatureType>I</signatureType>\r\n               <value>Signature Value</value>\r\n            </signature>\r\n         </signatures>\r\n      </document>\r\n   </documents>\r\n</submission>"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Export Debit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<submission>\r\n   <documents>\r\n      <document>\r\n         <issuer>\r\n            <type>B</type>\r\n            <id>*********</id>\r\n            <name>Issuer Company</name>\r\n            <address>\r\n               <buildingNumber>Bulid 2</buildingNumber>\r\n               <room>123</room>\r\n               <floor>16</floor>\r\n               <street>15</street>\r\n               <landmark>87</landmark>\r\n               <additionalInformation>Info</additionalInformation>\r\n               <governate>Cairo</governate>\r\n               <regionCity>Nasr City</regionCity>\r\n               <postalCode>098607</postalCode>\r\n               <country>EG</country>\r\n               <branchID>0</branchID>\r\n            </address>\r\n         </issuer>\r\n         <receiver>\r\n            <type>F</type>\r\n            <id>*********0</id>\r\n            <name>receiver</name>\r\n            <address>\r\n               <country>IE</country>\r\n               <governate>Leinster</governate>\r\n               <regionCity>Dublin</regionCity>\r\n               <street>Carmanhall and Leopardstown</street>                                       \r\n               <buildingNumber>One Microsoft Place</buildingNumber>\r\n               <postalCode>D18 P521</postalCode>\r\n               <floor></floor>\r\n               <room></room>\r\n               <landmark></landmark>\r\n               <additionalInformation></additionalInformation>\r\n            </address>\r\n         </receiver>\r\n         <documentType>ED</documentType>\r\n         <documentTypeVersion>1.0</documentTypeVersion>\r\n         <dateTimeIssued>2023-01-31T15:00:00Z</dateTimeIssued>\r\n         <serviceDeliveryDate>2023-01-21</serviceDeliveryDate>\r\n         <taxpayerActivityCode>1910</taxpayerActivityCode>\r\n         <internalID>Simple</internalID>\r\n         <purchaseOrderReference>120</purchaseOrderReference>\r\n         <purchaseOrderDescription>DESC</purchaseOrderDescription>\r\n         <salesOrderReference>1452</salesOrderReference>\r\n         <salesOrderDescription>Sales DESC</salesOrderDescription>\r\n         <proformaInvoiceNumber>1485</proformaInvoiceNumber>\r\n         <references>\r\n            <reference>0J8YQZ9049KDQEEY1XGX96RG10</reference>\r\n         </references>\r\n         <payment>\r\n            <bankName>AlAhly Bank</bankName>\r\n            <bankAddress>Street 1</bankAddress>\r\n            <bankAccountNo>1234567</bankAccountNo>\r\n            <bankAccountIBAN />\r\n            <swiftCode />\r\n            <terms />\r\n         </payment>\r\n         <delivery>\r\n            <approach />\r\n            <packaging />\r\n            <dateValidity />\r\n            <exportPort />\r\n            <countryOfOrigin />\r\n            <grossWeight>123.45</grossWeight>\r\n            <netWeight>122.87</netWeight>\r\n            <terms />\r\n         </delivery>\r\n         <invoiceLines>\r\n            <invoiceLine>\r\n               <description>Computer1</description>\r\n               <itemType>GS1</itemType>\r\n               <itemCode>*************</itemCode>\r\n               <unitType>EA</unitType>\r\n               <quantity>25</quantity>\r\n               <weightUnitType>EA</weightUnitType>\r\n               <weightQuantity>26</weightQuantity>\r\n               <unitValue>\r\n                  <currencySold>EGP</currencySold>\r\n                  <amountEGP>10</amountEGP>\r\n               </unitValue>\r\n               <salesTotal>250</salesTotal>\r\n               <discount>\r\n                  <rate>0</rate>\r\n                  <amount>0</amount>\r\n               </discount>\r\n               <taxableItems>\r\n                  <taxableItem>\r\n                     <taxType>T1</taxType>\r\n                     <amount>0.0</amount>\r\n                     <subType>V001</subType>\r\n                     <rate>0.00</rate>\r\n                  </taxableItem>\r\n               </taxableItems>\r\n               <internalCode>IC0</internalCode>\r\n               <itemsDiscount>0</itemsDiscount>\r\n               <netTotal>250</netTotal>\r\n               <totalTaxableFees>0.00</totalTaxableFees>\r\n               <valueDifference>0</valueDifference>\r\n               <total>250</total>\r\n            </invoiceLine>\r\n         </invoiceLines>\r\n         <totalSalesAmount>250</totalSalesAmount>\r\n         <totalDiscountAmount>0.0</totalDiscountAmount>\r\n         <netAmount>250</netAmount>\r\n         <taxTotals>\r\n            <taxTotal>\r\n               <taxType>T1</taxType>\r\n               <amount>0.00</amount>\r\n            </taxTotal>\r\n         </taxTotals>\r\n         <totalAmount>250</totalAmount>\r\n         <totalItemsDiscountAmount>0.00</totalItemsDiscountAmount>\r\n         <extraDiscountAmount>0.00</extraDiscountAmount>\r\n         <signatures>\r\n            <signature>\r\n               <signatureType>I</signatureType>\r\n               <value>Signature Value</value>\r\n            </signature>\r\n         </signatures>\r\n      </document>\r\n   </documents>\r\n</submission>"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}, {"name": "1. Submit Export Credit Note", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"documents\": [\r\n        {\r\n            \"issuer\": {\r\n                \"address\": {\r\n                    \"branchID\": \"0\",\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"Cairo\",\r\n                    \"regionCity\": \"Nasr City\",\r\n                    \"street\": \"580 Clementina Key\",\r\n                    \"buildingNumber\": \"Bldg. 0\",\r\n                    \"postalCode\": \"\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"B\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"Company\"\r\n            },\r\n            \"receiver\": {\r\n                \"address\": {\r\n                    \"country\": \"IE\",\r\n                    \"governate\": \"Leinster\",\r\n                    \"regionCity\": \"Dublin\",\r\n                    \"street\": \"Carmanhall and Leopardstown\",\r\n                    \"buildingNumber\": \"One Microsoft Place\",\r\n                    \"postalCode\": \"D18 P521\",\r\n                    \"floor\": \"\",\r\n                    \"room\": \"\",\r\n                    \"landmark\": \"\",\r\n                    \"additionalInformation\": \"\"\r\n                },\r\n                \"type\": \"F\",\r\n                \"id\": \"*********0\",\r\n                \"name\": \"Receiver Company\"\r\n            },\r\n            \"documentType\": \"EC\",\r\n            \"documentTypeVersion\": \"1.0\",\r\n            \"dateTimeIssued\": \"2023-01-31T15:00:00Z\",\r\n            \"taxpayerActivityCode\": \"1910\",\r\n            \"serviceDeliveryDate\": \"2023-11-19\",\r\n            \"internalID\": \"Simple1234\",\r\n            \"references\" : [\"90HGMKPEVN0CNAT5Q4GF96RG10\"],\r\n            \"purchaseOrderReference\": \"\",\r\n            \"purchaseOrderDescription\": \"\",\r\n            \"salesOrderReference\": \"\",\r\n            \"salesOrderDescription\": \"\",\r\n            \"proformaInvoiceNumber\": \"\",\r\n            \"payment\": {\r\n                \"bankName\": \"\",\r\n                \"bankAddress\": \"\",\r\n                \"bankAccountNo\": \"\",\r\n                \"bankAccountIBAN\": \"\",\r\n                \"swiftCode\": \"\",\r\n                \"terms\": \"\"\r\n            },\r\n            \"delivery\": {\r\n                \"approach\": \"\",\r\n                \"packaging\": \"\",\r\n                \"dateValidity\": \"\",\r\n                \"exportPort\": \"\",\r\n                \"countryOfOrigin\": \"\",\r\n                \"grossWeight\": 0,\r\n                \"netWeight\": 0,\r\n                \"terms\": \"\"\r\n            },\r\n            \"invoiceLines\": [\r\n                {\r\n                    \"description\": \"Computer1\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"*************\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 25,\r\n                    \"weightUnitType\": \"EA\",\r\n                    \"weightQuantity\": 21,\r\n                    \"internalCode\": \"\",\r\n                    \"salesTotal\": 250,\r\n                    \"total\": 250,\r\n                    \"valueDifference\": 0,\r\n                    \"totalTaxableFees\": 0,\r\n                    \"netTotal\": 250,\r\n                    \"itemsDiscount\": 0,\r\n                    \"unitValue\": {\r\n                        \"currencySold\": \"EGP\",\r\n                        \"amountEGP\": 10\r\n                    },\r\n                    \"discount\": {\r\n                        \"rate\": 0,\r\n                        \"amount\": 0\r\n                    },\r\n                    \"taxableItems\": [\r\n                        {\r\n                            \"taxType\": \"T1\",\r\n                            \"amount\": 0,\r\n                            \"subType\": \"V009\",\r\n                            \"rate\": 0\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalDiscountAmount\": 0,\r\n            \"totalSalesAmount\": 250,\r\n            \"netAmount\": 250,\r\n            \"taxTotals\": [\r\n                {\r\n                    \"taxType\": \"T1\",\r\n                    \"amount\": 0\r\n                }\r\n            ],\r\n            \"totalAmount\": 250,\r\n            \"extraDiscountAmount\": 0,\r\n            \"totalItemsDiscountAmount\": 0,\r\n            \"signatures\": [\r\n                {\r\n                    \"signatureType\": \"I\",\r\n                    \"value\": \"<Signature Value>\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentsubmissions"]}}, "response": []}]}]}, {"name": "2. Cancel Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"status\":\"cancelled\",\n\t\"reason\":\"some reason for cancelled document\"\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/state/:documentUUID/state", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "state", ":documentUUID", "state"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "3. Reject Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"status\":\"rejected\",\n\t\"reason\":\"some reason for rejected document\"\n}"}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/state/:documentUUID/state", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "state", ":documentUUID", "state"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "4. Get Recent Documents", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "PageSize", "value": "10"}, {"key": "PageNo", "value": "1"}], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/recent?pageNo=1&pageSize=100&submissionDateFrom=2022-12-01T15:00:59&submissionDateTo=2022-12-31T20:00:00&issueDateFrom=&issueDateTo=&direction=&status=Valid&documentType=i&receiverType=&receiverId=&issuerType=&issuerId=", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "recent"], "query": [{"key": "pageNo", "value": "1"}, {"key": "pageSize", "value": "100"}, {"key": "submissionDateFrom", "value": "2022-12-01T15:00:59"}, {"key": "submissionDateTo", "value": "2022-12-31T20:00:00"}, {"key": "issueDateFrom", "value": ""}, {"key": "issueDateTo", "value": ""}, {"key": "direction", "value": ""}, {"key": "status", "value": "<PERSON><PERSON>"}, {"key": "documentType", "value": "i"}, {"key": "receiverType", "value": ""}, {"key": "receiverId", "value": ""}, {"key": "issuerType", "value": ""}, {"key": "issuerId", "value": ""}]}}, "response": []}, {"name": "5. Request Document Package", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"Summary\",\r\n    \"format\": \"JSON\",\r\n    \"truncateifexceeded\": false,\r\n    \"queryParameters\": {\r\n        \"dateFrom\": \"2020-10-01T21:00:28.451Z\",\r\n        \"dateTo\": \"2020-12-30T21:00:28.451Z\",\r\n        \"statuses\": [\r\n            \"Valid\",\r\n            \"Cancelled\",\r\n            \"Rejected\"\r\n        ],\r\n        \"productsInternalCodes\": [],\r\n        \"receiverSenderId\": \"\",\r\n        \"receiverSenderType\": \"0\",\r\n        \"branchNumber\": \"\",\r\n        \"itemCodes\": [\r\n            {\r\n                \"codeValue\": \"\",\r\n                \"codeType\": \"\"\r\n            }\r\n        ],\r\n        \"documentTypeNames\": []\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentPackages/requests", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentPackages", "requests"]}}, "response": []}, {"name": "5.1 Request Document Package as intermediary", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"Full\",\r\n    \"format\": \"XML\",\r\n    \"truncateifexceeded\": false,\r\n    \"queryParameters\": {\r\n        \"dateFrom\": \"2021-01-01T00:00:00.000Z\",\r\n        \"dateTo\": \"2021-01-31T00:00:00.000Z\",\r\n        \"statuses\": [\r\n            \"Valid\"\r\n        ],\r\n        \"productsInternalCodes\": [],\r\n        \"receiverSenderType\": \"0\",\r\n        \"documentTypeNames\": [\r\n            \"C\",\r\n            \"D\",\r\n            \"I\"\r\n        ],\r\n        \"representedTaxpayerFilterType\": \"1\",\r\n        \"representeeRin\": \"\",\r\n        \"branchNumber\": \"\",\r\n        \"itemCodes\": [\r\n            {\r\n                \"codeValue\": \"\",\r\n                \"codeType\": \"\"\r\n            }\r\n        ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/documentPackages/requests", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentPackages", "requests"]}}, "response": []}, {"name": "6. Get Package Requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/documentPackages/requests?pageSize=10&pageNo=1", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentPackages", "requests"], "query": [{"key": "pageSize", "value": "10"}, {"key": "pageNo", "value": "1"}]}}, "response": []}, {"name": "7. Get Document Package", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/documentPackages/:rid", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documentPackages", ":rid"], "variable": [{"key": "rid", "value": ""}]}}, "response": []}, {"name": "8. Get Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/documents/:documentUUID/raw", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documents", ":documentUUID", "raw"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "9. Get Submission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "PageSize", "value": "10"}, {"key": "PageNo", "value": "1"}], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documentSubmissions/:submitionUUID?PageSize=1", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documentSubmissions", ":submitionUUID"], "query": [{"key": "PageSize", "value": "1"}], "variable": [{"key": "submitionUUID", "value": ""}]}}, "response": []}, {"name": "10. Get Document Printout (PDF)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/documents/:documentUUID/pdf", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documents", ":documentUUID", "pdf"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "11. Get Document Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/documents/:documentUUID/details", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "documents", ":documentUUID", "details"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "12. <PERSON>line Cancel Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/state/:documentUUID/decline/cancelation", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "state", ":documentUUID", "decline", "cancelation"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "13. Decline Rejection Document", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/state/:documentUUID/decline/rejection", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "state", ":documentUUID", "decline", "rejection"], "variable": [{"key": "documentUUID", "value": ""}]}}, "response": []}, {"name": "14. Search Documents", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "PageSize", "value": "10"}, {"key": "PageNo", "value": "1"}], "url": {"raw": "{{apiBaseUrl}}/api/v1.0/documents/search?submissionDateFrom=2022-12-01T15:00:59&submissionDateTo=2022-12-31T20:00:00&continuationToken=&pageSize=100&issueDateFrom=&issueDateTo=&direction=&status=Valid&documentType=i&receiverType=&receiverId=&issuerType=&issuerId=&uuid=&internalID=", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1.0", "documents", "search"], "query": [{"key": "submissionDateFrom", "value": "2022-12-01T15:00:59"}, {"key": "submissionDateTo", "value": "2022-12-31T20:00:00"}, {"key": "continuationToken", "value": ""}, {"key": "pageSize", "value": "100"}, {"key": "issueDateFrom", "value": ""}, {"key": "issueDateTo", "value": ""}, {"key": "direction", "value": ""}, {"key": "status", "value": "<PERSON><PERSON>"}, {"key": "documentType", "value": "i"}, {"key": "receiverType", "value": ""}, {"key": "receiverId", "value": ""}, {"key": "issuerType", "value": ""}, {"key": "issuerId", "value": ""}, {"key": "uuid", "value": ""}, {"key": "internalID", "value": ""}]}}, "response": []}]}, {"name": "eReceipt", "item": [{"name": "2.3. Submit Receipt - Industries", "item": [{"name": "2.3.1. Submit Receipt - Coffee and Restaurant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"orderdeliveryMode\": \"FC\"\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SC\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10.2\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                             \"rate\":10.0\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\",\r\n                             \"rate\":1.0\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\"\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.2. Submit Return - Coffee and Restaurant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"orderdeliveryMode\": \"FC\"\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RC\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.3. Submit Receipt - General Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SS\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.4. Submit Return - General Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RS\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.5. Submit Receipt - Retail", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"FC\"\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SR\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.6. Submit Return - Retail", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"FC\"\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RR\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.7. Submit Receipt - Transportation", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"ST\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n           \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.8. Submit Return - Transportation", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RT\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.9. Submit Receipt - Banking Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SB\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n          \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.10. Submit Return - Banking Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RB\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.11. Submit Receipt - Education Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SE\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.12. Submit Return - Education Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RE\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.13. Submit Receipt - Professional Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SP\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"syndicateLicenseNumber\": \"1000056\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0,\r\n            \"contractor\": {\r\n                \"name\": \"contractor1\",\r\n                \"amount\": 2.563,\r\n                \"rate\": 2.3\r\n            },\r\n            \"beneficiary\": {\r\n                \"amount\": 20.569,\r\n                \"rate\": 2.147\r\n            }\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.14. Submit Return - Professional Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0\r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RP\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"syndicateLicenseNumber\": \"1000056\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0,\r\n            \"contractor\": {\r\n                \"name\": \"contractor1\",\r\n                \"amount\": 2.563,\r\n                \"rate\": 2.3\r\n            },\r\n            \"beneficiary\": {\r\n                \"amount\": 20.569,\r\n                \"rate\": 2.147\r\n            }\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.15. Submit Receipt - Shipping Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"grossWeight\": 6.58,\r\n                \"netWeight\": 6.89\r\n              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SH\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n           \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.16. Submit Return- Shipping Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"grossWeight\": 6.58,\r\n                \"netWeight\": 6.89\r\n              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RH\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.17. Submit Receipt - Entertainment Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"\"              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SN\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n          \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.18. Submit Return - Entertainment Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"\"              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RN\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n           \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.19. Submit Receipt - Utilities Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"\"              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"SU\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n           \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.3.20. Submit Return- Utilities Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"orderdeliveryMode\": \"\"              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RU\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n           \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":10\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\":5.6\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\"\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\":10.12\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}]}, {"name": "1. Authenticate POS", "event": [{"listen": "test", "script": {"exec": ["var json = pm.response.json()\r", "var accesstoken=json.access_token\r", "pm.environment.set(\"generatedAccessToken\",accesstoken);\r", "\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "posserial", "value": "Test_SerialNo1", "type": "text"}, {"key": "pososversion", "value": "IOS", "type": "text"}, {"key": "posmodelframework", "value": "1", "type": "text", "disabled": true}, {"key": "preshared<PERSON>", "value": "C68FEC13-FE34-4839-89F9-ADB0D678CFE0", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{clientId}}", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}]}, "url": {"raw": "{{idSrvBaseUrl}}/connect/token", "host": ["{{idSrvBaseUrl}}"], "path": ["connect", "token"]}}, "response": []}, {"name": "2.1. Submit Receipt", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{receiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"orderdeliveryMode\": \"\",\r\n                \"grossWeight\": 6.58,\r\n                \"netWeight\": 6.89\r\n              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"S\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"syndicateLicenseNumber\": \"1000056\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\":2.3\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                             \"rate\":2.3\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\",\r\n                             \"rate\":4.0\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                    \"rate\":10\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0,\r\n            \"contractor\": {\r\n                \"name\": \"contractor1\",\r\n                \"amount\": 2.563,\r\n                \"rate\": 2.3\r\n            },\r\n            \"beneficiary\": {\r\n                \"amount\": 20.569,\r\n                \"rate\": 2.147\r\n            }\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.2. Submit Return Receipt", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"orderdeliveryMode\": \"\",\r\n                \"grossWeight\": 6.58,\r\n                \"netWeight\": 6.89,\r\n                \"documentUseReason\": \"I\",\r\n                \"salesIssuedDateTime\": \"2021-12-20T00:00:00Z\"\r\n              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"R\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"syndicateLicenseNumber\": \"1000056\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\": 10.0\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\": 10.0\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0,\r\n            \"contractor\": {\r\n                \"name\": \"contractor1\",\r\n                \"amount\": 2.563,\r\n                \"rate\": 2.3\r\n            },\r\n            \"beneficiary\": {\r\n                \"amount\": 20.569,\r\n                \"rate\": 2.147\r\n            }\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "2.4. Submit Return Receipt without Reference", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"receipts\": [\r\n        {\r\n            \"header\": {\r\n                \"dateTimeIssued\": \"2022-06-13T00:34:00Z\",\r\n                \"receiptNumber\": \"{{receiptNumber1}}\",\r\n                \"uuid\": \"{{returnreceiptUuid1}}\",\r\n                \"previousUUID\": \"\",\r\n\t\t\t\t\"referenceUUID\":\"{{receiptUuid1}}\",\r\n                \"referenceOldUUID\": \"\",\r\n                \"currency\": \"EGP\",\r\n                \"exchangeRate\": 0,\r\n                \"sOrderNameCode\": \"sOrderNameCode\",\r\n                \"orderdeliveryMode\": \"\",\r\n                \"grossWeight\": 6.58,\r\n                \"netWeight\": 6.89,\r\n                \"documentUseReason\": \"I\",\r\n                \"salesIssuedDateTime\": \"2021-12-20T00:00:00Z\"              \r\n            },\r\n            \"documentType\": {\r\n                \"receiptType\": \"RWR\",\r\n                \"typeVersion\": \"1.2\"\r\n            },\r\n            \"seller\": {\r\n                \"rin\": \"*********\",\r\n                \"companyTradeName\": \"شركة الصوٝى\",\r\n                \"branchCode\": \"0\",\r\n                \"branchAddress\": {\r\n                    \"country\": \"EG\",\r\n                    \"governate\": \"cairo\",\r\n                    \"regionCity\": \"city center\",\r\n                    \"street\": \"16 street\",\r\n                    \"buildingNumber\": \"14BN\",\r\n                    \"postalCode\": \"74235\",\r\n                    \"floor\": \"1F\",\r\n                    \"room\": \"3R\",\r\n                    \"landmark\": \"tahrir square\",\r\n                    \"additionalInformation\": \"talaat harb street\"\r\n                },\r\n                \"deviceSerialNumber\": \"Sofy123\",\r\n                \"syndicateLicenseNumber\": \"1000056\",\r\n                \"activityCode\": \"4620\"\r\n            },\r\n            \"buyer\": {\r\n                \"type\": \"F\",\r\n                \"id\": \"*********\",\r\n                \"name\": \"taxpayer 1\",\r\n                \"mobileNumber\": \"+201020567462\",\r\n                \"paymentNumber\": \"987654\"\r\n            },\r\n            \"itemData\": [\r\n                {\r\n                    \"internalCode\": \"880609\",\r\n                    \"description\": \"Samsung A02 32GB_LTE_BLACK_DS_SM-A022FZKDMEB_A022 _ A022_SM-A022FZKDMEB\",\r\n                    \"itemType\": \"GS1\",\r\n                    \"itemCode\": \"037000401629\",\r\n                    \"unitType\": \"EA\",\r\n                    \"quantity\": 35,\r\n                    \"unitPrice\":  247.96000 ,\r\n                    \"netSale\":  7810.74000 ,\r\n                    \"totalSale\":  8678.60000 ,\r\n                    \"total\":  8887.04360 ,\r\n                    \"commercialDiscountData\": [\r\n                         {\r\n                             \"amount\": 867.86000, \r\n                             \"description\": \"XYZ\",\r\n                             \"rate\": 10.0\r\n                         }\r\n                    ],\r\n                    \"itemDiscountData\": [\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\":\"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        {\r\n                            \"amount\": 10,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        }\r\n                    ],\r\n                     \"additionalCommercialDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"ABC\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                        \"additionalItemDiscount\": {\r\n                            \"amount\": 9456.1404,\r\n                            \"description\": \"XYZ\",\r\n                            \"rate\": 10.0\r\n                        },\r\n                    \"valueDifference\": 20,\r\n                    \"taxableItems\": [\r\n                        {\r\n                                \"taxType\": \"T1\",\r\n                                \"amount\":  1096.30360 ,\r\n                                \"subType\": \"V009\",\r\n                                \"rate\": 14\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"totalSales\": 8678.60000,\r\n            \"totalCommercialDiscount\": 867.86000,\r\n            \"totalItemsDiscount\": 20,\r\n            \"extraReceiptDiscountData\": [\r\n               {\r\n                   \"amount\": 0,\r\n                   \"description\": \"ABC\",\r\n                   \"rate\": 10.0\r\n               }\r\n            ],\r\n            \"netAmount\": 7810.74000,\r\n            \"feesAmount\": 0,\r\n            \"totalAmount\": 8887.04360,\r\n            \"taxTotals\": [\r\n                    {\r\n                        \"taxType\": \"T1\",\r\n                        \"amount\": 1096.30360\r\n                    }\r\n            ],\r\n            \"paymentMethod\": \"C\",\r\n            \"adjustment\": 0,\r\n            \"contractor\": {\r\n                \"name\": \"contractor1\",\r\n                \"amount\": 2.563,\r\n                \"rate\": 2.3\r\n            },\r\n            \"beneficiary\": {\r\n                \"amount\": 20.569,\r\n                \"rate\": 2.147\r\n            }\r\n        }\r\n    ]\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions"]}}, "response": []}, {"name": "3. Get Receipt Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receipts/:uuid/details", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receipts", ":uuid", "details"], "variable": [{"key": "uuid", "value": "b4c08c8b273c45d6b824b359d87d957345b0ec6859eb4ad7b68221244ca55d85"}]}}, "response": []}, {"name": "4. Get Receipt", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receipts/:uuid/raw", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receipts", ":uuid", "raw"], "variable": [{"key": "uuid", "value": "b4c08c8b273c45d6b824b359d87d957345b0ec6859eb4ad7b68221244ca55d85"}]}}, "response": []}, {"name": "5. Get Receipt Details Anonymously", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receipts/:uuid/share?dateTimeIssued=2022-03-08T00:00:00Z", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receipts", ":uuid", "share"], "query": [{"key": "dateTimeIssued", "value": "2022-03-08T00:00:00Z"}], "variable": [{"key": "uuid", "value": "b4c08c8b273c45d6b824b359d87d957345b0ec6859eb4ad7b68221244ca55d85"}]}}, "response": []}, {"name": "6. Get Receipt Submission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptsubmissions/:submissionUuid/details?DocumentId=a9d6831c554702f36fd782964ad1b4738f83ea2705eafe28d48d2cf64b331af0&ReceiptNumber=1234566788&DocumentTypeCode=R&DocumentTypeName=Return Receipt&DocumentTypeVersionNumber=1.2&ReceiverId=*********&ReceiverName=Taxpayer1&Status=Valid&SortBy=DateTimeReceived&SortDir=Asc&PageNo=1&PageSize=10", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptsubmissions", ":submissionUuid", "details"], "query": [{"key": "DocumentId", "value": "a9d6831c554702f36fd782964ad1b4738f83ea2705eafe28d48d2cf64b331af0"}, {"key": "ReceiptNumber", "value": "1234566788"}, {"key": "DocumentTypeCode", "value": "R"}, {"key": "DocumentTypeName", "value": "Return Receipt"}, {"key": "DocumentTypeVersionNumber", "value": "1.2"}, {"key": "ReceiverId", "value": "*********"}, {"key": "ReceiverName", "value": "Taxpayer1"}, {"key": "Status", "value": "<PERSON><PERSON>"}, {"key": "SortBy", "value": "DateTimeReceived"}, {"key": "SortDir", "value": "Asc"}, {"key": "PageNo", "value": "1"}, {"key": "PageSize", "value": "10"}], "variable": [{"key": "submissionUuid", "value": "RAQTYQ327VC4Q9170KJYMG5G10", "description": "(Required) "}]}}, "response": []}, {"name": "7. Search Receipts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receipts/search?FreeText=ut dolore&Direction=Submitted&DateTimeIssuedFrom=1997-06-27T10:04:59.938Z&DateTimeIssuedTo=1997-06-27T10:04:59.938Z&DateTimeReceivedFrom=1997-06-27T10:04:59.938Z&DateTimeReceivedTo=1997-06-27T10:04:59.938Z&DocumentTypeCode=ut dolore&DocumentTypeVersion=ut dolore&ReceiverName=ut dolore&ReceiverRegisterationNumber=ut dolore&PosSerialNumber=ut dolore&SubmissionChannel=POS&SortBy=None&SortDir=None&PageNo=63021623&PageSize=63021623", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receipts", "search"], "query": [{"key": "FreeText", "value": "ut dolore"}, {"key": "Direction", "value": "Submitted"}, {"key": "DateTimeIssuedFrom", "value": "1997-06-27T10:04:59.938Z"}, {"key": "DateTimeIssuedTo", "value": "1997-06-27T10:04:59.938Z"}, {"key": "DateTimeReceivedFrom", "value": "1997-06-27T10:04:59.938Z"}, {"key": "DateTimeReceivedTo", "value": "1997-06-27T10:04:59.938Z"}, {"key": "DocumentTypeCode", "value": "ut dolore"}, {"key": "DocumentTypeVersion", "value": "ut dolore"}, {"key": "ReceiverName", "value": "ut dolore"}, {"key": "ReceiverRegisterationNumber", "value": "ut dolore"}, {"key": "PosSerialNumber", "value": "ut dolore"}, {"key": "SubmissionChannel", "value": "POS"}, {"key": "SortBy", "value": "None"}, {"key": "SortDir", "value": "None"}, {"key": "PageNo", "value": "63021623"}, {"key": "PageSize", "value": "63021623"}]}}, "response": []}, {"name": "8. Get Recent Receipts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receipts/recent?Uuid=ut dolore&SubmissionUuid=ut dolore&ReceiptNumber=ut dolore&DocumentTypeCode=ut dolore&DocumentTypeVersion=ut dolore&ReceiverName=ut dolore&ReceiverRegisterationNumber=ut dolore&PosSerialNumber=ut dolore&PaymentMethod=ut dolore&SubmissionChannel=POS&Status=Valid&Direction=Submitted&SortBy=None&SortDir=None&PageNo=63021623&PageSize=63021623", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receipts", "recent"], "query": [{"key": "<PERSON><PERSON>", "value": "ut dolore"}, {"key": "SubmissionUuid", "value": "ut dolore"}, {"key": "ReceiptNumber", "value": "ut dolore"}, {"key": "DocumentTypeCode", "value": "ut dolore"}, {"key": "DocumentTypeVersion", "value": "ut dolore"}, {"key": "ReceiverName", "value": "ut dolore"}, {"key": "ReceiverRegisterationNumber", "value": "ut dolore"}, {"key": "PosSerialNumber", "value": "ut dolore"}, {"key": "PaymentMethod", "value": "ut dolore"}, {"key": "SubmissionChannel", "value": "POS"}, {"key": "Status", "value": "<PERSON><PERSON>"}, {"key": "Direction", "value": "Submitted"}, {"key": "SortBy", "value": "None"}, {"key": "SortDir", "value": "None"}, {"key": "PageNo", "value": "63021623"}, {"key": "PageSize", "value": "63021623"}]}}, "response": []}, {"name": "9. Request Receipt Package", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"Summary\",\r\n  \"format\": \"JSON\",\r\n  \"queryParameters\": {\r\n    \"represntedTaxpayerTypeId\": \"Both\",\r\n    \"dateFrom\": \"2022-01-01T01:39:46.197Z\",\r\n    \"dateTo\": \"2022-08-25T01:39:46.197Z\",\r\n    \"posSerial\": \"\",\r\n    \"receiptUUID\": \"\",\r\n    \"submissionUUID\": \"\",\r\n    \"receiverSenderId\": \"*********\",\r\n    \"receiverSenderType\": \"b\",\r\n    \"documentTypeNames\": [\"r\",\"s\"],\r\n    \"branchNumber\": null,\r\n    \"itemCodes\":\r\n               [\r\n                 {\"codeValue\": \"1000000000033\", \"codeType\": \"GS1\"},\r\n                 {\"codeValue\": \"1000000000014\", \"codeType\": \"GS1\"}\r\n               ] \r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptPackages/requests", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptPackages", "requests"]}}, "response": []}, {"name": "10. Get Package Requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptPackages/requests?pageSize=10&pageNo=1", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptPackages", "requests"], "query": [{"key": "pageSize", "value": "10"}, {"key": "pageNo", "value": "1"}]}}, "response": []}, {"name": "11. Get Receipt Package", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{generatedAccessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{apiBaseUrl}}/api/v1/receiptPackages/:rid", "host": ["{{apiBaseUrl}}"], "path": ["api", "v1", "receiptPackages", ":rid"], "variable": [{"key": "rid", "value": "12345"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}