﻿﻿Imports System.Data.SqlClient

Public Class frm_expenses
    Dim connx As New CLS_CON
    
    Private Sub frm_expenses_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadExpenseTypes()
        LoadBuildings()
        LoadExpenses()
        ClearForm()
    End Sub
    
    ' تحميل أنواع المصروفات
    Public Sub LoadExpenseTypes()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT ExpenseType_ID, ExpenseTypeName FROM ExpenseTypes_Tbl WHERE IsActive = 1 ORDER BY ExpenseTypeName", connx.Con)
            Dim dt As New DataTable()
            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)
            
            CmbExpenseType.DataSource = dt
            CmbExpenseType.DisplayMember = "ExpenseTypeName"
            CmbExpenseType.ValueMember = "ExpenseType_ID"
            CmbExpenseType.SelectedIndex = -1
            
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل أنواع المصروفات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' تحميل العمارات
    Public Sub LoadBuildings()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT Cat_ID, CatName FROM Cat_Tbl ORDER BY CatName", connx.Con)
            Dim dt As New DataTable()
            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)
            
            CmbBuilding.DataSource = dt
            CmbBuilding.DisplayMember = "CatName"
            CmbBuilding.ValueMember = "Cat_ID"
            CmbBuilding.SelectedIndex = -1
            
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل العمارات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' تحميل الوحدات حسب العمارة المختارة
    Public Sub LoadUnits()
        Try
            If CmbBuilding.SelectedValue Is Nothing Then
                CmbUnit.DataSource = Nothing
                Return
            End If
            
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT Item_ID, ItemName FROM Item_Tbl WHERE Cat_ID = @Cat_ID ORDER BY ItemName", connx.Con)
            cmd.Parameters.AddWithValue("@Cat_ID", CmbBuilding.SelectedValue)
            
            Dim dt As New DataTable()
            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(dt)
            
            CmbUnit.DataSource = dt
            CmbUnit.DisplayMember = "ItemName"
            CmbUnit.ValueMember = "Item_ID"
            CmbUnit.SelectedIndex = -1
            
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الوحدات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' تحميل المصروفات
    Public Sub LoadExpenses()
        Try
            DgvExpenses.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT * FROM View_Expenses ORDER BY ExpenseDate DESC", connx.Con)
            Dim reader As SqlDataReader = cmd.ExecuteReader()
            
            While reader.Read()
                Dim approvalStatus As String = If(reader("IsApproved"), "معتمد", "غير معتمد")
                DgvExpenses.Rows.Add(
                    reader("Expense_ID"),
                    reader("ExpenseTypeName"),
                    reader("ExpenseDescription"),
                    reader("ExpenseAmount"),
                    reader("ExpenseDate"),
                    If(IsDBNull(reader("BuildingName")), "", reader("BuildingName")),
                    If(IsDBNull(reader("UnitName")), "", reader("UnitName")),
                    reader("PaymentMethod"),
                    approvalStatus
                )
            End While
            
            reader.Close()
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل المصروفات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' حفظ مصروف جديد
    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If ValidateForm() Then
            SaveExpense()
        End If
    End Sub
    
    ' التحقق من صحة البيانات
    Private Function ValidateForm() As Boolean
        If CmbExpenseType.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار نوع المصروف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CmbExpenseType.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(TxtDescription.Text) Then
            MessageBox.Show("يرجى إدخال وصف المصروف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtDescription.Focus()
            Return False
        End If
        
        If Not IsNumeric(TxtAmount.Text) OrElse Val(TxtAmount.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtAmount.Focus()
            Return False
        End If
        
        If CmbPaymentMethod.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CmbPaymentMethod.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    ' حفظ المصروف
    Private Sub SaveExpense()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "INSERT INTO Expenses_Tbl (ExpenseType_ID, ExpenseDescription, ExpenseAmount, ExpenseDate, " &
                              "Building_ID, Unit_ID, PaymentMethod, ReceiptNumber, Notes, CreatedBy) " &
                              "VALUES (@ExpenseType_ID, @ExpenseDescription, @ExpenseAmount, @ExpenseDate, " &
                              "@Building_ID, @Unit_ID, @PaymentMethod, @ReceiptNumber, @Notes, @CreatedBy)"
                
                .Parameters.AddWithValue("@ExpenseType_ID", CmbExpenseType.SelectedValue)
                .Parameters.AddWithValue("@ExpenseDescription", TxtDescription.Text.Trim())
                .Parameters.AddWithValue("@ExpenseAmount", CDec(TxtAmount.Text))
                .Parameters.AddWithValue("@ExpenseDate", DtpExpenseDate.Value.Date)
                .Parameters.AddWithValue("@Building_ID", If(CmbBuilding.SelectedValue Is Nothing, DBNull.Value, CmbBuilding.SelectedValue))
                .Parameters.AddWithValue("@Unit_ID", If(CmbUnit.SelectedValue Is Nothing, DBNull.Value, CmbUnit.SelectedValue))
                .Parameters.AddWithValue("@PaymentMethod", CmbPaymentMethod.Text)
                .Parameters.AddWithValue("@ReceiptNumber", If(String.IsNullOrWhiteSpace(TxtReceiptNumber.Text), DBNull.Value, TxtReceiptNumber.Text.Trim()))
                .Parameters.AddWithValue("@Notes", If(String.IsNullOrWhiteSpace(TxtNotes.Text), DBNull.Value, TxtNotes.Text.Trim()))
                .Parameters.AddWithValue("@CreatedBy", _UserName) ' متغير عام للمستخدم الحالي
            End With
            
            cmd.ExecuteNonQuery()
            connx.Con.Close()
            
            MessageBox.Show("تم حفظ المصروف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearForm()
            LoadExpenses()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في حفظ المصروف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' مسح النموذج
    Private Sub ClearForm()
        CmbExpenseType.SelectedIndex = -1
        TxtDescription.Clear()
        TxtAmount.Clear()
        DtpExpenseDate.Value = Date.Today
        CmbBuilding.SelectedIndex = -1
        CmbUnit.DataSource = Nothing
        CmbPaymentMethod.SelectedIndex = -1
        TxtReceiptNumber.Clear()
        TxtNotes.Clear()
        TxtExpenseID.Clear()
        
        BtnSave.Enabled = True
        BtnUpdate.Enabled = False
        BtnDelete.Enabled = False
    End Sub
    
    ' عند تغيير العمارة المختارة
    Private Sub CmbBuilding_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CmbBuilding.SelectedIndexChanged
        LoadUnits()
    End Sub
    
    ' عند النقر على صف في الجدول
    Private Sub DgvExpenses_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvExpenses.CellClick
        If e.RowIndex >= 0 Then
            LoadExpenseToForm(e.RowIndex)
        End If
    End Sub
    
    ' تحميل بيانات المصروف للتعديل
    Private Sub LoadExpenseToForm(rowIndex As Integer)
        Try
            Dim expenseID As Integer = DgvExpenses.Rows(rowIndex).Cells(0).Value
            TxtExpenseID.Text = expenseID.ToString()
            
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT * FROM Expenses_Tbl WHERE Expense_ID = @Expense_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Expense_ID", expenseID)
            
            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                CmbExpenseType.SelectedValue = reader("ExpenseType_ID")
                TxtDescription.Text = reader("ExpenseDescription").ToString()
                TxtAmount.Text = reader("ExpenseAmount").ToString()
                DtpExpenseDate.Value = reader("ExpenseDate")
                
                If Not IsDBNull(reader("Building_ID")) Then
                    CmbBuilding.SelectedValue = reader("Building_ID")
                    LoadUnits()
                    If Not IsDBNull(reader("Unit_ID")) Then
                        CmbUnit.SelectedValue = reader("Unit_ID")
                    End If
                End If
                
                CmbPaymentMethod.Text = reader("PaymentMethod").ToString()
                TxtReceiptNumber.Text = If(IsDBNull(reader("ReceiptNumber")), "", reader("ReceiptNumber").ToString())
                TxtNotes.Text = If(IsDBNull(reader("Notes")), "", reader("Notes").ToString())
            End If
            
            reader.Close()
            connx.Con.Close()
            
            BtnSave.Enabled = False
            BtnUpdate.Enabled = True
            BtnDelete.Enabled = True
            
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات المصروف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' تحديث المصروف
    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If ValidateForm() AndAlso Not String.IsNullOrWhiteSpace(TxtExpenseID.Text) Then
            UpdateExpense()
        End If
    End Sub
    
    ' تحديث بيانات المصروف
    Private Sub UpdateExpense()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "UPDATE Expenses_Tbl SET ExpenseType_ID = @ExpenseType_ID, ExpenseDescription = @ExpenseDescription, " &
                              "ExpenseAmount = @ExpenseAmount, ExpenseDate = @ExpenseDate, Building_ID = @Building_ID, " &
                              "Unit_ID = @Unit_ID, PaymentMethod = @PaymentMethod, ReceiptNumber = @ReceiptNumber, " &
                              "Notes = @Notes WHERE Expense_ID = @Expense_ID"
                
                .Parameters.AddWithValue("@Expense_ID", CInt(TxtExpenseID.Text))
                .Parameters.AddWithValue("@ExpenseType_ID", CmbExpenseType.SelectedValue)
                .Parameters.AddWithValue("@ExpenseDescription", TxtDescription.Text.Trim())
                .Parameters.AddWithValue("@ExpenseAmount", CDec(TxtAmount.Text))
                .Parameters.AddWithValue("@ExpenseDate", DtpExpenseDate.Value.Date)
                .Parameters.AddWithValue("@Building_ID", If(CmbBuilding.SelectedValue Is Nothing, DBNull.Value, CmbBuilding.SelectedValue))
                .Parameters.AddWithValue("@Unit_ID", If(CmbUnit.SelectedValue Is Nothing, DBNull.Value, CmbUnit.SelectedValue))
                .Parameters.AddWithValue("@PaymentMethod", CmbPaymentMethod.Text)
                .Parameters.AddWithValue("@ReceiptNumber", If(String.IsNullOrWhiteSpace(TxtReceiptNumber.Text), DBNull.Value, TxtReceiptNumber.Text.Trim()))
                .Parameters.AddWithValue("@Notes", If(String.IsNullOrWhiteSpace(TxtNotes.Text), DBNull.Value, TxtNotes.Text.Trim()))
            End With
            
            cmd.ExecuteNonQuery()
            connx.Con.Close()
            
            MessageBox.Show("تم تحديث المصروف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearForm()
            LoadExpenses()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث المصروف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' حذف المصروف
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If Not String.IsNullOrWhiteSpace(TxtExpenseID.Text) Then
            If MessageBox.Show("هل أنت متأكد من حذف هذا المصروف؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                DeleteExpense()
            End If
        End If
    End Sub
    
    ' حذف المصروف
    Private Sub DeleteExpense()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("DELETE FROM Expenses_Tbl WHERE Expense_ID = @Expense_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Expense_ID", CInt(TxtExpenseID.Text))
            
            cmd.ExecuteNonQuery()
            connx.Con.Close()
            
            MessageBox.Show("تم حذف المصروف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearForm()
            LoadExpenses()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في حذف المصروف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' مسح النموذج
    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearForm()
    End Sub
    
    ' البحث في المصروفات
    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs) Handles TxtSearch.TextChanged
        SearchExpenses()
    End Sub
    
    ' البحث في المصروفات
    Private Sub SearchExpenses()
        Try
            DgvExpenses.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim searchText As String = TxtSearch.Text.Trim()
            Dim cmd As New SqlCommand()
            
            If String.IsNullOrWhiteSpace(searchText) Then
                cmd.CommandText = "SELECT * FROM View_Expenses ORDER BY ExpenseDate DESC"
            Else
                cmd.CommandText = "SELECT * FROM View_Expenses WHERE ExpenseDescription LIKE @Search OR " &
                                 "ExpenseTypeName LIKE @Search OR BuildingName LIKE @Search OR " &
                                 "UnitName LIKE @Search ORDER BY ExpenseDate DESC"
                cmd.Parameters.AddWithValue("@Search", "%" & searchText & "%")
            End If
            
            cmd.Connection = connx.Con
            Dim reader As SqlDataReader = cmd.ExecuteReader()
            
            While reader.Read()
                Dim approvalStatus As String = If(reader("IsApproved"), "معتمد", "غير معتمد")
                DgvExpenses.Rows.Add(
                    reader("Expense_ID"),
                    reader("ExpenseTypeName"),
                    reader("ExpenseDescription"),
                    reader("ExpenseAmount"),
                    reader("ExpenseDate"),
                    If(IsDBNull(reader("BuildingName")), "", reader("BuildingName")),
                    If(IsDBNull(reader("UnitName")), "", reader("UnitName")),
                    reader("PaymentMethod"),
                    approvalStatus
                )
            End While
            
            reader.Close()
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
