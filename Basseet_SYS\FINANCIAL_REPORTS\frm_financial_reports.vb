Imports System.Data.SqlClient

Public Class frm_financial_reports

    Private Sub frm_financial_reports_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadReportTypes()
        DtpFromDate.Value = New DateTime(DateTime.Now.Year, DateTime.Now.Month, 1)
        DtpToDate.Value = DateTime.Now
    End Sub

    Private Sub LoadReportTypes()
        CmbReportType.Items.Clear()
        CmbReportType.Items.Add("تقرير الإيرادات الشهرية")
        CmbReportType.Items.Add("تقرير المصروفات الشهرية")
        CmbReportType.Items.Add("تقرير صافي الربح")
        CmbReportType.Items.Add("تقرير العقود النشطة")
        CmbReportType.Items.Add("تقرير المتأخرات")
        CmbReportType.Items.Add("تقرير الوحدات الشاغرة")
        CmbReportType.Items.Add("تقرير مفصل للمستأجرين")
        CmbReportType.SelectedIndex = 0
    End Sub

    Private Sub BtnGenerateReport_Click(sender As Object, e As EventArgs) Handles BtnGenerateReport.Click
        If CmbReportType.SelectedIndex = -1 Then
            MsgBox("يرجى اختيار نوع التقرير", MsgBoxStyle.Exclamation, "تنبيه")
            Return
        End If

        Select Case CmbReportType.SelectedIndex
            Case 0 ' تقرير الإيرادات الشهرية
                GenerateIncomeReport()
            Case 1 ' تقرير المصروفات الشهرية
                GenerateExpenseReport()
            Case 2 ' تقرير صافي الربح
                GenerateProfitReport()
            Case 3 ' تقرير العقود النشطة
                GenerateActiveContractsReport()
            Case 4 ' تقرير المتأخرات
                GenerateOverdueReport()
            Case 5 ' تقرير الوحدات الشاغرة
                GenerateVacantUnitsReport()
            Case 6 ' تقرير مفصل للمستأجرين
                GenerateTenantsDetailReport()
        End Select
    End Sub

    Private Sub GenerateIncomeReport()
        Try
            Dim query As String = "SELECT MONTH(PaymentDate) AS الشهر, YEAR(PaymentDate) AS السنة, " &
                                "SUM(Amount) AS إجمالي_الإيرادات, COUNT(*) AS عدد_الدفعات " &
                                "FROM RentPayments_Tbl " &
                                "WHERE PaymentDate BETWEEN @FromDate AND @ToDate " &
                                "AND PaymentStatus = 'مدفوع' " &
                                "GROUP BY YEAR(PaymentDate), MONTH(PaymentDate) " &
                                "ORDER BY السنة DESC, الشهر DESC"

            LoadReportData(query, "تقرير الإيرادات الشهرية")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير الإيرادات: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateExpenseReport()
        Try
            Dim query As String = "SELECT ET.TypeName AS نوع_المصروف, " &
                                "SUM(E.Amount) AS إجمالي_المبلغ, COUNT(*) AS عدد_المصروفات " &
                                "FROM Expenses_Tbl E " &
                                "INNER JOIN ExpenseTypes_Tbl ET ON E.ExpenseType_ID = ET.TypeID " &
                                "WHERE E.ExpenseDate BETWEEN @FromDate AND @ToDate " &
                                "AND E.ApprovalStatus = 'معتمد' " &
                                "GROUP BY ET.TypeName " &
                                "ORDER BY إجمالي_المبلغ DESC"

            LoadReportData(query, "تقرير المصروفات الشهرية")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير المصروفات: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateProfitReport()
        Try
            Dim query As String = "SELECT " &
                                "'الإيرادات' AS البيان, " &
                                "ISNULL((SELECT SUM(Amount) FROM RentPayments_Tbl " &
                                "WHERE PaymentDate BETWEEN @FromDate AND @ToDate " &
                                "AND PaymentStatus = 'مدفوع'), 0) AS المبلغ " &
                                "UNION ALL " &
                                "SELECT " &
                                "'المصروفات' AS البيان, " &
                                "ISNULL((SELECT SUM(Amount) FROM Expenses_Tbl " &
                                "WHERE ExpenseDate BETWEEN @FromDate AND @ToDate " &
                                "AND ApprovalStatus = 'معتمد'), 0) AS المبلغ " &
                                "UNION ALL " &
                                "SELECT " &
                                "'صافي الربح' AS البيان, " &
                                "ISNULL((SELECT SUM(Amount) FROM RentPayments_Tbl " &
                                "WHERE PaymentDate BETWEEN @FromDate AND @ToDate " &
                                "AND PaymentStatus = 'مدفوع'), 0) - " &
                                "ISNULL((SELECT SUM(Amount) FROM Expenses_Tbl " &
                                "WHERE ExpenseDate BETWEEN @FromDate AND @ToDate " &
                                "AND ApprovalStatus = 'معتمد'), 0) AS المبلغ"

            LoadReportData(query, "تقرير صافي الربح")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير الربح: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateActiveContractsReport()
        Try
            Dim query As String = "SELECT RC.ContractNumber AS رقم_العقد, " &
                                "C.CustomerName AS المستأجر, " &
                                "CONCAT(Cat.Cat_Name, ' - ', I.Item_Name) AS الوحدة, " &
                                "RC.StartDate AS تاريخ_البداية, " &
                                "RC.EndDate AS تاريخ_الانتهاء, " &
                                "RC.MonthlyRent AS الإيجار_الشهري, " &
                                "RC.SecurityDeposit AS مبلغ_التأمين " &
                                "FROM RentalContracts_Tbl RC " &
                                "INNER JOIN tblCustomers C ON RC.Tenant_ID = C.CustomerID " &
                                "INNER JOIN Item_Tbl I ON RC.Unit_ID = I.Item_ID " &
                                "INNER JOIN Cat_Tbl Cat ON I.Cat_ID = Cat.Cat_ID " &
                                "WHERE RC.ContractStatus = 'نشط' " &
                                "ORDER BY RC.ContractNumber"

            LoadReportDataWithoutDateFilter(query, "تقرير العقود النشطة")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير العقود: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateOverdueReport()
        Try
            Dim query As String = "SELECT RC.ContractNumber AS رقم_العقد, " &
                                "C.CustomerName AS المستأجر, " &
                                "CONCAT(Cat.Cat_Name, ' - ', I.Item_Name) AS الوحدة, " &
                                "RC.MonthlyRent AS الإيجار_الشهري, " &
                                "DATEDIFF(MONTH, RC.StartDate, GETDATE()) AS الأشهر_المستحقة, " &
                                "ISNULL(Paid.TotalPaid, 0) AS المدفوع, " &
                                "(DATEDIFF(MONTH, RC.StartDate, GETDATE()) * RC.MonthlyRent) - ISNULL(Paid.TotalPaid, 0) AS المتأخرات " &
                                "FROM RentalContracts_Tbl RC " &
                                "INNER JOIN tblCustomers C ON RC.Tenant_ID = C.CustomerID " &
                                "INNER JOIN Item_Tbl I ON RC.Unit_ID = I.Item_ID " &
                                "INNER JOIN Cat_Tbl Cat ON I.Cat_ID = Cat.Cat_ID " &
                                "LEFT JOIN (SELECT Contract_ID, SUM(Amount) AS TotalPaid " &
                                "FROM RentPayments_Tbl WHERE PaymentStatus = 'مدفوع' " &
                                "GROUP BY Contract_ID) Paid ON RC.ContractID = Paid.Contract_ID " &
                                "WHERE RC.ContractStatus = 'نشط' " &
                                "AND (DATEDIFF(MONTH, RC.StartDate, GETDATE()) * RC.MonthlyRent) > ISNULL(Paid.TotalPaid, 0) " &
                                "ORDER BY المتأخرات DESC"

            LoadReportDataWithoutDateFilter(query, "تقرير المتأخرات")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير المتأخرات: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateVacantUnitsReport()
        Try
            Dim query As String = "SELECT Cat.Cat_Name AS العمارة, " &
                                "I.Item_Name AS الوحدة, " &
                                "I.UnitStatus AS الحالة, " &
                                "I.RentPrice AS سعر_الإيجار " &
                                "FROM Item_Tbl I " &
                                "INNER JOIN Cat_Tbl Cat ON I.Cat_ID = Cat.Cat_ID " &
                                "WHERE I.UnitStatus = 'شاغر' " &
                                "ORDER BY Cat.Cat_Name, I.Item_Name"

            LoadReportDataWithoutDateFilter(query, "تقرير الوحدات الشاغرة")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير الوحدات الشاغرة: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub GenerateTenantsDetailReport()
        Try
            Dim query As String = "SELECT C.CustomerName AS المستأجر, " &
                                "C.Mobile AS الجوال, " &
                                "C.Email AS البريد_الإلكتروني, " &
                                "RC.ContractNumber AS رقم_العقد, " &
                                "CONCAT(Cat.Cat_Name, ' - ', I.Item_Name) AS الوحدة, " &
                                "RC.MonthlyRent AS الإيجار_الشهري, " &
                                "RC.StartDate AS تاريخ_بداية_العقد, " &
                                "RC.EndDate AS تاريخ_انتهاء_العقد, " &
                                "RC.ContractStatus AS حالة_العقد " &
                                "FROM tblCustomers C " &
                                "INNER JOIN RentalContracts_Tbl RC ON C.CustomerID = RC.Tenant_ID " &
                                "INNER JOIN Item_Tbl I ON RC.Unit_ID = I.Item_ID " &
                                "INNER JOIN Cat_Tbl Cat ON I.Cat_ID = Cat.Cat_ID " &
                                "WHERE C.CustomerType = 'مستأجر' " &
                                "ORDER BY C.CustomerName"

            LoadReportDataWithoutDateFilter(query, "تقرير مفصل للمستأجرين")

        Catch ex As Exception
            MsgBox("خطأ في إنشاء تقرير المستأجرين: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub LoadReportData(query As String, reportTitle As String)
        Try
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            
            Using cmd As New SqlCommand(query, connx.Con)
                cmd.Parameters.AddWithValue("@FromDate", DtpFromDate.Value.Date)
                cmd.Parameters.AddWithValue("@ToDate", DtpToDate.Value.Date.AddDays(1).AddSeconds(-1))
                
                Dim adapter As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                adapter.Fill(dt)
                
                DgvReport.DataSource = dt
                LblReportTitle.Text = reportTitle & " من " & DtpFromDate.Value.ToShortDateString() & " إلى " & DtpToDate.Value.ToShortDateString()
            End Using
            
        Catch ex As Exception
            Throw ex
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub LoadReportDataWithoutDateFilter(query As String, reportTitle As String)
        Try
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            
            Using cmd As New SqlCommand(query, connx.Con)
                Dim adapter As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                adapter.Fill(dt)
                
                DgvReport.DataSource = dt
                LblReportTitle.Text = reportTitle
            End Using
            
        Catch ex As Exception
            Throw ex
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs) Handles BtnExport.Click
        If DgvReport.DataSource Is Nothing Then
            MsgBox("لا توجد بيانات للتصدير", MsgBoxStyle.Exclamation, "تنبيه")
            Return
        End If

        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.Title = "حفظ التقرير"
            saveDialog.FileName = CmbReportType.Text & "_" & DateTime.Now.ToString("yyyyMMdd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ExportToExcel(saveDialog.FileName)
                MsgBox("تم تصدير التقرير بنجاح", MsgBoxStyle.Information, "نجح التصدير")
            End If

        Catch ex As Exception
            MsgBox("خطأ في تصدير التقرير: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub ExportToExcel(fileName As String)
        ' هذه الطريقة تحتاج إلى مكتبة Excel أو يمكن تصدير البيانات كـ CSV
        ' للبساطة، سنصدر كـ CSV
        Dim csvFileName As String = fileName.Replace(".xlsx", ".csv")
        
        Using writer As New System.IO.StreamWriter(csvFileName, False, System.Text.Encoding.UTF8)
            ' كتابة العناوين
            Dim headers As String = ""
            For i As Integer = 0 To DgvReport.Columns.Count - 1
                headers += DgvReport.Columns(i).HeaderText
                If i < DgvReport.Columns.Count - 1 Then headers += ","
            Next
            writer.WriteLine(headers)
            
            ' كتابة البيانات
            For Each row As DataGridViewRow In DgvReport.Rows
                If Not row.IsNewRow Then
                    Dim line As String = ""
                    For i As Integer = 0 To row.Cells.Count - 1
                        line += If(row.Cells(i).Value?.ToString(), "")
                        If i < row.Cells.Count - 1 Then line += ","
                    Next
                    writer.WriteLine(line)
                End If
            Next
        End Using
    End Sub
End Class
