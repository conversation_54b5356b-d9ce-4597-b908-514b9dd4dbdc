﻿Imports System.Data.SqlClient

Public Class frmCustomers
    Dim connx As New CLS_CON
    Dim CustomerID As Integer = 0

    Private Sub frmCustomers_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadCustomers()
        ClearForm()
    End Sub

    Private Sub LoadCustomers()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SELECT CustomerID, CustomerName, Mobile, Email, NationalID, CustomerType FROM tblCustomers WHERE CustomerType = 'مستأجر' ORDER BY CustomerName", connx.Con)
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            DgvCustomers.DataSource = dt

            ' تنسيق الأعمدة
            If DgvCustomers.Columns.Count > 0 Then
                DgvCustomers.Columns("CustomerID").HeaderText = "رقم المستأجر"
                DgvCustomers.Columns("CustomerName").HeaderText = "اسم المستأجر"
                DgvCustomers.Columns("Mobile").HeaderText = "رقم الجوال"
                DgvCustomers.Columns("Email").HeaderText = "البريد الإلكتروني"
                DgvCustomers.Columns("NationalID").HeaderText = "رقم الهوية"
                DgvCustomers.Columns("CustomerType").HeaderText = "نوع العميل"

                DgvCustomers.Columns("CustomerID").Width = 100
                DgvCustomers.Columns("CustomerName").Width = 200
                DgvCustomers.Columns("Mobile").Width = 150
                DgvCustomers.Columns("Email").Width = 200
                DgvCustomers.Columns("NationalID").Width = 150
                DgvCustomers.Columns("CustomerType").Width = 100
            End If

            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات المستأجرين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        TxtCustomerName.Text = ""
        TxtMobile.Text = ""
        TxtEmail.Text = ""
        TxtNationalID.Text = ""
        TxtAddress.Text = ""
        TxtNotes.Text = ""
        CustomerID = 0
        BtnSave.Enabled = True
        BtnUpdate.Enabled = False
        BtnDelete.Enabled = False
        TxtCustomerName.Focus()
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If ValidateForm() Then
            SaveCustomer()
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(TxtCustomerName.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستأجر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtCustomerName.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(TxtMobile.Text) Then
            MessageBox.Show("يرجى إدخال رقم الجوال", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtMobile.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(TxtNationalID.Text) Then
            MessageBox.Show("يرجى إدخال رقم الهوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtNationalID.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub SaveCustomer()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "INSERT INTO tblCustomers (CustomerName, Mobile, Email, NationalID, CustomerAddress, Notes, CustomerType) " &
                              "VALUES (@CustomerName, @Mobile, @Email, @NationalID, @CustomerAddress, @Notes, @CustomerType)"

                .Parameters.AddWithValue("@CustomerName", TxtCustomerName.Text.Trim())
                .Parameters.AddWithValue("@Mobile", TxtMobile.Text.Trim())
                .Parameters.AddWithValue("@Email", If(String.IsNullOrWhiteSpace(TxtEmail.Text), DBNull.Value, TxtEmail.Text.Trim()))
                .Parameters.AddWithValue("@NationalID", TxtNationalID.Text.Trim())
                .Parameters.AddWithValue("@CustomerAddress", If(String.IsNullOrWhiteSpace(TxtAddress.Text), DBNull.Value, TxtAddress.Text.Trim()))
                .Parameters.AddWithValue("@Notes", If(String.IsNullOrWhiteSpace(TxtNotes.Text), DBNull.Value, TxtNotes.Text.Trim()))
                .Parameters.AddWithValue("@CustomerType", "مستأجر")
            End With

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم حفظ المستأجر بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCustomers()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في حفظ المستأجر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvCustomers_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvCustomers.CellClick
        If e.RowIndex >= 0 Then
            Try
                CustomerID = DgvCustomers.Rows(e.RowIndex).Cells("CustomerID").Value
                TxtCustomerName.Text = If(IsDBNull(DgvCustomers.Rows(e.RowIndex).Cells("CustomerName").Value), "", DgvCustomers.Rows(e.RowIndex).Cells("CustomerName").Value.ToString())
                TxtMobile.Text = If(IsDBNull(DgvCustomers.Rows(e.RowIndex).Cells("Mobile").Value), "", DgvCustomers.Rows(e.RowIndex).Cells("Mobile").Value.ToString())
                TxtEmail.Text = If(IsDBNull(DgvCustomers.Rows(e.RowIndex).Cells("Email").Value), "", DgvCustomers.Rows(e.RowIndex).Cells("Email").Value.ToString())
                TxtNationalID.Text = If(IsDBNull(DgvCustomers.Rows(e.RowIndex).Cells("NationalID").Value), "", DgvCustomers.Rows(e.RowIndex).Cells("NationalID").Value.ToString())

                ' تحميل باقي البيانات من قاعدة البيانات
                LoadCustomerDetails(CustomerID)

                BtnSave.Enabled = False
                BtnUpdate.Enabled = True
                BtnDelete.Enabled = True

            Catch ex As Exception
                MessageBox.Show("خطأ في تحديد المستأجر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub LoadCustomerDetails(custID As Integer)
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SELECT CustomerAddress, Notes FROM tblCustomers WHERE CustomerID = @CustomerID", connx.Con)
            cmd.Parameters.AddWithValue("@CustomerID", custID)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                TxtAddress.Text = If(IsDBNull(reader("CustomerAddress")), "", reader("CustomerAddress").ToString())
                TxtNotes.Text = If(IsDBNull(reader("Notes")), "", reader("Notes").ToString())
            End If

            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل تفاصيل المستأجر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If ValidateForm() AndAlso CustomerID > 0 Then
            UpdateCustomer()
        End If
    End Sub

    Private Sub UpdateCustomer()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "UPDATE tblCustomers SET CustomerName = @CustomerName, Mobile = @Mobile, " &
                              "Email = @Email, NationalID = @NationalID, CustomerAddress = @CustomerAddress, " &
                              "Notes = @Notes WHERE CustomerID = @CustomerID"

                .Parameters.AddWithValue("@CustomerID", CustomerID)
                .Parameters.AddWithValue("@CustomerName", TxtCustomerName.Text.Trim())
                .Parameters.AddWithValue("@Mobile", TxtMobile.Text.Trim())
                .Parameters.AddWithValue("@Email", If(String.IsNullOrWhiteSpace(TxtEmail.Text), DBNull.Value, TxtEmail.Text.Trim()))
                .Parameters.AddWithValue("@NationalID", TxtNationalID.Text.Trim())
                .Parameters.AddWithValue("@CustomerAddress", If(String.IsNullOrWhiteSpace(TxtAddress.Text), DBNull.Value, TxtAddress.Text.Trim()))
                .Parameters.AddWithValue("@Notes", If(String.IsNullOrWhiteSpace(TxtNotes.Text), DBNull.Value, TxtNotes.Text.Trim()))
            End With

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم تحديث بيانات المستأجر بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCustomers()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث المستأجر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If CustomerID > 0 Then
            If MessageBox.Show("هل أنت متأكد من حذف هذا المستأجر؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                DeleteCustomer()
            End If
        End If
    End Sub

    Private Sub DeleteCustomer()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("DELETE FROM tblCustomers WHERE CustomerID = @CustomerID", connx.Con)
            cmd.Parameters.AddWithValue("@CustomerID", CustomerID)

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم حذف المستأجر بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCustomers()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في حذف المستأجر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearForm()
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs) Handles TxtSearch.TextChanged
        SearchCustomers()
    End Sub

    Private Sub SearchCustomers()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim searchText As String = TxtSearch.Text.Trim()
            Dim cmd As New SqlCommand()

            If String.IsNullOrWhiteSpace(searchText) Then
                cmd.CommandText = "SELECT CustomerID, CustomerName, Mobile, Email, NationalID, CustomerType FROM tblCustomers WHERE CustomerType = 'مستأجر' ORDER BY CustomerName"
            Else
                cmd.CommandText = "SELECT CustomerID, CustomerName, Mobile, Email, NationalID, CustomerType FROM tblCustomers " &
                                 "WHERE CustomerType = 'مستأجر' AND (CustomerName LIKE @search OR Mobile LIKE @search OR NationalID LIKE @search) " &
                                 "ORDER BY CustomerName"
                cmd.Parameters.AddWithValue("@search", "%" & searchText & "%")
            End If

            cmd.Connection = connx.Con
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            DgvCustomers.DataSource = dt
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Class