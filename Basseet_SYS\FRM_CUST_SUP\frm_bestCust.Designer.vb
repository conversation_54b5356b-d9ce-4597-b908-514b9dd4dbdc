﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frm_bestCust
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim ChartArea2 As System.Windows.Forms.DataVisualization.Charting.ChartArea = New System.Windows.Forms.DataVisualization.Charting.ChartArea()
        Dim Legend2 As System.Windows.Forms.DataVisualization.Charting.Legend = New System.Windows.Forms.DataVisualization.Charting.Legend()
        Dim Series2 As System.Windows.Forms.DataVisualization.Charting.Series = New System.Windows.Forms.DataVisualization.Charting.Series()
        Dim ChartArea1 As System.Windows.Forms.DataVisualization.Charting.ChartArea = New System.Windows.Forms.DataVisualization.Charting.ChartArea()
        Dim Legend1 As System.Windows.Forms.DataVisualization.Charting.Legend = New System.Windows.Forms.DataVisualization.Charting.Legend()
        Dim Series1 As System.Windows.Forms.DataVisualization.Charting.Series = New System.Windows.Forms.DataVisualization.Charting.Series()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Dgv = New System.Windows.Forms.DataGridView()
        Me.ComboBoxCustomers = New System.Windows.Forms.ComboBox()
        Me.ChartTopCustomers = New System.Windows.Forms.DataVisualization.Charting.Chart()
        Me.ChartTopCustomers1 = New System.Windows.Forms.DataVisualization.Charting.Chart()
        Me.Panel3.SuspendLayout()
        CType(Me.Dgv, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChartTopCustomers, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChartTopCustomers1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Label18
        '
        Me.Label18.BackColor = System.Drawing.Color.DodgerBlue
        Me.Label18.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label18.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label18.Font = New System.Drawing.Font("Arial", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.Yellow
        Me.Label18.Location = New System.Drawing.Point(0, 0)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(1007, 47)
        Me.Label18.TabIndex = 1156
        Me.Label18.Text = "افضل زبون"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.Yellow
        Me.Panel3.Controls.Add(Me.ChartTopCustomers1)
        Me.Panel3.Controls.Add(Me.ChartTopCustomers)
        Me.Panel3.Controls.Add(Me.ComboBoxCustomers)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel3.Location = New System.Drawing.Point(0, 47)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(2)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1007, 366)
        Me.Panel3.TabIndex = 1157
        '
        'Dgv
        '
        Me.Dgv.AllowUserToAddRows = False
        Me.Dgv.AllowUserToDeleteRows = False
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(156, Byte), Integer), CType(CType(252, Byte), Integer))
        DataGridViewCellStyle1.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.Dgv.ColumnHeadersHeight = 32
        Me.Dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(192, Byte), Integer))
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv.DefaultCellStyle = DataGridViewCellStyle2
        Me.Dgv.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Dgv.EnableHeadersVisualStyles = False
        Me.Dgv.Location = New System.Drawing.Point(0, 413)
        Me.Dgv.Name = "Dgv"
        Me.Dgv.ReadOnly = True
        Me.Dgv.RowHeadersVisible = False
        Me.Dgv.RowHeadersWidth = 51
        Me.Dgv.RowTemplate.Height = 30
        Me.Dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv.Size = New System.Drawing.Size(1007, 145)
        Me.Dgv.TabIndex = 1158
        '
        'ComboBoxCustomers
        '
        Me.ComboBoxCustomers.FormattingEnabled = True
        Me.ComboBoxCustomers.Location = New System.Drawing.Point(12, 14)
        Me.ComboBoxCustomers.Name = "ComboBoxCustomers"
        Me.ComboBoxCustomers.Size = New System.Drawing.Size(153, 21)
        Me.ComboBoxCustomers.TabIndex = 0
        '
        'ChartTopCustomers
        '
        ChartArea2.Name = "ChartArea1"
        Me.ChartTopCustomers.ChartAreas.Add(ChartArea2)
        Legend2.Name = "Legend1"
        Me.ChartTopCustomers.Legends.Add(Legend2)
        Me.ChartTopCustomers.Location = New System.Drawing.Point(603, 14)
        Me.ChartTopCustomers.Name = "ChartTopCustomers"
        Series2.ChartArea = "ChartArea1"
        Series2.Legend = "Legend1"
        Series2.Name = "Series1"
        Me.ChartTopCustomers.Series.Add(Series2)
        Me.ChartTopCustomers.Size = New System.Drawing.Size(392, 346)
        Me.ChartTopCustomers.TabIndex = 1
        Me.ChartTopCustomers.Text = " "
        '
        'ChartTopCustomers1
        '
        ChartArea1.Name = "ChartArea1"
        Me.ChartTopCustomers1.ChartAreas.Add(ChartArea1)
        Legend1.Name = "Legend1"
        Me.ChartTopCustomers1.Legends.Add(Legend1)
        Me.ChartTopCustomers1.Location = New System.Drawing.Point(171, 14)
        Me.ChartTopCustomers1.Name = "ChartTopCustomers1"
        Series1.ChartArea = "ChartArea1"
        Series1.Legend = "Legend1"
        Series1.Name = "Series1"
        Me.ChartTopCustomers1.Series.Add(Series1)
        Me.ChartTopCustomers1.Size = New System.Drawing.Size(426, 349)
        Me.ChartTopCustomers1.TabIndex = 2
        Me.ChartTopCustomers1.Text = "Chart1"
        '
        'frm_bestCust
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1007, 558)
        Me.Controls.Add(Me.Dgv)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Label18)
        Me.Name = "frm_bestCust"
        Me.Text = "frm_bestCust"
        Me.Panel3.ResumeLayout(False)
        CType(Me.Dgv, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChartTopCustomers, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChartTopCustomers1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents Label18 As Label
    Friend WithEvents Panel3 As Panel
    Friend WithEvents Dgv As DataGridView
    Friend WithEvents ComboBoxCustomers As ComboBox
    Friend WithEvents ChartTopCustomers As DataVisualization.Charting.Chart
    Friend WithEvents ChartTopCustomers1 As DataVisualization.Charting.Chart
End Class
