﻿Imports System.Data.SqlClient

Public Class frm_bestCust
    Dim connex As New CLS_CON
    Private Sub frm_bestCust_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadTopCustomers()
    End Sub
    Sub LoadTopCustomers()
        Dim dt As New DataTable

        ' تأكد من أن الاتصال مفتوح قبل الاستخدام
        If connex.Con.State = ConnectionState.Open Then
            connex.Con.Close()
        End If
        connex.Con.Open()

        Using cmd As New SqlCommand("
        SELECT 
            o.Table_Name AS CustomerName,
            SUM(o.ord_Total) AS TotalSales
        FROM 
            Order_Tbl o
        INNER JOIN 
            tblCustomers c ON o.Table_Name = c.CustomerName
        GROUP BY 
            o.Table_Name
        ORDER BY 
            TotalSales DESC  ", connex.Con)
            dt.Load(cmd.ExecuteReader())
        End Using
        connex.Con.Close()
        ' تحميل الداتا في الجريد
        Dgv.DataSource = dt
        ' تغيير أسماء الأعمدة للعربي
        Dgv.Columns("CustomerName").HeaderText = "اسم العميل"
        Dgv.Columns("TotalSales").HeaderText = "إجمالي المبيعات"
        Dgv.Columns("CustomerName").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
        Dgv.Columns("TotalSales").Width = 150
        ChartTopCustomers.Series(0).Points.Clear()
        ChartTopCustomers1.Series(0).Points.Clear()

        Dim colors() As Color = {Color.Red, Color.Blue, Color.Green, Color.Orange, Color.Purple, Color.Yellow}
        Dim i As Integer = 0

        For Each row As DataRow In dt.Rows
            Dim customerName As String = row("CustomerName").ToString()
            Dim totalSales As Decimal = Convert.ToDecimal(row("TotalSales"))
            Dim color As Color = colors(i Mod colors.Length)

            ' الشارت العمودي
            Dim index1 As Integer = ChartTopCustomers.Series(0).Points.AddXY(customerName, totalSales)
            ChartTopCustomers.Series(0).Points(index1).Color = color

            ' الشارت الدائري
            Dim index2 As Integer = ChartTopCustomers1.Series(0).Points.AddXY(customerName, totalSales)
            ChartTopCustomers1.Series(0).Points(index2).Color = color

            i += 1
        Next

        ChartTopCustomers1.Series(0).ChartType = DataVisualization.Charting.SeriesChartType.Pie

        ' تحميل الداتا في الرسم البياني
        'ChartTopCustomers.Series(0).Points.Clear()
        'Dim colors() As Color = {Color.Red, Color.Blue, Color.Green, Color.Orange, Color.Purple, Color.Yellow}
        'Dim i As Integer = 0

        'For Each row As DataRow In dt.Rows
        '    Dim pntIndex As Integer = ChartTopCustomers.Series(0).Points.AddXY(row("CustomerName"), row("TotalSales"))
        '    ChartTopCustomers.Series(0).Points(pntIndex).Color = colors(i Mod colors.Length)
        '    i += 1

        '    ' الرسم البياني الدائري
        '    ChartTopCustomers1.Series(0).Points.AddXY(row("CustomerName"), row("TotalSales"))
        '    ChartTopCustomers1.Series(0).ChartType = DataVisualization.Charting.SeriesChartType.Pie
        'Next
        '' تحميل العملاء في الكومبو بوكس
        ComboBoxCustomers.DataSource = dt
        ComboBoxCustomers.DisplayMember = "CustomerName"
        ComboBoxCustomers.ValueMember = "CustomerName"
    End Sub

End Class