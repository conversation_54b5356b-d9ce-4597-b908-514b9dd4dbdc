﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FRM_HOME
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FRM_HOME))
        Me.RibbonClientPanel1 = New DevComponents.DotNetBar.Ribbon.RibbonClientPanel()
        Me.RibbonClientPanel2 = New DevComponents.DotNetBar.Ribbon.RibbonClientPanel()
        Me.ButtonX8 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX7 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX6 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX5 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX4 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX3 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX2 = New DevComponents.DotNetBar.ButtonX()
        Me.ButtonX1 = New DevComponents.DotNetBar.ButtonX()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MainPanel = New System.Windows.Forms.Panel()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.ButtonX9 = New DevComponents.DotNetBar.ButtonX()
        Me.RibbonClientPanel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'RibbonClientPanel1
        '
        Me.RibbonClientPanel1.CanvasColor = System.Drawing.SystemColors.Control
        Me.RibbonClientPanel1.ColorSchemeStyle = DevComponents.DotNetBar.eDotNetBarStyle.Office2007
        Me.RibbonClientPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.RibbonClientPanel1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonClientPanel1.Name = "RibbonClientPanel1"
        Me.RibbonClientPanel1.Size = New System.Drawing.Size(1664, 70)
        '
        '
        '
        Me.RibbonClientPanel1.Style.Class = "RibbonClientPanel"
        Me.RibbonClientPanel1.TabIndex = 0
        Me.RibbonClientPanel1.Text = "RibbonClientPanel1"
        '
        'RibbonClientPanel2
        '
        Me.RibbonClientPanel2.CanvasColor = System.Drawing.Color.Yellow
        Me.RibbonClientPanel2.ColorSchemeStyle = DevComponents.DotNetBar.eDotNetBarStyle.OfficeXP
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX9)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX8)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX7)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX6)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX5)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX4)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX3)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX2)
        Me.RibbonClientPanel2.Controls.Add(Me.ButtonX1)
        Me.RibbonClientPanel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.RibbonClientPanel2.Location = New System.Drawing.Point(0, 943)
        Me.RibbonClientPanel2.Name = "RibbonClientPanel2"
        Me.RibbonClientPanel2.Size = New System.Drawing.Size(1664, 68)
        '
        '
        '
        Me.RibbonClientPanel2.Style.BackColor = System.Drawing.Color.Black
        Me.RibbonClientPanel2.Style.Class = "RibbonClientPanel"
        Me.RibbonClientPanel2.TabIndex = 1
        Me.RibbonClientPanel2.Text = "RibbonClientPanel2"
        '
        'ButtonX8
        '
        Me.ButtonX8.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX8.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX8.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX8.ForeColor = System.Drawing.Color.Red
        Me.ButtonX8.Location = New System.Drawing.Point(426, 0)
        Me.ButtonX8.Name = "ButtonX8"
        Me.ButtonX8.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX8.TabIndex = 9
        Me.ButtonX8.Text = "التقارير المالية"
        '
        'ButtonX7
        '
        Me.ButtonX7.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX7.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX7.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX7.ForeColor = System.Drawing.Color.Red
        Me.ButtonX7.Location = New System.Drawing.Point(580, 0)
        Me.ButtonX7.Name = "ButtonX7"
        Me.ButtonX7.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX7.TabIndex = 8
        Me.ButtonX7.Text = "دفعات الإيجار"
        '
        'ButtonX6
        '
        Me.ButtonX6.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX6.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX6.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX6.ForeColor = System.Drawing.Color.Red
        Me.ButtonX6.Location = New System.Drawing.Point(734, 0)
        Me.ButtonX6.Name = "ButtonX6"
        Me.ButtonX6.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX6.TabIndex = 7
        Me.ButtonX6.Text = "إدارة المصروفات"
        Me.ButtonX6.Visible = True
        '
        'ButtonX5
        '
        Me.ButtonX5.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX5.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX5.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX5.ForeColor = System.Drawing.Color.SaddleBrown
        Me.ButtonX5.Image = Global.Basseet_SYS.My.Resources.Resources.Settings_5_icon1
        Me.ButtonX5.Location = New System.Drawing.Point(888, 0)
        Me.ButtonX5.Name = "ButtonX5"
        Me.ButtonX5.Size = New System.Drawing.Size(160, 68)
        Me.ButtonX5.TabIndex = 6
        Me.ButtonX5.Text = "الاعدادات و التسجيل"
        '
        'ButtonX4
        '
        Me.ButtonX4.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX4.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX4.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX4.ForeColor = System.Drawing.Color.Red
        Me.ButtonX4.Location = New System.Drawing.Point(1048, 0)
        Me.ButtonX4.Name = "ButtonX4"
        Me.ButtonX4.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX4.TabIndex = 5
        Me.ButtonX4.Text = "عقود الإيجار"
        '
        'ButtonX3
        '
        Me.ButtonX3.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX3.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX3.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX3.Location = New System.Drawing.Point(1202, 0)
        Me.ButtonX3.Name = "ButtonX3"
        Me.ButtonX3.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX3.TabIndex = 4
        Me.ButtonX3.Text = "إدارة المستأجرين"
        '
        'ButtonX2
        '
        Me.ButtonX2.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX2.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX2.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX2.Location = New System.Drawing.Point(1356, 0)
        Me.ButtonX2.Name = "ButtonX2"
        Me.ButtonX2.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX2.TabIndex = 3
        Me.ButtonX2.Text = "إدارة الوحدات"
        '
        'ButtonX1
        '
        Me.ButtonX1.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX1.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX1.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX1.Location = New System.Drawing.Point(1510, 0)
        Me.ButtonX1.Name = "ButtonX1"
        Me.ButtonX1.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX1.TabIndex = 2
        Me.ButtonX1.Text = "إدارة العمارات"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.SystemColors.ActiveCaption
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel1.Location = New System.Drawing.Point(1529, 70)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(135, 873)
        Me.Panel1.TabIndex = 2
        '
        'MainPanel
        '
        Me.MainPanel.BackColor = System.Drawing.Color.White
        Me.MainPanel.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MainPanel.Location = New System.Drawing.Point(0, 70)
        Me.MainPanel.Name = "MainPanel"
        Me.MainPanel.Size = New System.Drawing.Size(1529, 873)
        Me.MainPanel.TabIndex = 5
        '
        'ButtonX9
        '
        Me.ButtonX9.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton
        Me.ButtonX9.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground
        Me.ButtonX9.Dock = System.Windows.Forms.DockStyle.Right
        Me.ButtonX9.ForeColor = System.Drawing.Color.Red
        Me.ButtonX9.Location = New System.Drawing.Point(272, 0)
        Me.ButtonX9.Name = "ButtonX9"
        Me.ButtonX9.Size = New System.Drawing.Size(154, 68)
        Me.ButtonX9.TabIndex = 10
        Me.ButtonX9.Text = "افضل الزبائن"
        '
        'FRM_HOME
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9!, 18!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1664, 1011)
        Me.Controls.Add(Me.MainPanel)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.RibbonClientPanel2)
        Me.Controls.Add(Me.RibbonClientPanel1)
        Me.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "FRM_HOME"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = true
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "بسيط لادارة المطاعم و الكافيهات"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.RibbonClientPanel2.ResumeLayout(false)
        Me.ResumeLayout(false)

End Sub

    Friend WithEvents RibbonClientPanel1 As DevComponents.DotNetBar.Ribbon.RibbonClientPanel
    Friend WithEvents RibbonClientPanel2 As DevComponents.DotNetBar.Ribbon.RibbonClientPanel
    Friend WithEvents ButtonX1 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX2 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX3 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX4 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents Panel1 As Panel
    Friend WithEvents MainPanel As Panel
    Friend WithEvents ButtonX5 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX6 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX7 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents ButtonX8 As DevComponents.DotNetBar.ButtonX
    Friend WithEvents ButtonX9 As DevComponents.DotNetBar.ButtonX
End Class
