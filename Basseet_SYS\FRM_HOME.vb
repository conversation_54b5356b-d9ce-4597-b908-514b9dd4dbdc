Imports DevComponents
Imports DevComponents.DotNetBar
Public Class FRM_HOME

Private Sub FRM_HOME_Load(sender As Object, e As EventArgs) Handles MyBase.Load
     
' إنشاء كائن من الكلاس
    Dim formatter As New CLS_FORMAT()

        ' استدعاء الدالة وتمرير الأزرار والـ RibbonClientPanel
        Dim buttons() As ButtonX = {ButtonX5, ButtonX1, ButtonX2, ButtonX3, ButtonX4, ButtonX5, ButtonX6, ButtonX7, ButtonX8} ' ضع هنا أزرارك
        formatter.FORMATE_BUTT(buttons, RibbonClientPanel2.Width)
End Sub

    Private Sub ButtonX1_Click(sender As Object, e As EventArgs) Handles ButtonX1.Click
        ' إدارة العمارات والمباني
        Panel1.Width = 135
        SendFormToPanel(New FRM_DEP_CAT)
    End Sub

Private Sub ButtonX2_Click(sender As Object, e As EventArgs) Handles ButtonX2.Click
        ' إدارة الوحدات السكنية (الشقق)
        Panel1.Width = 135
        SendFormToPanel(New frm_manage_units)
End Sub
     Public Sub SendFormToPanel(ByVal Sform As Object)

        If MainPanel.Controls.Count > 0 Then MainPanel.Controls.RemoveAt(0)
        Dim frm As Form = TryCast(Sform, Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        MainPanel.Controls.Add(frm)
        MainPanel.Tag = frm
        frm.Show()

    End Sub

    Private Sub ButtonX3_Click(sender As Object, e As EventArgs) Handles ButtonX3.Click
        ' إدارة المستأجرين والعملاء
        Panel1.Width = 135
        SendFormToPanel(New frmCustomers)
    End Sub

    Private Sub ButtonX4_Click(sender As Object, e As EventArgs) Handles ButtonX4.Click
        ' عقود الإيجار
        Panel1.Width = 135
        SendFormToPanel(New frm_rental_contracts)
    End Sub

    Private Sub ButtonX5_Click(sender As Object, e As EventArgs) Handles ButtonX5.Click
        Panel1.Width = 135
        SendFormToPanel(New frmSettings)
    End Sub

    Private Sub ButtonX6_Click(sender As Object, e As EventArgs) Handles ButtonX6.Click
        ' إدارة المصروفات والنفقات
        Panel1.Width = 135
        SendFormToPanel(New frm_expenses)
    End Sub

    Private Sub ButtonX7_Click(sender As Object, e As EventArgs) Handles ButtonX7.Click
        ' دفعات الإيجار والمدفوعات
        Panel1.Width = 600
        SendFormToPanel(New frm_rent_payments)
    End Sub

    Private Sub ButtonX1_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX1.MouseMove
        ToolTip1.SetToolTip(ButtonX1, "إدارة العمارات والمباني")
    End Sub

    Private Sub ButtonX2_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX2.MouseMove
        ToolTip1.SetToolTip(ButtonX2, "إدارة الوحدات السكنية والشقق")
    End Sub

    Private Sub ButtonX3_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX3.MouseMove
        ToolTip1.SetToolTip(ButtonX3, "إدارة المستأجرين والعملاء")
    End Sub

    Private Sub ButtonX4_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX4.MouseMove
        ToolTip1.SetToolTip(ButtonX4, "إدارة عقود الإيجار")
    End Sub

    Private Sub ButtonX5_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX5.MouseMove
        ToolTip1.SetToolTip(ButtonX5, "الإعدادات العامة للنظام")
    End Sub

    Private Sub ButtonX6_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX6.MouseMove
        ToolTip1.SetToolTip(ButtonX6, "إدارة المصروفات والنفقات")
    End Sub

    Private Sub ButtonX7_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX7.MouseMove
        ToolTip1.SetToolTip(ButtonX7, "دفعات الإيجار والمدفوعات")
    End Sub

    Private Sub ButtonX8_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX8.MouseMove
        ToolTip1.SetToolTip(ButtonX8, "التقارير المالية والإحصائيات")
    End Sub

    Private Sub ButtonX8_Click(sender As Object, e As EventArgs) Handles ButtonX8.Click
        ' التقارير المالية والإحصائيات
        Panel1.Width = 400
        SendFormToPanel(New frm_financial_reports)
    End Sub

    Private Sub ButtonX9_Click(sender As Object, e As EventArgs) Handles ButtonX9.Click
        Panel1.Width = 400
        SendFormToPanel(New frm_bestCust)
    End Sub
End Class
