﻿Imports DevComponents
Imports DevComponents.DotNetBar
Public Class FRM_HOME

Private Sub FRM_HOME_Load(sender As Object, e As EventArgs) Handles MyBase.Load
     
' إنشاء كائن من الكلاس
    Dim formatter As New CLS_FORMAT()

        ' استدعاء الدالة وتمرير الأزرار والـ RibbonClientPanel
        Dim buttons() As ButtonX = {ButtonX5, ButtonX1, ButtonX2, ButtonX3, ButtonX4, ButtonX5, ButtonX6, ButtonX7, ButtonX8} ' ضع هنا أزرارك
        formatter.FORMATE_BUTT(buttons, RibbonClientPanel2.Width)
End Sub

    Private Sub ButtonX1_Click(sender As Object, e As EventArgs) Handles ButtonX1.Click
        '    Dim frm As New FRM_DEP_CAT
        '    frm.TopLevel = False
        '    MAINPanel.Controls.Add(frm)
        '        ' ضبط الموقع ليكون في منتصف الـ Panel
        'frm.Left = (MAINPanel.Width - frm.Width) \ 2
        'frm.Top = (MAINPanel.Height - frm.Height) \ 2
        '    frm.BringToFront()
        '    frm.Show()
        Panel1.Width = 135
        SendFormToPanel(New FRM_DEP_CAT)
    End Sub

Private Sub ButtonX2_Click(sender As Object, e As EventArgs) Handles ButtonX2.Click
        'Dim frm As New frm_manage_product
        'frm.TopLevel = False
        'MAINPanel.Controls.Add(frm)

        '' ضبط الموقع ليكون في منتصف الـ Panel
        'frm.Left = (MAINPanel.Width - frm.Width) \ 2
        'frm.Top = (MAINPanel.Height - frm.Height) \ 2

        'frm.BringToFront()
        'frm.Show()
        Panel1.Width = 135
        SendFormToPanel(New frm_manage_product)
End Sub
     Public Sub SendFormToPanel(ByVal Sform As Object)

        If MainPanel.Controls.Count > 0 Then MainPanel.Controls.RemoveAt(0)
        Dim frm As Form = TryCast(Sform, Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        MainPanel.Controls.Add(frm)
        MainPanel.Tag = frm
        frm.Show()

    End Sub

    Private Sub ButtonX3_Click(sender As Object, e As EventArgs) Handles ButtonX3.Click
        Panel1.Width = 135
        SendFormToPanel(New Frm_Tabels)
    End Sub

    Private Sub ButtonX4_Click(sender As Object, e As EventArgs) Handles ButtonX4.Click
        Panel1.Width = 137
        Frm_pos.Show()
    End Sub

    Private Sub ButtonX5_Click(sender As Object, e As EventArgs) Handles ButtonX5.Click
        Panel1.Width = 135
        SendFormToPanel(New frmSettings)
    End Sub

    Private Sub ButtonX6_Click(sender As Object, e As EventArgs) Handles ButtonX6.Click
        Panel1.Width = 135
        SendFormToPanel(New frmBuyOrder)
    End Sub

    Private Sub ButtonX7_Click(sender As Object, e As EventArgs) Handles ButtonX7.Click
        Panel1.Width = 600
        SendFormToPanel(New Frm_Mange_Sales)
    End Sub

    Private Sub ButtonX1_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX1.MouseMove
        ToolTip1.SetToolTip(ButtonX1, "تسجيل الاقسام و اختيار البرنتر")
    End Sub
    Private Sub ButtonX5_MouseMove(sender As Object, e As MouseEventArgs) Handles ButtonX5.MouseMove
        ToolTip1.SetToolTip(ButtonX5, "اعدادات و تسجيل عملاء و موردين و خدمة  توصيل و مناطق")
    End Sub

    Private Sub ButtonX8_Click(sender As Object, e As EventArgs) Handles ButtonX8.Click
        Panel1.Width = 400
        SendFormToPanel(New FRM_SALES_BINFIT)
    End Sub

    Private Sub ButtonX9_Click(sender As Object, e As EventArgs) Handles ButtonX9.Click
        Panel1.Width = 400
        SendFormToPanel(New frm_bestCust)
    End Sub
End Class
