﻿Imports System.Data.SqlClient
Imports System.IO

Public Class CLS_CON

    Private SqlCon As SqlConnection

    ' تعريف متغير الاتصال العامة
    Public ReadOnly Property Con As SqlConnection

    ' Constructor
    Public Sub New()
        ' قراءة سلسلة الاتصال من الملف وتخزينها في SqlCon
        SqlCon = New SqlConnection(File.ReadAllText("d:\Basseet_SYS -ok\design\new"))

        ' تهيئة متغير الاتصال العامة
        Con = SqlCon
    End Sub
        Private ds As New DataSet
    Private da As New SqlDataAdapter
    Public cmd As New SqlCommand
    Private dv As New DataView
    Public cur As CurrencyManager
    Private sdv As New DataView
    Public scur As CurrencyManager
    Private ddv As New DataView
    Public dcur As CurrencyManager
    Public frm As New Form
    Private TRANC As SqlTransaction
    Public rdr As SqlDataReader
    Public adp As SqlDataAdapter
    Public dt As DataTable
    Public _ID As Integer = 0
    Public _PrdName As String = ""
    Public _SalePrice As Double = 0.0
    ' Public _Tax_Value As Double = 0.0
    '***************************************************

    '***************************************************

    ' Public _vat_No As String = ""
    Public Sub Get_Tax_Value()
        If SqlCon.State = 1 Then SqlCon.Close()
        SqlCon.Open()
        Cmd = New SqlCommand("Select * from Tax_Tbl ", SqlCon)
        rdr = Cmd.ExecuteReader
        rdr.Read()
        If rdr.HasRows Then
            _Tax_Value = rdr("Tax_Value").ToString
        Else
            _Tax_Value = 0.0
        End If
        rdr.Close()
        SqlCon.Close()
    End Sub
    Public Sub Connect()
        If SqlCon.State <> ConnectionState.Open Then
            SqlCon.Open()
        End If
    End Sub
    Public Sub Disconnect()
        If SqlCon.State <> ConnectionState.Closed Then
            SqlCon.Close()
        End If
    End Sub
       Public Function SearchDataSet(SQL As String, Optional TableName As String = "0") As DataView
        ds = New DataSet
        adp = New SqlDataAdapter(SQL, SqlCon)
        adp.Fill(ds, TableName)
        sdv = New DataView(ds.Tables(TableName))
        scur = CType(frm.BindingContext(sdv), CurrencyManager)
        Return sdv
    End Function
    Public Sub FillComboBox(cbo As ComboBox, FirstCol As String, SecondCol As String, TableName As String)
        Dim SQL As String = "SELECT " & FirstCol & ", " & SecondCol & " FROM " & TableName
        cbo.DataSource = SearchDataSet(SQL, TableName)
        cbo.ValueMember = FirstCol
        cbo.DisplayMember = SecondCol
    End Sub
    Public Function GetNextID(tablename, coulmnname) As Integer
        Dim dt As New DataTable
        Dim adp As New SqlDataAdapter
        dt.Clear()
        adp = New SqlDataAdapter("select max(" & coulmnname & ")from " & tablename & "", Con)
        adp.Fill(dt)
        Dim MYID As Integer
        If IsDBNull(dt(0)(0)) = True Then
            MYID = 1
        Else
            MYID = dt(0)(0) + 1

        End If
        Return MYID
    End Function
End Class
 