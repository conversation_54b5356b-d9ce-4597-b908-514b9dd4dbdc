﻿Imports Net.Pkcs11Interop.Common
Imports System.Data.SqlClient

Module Module1
    Dim connx As New CLS_CON
    Public _finalTotal As Double = 0.0
    Public _Order_Total As Double = 0.0
    Public _TaxTotal As Double = 0.0
    Public _Tax_VALUE As Double = 0.0
    Public _Order_No As String = ""
    Public _vat_No As String = ""
    Public _CompanyName As String = ""
    Public receiver1 As Receiver = New Receiver()
    Public _Order_Type As String = ""
    Public _customer_id As Integer = 0
    Public _drliveryman_id As Integer = 0
    Public _servive_fee As Double = 0.0
    Public _cashier_id As Integer = 0
    Public _cashier_name As String
    Public _cashier_fullname As String
    Public _cashier_balance As Double = 0.0
    Public _cashier_status As Boolean
    Public _is_owner As Boolean
    Public Str_UserName, Str_UserPass, Str_FullName, Str_UserRole As String
    Public Sub LogFunc(ByVal st1 As String, ByVal st2 As String)
        Dim sdate As String = Now.ToString("yyyy-MM-dd")
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cb As String = "insert into Logs_Tbl(UserName,LogDate,Operation) VALUES (@d1,@d3,@d2)"
        Dim cmd As New SqlCommand(cb)
        cmd.Connection = connx.Con
        cmd.Parameters.AddWithValue("@d1", st1)
        cmd.Parameters.AddWithValue("@d2", st2)
        cmd.Parameters.AddWithValue("@d3", sdate)
        cmd.ExecuteNonQuery()
        connx.Con.Close()
    End Sub
End Module
