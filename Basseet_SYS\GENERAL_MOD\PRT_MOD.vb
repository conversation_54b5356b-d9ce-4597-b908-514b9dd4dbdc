﻿Imports System.Drawing.Printing



Module PrintModule
    Public Sub PrintUsingDefaultPrinter(targetPrinterName As String)
        ' الحصول على الطابعة الافتراضية الحالية
        Dim originalPrinterName As String = New PrinterSettings().PrinterName

        Try
            ' تعيين الطابعة المستهدفة كطابعة افتراضية
            For Each printer As String In PrinterSettings.InstalledPrinters
                If printer = targetPrinterName Then
                    Dim printDocument As New PrintDocument()
                    printDocument.PrinterSettings.PrinterName = printer

                    ' تنفيذ الطباعة
                    printDocument.Print()
                    Exit For
                End If
            Next
        Catch ex As Exception
            Console.WriteLine("حدث خطأ أثناء الطباعة: " & ex.Message)
        Finally
            ' إعادة تعيين الطابعة الافتراضية
            Dim resetPrinter As New PrintDocument()
            resetPrinter.PrinterSettings.PrinterName = originalPrinterName
        End Try
    End Sub
End Module
