﻿Imports QRCoder
Imports System.IO
Imports System.Text
Module Qr_Mod
    Dim decValue As Integer = 0
    Public Function StrToHex(ByRef Datastring As String) As String
        Dim name As String = Datastring
        Dim utf8 As New UTF8Encoding()
        Dim encodedBytes As Byte() = utf8.GetBytes(name)
        Dim decodedString As String = utf8.GetString(encodedBytes)
        Dim sVal As String = ""
        Dim sHex As String = ""
        While decodedString.Length > 0
            Dim mcar As String = decodedString.Substring(0, 1).ToString()
            decValue = decValue + ToByteArray(mcar).Length
            sVal = ToHex(ToByteArray(mcar))
            decodedString = decodedString.Substring(1, decodedString.Length - 1)
            sHex = sHex & sVal
        End While
        Return sHex
    End Function
    Public Function convertTohex(ByVal mtag As String, mtxt As String) As String
        Dim mlen As String = ""
        decValue = 0
        Dim mvalue As String = StrToHex(mtxt)
        mlen = Hex(decValue)
        If Len(mlen) = 1 Then
            mlen = "0" & mlen
        End If
        Dim mall = mtag & mlen & mvalue
        Return mall
    End Function

    Public Function HexToBase64(ByVal strInput As String) As String
        Try
            Dim bytes = New Byte((strInput.Length \ 2) - 1) {}
            For i = 0 To bytes.Length - 1
                Dim mi = strInput.Substring(i * 2, 2)
                bytes(i) = Convert.ToByte(mi, 16)
            Next i
            Return Convert.ToBase64String(bytes)
        Catch e1 As Exception
            Return "-1"
        End Try
    End Function
    Public Function Base64ToHex(ByVal strInput As String) As String
        Try
            Dim base64Decoded As String
            Dim data() As Byte
            data = System.Convert.FromBase64String(strInput)
            Dim mhex As String = BitConverter.ToString(data)
            mhex = mhex.Replace("-", "")
            base64Decoded = System.Text.Encoding.UTF8.GetString(data)
            Frm_pos.txtBase64ToHex.Text = base64Decoded
            Return base64Decoded
        Catch e1 As Exception
            Return "-1"
        End Try
    End Function
    Public Function ToHex(ByVal data() As Byte) As String
        Dim ret As New StringBuilder(String.Empty)
        For Each Value As Byte In data
            ret.Append(Value.ToString("x2"))
        Next Value
        Return ret.ToString()
    End Function

    Public Function ToByteArray(ByVal data As String) As Byte()
        Dim vOut() As Byte = Encoding.UTF8.GetBytes(data)
        Return vOut
    End Function

    Public Sub Get_QrCode()
        Frm_pos.QrCode_Pic.Image = Nothing
        If Frm_pos.txtHexToBase64.Text <> "" Then
            Dim gen As New QRCodeGenerator
            Dim data = gen.CreateQrCode(Frm_pos.txtHexToBase64.Text, QRCodeGenerator.ECCLevel.Q)
            Dim code As New QRCode(data)
            Frm_pos.QrCode_Pic.Image = code.GetGraphic(20)
        End If
    End Sub

End Module
