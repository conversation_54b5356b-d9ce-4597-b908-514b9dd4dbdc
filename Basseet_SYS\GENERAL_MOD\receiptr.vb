﻿Module ReceiptR
    Public Class Header
        Public Property dateTimeIssued As String
        Public Property receiptNumber As String
        Public Property uuid As String
        Public Property previousUUID As String
        Public Property referenceOldUUID As String
        Public Property currency As String
        Public Property exchangeRate As Double
        Public Property sOrderNameCode As String
        Public Property orderdeliveryMode As String
        Public Property grossWeight As Double
        Public Property netWeight As Double
    End Class

    Public Class DocumentType
        Public Property receiptType As String
        Public Property typeVersion As String
    End Class

    Public Class BranchAddress
        Public Property country As String
        Public Property governate As String
        Public Property regionCity As String
        Public Property street As String
        Public Property buildingNumber As String
        Public Property postalCode As String
        Public Property floor As String
        Public Property room As String
        Public Property landmark As String
        Public Property additionalInformation As String
    End Class

    Public Class Seller
        Public Property rin As String
        Public Property companyTradeName As String
        Public Property branchCode As String
        Public Property branchAddress As BranchAddress
        Public Property deviceSerialNumber As String
        Public Property syndicateLicenseNumber As String
        Public Property activityCode As String
    End Class

    Public Class Buyer
        Public Property type As String
        Public Property id As String
        Public Property name As String
        Public Property mobileNumber As String
        Public Property paymentNumber As String
    End Class

    Public Class CommercialDiscountData
        'Public Property amount As Double
        'Public Property description As String
    End Class

    Public Class ItemDiscountData
        'Public Property amount As Double
        'Public Property description As String
    End Class

    Public Class TaxableItem
        Public Property taxType As String
        Public Property amount As Double
        Public Property subType As String
        Public Property rate As Double
    End Class

    Public Class ItemData
        Public Property internalCode As String
        Public Property description As String
        Public Property itemType As String
        Public Property itemCode As String
        Public Property unitType As String
        Public Property quantity As Double
        Public Property unitPrice As Double
        Public Property netSale As Double
        Public Property totalSale As Double
        Public Property total As Double
        Public Property commercialDiscountData As List(Of CommercialDiscountData)
        Public Property itemDiscountData As List(Of ItemDiscountData)
        Public Property valueDifference As Double
        Public Property taxableItems As List(Of TaxableItem)
    End Class

    Public Class ExtraReceiptDiscountData
        'Public Property amount As Double
        'Public Property description As String
    End Class

    Public Class TaxTotal
        Public Property taxType As String
        Public Property amount As Double
    End Class

    Public Class Contractor
        Public Property name As String
        Public Property amount As Double
        Public Property rate As Double
    End Class

    Public Class Beneficiary
        Public Property amount As Double
        Public Property rate As Double
    End Class

    Public Class Receipt
        Public Property header As Header
        Public Property documentType As DocumentType
        Public Property seller As Seller
        Public Property buyer As Buyer
        Public Property itemData As List(Of ItemData)
        Public Property totalSales As Double
        Public Property totalCommercialDiscount As Double
        Public Property totalItemsDiscount As Double
        Public Property extraReceiptDiscountData As List(Of ExtraReceiptDiscountData)
        Public Property netAmount As Double
        Public Property feesAmount As Double
        Public Property totalAmount As Double
        Public Property taxTotals As List(Of TaxTotal)
        Public Property paymentMethod As String
        Public Property adjustment As Double
        Public Property contractor As Contractor
        Public Property beneficiary As Beneficiary
    End Class

    Public Class Receiptlist
        Public Property receipts As List(Of Receipt) = New List(Of Receipt)()
        Public Property StringBuilder As String
    End Class
End Module
