﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="PrinterName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Tax_Status" Type="System.String" Scope="User">
      <Value Profile="(Default)">true</Value>
    </Setting>
    <Setting Name="SalePrint" Type="System.String" Scope="User">
      <Value Profile="(Default)">8CM</Value>
    </Setting>
    <Setting Name="smart_rest1ConnectionString" Type="(Connection string)" Scope="Application">
      <DesignTimeValue Profile="(Default)">&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;SerializableConnectionString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;
  &lt;ConnectionString&gt;Data Source=DESKTOP-QI6H2EA;Initial Catalog=smart_rest1;Integrated Security=True;Encrypt=False&lt;/ConnectionString&gt;
  &lt;ProviderName&gt;System.Data.SqlClient&lt;/ProviderName&gt;
&lt;/SerializableConnectionString&gt;</DesignTimeValue>
      <Value Profile="(Default)">Data Source=DESKTOP-QI6H2EA;Initial Catalog=smart_rest1;Integrated Security=True;Encrypt=False</Value>
    </Setting>
    <Setting Name="size" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="previousUUID" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="activitycode" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="name1" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RegistrationNumber" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="serialDevice" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>