﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Runtime.Remoting.Contexts

Public Class Frm_Add_Update_Prod
    DIM CONNX1 As New CLS_CON 

'\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
'                                                                                       \
' Sub: Button1_Click                                                                     \
'                                                                                        \
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub
'                                                                                        /
' End Sub: Button1_Click                                                                 /
'/////////////////////////////////////////////////////////////////////////////////////////

'\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
'                                                                                       \
' Sub: ClearItems                                                                        \
'                                                                                        \
    Public Sub ClearItems()
        TxtPrdName.Text = ""
        TxtPrd_Barcode.Text = ""
        TxtPrdPrice.Text = ""
        txtCost.Text = ""
        Cmb_Cat.Text = ""
        TxtPrd_Barcode.Focus()
        Prd_Image.Image = My.Resources._18_06_2022_08_16_59_م
        BtnSave.Enabled = True
        BtnEdit.Enabled = False
    End Sub
    '                                                                                        /
    ' End Sub: ClearItems                                                                    /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: BtnBrowse_Click                                                                   \
    '                                                                                        \
    Private Sub BtnBrowse_Click(sender As Object, e As EventArgs) Handles BtnBrowse.Click
        Try
            With OpenFileDialog1
                .Filter = ("Images |*.png; *.bmp; *.jpg;*.jpeg; *.gif;")
                .FilterIndex = 4
            End With
            OpenFileDialog1.FileName = ""
            If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
                Prd_Image.Image = Image.FromFile(OpenFileDialog1.FileName)
            End If
        Catch ex As Exception
            MsgBox(ex.ToString())
        End Try
    End Sub
    '                                                                                        /
    ' End Sub: BtnBrowse_Click                                                               /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: Button2_Click                                                                     \
    '                                                                                        \
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Prd_Image.Image = My.Resources._18_06_2022_08_16_59_م
    End Sub
    '                                                                                        /
    ' End Sub: Button2_Click                                                                 /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: Load_Cat_Tbl                                                                      \
    '                                                                                        \
    Public Sub Load_Cat_Tbl()
        CONNX1.FillComboBox(Cmb_Cat, "cat_id", "catName", "cat_tbl")
    End Sub
    '                                                                                        /
    ' End Sub: Load_Cat_Tbl                                                                  /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: Frm_Add_Update_Prod_Load                                                          \
    '                                                                                        \
    Private Sub Frm_Add_Update_Prod_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Load_Cat_Tbl()
    End Sub
    '                                                                                        /
    ' End Sub: Frm_Add_Update_Prod_Load                                                      /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: BtnSave_Click                                                                     \
    '                                                                                        \
    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        ' التحقق من أن جميع الحقول ممتلئة
        If TxtPrd_Barcode.Text = vbNullString Or TxtPrdName.Text = vbNullString Or Cmb_Cat.Text = vbNullString Or TxtPrdPrice.Text = vbNullString Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If

        ' فتح الاتصال بقاعدة البيانات
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()

        ' التحقق من وجود الباركود أو اسم الصنف مسبقاً
        CONNX1.cmd = New SqlCommand("SELECT * FROM Item_Tbl WHERE Itembarcode = @Itembarcode OR Itemname = @Itemname", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@Itembarcode", TxtPrd_Barcode.Text)
        CONNX1.cmd.Parameters.AddWithValue("@Itemname", TxtPrdName.Text)
        CONNX1.rdr = CONNX1.cmd.ExecuteReader
        CONNX1.rdr.Read()

        ' إذا كانت البيانات موجودة مسبقاً
        If CONNX1.rdr.HasRows Then
            MsgBox("الباركود أو اسم الصنف المراد إدخاله مدخل مسبقاً")
            Exit Sub
            TxtPrd_Barcode.Text = ""
            TxtPrd_Barcode.Focus()
        Else
            ' إدخال بيانات المنتج الجديد إذا لم يكن موجوداً مسبقاً
            Insert_Prd_Tbl(TxtPrd_Barcode.Text, TxtPrdName.Text, Cmb_Cat.SelectedValue, txtCost.Text, TxtPrdPrice.Text, CheckStatus.CheckState, Prd_Image)
            ClearItems()
        End If

        ' إغلاق القارئ والاتصال
        CONNX1.rdr.Close()
        CONNX1.Con.Close()

        ' تحديث البيانات في واجهة إدارة المنتجات
        With frm_manage_product
            ' .Load_Prd()  ' يمكن تفعيل هذه السطر إذا كنت تريد إعادة تحميل المنتجات
        End With
    End Sub

    '                                                                                        /
    ' End Sub: BtnSave_Click                                                                 /
    '/////////////////////////////////////////////////////////////////////////////////////////

    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    '                                                                                       \
    ' Sub: Insert_Prd_Tbl                                                                    \
    '                                                                                        \
    Public Sub Insert_Prd_Tbl(ByVal Prd_Barcode As String, ByVal PrdName As String, ByVal Cat_ID As Int32, ByVal Prdcost As Double, ByVal PrdPrice As Double, ByVal prd_Status As Boolean, ByVal Prd_Image As PictureBox)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX1.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into Item_Tbl (Itembarcode,ItemName,Cat_ID,cost_price,Item_Price,Item_Status,Item_Image) values (@Itembarcode,@ItemName,@Cat_ID,@cost_price,@Item_Price,@Item_Status,@Item_Image)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Itembarcode", SqlDbType.VarChar).Value = Prd_Barcode
            .Parameters.AddWithValue("@ItemName", SqlDbType.VarChar).Value = PrdName
            .Parameters.AddWithValue("@Cat_ID", SqlDbType.Int).Value = Cat_ID
            .Parameters.AddWithValue("@cost_price", SqlDbType.Decimal).Value = Prdcost
            .Parameters.AddWithValue("@Item_Price", SqlDbType.Decimal).Value = PrdPrice
            .Parameters.AddWithValue("@Item_Status", SqlDbType.Bit).Value = prd_Status

            Dim ms As New MemoryStream()
            Dim bmpImage As New Bitmap(Prd_Image.Image)
            bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
            Dim data As Byte() = ms.GetBuffer()
            Dim p As New SqlParameter("@Item_Image", SqlDbType.Image)
            p.Value = data
            .Parameters.Add(p)
        End With

        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX1.Con.Close()

        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    '                                                                                        /
    ' End Sub: Insert_Prd_Tbl                                                                /
    '/////////////////////////////////////////////////////////////////////////////////////////
    '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
    Public Sub Update_Prd_Tbl(ByVal Prd_Barcode As String, ByVal PrdName As String, ByVal Cat_ID As Int32, ByVal Prdcost As Double, ByVal PrdPrice As Double, ByVal prd_Status As Boolean, ByVal Prd_Image As PictureBox, ByVal Prd_IDW As Int32)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX1.Con
            .CommandType = CommandType.Text


            .CommandText = "Update item_Tbl Set Itembarcode = @Itembarcode,ItemName = @ItemName,Cat_ID = @Cat_ID,cost_price = @cost_price,Item_Price = @Item_Price,Item_Status = @Item_Status,Item_Image = @Item_Image Where Item_ID = @Item_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Itembarcode", SqlDbType.VarChar).Value = Prd_Barcode
            .Parameters.AddWithValue("@ItemName", SqlDbType.VarChar).Value = PrdName
            .Parameters.AddWithValue("@Cat_ID", SqlDbType.Int).Value = Cat_ID
            .Parameters.AddWithValue("@cost_price", SqlDbType.Decimal).Value = Prdcost
            .Parameters.AddWithValue("@Item_Price", SqlDbType.Decimal).Value = PrdPrice
            .Parameters.AddWithValue("@Item_Status", SqlDbType.Bit).Value = prd_Status
            Dim ms As New MemoryStream()
            Dim bmpImage As New Bitmap(Prd_Image.Image)
            bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
            Dim data As Byte() = ms.GetBuffer()
            Dim p As New SqlParameter("@Item_Image", SqlDbType.Image)
            p.Value = data
            .Parameters.Add(p)
            .Parameters.AddWithValue("@Item_ID", SqlDbType.Int).Value = Prd_IDW
        End With
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX1.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
        Cmd = Nothing
        With frm_manage_product
            .Load_Prd()
            .BtnAllPrd.PerformClick()

        End With
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs) Handles BtnEdit.Click
        If TxtPrd_Barcode.Text = vbNullString Or TxtPrdName.Text = vbNullString Or Cmb_Cat.Text = vbNullString Or TxtPrdPrice.Text = vbNullString Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Update_Prd_Tbl(TxtPrd_Barcode.Text, TxtPrdName.Text, Cmb_Cat.SelectedValue, txtCost.Text, TxtPrdPrice.Text, CheckStatus.CheckState, Prd_Image, TxtPrd_ID.Text)
        ClearItems()
     
        With Frm_Manage_Product
            .BtnAllPrd.PerformClick()
        End With
        Me.Close
    End Sub


    '///////////////////////////////////////////////////////////////////////////////////////////
    '

End Class
