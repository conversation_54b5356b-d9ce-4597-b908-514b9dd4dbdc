Imports System.Data.SqlClient

Public Class frm_rental_contracts
    Dim connx As New CLS_CON
    Dim ContractID As Integer = 0

    Private Sub frm_rental_contracts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadContracts()
        LoadTenants()
        LoadUnits()
        ClearForm()
    End Sub

    Private Sub LoadContracts()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT * FROM View_ActiveContracts ORDER BY ContractDate DESC", connx.Con)
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)
            
            DgvContracts.DataSource = dt
            
            ' تنسيق الأعمدة
            If DgvContracts.Columns.Count > 0 Then
                DgvContracts.Columns("Contract_ID").HeaderText = "رقم العقد"
                DgvContracts.Columns("ContractNumber").HeaderText = "رقم العقد"
                DgvContracts.Columns("TenantName").HeaderText = "اسم المستأجر"
                DgvContracts.Columns("BuildingName").HeaderText = "العمارة"
                DgvContracts.Columns("UnitName").HeaderText = "الوحدة"
                DgvContracts.Columns("StartDate").HeaderText = "تاريخ البداية"
                DgvContracts.Columns("EndDate").HeaderText = "تاريخ الانتهاء"
                DgvContracts.Columns("MonthlyRent").HeaderText = "الإيجار الشهري"
                DgvContracts.Columns("ContractStatus").HeaderText = "حالة العقد"
            End If
            
            connx.Con.Close()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل العقود: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTenants()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            connx.FillComboBox(CmbTenant, "CustomerID", "CustomerName", "tblCustomers WHERE CustomerType = 'مستأجر'")
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل المستأجرين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadUnits()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            connx.FillComboBox(CmbUnit, "UnitID", "UnitName", "View_Units WHERE UnitStatus = 'فارغ'")
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الوحدات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        TxtContractNumber.Text = ""
        CmbTenant.SelectedIndex = -1
        CmbUnit.SelectedIndex = -1
        DtpStartDate.Value = DateTime.Now
        DtpEndDate.Value = DateTime.Now.AddYears(1)
        TxtMonthlyRent.Text = ""
        TxtSecurityDeposit.Text = ""
        TxtContractTerms.Text = ""
        CmbContractStatus.SelectedIndex = 0 ' نشط
        ContractID = 0
        BtnSave.Enabled = True
        BtnUpdate.Enabled = False
        BtnDelete.Enabled = False
        GenerateContractNumber()
    End Sub

    Private Sub GenerateContractNumber()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT COUNT(*) FROM RentalContracts_Tbl WHERE YEAR(ContractDate) = YEAR(GETDATE())", connx.Con)
            Dim count As Integer = cmd.ExecuteScalar() + 1
            
            TxtContractNumber.Text = DateTime.Now.Year.ToString() & count.ToString("0000")
            
            connx.Con.Close()
        Catch ex As Exception
            TxtContractNumber.Text = DateTime.Now.Year.ToString() & "0001"
        End Try
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If ValidateForm() Then
            SaveContract()
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(TxtContractNumber.Text) Then
            MessageBox.Show("يرجى إدخال رقم العقد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtContractNumber.Focus()
            Return False
        End If

        If CmbTenant.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار المستأجر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CmbTenant.Focus()
            Return False
        End If

        If CmbUnit.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار الوحدة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CmbUnit.Focus()
            Return False
        End If

        If Not IsNumeric(TxtMonthlyRent.Text) OrElse Val(TxtMonthlyRent.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال إيجار شهري صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtMonthlyRent.Focus()
            Return False
        End If

        If DtpEndDate.Value <= DtpStartDate.Value Then
            MessageBox.Show("تاريخ انتهاء العقد يجب أن يكون بعد تاريخ البداية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            DtpEndDate.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub SaveContract()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "INSERT INTO RentalContracts_Tbl (ContractNumber, Tenant_ID, Unit_ID, StartDate, EndDate, " &
                              "MonthlyRent, SecurityDeposit, ContractTerms, ContractStatus, ContractDate) " &
                              "VALUES (@ContractNumber, @Tenant_ID, @Unit_ID, @StartDate, @EndDate, " &
                              "@MonthlyRent, @SecurityDeposit, @ContractTerms, @ContractStatus, @ContractDate)"

                .Parameters.AddWithValue("@ContractNumber", TxtContractNumber.Text.Trim())
                .Parameters.AddWithValue("@Tenant_ID", CmbTenant.SelectedValue)
                .Parameters.AddWithValue("@Unit_ID", CmbUnit.SelectedValue)
                .Parameters.AddWithValue("@StartDate", DtpStartDate.Value)
                .Parameters.AddWithValue("@EndDate", DtpEndDate.Value)
                .Parameters.AddWithValue("@MonthlyRent", CDec(TxtMonthlyRent.Text))
                .Parameters.AddWithValue("@SecurityDeposit", If(String.IsNullOrWhiteSpace(TxtSecurityDeposit.Text), DBNull.Value, CDec(TxtSecurityDeposit.Text)))
                .Parameters.AddWithValue("@ContractTerms", If(String.IsNullOrWhiteSpace(TxtContractTerms.Text), DBNull.Value, TxtContractTerms.Text.Trim()))
                .Parameters.AddWithValue("@ContractStatus", CmbContractStatus.Text)
                .Parameters.AddWithValue("@ContractDate", DateTime.Now)
            End With

            cmd.ExecuteNonQuery()
            
            ' تحديث حالة الوحدة إلى مؤجر
            Dim updateCmd As New SqlCommand("UPDATE Item_Tbl SET UnitStatus = 'مؤجر' WHERE Item_ID = @Unit_ID", connx.Con)
            updateCmd.Parameters.AddWithValue("@Unit_ID", CmbUnit.SelectedValue)
            updateCmd.ExecuteNonQuery()
            
            connx.Con.Close()

            MessageBox.Show("تم حفظ العقد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadContracts()
            LoadUnits() ' إعادة تحميل الوحدات لإزالة الوحدة المؤجرة
            ClearForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في حفظ العقد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvContracts_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvContracts.CellClick
        If e.RowIndex >= 0 Then
            Try
                ContractID = DgvContracts.Rows(e.RowIndex).Cells("Contract_ID").Value
                LoadContractData(ContractID)
                
                BtnSave.Enabled = False
                BtnUpdate.Enabled = True
                BtnDelete.Enabled = True

            Catch ex As Exception
                MessageBox.Show("خطأ في تحديد العقد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub LoadContractData(contractId As Integer)
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM RentalContracts_Tbl WHERE Contract_ID = @Contract_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Contract_ID", contractId)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                TxtContractNumber.Text = reader("ContractNumber").ToString()
                CmbTenant.SelectedValue = reader("Tenant_ID")
                CmbUnit.SelectedValue = reader("Unit_ID")
                DtpStartDate.Value = reader("StartDate")
                DtpEndDate.Value = reader("EndDate")
                TxtMonthlyRent.Text = reader("MonthlyRent").ToString()
                TxtSecurityDeposit.Text = If(IsDBNull(reader("SecurityDeposit")), "", reader("SecurityDeposit").ToString())
                TxtContractTerms.Text = If(IsDBNull(reader("ContractTerms")), "", reader("ContractTerms").ToString())
                CmbContractStatus.Text = reader("ContractStatus").ToString()
            End If

            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات العقد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If ValidateForm() AndAlso ContractID > 0 Then
            UpdateContract()
        End If
    End Sub

    Private Sub UpdateContract()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "UPDATE RentalContracts_Tbl SET ContractNumber = @ContractNumber, Tenant_ID = @Tenant_ID, " &
                              "Unit_ID = @Unit_ID, StartDate = @StartDate, EndDate = @EndDate, MonthlyRent = @MonthlyRent, " &
                              "SecurityDeposit = @SecurityDeposit, ContractTerms = @ContractTerms, ContractStatus = @ContractStatus " &
                              "WHERE Contract_ID = @Contract_ID"

                .Parameters.AddWithValue("@Contract_ID", ContractID)
                .Parameters.AddWithValue("@ContractNumber", TxtContractNumber.Text.Trim())
                .Parameters.AddWithValue("@Tenant_ID", CmbTenant.SelectedValue)
                .Parameters.AddWithValue("@Unit_ID", CmbUnit.SelectedValue)
                .Parameters.AddWithValue("@StartDate", DtpStartDate.Value)
                .Parameters.AddWithValue("@EndDate", DtpEndDate.Value)
                .Parameters.AddWithValue("@MonthlyRent", CDec(TxtMonthlyRent.Text))
                .Parameters.AddWithValue("@SecurityDeposit", If(String.IsNullOrWhiteSpace(TxtSecurityDeposit.Text), DBNull.Value, CDec(TxtSecurityDeposit.Text)))
                .Parameters.AddWithValue("@ContractTerms", If(String.IsNullOrWhiteSpace(TxtContractTerms.Text), DBNull.Value, TxtContractTerms.Text.Trim()))
                .Parameters.AddWithValue("@ContractStatus", CmbContractStatus.Text)
            End With

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم تحديث العقد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadContracts()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث العقد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearForm()
    End Sub

    Private Sub CmbUnit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CmbUnit.SelectedIndexChanged
        If CmbUnit.SelectedIndex <> -1 Then
            LoadUnitRent()
        End If
    End Sub

    Private Sub LoadUnitRent()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SELECT MonthlyRent FROM Item_Tbl WHERE Item_ID = @Item_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Item_ID", CmbUnit.SelectedValue)

            Dim rent As Object = cmd.ExecuteScalar()
            If rent IsNot Nothing AndAlso Not IsDBNull(rent) Then
                TxtMonthlyRent.Text = rent.ToString()
            End If

            connx.Con.Close()

        Catch ex As Exception
            ' تجاهل الأخطاء في تحميل الإيجار
        End Try
    End Sub

End Class
