﻿﻿Imports System.Data.SqlClient
Imports System.IO

Public Class frm_add_update_unit
    Dim connx As New CLS_CON
    Dim UnitID As Integer = 0
    
    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub

    Public Sub ClearItems()
        TxtUnitName.Text = ""
        TxtUnitCode.Text = ""
        CmbBuilding.SelectedIndex = -1
        TxtFloorNumber.Text = ""
        TxtRoomsCount.Text = ""
        TxtBathroomsCount.Text = ""
        TxtArea.Text = ""
        TxtMonthlyRent.Text = ""
        CmbUnitStatus.SelectedIndex = 0 ' فارغ
        TxtDescription.Text = ""
        TxtSecurityDeposit.Text = ""
        ChkHasBalcony.Checked = False
        ChkHasParking.Checked = False
        ChkFurnished.Checked = False
        UnitImage.Image = My.Resources.default_unit_image
        BtnSave.Enabled = True
        BtnUpdate.Enabled = False
        UnitID = 0
        TxtUnitCode.Focus()
    End Sub

    Public Sub Load_Buildings()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            connx.FillComboBox(CmbBuilding, "Cat_ID", "CatName", "Cat_Tbl")
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل العمارات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnBrowseImage_Click(sender As Object, e As EventArgs) Handles BtnBrowseImage.Click
        Try
            With OpenFileDialog1
                .Filter = ("Images |*.png; *.bmp; *.jpg;*.jpeg; *.gif;")
                .FilterIndex = 4
            End With
            OpenFileDialog1.FileName = ""
            If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
                UnitImage.Image = Image.FromFile(OpenFileDialog1.FileName)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الصورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If ValidateForm() Then
            SaveUnit()
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(TxtUnitName.Text) Then
            MessageBox.Show("يرجى إدخال اسم الوحدة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtUnitName.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(TxtUnitCode.Text) Then
            MessageBox.Show("يرجى إدخال رمز الوحدة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtUnitCode.Focus()
            Return False
        End If

        If CmbBuilding.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار العمارة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CmbBuilding.Focus()
            Return False
        End If

        If Not IsNumeric(TxtFloorNumber.Text) OrElse Val(TxtFloorNumber.Text) < 0 Then
            MessageBox.Show("يرجى إدخال رقم طابق صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtFloorNumber.Focus()
            Return False
        End If

        If Not IsNumeric(TxtRoomsCount.Text) OrElse Val(TxtRoomsCount.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال عدد غرف صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtRoomsCount.Focus()
            Return False
        End If

        If Not IsNumeric(TxtArea.Text) OrElse Val(TxtArea.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال مساحة صحيحة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtArea.Focus()
            Return False
        End If

        If Not IsNumeric(TxtMonthlyRent.Text) OrElse Val(TxtMonthlyRent.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال إيجار شهري صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtMonthlyRent.Focus()
            Return False
        End If

        ' التحقق من عدم تكرار رمز الوحدة
        If Not CheckUnitCodeUnique() Then
            MessageBox.Show("رمز الوحدة موجود مسبقاً، يرجى اختيار رمز آخر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            TxtUnitCode.Focus()
            Return False
        End If

        Return True
    End Function

    Private Function CheckUnitCodeUnique() As Boolean
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand()
            If UnitID = 0 Then ' إضافة جديدة
                cmd.CommandText = "SELECT COUNT(*) FROM Item_Tbl WHERE ItemCode = @ItemCode"
            Else ' تعديل
                cmd.CommandText = "SELECT COUNT(*) FROM Item_Tbl WHERE ItemCode = @ItemCode AND Item_ID <> @Item_ID"
                cmd.Parameters.AddWithValue("@Item_ID", UnitID)
            End If
            
            cmd.Parameters.AddWithValue("@ItemCode", TxtUnitCode.Text.Trim())
            cmd.Connection = connx.Con
            
            Dim count As Integer = cmd.ExecuteScalar()
            connx.Con.Close()
            
            Return count = 0
        Catch ex As Exception
            MessageBox.Show("خطأ في التحقق من رمز الوحدة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    Private Sub SaveUnit()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "INSERT INTO Item_Tbl (ItemName, ItemCode, Cat_ID, FloorNumber, RoomsCount, " &
                              "BathroomsCount, Area, MonthlyRent, UnitStatus, UnitDescription, SecurityDeposit, " &
                              "HasBalcony, HasParking, IsFurnished) " &
                              "VALUES (@ItemName, @ItemCode, @Cat_ID, @FloorNumber, @RoomsCount, " &
                              "@BathroomsCount, @Area, @MonthlyRent, @UnitStatus, @UnitDescription, " &
                              "@SecurityDeposit, @HasBalcony, @HasParking, @IsFurnished)"

                .Parameters.AddWithValue("@ItemName", TxtUnitName.Text.Trim())
                .Parameters.AddWithValue("@ItemCode", TxtUnitCode.Text.Trim())
                .Parameters.AddWithValue("@Cat_ID", CmbBuilding.SelectedValue)
                .Parameters.AddWithValue("@FloorNumber", CInt(TxtFloorNumber.Text))
                .Parameters.AddWithValue("@RoomsCount", CInt(TxtRoomsCount.Text))
                .Parameters.AddWithValue("@BathroomsCount", If(String.IsNullOrWhiteSpace(TxtBathroomsCount.Text), 1, CInt(TxtBathroomsCount.Text)))
                .Parameters.AddWithValue("@Area", CDec(TxtArea.Text))
                .Parameters.AddWithValue("@MonthlyRent", CDec(TxtMonthlyRent.Text))
                .Parameters.AddWithValue("@UnitStatus", CmbUnitStatus.Text)
                .Parameters.AddWithValue("@UnitDescription", If(String.IsNullOrWhiteSpace(TxtDescription.Text), DBNull.Value, TxtDescription.Text.Trim()))
                .Parameters.AddWithValue("@SecurityDeposit", If(String.IsNullOrWhiteSpace(TxtSecurityDeposit.Text), DBNull.Value, CDec(TxtSecurityDeposit.Text)))
                .Parameters.AddWithValue("@HasBalcony", ChkHasBalcony.Checked)
                .Parameters.AddWithValue("@HasParking", ChkHasParking.Checked)
                .Parameters.AddWithValue("@IsFurnished", ChkFurnished.Checked)
            End With

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم حفظ الوحدة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearItems()
            
            ' تحديث قائمة الوحدات في الشاشة الرئيسية
            If frm_manage_units.Visible Then
                frm_manage_units.Load_Units()
            End If

        Catch ex As Exception
            MessageBox.Show("خطأ في حفظ الوحدة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Public Sub Load_Unit_Data(unitId As Integer)
        Try
            UnitID = unitId
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM Item_Tbl WHERE Item_ID = @Item_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Item_ID", unitId)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                TxtUnitName.Text = reader("ItemName").ToString()
                TxtUnitCode.Text = reader("ItemCode").ToString()
                CmbBuilding.SelectedValue = reader("Cat_ID")
                TxtFloorNumber.Text = If(IsDBNull(reader("FloorNumber")), "1", reader("FloorNumber").ToString())
                TxtRoomsCount.Text = If(IsDBNull(reader("RoomsCount")), "1", reader("RoomsCount").ToString())
                TxtBathroomsCount.Text = If(IsDBNull(reader("BathroomsCount")), "1", reader("BathroomsCount").ToString())
                TxtArea.Text = If(IsDBNull(reader("Area")), "0", reader("Area").ToString())
                TxtMonthlyRent.Text = If(IsDBNull(reader("MonthlyRent")), "0", reader("MonthlyRent").ToString())
                CmbUnitStatus.Text = If(IsDBNull(reader("UnitStatus")), "فارغ", reader("UnitStatus").ToString())
                TxtDescription.Text = If(IsDBNull(reader("UnitDescription")), "", reader("UnitDescription").ToString())
                TxtSecurityDeposit.Text = If(IsDBNull(reader("SecurityDeposit")), "", reader("SecurityDeposit").ToString())
                ChkHasBalcony.Checked = If(IsDBNull(reader("HasBalcony")), False, reader("HasBalcony"))
                ChkHasParking.Checked = If(IsDBNull(reader("HasParking")), False, reader("HasParking"))
                ChkFurnished.Checked = If(IsDBNull(reader("IsFurnished")), False, reader("IsFurnished"))
            End If

            reader.Close()
            connx.Con.Close()

            BtnSave.Enabled = False
            BtnUpdate.Enabled = True

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات الوحدة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If ValidateForm() AndAlso UnitID > 0 Then
            UpdateUnit()
        End If
    End Sub

    Private Sub UpdateUnit()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand()
            With cmd
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "UPDATE Item_Tbl SET ItemName = @ItemName, ItemCode = @ItemCode, Cat_ID = @Cat_ID, " &
                              "FloorNumber = @FloorNumber, RoomsCount = @RoomsCount, BathroomsCount = @BathroomsCount, " &
                              "Area = @Area, MonthlyRent = @MonthlyRent, UnitStatus = @UnitStatus, " &
                              "UnitDescription = @UnitDescription, SecurityDeposit = @SecurityDeposit, " &
                              "HasBalcony = @HasBalcony, HasParking = @HasParking, IsFurnished = @IsFurnished " &
                              "WHERE Item_ID = @Item_ID"

                .Parameters.AddWithValue("@Item_ID", UnitID)
                .Parameters.AddWithValue("@ItemName", TxtUnitName.Text.Trim())
                .Parameters.AddWithValue("@ItemCode", TxtUnitCode.Text.Trim())
                .Parameters.AddWithValue("@Cat_ID", CmbBuilding.SelectedValue)
                .Parameters.AddWithValue("@FloorNumber", CInt(TxtFloorNumber.Text))
                .Parameters.AddWithValue("@RoomsCount", CInt(TxtRoomsCount.Text))
                .Parameters.AddWithValue("@BathroomsCount", If(String.IsNullOrWhiteSpace(TxtBathroomsCount.Text), 1, CInt(TxtBathroomsCount.Text)))
                .Parameters.AddWithValue("@Area", CDec(TxtArea.Text))
                .Parameters.AddWithValue("@MonthlyRent", CDec(TxtMonthlyRent.Text))
                .Parameters.AddWithValue("@UnitStatus", CmbUnitStatus.Text)
                .Parameters.AddWithValue("@UnitDescription", If(String.IsNullOrWhiteSpace(TxtDescription.Text), DBNull.Value, TxtDescription.Text.Trim()))
                .Parameters.AddWithValue("@SecurityDeposit", If(String.IsNullOrWhiteSpace(TxtSecurityDeposit.Text), DBNull.Value, CDec(TxtSecurityDeposit.Text)))
                .Parameters.AddWithValue("@HasBalcony", ChkHasBalcony.Checked)
                .Parameters.AddWithValue("@HasParking", ChkHasParking.Checked)
                .Parameters.AddWithValue("@IsFurnished", ChkFurnished.Checked)
            End With

            cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم تحديث الوحدة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            ' تحديث قائمة الوحدات في الشاشة الرئيسية
            If frm_manage_units.Visible Then
                frm_manage_units.Load_Units()
            End If
            
            Me.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث الوحدة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub frm_add_update_unit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تحميل حالات الوحدة
        CmbUnitStatus.Items.Clear()
        CmbUnitStatus.Items.AddRange({"فارغ", "مؤجر", "صيانة", "محجوز"})
        CmbUnitStatus.SelectedIndex = 0
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearItems()
    End Sub

    ' حساب الإيجار السنوي تلقائياً
    Private Sub TxtMonthlyRent_TextChanged(sender As Object, e As EventArgs) Handles TxtMonthlyRent.TextChanged
        If IsNumeric(TxtMonthlyRent.Text) Then
            Dim monthlyRent As Decimal = CDec(TxtMonthlyRent.Text)
            LblYearlyRent.Text = "الإيجار السنوي: " & (monthlyRent * 12).ToString("N2") & " ريال"
        Else
            LblYearlyRent.Text = "الإيجار السنوي: 0.00 ريال"
        End If
    End Sub

    ' إنشاء رمز الوحدة تلقائياً
    Private Sub CmbBuilding_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CmbBuilding.SelectedIndexChanged
        If CmbBuilding.SelectedIndex <> -1 AndAlso Not String.IsNullOrWhiteSpace(TxtFloorNumber.Text) Then
            GenerateUnitCode()
        End If
    End Sub

    Private Sub TxtFloorNumber_TextChanged(sender As Object, e As EventArgs) Handles TxtFloorNumber.TextChanged
        If CmbBuilding.SelectedIndex <> -1 AndAlso IsNumeric(TxtFloorNumber.Text) Then
            GenerateUnitCode()
        End If
    End Sub

    Private Sub GenerateUnitCode()
        Try
            If CmbBuilding.SelectedIndex = -1 OrElse Not IsNumeric(TxtFloorNumber.Text) Then Return
            
            Dim buildingCode As String = CmbBuilding.Text.Substring(0, Math.Min(2, CmbBuilding.Text.Length))
            Dim floorNumber As String = TxtFloorNumber.Text.PadLeft(2, "0"c)
            
            ' البحث عن آخر رقم وحدة في نفس الطابق
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim cmd As New SqlCommand("SELECT COUNT(*) FROM Item_Tbl WHERE Cat_ID = @Cat_ID AND FloorNumber = @FloorNumber", connx.Con)
            cmd.Parameters.AddWithValue("@Cat_ID", CmbBuilding.SelectedValue)
            cmd.Parameters.AddWithValue("@FloorNumber", CInt(TxtFloorNumber.Text))
            
            Dim unitCount As Integer = cmd.ExecuteScalar() + 1
            connx.Con.Close()
            
            Dim unitCode As String = buildingCode & floorNumber & unitCount.ToString("00")
            TxtUnitCode.Text = unitCode
            
        Catch ex As Exception
            ' تجاهل الأخطاء في إنشاء الرمز التلقائي
        End Try
    End Sub
End Class
