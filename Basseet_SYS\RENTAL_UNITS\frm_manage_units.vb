﻿﻿Imports System.Data.SqlClient
Imports System.IO

Public Class frm_manage_units
    Dim connx As New CLS_CON
    Dim status_0 As Boolean
    Dim status_1 As Boolean
    
    Private Sub BtnNewUnit_Click(sender As Object, e As EventArgs) Handles BtnNewUnit.Click
        With frm_add_update_unit
            .Label1.Text = "شاشة إضافة وحدة جديدة"
            .ClearItems()
            .Load_Buildings()
            .ShowDialog()
        End With
    End Sub

    Public Sub Load_Units()
        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()
        connx.FillComboBox(CmbBuilding, "Cat_ID", "CatName", "Cat_Tbl")
        DgvUnits.Rows.Clear()
        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()

        connx.Cmd = New SqlCommand("Select * from View_Units", connx.Con)
        connx.rdr = connx.Cmd.ExecuteReader
        While connx.rdr.Read
            Dim unitStatus As String = ""
            Select Case connx.rdr("UnitStatus").ToString()
                Case "فارغ"
                    unitStatus = "فارغ"
                Case "مؤجر"
                    unitStatus = "مؤجر"
                Case "صيانة"
                    unitStatus = "صيانة"
                Case Else
                    unitStatus = "غير محدد"
            End Select
            
            DgvUnits.Rows.Add(
                connx.rdr("UnitID").ToString,
                connx.rdr("UnitCode").ToString,
                connx.rdr("UnitName").ToString,
                connx.rdr("BuildingName").ToString,
                connx.rdr("FloorNumber").ToString,
                connx.rdr("RoomsCount").ToString,
                connx.rdr("Area").ToString,
                connx.rdr("MonthlyRent").ToString,
                unitStatus
            )
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub frm_manage_units_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Load_Units()
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs) Handles BtnEdit.Click
        If DgvUnits.CurrentRow IsNot Nothing Then
            With frm_add_update_unit
                .Label1.Text = "شاشة تعديل الوحدة"
                .Load_Buildings()
                .Load_Unit_Data(DgvUnits.CurrentRow.Cells(0).Value)
                .ShowDialog()
            End With
            Load_Units()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If DgvUnits.CurrentRow IsNot Nothing Then
            If MessageBox.Show("هل أنت متأكد من حذف هذه الوحدة؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Delete_Unit(DgvUnits.CurrentRow.Cells(0).Value)
                Load_Units()
            End If
        End If
    End Sub

    Private Sub Delete_Unit(unitID As Integer)
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            ' التحقق من وجود عقود نشطة للوحدة
            Dim checkCmd As New SqlCommand("SELECT COUNT(*) FROM RentalContracts_Tbl WHERE Unit_ID = @Unit_ID AND ContractStatus = 'نشط'", connx.Con)
            checkCmd.Parameters.AddWithValue("@Unit_ID", unitID)
            Dim activeContracts As Integer = checkCmd.ExecuteScalar()
            
            If activeContracts > 0 Then
                MessageBox.Show("لا يمكن حذف هذه الوحدة لوجود عقود إيجار نشطة عليها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                connx.Con.Close()
                Return
            End If
            
            Dim cmd As New SqlCommand("DELETE FROM Item_Tbl WHERE Item_ID = @Item_ID", connx.Con)
            cmd.Parameters.AddWithValue("@Item_ID", unitID)
            cmd.ExecuteNonQuery()
            connx.Con.Close()
            
            MessageBox.Show("تم حذف الوحدة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("خطأ في حذف الوحدة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CmbBuilding_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CmbBuilding.SelectedIndexChanged
        DgvUnits.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.Cmd = New SqlCommand("Select * from View_Units Where BuildingName Like '" & CmbBuilding.Text & "' ", connx.Con)
        connx.rdr = connx.Cmd.ExecuteReader
        While connx.rdr.Read
            Dim unitStatus As String = ""
            Select Case connx.rdr("UnitStatus").ToString()
                Case "فارغ"
                    unitStatus = "فارغ"
                Case "مؤجر"
                    unitStatus = "مؤجر"
                Case "صيانة"
                    unitStatus = "صيانة"
                Case Else
                    unitStatus = "غير محدد"
            End Select
            
            DgvUnits.Rows.Add(
                connx.rdr("UnitID").ToString,
                connx.rdr("UnitCode").ToString,
                connx.rdr("UnitName").ToString,
                connx.rdr("BuildingName").ToString,
                connx.rdr("FloorNumber").ToString,
                connx.rdr("RoomsCount").ToString,
                connx.rdr("Area").ToString,
                connx.rdr("MonthlyRent").ToString,
                unitStatus
            )
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs) Handles TxtSearch.TextChanged
        SearchUnits()
    End Sub

    Private Sub SearchUnits()
        Try
            DgvUnits.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()
            
            Dim searchText As String = TxtSearch.Text.Trim()
            Dim cmd As New SqlCommand()
            
            If String.IsNullOrWhiteSpace(searchText) Then
                cmd.CommandText = "SELECT * FROM View_Units ORDER BY BuildingName, UnitName"
            Else
                cmd.CommandText = "SELECT * FROM View_Units WHERE UnitName LIKE @Search OR " &
                                 "UnitCode LIKE @Search OR BuildingName LIKE @Search " &
                                 "ORDER BY BuildingName, UnitName"
                cmd.Parameters.AddWithValue("@Search", "%" & searchText & "%")
            End If
            
            cmd.Connection = connx.Con
            connx.rdr = cmd.ExecuteReader()
            
            While connx.rdr.Read
                Dim unitStatus As String = ""
                Select Case connx.rdr("UnitStatus").ToString()
                    Case "فارغ"
                        unitStatus = "فارغ"
                    Case "مؤجر"
                        unitStatus = "مؤجر"
                    Case "صيانة"
                        unitStatus = "صيانة"
                    Case Else
                        unitStatus = "غير محدد"
                End Select
                
                DgvUnits.Rows.Add(
                    connx.rdr("UnitID").ToString,
                    connx.rdr("UnitCode").ToString,
                    connx.rdr("UnitName").ToString,
                    connx.rdr("BuildingName").ToString,
                    connx.rdr("FloorNumber").ToString,
                    connx.rdr("RoomsCount").ToString,
                    connx.rdr("Area").ToString,
                    connx.rdr("MonthlyRent").ToString,
                    unitStatus
                )
            End While
            connx.rdr.Close()
            connx.Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs) Handles BtnRefresh.Click
        Load_Units()
        TxtSearch.Clear()
        CmbBuilding.SelectedIndex = -1
    End Sub

    Private Sub BtnViewContracts_Click(sender As Object, e As EventArgs) Handles BtnViewContracts.Click
        If DgvUnits.CurrentRow IsNot Nothing Then
            Dim unitID As Integer = DgvUnits.CurrentRow.Cells(0).Value
            ' فتح شاشة عرض عقود الوحدة
            With frm_unit_contracts
                .UnitID = unitID
                .UnitName = DgvUnits.CurrentRow.Cells(2).Value.ToString()
                .LoadContracts()
                .ShowDialog()
            End With
        End If
    End Sub

    Private Sub BtnMaintenanceHistory_Click(sender As Object, e As EventArgs) Handles BtnMaintenanceHistory.Click
        If DgvUnits.CurrentRow IsNot Nothing Then
            Dim unitID As Integer = DgvUnits.CurrentRow.Cells(0).Value
            ' فتح شاشة تاريخ الصيانة
            With frm_maintenance_history
                .UnitID = unitID
                .UnitName = DgvUnits.CurrentRow.Cells(2).Value.ToString()
                .LoadMaintenanceHistory()
                .ShowDialog()
            End With
        End If
    End Sub

    Private Sub DgvUnits_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvUnits.CellDoubleClick
        If e.RowIndex >= 0 Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    ' تلوين الصفوف حسب حالة الوحدة
    Private Sub DgvUnits_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs) Handles DgvUnits.CellFormatting
        If e.RowIndex >= 0 AndAlso e.ColumnIndex = 8 Then ' عمود حالة الوحدة
            Dim status As String = e.Value?.ToString()
            Select Case status
                Case "فارغ"
                    DgvUnits.Rows(e.RowIndex).DefaultCellStyle.BackColor = Color.LightGreen
                Case "مؤجر"
                    DgvUnits.Rows(e.RowIndex).DefaultCellStyle.BackColor = Color.LightBlue
                Case "صيانة"
                    DgvUnits.Rows(e.RowIndex).DefaultCellStyle.BackColor = Color.LightCoral
            End Select
        End If
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs) Handles BtnExport.Click
        ' تصدير البيانات إلى Excel
        ExportToExcel()
    End Sub

    Private Sub ExportToExcel()
        Try
            Dim saveFileDialog As New SaveFileDialog()
            saveFileDialog.Filter = "Excel Files|*.xlsx"
            saveFileDialog.Title = "حفظ تقرير الوحدات"
            saveFileDialog.FileName = "تقرير_الوحدات_" & DateTime.Now.ToString("yyyy-MM-dd")
            
            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                ' هنا يمكن إضافة كود تصدير Excel
                MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnPrint_Click(sender As Object, e As EventArgs) Handles BtnPrint.Click
        ' طباعة التقرير
        PrintReport()
    End Sub

    Private Sub PrintReport()
        Try
            ' هنا يمكن إضافة كود الطباعة
            MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("خطأ في الطباعة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
