Imports System.Data.SqlClient

Public Class frm_rent_payments
    Private currentPaymentID As Integer = 0
    Private isEditMode As Boolean = False

    Private Sub frm_rent_payments_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadContracts()
        LoadPayments()
        ClearFields()
        SetupDataGridView()
    End Sub

    Private Sub LoadContracts()
        Try
            CmbContract.Items.Clear()
            Dim cmd As New SqlCommand("SELECT ContractID, ContractNumber + ' - ' + CustomerName AS ContractDisplay " &
                                    "FROM RentalContracts_Tbl RC " &
                                    "INNER JOIN tblCustomers C ON RC.Tenant_ID = C.CustomerID " &
                                    "WHERE RC.ContractStatus = 'نشط' " &
                                    "ORDER BY RC.ContractNumber", connx.Con)
            
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            Dim reader As SqlDataReader = cmd.ExecuteReader()
            
            While reader.Read()
                CmbContract.Items.Add(New With {.Value = reader("ContractID"), .Text = reader("ContractDisplay")})
            End While
            reader.Close()
            
            CmbContract.DisplayMember = "Text"
            CmbContract.ValueMember = "Value"
            
        Catch ex As Exception
            MsgBox("خطأ في تحميل العقود: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub LoadPayments()
        Try
            Dim cmd As New SqlCommand("SELECT RP.PaymentID, RC.ContractNumber, C.CustomerName, " &
                                    "RP.PaymentDate, RP.Amount, RP.PaymentMethod, RP.PaymentStatus, RP.Notes " &
                                    "FROM RentPayments_Tbl RP " &
                                    "INNER JOIN RentalContracts_Tbl RC ON RP.Contract_ID = RC.ContractID " &
                                    "INNER JOIN tblCustomers C ON RC.Tenant_ID = C.CustomerID " &
                                    "ORDER BY RP.PaymentDate DESC", connx.Con)
            
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)
            
            DgvPayments.DataSource = dt
            
        Catch ex As Exception
            MsgBox("خطأ في تحميل الدفعات: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub SetupDataGridView()
        With DgvPayments
            .Columns("PaymentID").HeaderText = "رقم الدفعة"
            .Columns("ContractNumber").HeaderText = "رقم العقد"
            .Columns("CustomerName").HeaderText = "المستأجر"
            .Columns("PaymentDate").HeaderText = "تاريخ الدفع"
            .Columns("Amount").HeaderText = "المبلغ"
            .Columns("PaymentMethod").HeaderText = "طريقة الدفع"
            .Columns("PaymentStatus").HeaderText = "حالة الدفع"
            .Columns("Notes").HeaderText = "ملاحظات"
            
            .Columns("PaymentID").Visible = False
        End With
    End Sub

    Private Sub ClearFields()
        CmbContract.SelectedIndex = -1
        DtpPaymentDate.Value = DateTime.Now
        TxtAmount.Clear()
        CmbPaymentMethod.SelectedIndex = -1
        CmbPaymentStatus.SelectedIndex = -1
        TxtNotes.Clear()
        currentPaymentID = 0
        isEditMode = False
        BtnSave.Text = "حفظ"
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearFields()
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If ValidateFields() Then
            If isEditMode Then
                UpdatePayment()
            Else
                SavePayment()
            End If
        End If
    End Sub

    Private Function ValidateFields() As Boolean
        If CmbContract.SelectedIndex = -1 Then
            MsgBox("يرجى اختيار العقد", MsgBoxStyle.Exclamation, "تنبيه")
            CmbContract.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(TxtAmount.Text) OrElse Not IsNumeric(TxtAmount.Text) Then
            MsgBox("يرجى إدخال مبلغ صحيح", MsgBoxStyle.Exclamation, "تنبيه")
            TxtAmount.Focus()
            Return False
        End If

        If CmbPaymentMethod.SelectedIndex = -1 Then
            MsgBox("يرجى اختيار طريقة الدفع", MsgBoxStyle.Exclamation, "تنبيه")
            CmbPaymentMethod.Focus()
            Return False
        End If

        If CmbPaymentStatus.SelectedIndex = -1 Then
            MsgBox("يرجى اختيار حالة الدفع", MsgBoxStyle.Exclamation, "تنبيه")
            CmbPaymentStatus.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub SavePayment()
        Try
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            
            Using cmd As New SqlCommand()
                cmd.Connection = connx.Con
                cmd.CommandText = "INSERT INTO RentPayments_Tbl (Contract_ID, PaymentDate, Amount, PaymentMethod, PaymentStatus, Notes) " &
                                "VALUES (@Contract_ID, @PaymentDate, @Amount, @PaymentMethod, @PaymentStatus, @Notes)"
                
                cmd.Parameters.AddWithValue("@Contract_ID", CmbContract.SelectedValue)
                cmd.Parameters.AddWithValue("@PaymentDate", DtpPaymentDate.Value)
                cmd.Parameters.AddWithValue("@Amount", CDec(TxtAmount.Text))
                cmd.Parameters.AddWithValue("@PaymentMethod", CmbPaymentMethod.Text)
                cmd.Parameters.AddWithValue("@PaymentStatus", CmbPaymentStatus.Text)
                cmd.Parameters.AddWithValue("@Notes", TxtNotes.Text)
                
                cmd.ExecuteNonQuery()
                
                MsgBox("تم حفظ الدفعة بنجاح", MsgBoxStyle.Information, "نجح الحفظ")
                LoadPayments()
                ClearFields()
            End Using
            
        Catch ex As Exception
            MsgBox("خطأ في حفظ الدفعة: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub UpdatePayment()
        Try
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            
            Using cmd As New SqlCommand()
                cmd.Connection = connx.Con
                cmd.CommandText = "UPDATE RentPayments_Tbl SET Contract_ID = @Contract_ID, PaymentDate = @PaymentDate, " &
                                "Amount = @Amount, PaymentMethod = @PaymentMethod, PaymentStatus = @PaymentStatus, " &
                                "Notes = @Notes WHERE PaymentID = @PaymentID"
                
                cmd.Parameters.AddWithValue("@Contract_ID", CmbContract.SelectedValue)
                cmd.Parameters.AddWithValue("@PaymentDate", DtpPaymentDate.Value)
                cmd.Parameters.AddWithValue("@Amount", CDec(TxtAmount.Text))
                cmd.Parameters.AddWithValue("@PaymentMethod", CmbPaymentMethod.Text)
                cmd.Parameters.AddWithValue("@PaymentStatus", CmbPaymentStatus.Text)
                cmd.Parameters.AddWithValue("@Notes", TxtNotes.Text)
                cmd.Parameters.AddWithValue("@PaymentID", currentPaymentID)
                
                cmd.ExecuteNonQuery()
                
                MsgBox("تم تحديث الدفعة بنجاح", MsgBoxStyle.Information, "نجح التحديث")
                LoadPayments()
                ClearFields()
            End Using
            
        Catch ex As Exception
            MsgBox("خطأ في تحديث الدفعة: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        If DgvPayments.SelectedRows.Count > 0 Then
            LoadPaymentForEdit()
        Else
            MsgBox("يرجى اختيار دفعة للتعديل", MsgBoxStyle.Exclamation, "تنبيه")
        End If
    End Sub

    Private Sub LoadPaymentForEdit()
        Try
            Dim row As DataGridViewRow = DgvPayments.SelectedRows(0)
            
            currentPaymentID = Convert.ToInt32(row.Cells("PaymentID").Value)
            
            ' البحث عن العقد في ComboBox
            For i As Integer = 0 To CmbContract.Items.Count - 1
                If CmbContract.Items(i).Value.ToString() = row.Cells("ContractNumber").Value.ToString() Then
                    CmbContract.SelectedIndex = i
                    Exit For
                End If
            Next
            
            DtpPaymentDate.Value = Convert.ToDateTime(row.Cells("PaymentDate").Value)
            TxtAmount.Text = row.Cells("Amount").Value.ToString()
            CmbPaymentMethod.Text = row.Cells("PaymentMethod").Value.ToString()
            CmbPaymentStatus.Text = row.Cells("PaymentStatus").Value.ToString()
            TxtNotes.Text = row.Cells("Notes").Value.ToString()
            
            isEditMode = True
            BtnSave.Text = "تحديث"
            
        Catch ex As Exception
            MsgBox("خطأ في تحميل بيانات الدفعة: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If DgvPayments.SelectedRows.Count > 0 Then
            If MsgBox("هل أنت متأكد من حذف هذه الدفعة؟", MsgBoxStyle.YesNo + MsgBoxStyle.Question, "تأكيد الحذف") = MsgBoxResult.Yes Then
                DeletePayment()
            End If
        Else
            MsgBox("يرجى اختيار دفعة للحذف", MsgBoxStyle.Exclamation, "تنبيه")
        End If
    End Sub

    Private Sub DeletePayment()
        Try
            Dim paymentID As Integer = Convert.ToInt32(DgvPayments.SelectedRows(0).Cells("PaymentID").Value)
            
            If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
            
            Using cmd As New SqlCommand("DELETE FROM RentPayments_Tbl WHERE PaymentID = @PaymentID", connx.Con)
                cmd.Parameters.AddWithValue("@PaymentID", paymentID)
                cmd.ExecuteNonQuery()
                
                MsgBox("تم حذف الدفعة بنجاح", MsgBoxStyle.Information, "نجح الحذف")
                LoadPayments()
                ClearFields()
            End Using
            
        Catch ex As Exception
            MsgBox("خطأ في حذف الدفعة: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub DgvPayments_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvPayments.CellDoubleClick
        If e.RowIndex >= 0 Then
            LoadPaymentForEdit()
        End If
    End Sub
End Class
