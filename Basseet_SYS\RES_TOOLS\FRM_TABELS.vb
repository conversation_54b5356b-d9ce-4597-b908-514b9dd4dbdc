﻿Imports System.Data.SqlClient
Public Class Frm_Tabels
      Dim connx As New CLS_CON
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub
    Public Sub Insert_Table_Tbl()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into Table_Tbl (Table_Name)values(@Table_Name)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Table_Name", SqlDbType.VarChar).Value = TxtTableName.Text
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub Load_Table()
        DgvTable.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.Cmd = New SqlCommand("Select * from Table_Tbl", connx.Con)
        connx.rdr = connx.Cmd.ExecuteReader
        While connx.rdr.Read

            DgvTable.Rows.Add(connx.rdr("Table_ID").ToString, connx.rdr("Table_Name").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()

    End Sub


    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If TxtTableName.Text = "" Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)

            Exit Sub
        End If
        Insert_Table_Tbl()
        Load_Table()
        ClearText()
    End Sub
    Public Sub ClearText()
        TxtTable_ID.Text = ""
        TxtTableName.Text = ""
        BtnSave.Enabled = True
        BtnEdit.Enabled = False
        BtnDelete.Enabled = False
        BtnNew.Enabled = False
    End Sub
    Public Sub Update_Table_Tbl()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Table_Tbl Set Table_Name = @Table_Name Where Table_ID = @Table_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Table_Name", SqlDbType.VarChar).Value = TxtTableName.Text
            .Parameters.AddWithValue("@Table_ID", SqlDbType.Int).Value = TxtTable_ID.Text
        End With
        If CONNX.Con.State = 1 Then CONNX.Con.Close()
        CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
        Cmd = Nothing
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs) Handles BtnEdit.Click
        If TxtTableName.Text = "" Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)

            Exit Sub
        End If
        Update_Table_Tbl()
        Load_Table()
        ClearText()
    End Sub
    Public Sub Delete_Table_Tbl(ByVal dgv_Table_Tbl As DataGridView)
        Dim Position As Integer = dgv_Table_Tbl.CurrentRow.Index
        Dim ID_Position As Integer = dgv_Table_Tbl.Rows(Position).Cells(0).Value
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Delete  From Table_Tbl Where Table_ID = @Table_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Table_ID", SqlDbType.Int).Value = ID_Position
        End With
        If CONNX.Con.State = 1 Then CONNX.Con.Close()
        CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX.Con.Close()
        MsgBox("تم حذف السجل.", MsgBoxStyle.Information, "حذف")
        Cmd = Nothing
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If MessageBox.Show("هل أنت متأكد من مواصلة عملية الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
            Exit Sub
        Else
            Delete_Table_Tbl(DgvTable)
        End If
        Load_Table()
        ClearText()
    End Sub

    Private Sub Frm_Tabels_Load(sender As Object, e As EventArgs) Handles Me.Load
        Load_Table()

    End Sub

    Private Sub DgvTable_Click(sender As Object, e As EventArgs) Handles DgvTable.Click
        Me.TxtTable_ID.Text = DgvTable.CurrentRow.Cells(0).Value
        Me.TxtTableName.Text = DgvTable.CurrentRow.Cells(1).Value
        BtnSave.Enabled = False
        BtnEdit.Enabled = True
        BtnDelete.Enabled = True
        BtnNew.Enabled = True
    End Sub

    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        ClearText()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Me.Close()
    End Sub
End Class