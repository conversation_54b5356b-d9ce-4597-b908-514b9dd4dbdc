﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports System.IO
Imports Net.Pkcs11Interop.Common

Public Class frmSettings
    Dim printerName As String = ""
    Dim connx As New CLS_CON
    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from Tax_Tbl ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            connx.cmd = New SqlCommand("Update Tax_Tbl Set Tax_Value =@Tax_Value Where Tax_ID=@Tax_ID", connx.Con)
            connx.cmd.Parameters.AddWithValue("@Tax_Value", CDbl(TxtTax_Value.Text))
            connx.cmd.Parameters.AddWithValue("@Tax_ID", CInt(TxtTax_ID.Text))
            connx.cmd.ExecuteNonQuery()
            connx.Con.Close()
            MsgBox("تم تعديل بيانات الضريبة بنجاح")
        Else
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            connx.cmd = New SqlCommand("Insert into Tax_Tbl(Tax_Value)Values(@Tax_Value)", connx.Con)
            connx.cmd.Parameters.AddWithValue("@Tax_Value", CDbl(TxtTax_Value.Text))
            connx.cmd.ExecuteNonQuery()
            connx.Con.Close()
            MsgBox("تم  اضافة الضريبة بنجاح")

        End If
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub
    '############################### اعدادات الطباعة################################

    Public Sub showPrinters()
        For i As Integer = 0 To PrinterSettings.InstalledPrinters.Count - 1
            printerName = PrinterSettings.InstalledPrinters(i)
            cbxPrinter.Items.Add(printerName)
        Next

        If My.Settings.PrinterName = "" Then
            cbxPrinter.SelectedIndex = 0
        Else
            cbxPrinter.Text = My.Settings.PrinterName
        End If
    End Sub

    Private Sub BtnSave_Status_Click(sender As Object, e As EventArgs) Handles BtnSave_Status.Click
        If CheckTax.Checked = True Then
            My.Settings.Tax_Status = True
            My.Settings.Save()
            MsgBox("تم  تفعيل الضريبة بنجاح")

        Else
            My.Settings.Tax_Status = False
            My.Settings.Save()
            MsgBox("تم  الغاء تفعيل الضريبة بنجاح")

        End If
    End Sub

    Private Sub Frm_Setting_Load(sender As Object, e As EventArgs) Handles Me.Load
        txtserialDevice.Text = My.Settings.serialDevice
        If My.Settings.Tax_Status = True Then
            CheckTax.Checked = True
        Else
            CheckTax.Checked = False
        End If
        Get_Tax()
        showPrinters()
        If My.Settings.SalePrint = "A4" Then
            CheckSaleA4.Checked = True
            CheckSale8Cm.Checked = False
        ElseIf My.Settings.SalePrint = "8Cm" Then
            CheckSale8Cm.Checked = True
            CheckSaleA4.Checked = False

        End If

        Check()
        clear_area()
        clear_DELEVERY()
    End Sub
    Public Sub Get_Tax()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from Tax_Tbl", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            TxtTax_ID.Text = connx.rdr("Tax_ID").ToString
            TxtTax_Value.Text = connx.rdr("Tax_Value").ToString
        Else
            TxtTax_ID.Text = ""
            TxtTax_Value.Text = ""
        End If
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub BtnSave_Printer_Click(sender As Object, e As EventArgs) Handles BtnSave_Printer.Click
        If cbxPrinter.Text = "" Then
            MessageBox.Show("من فضلك تاكد من بيانات الطابعة", "تاكيد")
            Return
        End If
        My.Settings.PrinterName = cbxPrinter.Text
        My.Settings.Save()

        MessageBox.Show("تم الحفظ بنجاح", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information)

    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click

        If CheckSaleA4.Checked = True Then
            My.Settings.SalePrint = "A4"
            My.Settings.Save()
        ElseIf CheckSale8Cm.Checked = True Then
            My.Settings.SalePrint = "8Cm"
            My.Settings.Save()
        End If
        MessageBox.Show("تم بنجاح حفظ البيانات ", "شاشة البيع", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)

    End Sub

    Private Sub CheckSaleA4_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSaleA4.CheckedChanged
        If CheckSaleA4.Checked = True Then
            CheckSale8Cm.Checked = False
        End If
    End Sub

    Private Sub CheckSale8Cm_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSale8Cm.CheckedChanged
        If CheckSale8Cm.Checked = True Then
            CheckSaleA4.Checked = False
        End If
    End Sub
    Public Sub Insert_Company(ByVal CompanyName As String, ByVal Phone1 As String, ByVal Phone2 As String, ByVal EmailID As String, ByVal Fax As String, ByVal City As String, ByVal countryName As String, ByVal Address As String, ByVal Vat_No As String, ByVal logo As PictureBox)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into comSetting_Tbl ( CompanyName,Phone1,Phone2,EmailID,Fax,City,countryName,Address,Vat_No,CompanyLogo)values(@CompanyName,@Phone1,@Phone2,@EmailID,@Fax,@City,@countryName,@Address,@Vat_No,@CompanyLogo)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CompanyName", SqlDbType.VarChar).Value = CompanyName
            .Parameters.AddWithValue("@Phone1", SqlDbType.VarChar).Value = Phone1
            .Parameters.AddWithValue("@Phone2", SqlDbType.VarChar).Value = Phone2
            .Parameters.AddWithValue("@EmailID", SqlDbType.VarChar).Value = EmailID
            .Parameters.AddWithValue("@Fax", SqlDbType.VarChar).Value = Fax
            .Parameters.AddWithValue("@City", SqlDbType.VarChar).Value = City
            .Parameters.AddWithValue("@countryName", SqlDbType.VarChar).Value = countryName
            .Parameters.AddWithValue("@Address", SqlDbType.VarChar).Value = Address
            .Parameters.AddWithValue("@Vat_No", SqlDbType.VarChar).Value = Vat_No
            Dim ms As New MemoryStream()
            Dim bmpImage As New Bitmap(logo.Image)
            bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
            Dim data As Byte() = ms.GetBuffer()
            Dim p As New SqlParameter("@CompanyLogo", SqlDbType.Image)
            p.Value = data
            .Parameters.Add(p)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub

    Private Sub Browse_Click(sender As Object, e As EventArgs) Handles Browse.Click
        Try
            With OpenFileDialog1
                .Filter = ("Images |*.png; *.bmp; *.jpg;*.jpeg; *.gif;")
                .FilterIndex = 4
            End With
            'Clear the file name
            OpenFileDialog1.FileName = ""
            If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
                logo.Image = Image.FromFile(OpenFileDialog1.FileName)
            End If
        Catch ex As Exception
            MsgBox(ex.ToString())
        End Try
    End Sub
    Public Sub Check()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand(" Select * from comSetting_Tbl ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            TxtCompany_ID.Text = connx.rdr("Company_ID").ToString
            TxtCompanyName.Text = connx.rdr("CompanyName").ToString
            TxtPhone1.Text = connx.rdr("Phone1").ToString
            TxtPhone2.Text = connx.rdr("Phone2").ToString
            txtEmailID.Text = connx.rdr("EmailID").ToString
            TxtFax.Text = connx.rdr("Fax").ToString
            TxtCity.Text = connx.rdr("City").ToString
            TxtCountry.Text = connx.rdr("countryName").ToString
            TxtAddress.Text = connx.rdr("Address").ToString
            Txt_Vat_No.Text = connx.rdr("Vat_No").ToString
            Dim data As Byte() = DirectCast(connx.rdr("CompanyLogo"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            logo.Image = bitamp
            btn_save1.Text = "تعديل البيانات"
        Else
            TxtCompany_ID.Text = ""
            TxtCompanyName.Text = ""
            TxtPhone1.Text = ""
            TxtPhone2.Text = ""
            txtEmailID.Text = ""
            TxtFax.Text = ""
            TxtCity.Text = ""
            TxtCountry.Text = ""
            TxtAddress.Text = ""
            Txt_Vat_No.Text = ""
            TxtCompanyName.Focus()
            logo.Image = My.Resources._18_06_2022_08_15_58_م
            BtnSave.Text = "حفظ البيانات"
        End If

        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Public Sub Update_Company(ByVal CompanyName As String, ByVal Phone1 As String, ByVal Phone2 As String, ByVal EmailID As String, ByVal Fax As String, ByVal City As String, ByVal countryName As String, ByVal Address As String, ByVal Vat_No As String, ByVal logo As PictureBox, ByVal Company_IDW As Int32)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update comSetting_Tbl Set CompanyName = @CompanyName,Phone1 = @Phone1,Phone2 = @Phone2,EmailID = @EmailID,Fax = @Fax,City = @City,countryName = @countryName,Address = @Address,Vat_No=@Vat_No,CompanyLogo = @CompanyLogo Where Company_ID = @Company_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CompanyName", SqlDbType.VarChar).Value = CompanyName
            .Parameters.AddWithValue("@Phone1", SqlDbType.VarChar).Value = Phone1
            .Parameters.AddWithValue("@Phone2", SqlDbType.VarChar).Value = Phone2
            .Parameters.AddWithValue("@EmailID", SqlDbType.VarChar).Value = EmailID
            .Parameters.AddWithValue("@Fax", SqlDbType.VarChar).Value = Fax
            .Parameters.AddWithValue("@City", SqlDbType.VarChar).Value = City
            .Parameters.AddWithValue("@countryName", SqlDbType.VarChar).Value = countryName
            .Parameters.AddWithValue("@Address", SqlDbType.VarChar).Value = Address
            .Parameters.AddWithValue("@Vat_No", SqlDbType.VarChar).Value = Vat_No
            Dim ms As New MemoryStream()
            Dim bmpImage As New Bitmap(logo.Image)
            bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
            Dim data As Byte() = ms.GetBuffer()
            Dim p As New SqlParameter("@CompanyLogo", SqlDbType.Image)
            p.Value = data
            .Parameters.Add(p)
            .Parameters.AddWithValue("@Company_ID", SqlDbType.Int).Value = Company_IDW
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
        Cmd = Nothing
    End Sub

    Private Sub btn_save1_Click(sender As Object, e As EventArgs) Handles btn_save1.Click
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand(" Select * from comSetting_Tbl ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            Update_Company(TxtCompanyName.Text, TxtPhone1.Text, TxtPhone2.Text, txtEmailID.Text, TxtFax.Text, TxtCity.Text, TxtCountry.Text, TxtAddress.Text, Txt_Vat_No.Text, logo, TxtCompany_ID.Text)
            Check()
            My.Settings.serialDevice = txtserialDevice.Text
            My.Settings.Save()
        Else
            Insert_Company(TxtCompanyName.Text, TxtPhone1.Text, TxtPhone2.Text, txtEmailID.Text, TxtFax.Text, TxtCity.Text, TxtCountry.Text, TxtAddress.Text, Txt_Vat_No.Text, logo)
            Check()
        End If
    End Sub
    '*************************************************area*****************************************************************
    Sub clear_area()
        txtAreaId.Text = connx.GetNextID("tbl_area", "AREA_ID").ToString("00000")
        txtAreaName.Text = vbNullString
        txtAreaFee.Text = 0.0
        btn_new.Enabled = False
        btn_save.Enabled = True
        btn_edit.Enabled = False
        btn_delete.Enabled = False
    End Sub
    Public Sub Insert_area(ByVal AREAid As Integer, ByVal AREANAME As String, ByVal AREAPRICE As String)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into TBL_AREA ( AREA_ID,AREA_NAME,AREA_PRICE)values(@AREA_ID,@AREA_NAME,@AREA_PRICE)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@AREA_ID", CInt(txtAreaId.Text))
            .Parameters.AddWithValue("@AREA_NAME", txtAreaName.Text)
            .Parameters.AddWithValue("@AREA_PRICE", CDbl(txtAreaFee.Text))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub update_area(ByVal AREAid As Integer, ByVal AREANAME As String, ByVal AREAPRICE As String)
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "update TBL_AREA set AREA_NAME=@AREA_NAME,AREA_PRICE=@AREA_PRICE where AREA_ID=@AREA_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@AREA_NAME", txtAreaName.Text)
            .Parameters.AddWithValue("@AREA_PRICE", CDbl(txtAreaFee.Text))
            .Parameters.AddWithValue("@AREA_ID", CInt(txtAreaId.Text))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Private Sub btn_save_Click(sender As Object, e As EventArgs) Handles btn_save.Click
        Insert_area(txtAreaId.Text, txtAreaName.Text, txtAreaFee.Text)
    End Sub
    Public Sub Load_area()
        dgvArea.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from TBL_AREA", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            dgvArea.Rows.Add(connx.rdr("AREA_ID").ToString, connx.rdr("AREA_NAME").ToString, connx.rdr("AREA_PRICE").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()

    End Sub

    Private Sub TabPage14_Enter(sender As Object, e As EventArgs) Handles TabPage14.Enter
        Load_area()

    End Sub

    Private Sub btn_edit_Click(sender As Object, e As EventArgs) Handles btn_edit.Click
        update_area(txtAreaId.Text, txtAreaName.Text, txtAreaFee.Text)
        Load_area()
    End Sub

    Private Sub dgvArea_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvArea.CellClick
        txtAreaId.Text = dgvArea.CurrentRow.Cells(0).Value
        txtAreaName.Text = dgvArea.CurrentRow.Cells(1).Value
        txtAreaFee.Text = dgvArea.CurrentRow.Cells(2).Value
        btn_save.Enabled = False
        btn_edit.Enabled = True
        btn_delete.Enabled = True
        btn_new.Enabled = True
    End Sub
    Sub fill_area()
        connx.FillComboBox(Cmb_Area, "AREA_id", "AREA_Name", "TBL_AREA")
    End Sub
    '**********************************************************&customer&***************************************************
    Private Sub TabPage10_Enter(sender As Object, e As EventArgs) Handles TabPage10.Enter
        fill_area()
        clear_customer()
        Load_customer()
    End Sub

    Private Sub Button27_Click(sender As Object, e As EventArgs) Handles Button27.Click
        Me.Close()
    End Sub
    Sub clear_customer()
        txtCustomerID.Text = connx.GetNextID("tblCustomers", "CustomerID").ToString("00000")
        txtCustomerName.Text = vbNullString
        txtMobile.Text = vbNullString
        txtMobile2.Text = vbNullString
        txtPhone.Text = vbNullString
        txt_adrs.Text = vbNullString
        txtSatrtBalance.Text = 0.0
        txtBalanceLimit.Text = 0.0
        Button14.Enabled = False
        Button13.Enabled = True
        Button7.Enabled = False

    End Sub
    Public Sub Insert_customers()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into tblCustomers ( CustomerID,CustomerName,Address,Mobile,Mobile2,StartBalance,BalanceLimit,debcridt,Notes,Area_ID)
                            values(@CustomerID,@CustomerName,@Address,@Mobile,@Mobile2,@StartBalance,@BalanceLimit,@debcridt,@Notes,@Area_ID)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CustomerID", CInt(txtCustomerID.Text))
            .Parameters.AddWithValue("@CustomerName", txtCustomerName.Text)
            .Parameters.AddWithValue("@Address", txt_adrs.Text)
            .Parameters.AddWithValue("@Mobile", txtMobile.Text)
            .Parameters.AddWithValue("@Mobile2", txtMobile2.Text)
            .Parameters.AddWithValue("@StartBalance", CDbl(txtBalanceLimit.Text))
            .Parameters.AddWithValue("@BalanceLimit", CDbl(txtSatrtBalance.Text))
            .Parameters.AddWithValue("@debcridt", If(CusDebtor.Checked, 1, 0))
            .Parameters.AddWithValue("@Notes", txtAreaName.Text)
            .Parameters.AddWithValue("@Area_ID", CInt(Cmb_Area.SelectedValue))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub update_customers()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "update tblCustomers set CustomerName=@CustomerName,Address=@Address,Mobile=@Mobile,Mobile2=@Mobile2,StartBalance=@StartBalance,BalanceLimit=@BalanceLimit,debcridt=@debcridt,Notes=@Notes,Area_ID=@Area_ID where CustomerID = @CustomerID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CustomerID", CInt(txtCustomerID.Text))
            .Parameters.AddWithValue("@CustomerName", txtCustomerName.Text)
            .Parameters.AddWithValue("@Address", txt_adrs.Text)
            .Parameters.AddWithValue("@Mobile", txtMobile.Text)
            .Parameters.AddWithValue("@Mobile2", txtMobile2.Text)
            .Parameters.AddWithValue("@StartBalance", CDbl(txtBalanceLimit.Text))
            .Parameters.AddWithValue("@BalanceLimit", CDbl(txtSatrtBalance.Text))
            .Parameters.AddWithValue("@debcridt", If(CusDebtor.Checked, 1, 0))
            .Parameters.AddWithValue("@Notes", txtAreaName.Text)
            .Parameters.AddWithValue("@Area_ID", CInt(Cmb_Area.SelectedValue))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Private Sub Button13_Click(sender As Object, e As EventArgs) Handles Button13.Click
        Insert_customers()
    End Sub

    Private Sub Button14_Click(sender As Object, e As EventArgs) Handles Button14.Click
        clear_customer()
    End Sub
    Public Sub Load_customer()
        grdcustomers.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from v_customer_area", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            grdcustomers.Rows.Add(connx.rdr("CustomerID").ToString, connx.rdr("CustomerName").ToString, connx.rdr("Address").ToString,
                                  connx.rdr("Mobile").ToString, connx.rdr("Mobile2").ToString, connx.rdr("AREA_NAME").ToString, connx.rdr("AREA_PRICE").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub grdcustomers_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles grdcustomers.CellClick
        txtCustomerID.Text = grdcustomers.CurrentRow.Cells(0).Value
        txtCustomerName.Text = grdcustomers.CurrentRow.Cells(1).Value
        txt_adrs.Text = grdcustomers.CurrentRow.Cells(2).Value
        txtMobile.Text = grdcustomers.CurrentRow.Cells(3).Value
        txtMobile2.Text = grdcustomers.CurrentRow.Cells(4).Value
        Cmb_Area.Text = grdcustomers.CurrentRow.Cells(5).Value
        Button14.Enabled = False
        Button13.Enabled = True
        Button7.Enabled = True
        If e.ColumnIndex = 7 Then
            _customer_id = grdcustomers.CurrentRow.Cells(0).Value
            _servive_fee = grdcustomers.CurrentRow.Cells(6).Value
            With Frm_pos
                .txtDeleveryFee.Text = _servive_fee
            End With
        End If
    End Sub

    Private Sub Button7_Click(sender As Object, e As EventArgs) Handles Button7.Click
        update_customers()
    End Sub
    '&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&DELEVERY&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
    Sub clear_DELEVERY()
        TXT_DELV_ID.Text = connx.GetNextID("TBL_DELEVERYMAN", "DELEVERYMAN_ID").ToString("00000")
        TXT_DELV_NAME.Text = vbNullString
        TXT_DELV_MOB1.Text = vbNullString
        TXT_DELV_MOB2.Text = vbNullString
        TXT_DELV_MOB3.Text = vbNullString
        TXT_DELV_ADDRESS.Text = vbNullString
        TXT_DELV_BALANCE.Text = 0.0
        BUT_DELV_NEW.Enabled = False
        BUT_DELV_SAVE.Enabled = True
        BUT_DELV_EDIT.Enabled = False

    End Sub
    Public Sub Insert_delivery()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into TBL_DELEVERYMAN ( DELEVERYMAN_id,DELEVERYMAN_name,DELEVERYMAN_ADDRESS,DELEVERYMAN_PHONE1,DELEVERYMAN_PHONE2,DELEVERYMAN_BALANCE)
                            values(@DELEVERYMAN_id,@DELEVERYMAN_name,@DELEVERYMAN_ADDRESS,@DELEVERYMAN_PHONE1,@DELEVERYMAN_PHONE2,@DELEVERYMAN_BALANCE)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DELEVERYMAN_id", CInt(TXT_DELV_ID.Text))
            .Parameters.AddWithValue("@DELEVERYMAN_name", TXT_DELV_NAME.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_ADDRESS", TXT_DELV_ADDRESS.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_PHONE1", TXT_DELV_MOB1.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_PHONE2", TXT_DELV_MOB2.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_BALANCE", CDbl(TXT_DELV_BALANCE.Text))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub update_delivery()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "update TBL_DELEVERYMAN set DELEVERYMAN_name=@DELEVERYMAN_name,DELEVERYMAN_ADDRESS=@DELEVERYMAN_ADDRESS,
DELEVERYMAN_PHONE1=@DELEVERYMAN_PHONE1,DELEVERYMAN_PHONE2=@DELEVERYMAN_PHONE2,DELEVERYMAN_BALANCE=@DELEVERYMAN_BALANCE  where DELEVERYMAN_id = @DELEVERYMAN_id"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DELEVERYMAN_id", CInt(TXT_DELV_ID.Text))
            .Parameters.AddWithValue("@DELEVERYMAN_name", TXT_DELV_NAME.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_ADDRESS", TXT_DELV_ADDRESS.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_PHONE1", TXT_DELV_MOB1.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_PHONE2", TXT_DELV_MOB2.Text)
            .Parameters.AddWithValue("@DELEVERYMAN_BALANCE", CDbl(TXT_DELV_BALANCE.Text))
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub Load_delivery()
        DGV_DELV.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from TBL_DELEVERYMAN", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            DGV_DELV.Rows.Add(connx.rdr("DELEVERYMAN_id").ToString, connx.rdr("DELEVERYMAN_name").ToString, connx.rdr("DELEVERYMAN_ADDRESS").ToString,
                                  connx.rdr("DELEVERYMAN_PHONE1").ToString, connx.rdr("DELEVERYMAN_PHONE2").ToString, connx.rdr("DELEVERYMAN_BALANCE").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Private Sub BUT_DELV_SAVE_Click(sender As Object, e As EventArgs) Handles BUT_DELV_SAVE.Click
        Insert_delivery()
    End Sub

    Private Sub BUT_DELV_EDIT_Click(sender As Object, e As EventArgs) Handles BUT_DELV_EDIT.Click
        update_delivery()
        Load_delivery()
    End Sub

    Private Sub TabPage13_Enter(sender As Object, e As EventArgs) Handles TabPage13.Enter
        Load_delivery()
    End Sub

    Private Sub DGV_DELV_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DGV_DELV.CellClick
        TXT_DELV_ID.Text = DGV_DELV.CurrentRow.Cells(0).Value
        TXT_DELV_NAME.Text = DGV_DELV.CurrentRow.Cells(1).Value
        TXT_DELV_ADDRESS.Text = DGV_DELV.CurrentRow.Cells(2).Value
        TXT_DELV_MOB1.Text = DGV_DELV.CurrentRow.Cells(3).Value
        TXT_DELV_MOB2.Text = DGV_DELV.CurrentRow.Cells(4).Value
        TXT_DELV_BALANCE.Text = DGV_DELV.CurrentRow.Cells(5).Value
        BUT_DELV_NEW.Enabled = False
        BUT_DELV_SAVE.Enabled = False
        BUT_DELV_EDIT.Enabled = True
        If e.ColumnIndex = 6 Then
            _drliveryman_id = DGV_DELV.CurrentRow.Cells(0).Value
        End If
    End Sub

    Private Sub txtSearch_TextChanged(sender As Object, e As EventArgs) Handles txtSearch.TextChanged
        grdcustomers.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from v_customer_area where CustomerName like '%" & txtSearch.Text & "%'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            grdcustomers.Rows.Add(connx.rdr("CustomerID").ToString, connx.rdr("CustomerName").ToString, connx.rdr("Address").ToString,
                                  connx.rdr("Mobile").ToString, connx.rdr("Mobile2").ToString, connx.rdr("AREA_NAME").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub TextphonSearche_TextChanged(sender As Object, e As EventArgs) Handles TextphonSearche.TextChanged
        grdcustomers.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("SELECT * FROM v_customer_area WHERE Mobile LIKE '%" & TextphonSearche.Text & "%' OR Mobile2 LIKE '%" & TextphonSearche.Text & "%'", connx.Con)

        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            grdcustomers.Rows.Add(connx.rdr("CustomerID").ToString, connx.rdr("CustomerName").ToString, connx.rdr("Address").ToString,
                                  connx.rdr("Mobile").ToString, connx.rdr("Mobile2").ToString, connx.rdr("AREA_NAME").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub btn_update_Click(sender As Object, e As EventArgs) Handles btn_update.Click

        If txtoldname.Text = "" Or txtoldpass.Text = "" Or txtNewName.Text = "" Or txtNewPass.Text = "" Then
            MessageBox.Show("يرجى ملء جميع الحقول", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        ' التحقق من البيانات القديمة
        Dim isValid As Boolean = False
        connx.cmd = New SqlCommand("SELECT COUNT(*) FROM Tbl_cashier WHERE cashier_name=@oldName AND cashier_password=@oldPass", connx.Con)
        connx.cmd.Parameters.AddWithValue("@oldName", txtoldname.Text)
        connx.cmd.Parameters.AddWithValue("@oldPass", txtoldpass.Text)

        connx.Con.Open()
        Dim count As Integer = CInt(connx.cmd.ExecuteScalar())
        connx.Con.Close()

        If count = 1 Then
            ' تحديث البيانات
            connx.cmd = New SqlCommand("UPDATE Tbl_cashier SET cashier_name=@newName, cashier_password=@newPass WHERE cashier_name=@oldName AND cashier_password=@oldPass", connx.Con)
            connx.cmd.Parameters.AddWithValue("@newName", txtNewName.Text)
            connx.cmd.Parameters.AddWithValue("@newPass", txtNewPass.Text)
            connx.cmd.Parameters.AddWithValue("@oldName", txtoldname.Text)
            connx.cmd.Parameters.AddWithValue("@oldPass", txtoldpass.Text)

            connx.Con.Open()
            connx.cmd.ExecuteNonQuery()
            connx.Con.Close()

            MessageBox.Show("تم تعديل البيانات بنجاح 🧱 ", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            MessageBox.Show("الاسم أو الباسورد القديم غير صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
End Class