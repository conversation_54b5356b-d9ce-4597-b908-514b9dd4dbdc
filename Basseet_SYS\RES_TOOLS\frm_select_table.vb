﻿Imports System.Data.SqlClient
Imports org.apache.xerces.impl

Public Class Frm_Select_Table
    Dim BtnTable As New Button
    Dim connx As New CLS_CON
    Sub loadall_Tables()
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New SqlCommand("Select * from Table_view", connx.Con)
        Dim adp As New SqlDataAdapter(cmd)
        connx.rdr = cmd.ExecuteReader
        While connx.rdr.Read
            BtnTable = New Button
            BtnTable.Width = 195
            BtnTable.Height = 64
            BtnTable.Tag = connx.rdr("Table_Name").ToString
            If CDbl(connx.rdr("Bill")) > 0 Then
                BtnTable.Text = connx.rdr("Table_Name").ToString & vbNewLine & connx.rdr("Bill").ToString
                BtnTable.BackColor = Color.Red
            Else
                BtnTable.Text = connx.rdr("Table_Name").ToString
                BtnTable.BackColor = Color.FromArgb(45, 45, 45)
            End If
            BtnTable.FlatStyle = FlatStyle.Flat
            BtnTable.FlatAppearance.BorderSize = 0
            BtnTable.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            BtnTable.ForeColor = Color.White
            BtnTable.Cursor = Cursors.Hand
            BtnTable.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            BtnTable.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            BtnTable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            FlowLayoutPanel1.Controls.Add(BtnTable)
            AddHandler BtnTable.Click, AddressOf Get_Table_Click1
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Sub LoadOpenTablesWithBills()
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()

        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()

        Dim cmd As New SqlCommand("
        SELECT 
            Table_Name, 
            ISNULL(
                (SELECT SUM(ord_Total)
                 FROM dbo.Order_Tbl
                 WHERE Table_Name = dbo.Table_Tbl.Table_Name 
                   AND ord_Status = 'open'
                 GROUP BY Table_Name), 
            0) AS Bill
        FROM dbo.Table_Tbl
        WHERE 
            ISNULL(
                (SELECT SUM(ord_Total)
                 FROM dbo.Order_Tbl
                 WHERE Table_Name = dbo.Table_Tbl.Table_Name 
                   AND ord_Status = 'open'
                 GROUP BY Table_Name), 
            0) > 0
    ", connx.Con)

        Dim rdr As SqlDataReader = cmd.ExecuteReader()

        While rdr.Read()
            BtnTable = New Button
            BtnTable.Width = 195
            BtnTable.Height = 64
            BtnTable.Tag = rdr("Table_Name").ToString
            BtnTable.Text = rdr("Table_Name").ToString & vbNewLine & rdr("Bill").ToString
            BtnTable.BackColor = Color.Red
            BtnTable.FlatStyle = FlatStyle.Flat
            BtnTable.FlatAppearance.BorderSize = 0
            BtnTable.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            BtnTable.ForeColor = Color.White
            BtnTable.Cursor = Cursors.Hand
            BtnTable.Font = New System.Drawing.Font("Tajawal", 10.0!, FontStyle.Bold)
            BtnTable.TextAlign = ContentAlignment.MiddleCenter
            BtnTable.FlatAppearance.MouseOverBackColor = Color.FromArgb(64, 64, 64)
            FlowLayoutPanel1.Controls.Add(BtnTable)
            AddHandler BtnTable.Click, AddressOf Get_Table_Click1
        End While

        rdr.Close()
        connx.Con.Close()
    End Sub


    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub
    Dim TableName As String
    Public Sub Get_Table_Click1(sender As Object, e As EventArgs)
        TableName = sender.Tag.ToString


        ' تحديث الحقول في النموذج الهدف
        With Frm_pos
            .OrderDate.Value = Today
            .TxtTableName.Text = TableName  ' تغيير النص إلى اسم الطاولة
            .TxtOrder_No.Text = .Get_Order_No ' تغيير النص إلى قيمة أخرى
            .getorder()
            .Load_Order()
        End With

        ' إغلاق النموذج الحالي
        Me.Close()
    End Sub


End Class