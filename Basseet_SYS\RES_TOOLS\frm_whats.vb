﻿Imports System.Diagnostics
Imports System.Web

Public Class frm_whats
    Private Sub btnSendWhatsApp_Click(sender As Object, e As EventArgs) Handles btnSendWhatsApp.Click
        ' قراءة الأرقام
        Dim phoneNumbers As New List(Of String)
        Dim input As String = txtPhoneNumbers.Text.Trim()

        If String.IsNullOrEmpty(input) Then
            MessageBox.Show("يرجى إدخال أرقام الهواتف.")
            Return
        End If

        Dim separators As Char() = {","c, vbCr, vbLf}
        Dim phoneArray As String() = input.Split(separators, StringSplitOptions.RemoveEmptyEntries)

        For Each phone In phoneArray
            Dim cleanedPhone As String = phone.Trim().Replace("+", "").Replace("-", "").Replace(" ", "")
            If Not cleanedPhone.StartsWith("20") Then
                cleanedPhone = "20" & cleanedPhone ' تعديل حسب دولتك
            End If
            phoneNumbers.Add(cleanedPhone)
        Next

        If phoneNumbers.Count = 0 Then
            MessageBox.Show("لم يتم العثور على أرقام هواتف صالحة.")
            Return
        End If

        ' قراءة الرسالة
        Dim message As String = txtMessage.Text.Trim()
        If String.IsNullOrEmpty(message) Then
            message = "السلام عليكم و رحمة الله"
        End If

        ' إرسال لكل رقم
        For Each phone In phoneNumbers
            Dim cleanedPhone As String = phone.Trim().Replace("+", "").Replace("-", "").Replace(" ", "")
            If Not cleanedPhone.StartsWith("20") Then
                cleanedPhone = "20" & cleanedPhone
            End If

            Dim encodedMessage As String = Uri.EscapeDataString(message)
            Dim url As String = $"whatsapp://send?phone={cleanedPhone}&text={encodedMessage}"

            ' تشغيل رابط البروتوكول لفتح WhatsApp Windows App
            Process.Start(url)

            Threading.Thread.Sleep(3000)

            ' تشغيل سكربت AutoHotKey للضغط على Enter
            Process.Start("C:\Users\<USER>\Documents\AutoHotkey\Untitled.ahk")

            Threading.Thread.Sleep(2000)
        Next
        'For Each phone In phoneNumbers
        '    Dim encodedMessage As String = Uri.EscapeDataString(message)
        '    Dim url As String = $"https://wa.me/{phone}?text={encodedMessage}"

        '    Process.Start(url)
        '    Threading.Thread.Sleep(5000) ' وقت كافٍ لتحميل الصفحة وفتحها في التطبيق

        '    ' تشغيل سكربت AutoHotKey اللي بيضغط Enter
        '    Process.Start("C:\Users\<USER>\Documents\AutoHotkey\Untitled.ahk")

        '    Threading.Thread.Sleep(3000) ' تأخير بسيط بين كل رسالة
        'Next


    End Sub


End Class