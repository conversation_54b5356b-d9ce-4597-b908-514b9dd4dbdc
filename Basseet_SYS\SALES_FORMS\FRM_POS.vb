﻿Imports System.Data.SqlClient
Imports System.IO
Imports Basseet_SYS.DocumentR
Imports Newtonsoft.Json
Imports Newtonsoft.Json.Linq
Imports RestSharp
Imports System.ComponentModel
Imports System.Runtime.Serialization.Json
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports Svg
Imports Net.Pkcs11Interop.Common
Imports FastReport.Fonts.TrueTypeFont
Imports System.Xml.Linq
Imports System.Globalization
Imports System.Drawing
Imports System.Drawing.Imaging
Imports ZXing
Imports ZXing.Common
Imports ZXing.QrCode
'******************************************************************************************************************
Public Class Frm_pos
    Dim connx As New CLS_CON
    Dim btncat As New Button
    Dim btnPrd As New Button
    Dim LblName As New Label
    Dim LblPrice As New Label
    Dim picPanel As New Panel
    Dim MainPanel As New Panel
    Dim _filter As String = ""
    Public dt_cuvn As New DataTable
    Public dt_delev As New DataTable
    Sub loadall_cat()
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New SqlCommand(" Select * from Cat_Tbl", connx.Con)
        Dim adp As New SqlDataAdapter(cmd)
        connx.rdr = cmd.ExecuteReader
        While connx.rdr.Read
            btncat = New Button
            btncat.Width = 195
            btncat.Height = 40
            btncat.Text = connx.rdr("CatName").ToString
            btncat.Tag = connx.rdr("Cat_ID").ToString
            btncat.FlatStyle = FlatStyle.Flat
            btncat.FlatAppearance.BorderSize = 0
            btncat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            btncat.BackColor = Color.FromArgb(Val(connx.rdr.GetValue(2)))
            btncat.ForeColor = Color.White
            btncat.Cursor = Cursors.Hand
            btncat.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btncat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btncat.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            FlowLayoutPanel1.Controls.Add(btncat)
            AddHandler btncat.Click, AddressOf filter_click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Public Sub SendFormToPanel(ByVal Sform As Object)

        If Panel11.Controls.Count > 0 Then Panel11.Controls.RemoveAt(0)
        Dim frm As Form = TryCast(Sform, Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        Panel11.Controls.Add(frm)
        Panel11.Tag = frm
        frm.Show()

    End Sub

    Private Sub Frm_pos_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        loadall_cat()
        Load_Items()
        Get_Info()
        Dgv.Parent = Panel5
        Dgv.Dock = DockStyle.Top
        Dgv.Height = 650 ' تعيين ارتفاع للجريد فيو

        ' تعيين Panel الفرعية داخل Panel الرئيسي
        Panel10.Parent = Panel5
        Panel10.Dock = DockStyle.Fill
        If _cashier_status = False Then
            frm_istlam.ShowDialog()
        End If

    End Sub

    Private Sub TxtSearchCat_TextChanged(sender As Object, e As EventArgs) Handles TxtSearchCat.TextChanged
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New SqlCommand(" Select * from Cat_Tbl Where CatName Like '%" & TxtSearchCat.Text & "%'", connx.Con)
        Dim adp As New SqlDataAdapter(cmd)
        connx.rdr = cmd.ExecuteReader
        While connx.rdr.Read
            btncat = New Button
            btncat.Width = 195
            btncat.Height = 40
            btncat.Text = connx.rdr("CatName").ToString
            btncat.Tag = connx.rdr("Cat_ID").ToString
            btncat.FlatStyle = FlatStyle.Flat
            btncat.FlatAppearance.BorderSize = 0
            btncat.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            btncat.BackColor = Color.FromArgb(Val(connx.rdr.GetValue(2)))
            btncat.ForeColor = Color.White
            btncat.Cursor = Cursors.Hand
            btncat.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btncat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btncat.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            FlowLayoutPanel1.Controls.Add(btncat)

            AddHandler btncat.Click, AddressOf filter_click

        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Public Sub Load_Items()
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from item_Tbl", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185
                MainPanel.Height = 200
            Else
                MainPanel.Width = 89 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "سعر:" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 8.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)

            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
                AddHandler LblPrice.Click, AddressOf Select_Click


        End While
        connx.rdr.Close()
        connx.Con.Close()
        TrackBar1.Value = My.Settings.size
    End Sub
    Public Sub Select_Click(sender As Object, e As EventArgs)
        connx._ID = sender.Tag.ToString()
        txt_pr_id.Text = connx._ID
        ' فتح الاتصال مع قاعدة البيانات
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' جلب بيانات الصنف باستخدام ID
        Dim cmd As New SqlCommand("SELECT item_Price FROM item_Tbl WHERE item_ID=@item_ID", connx.Con)
        cmd.Parameters.AddWithValue("@item_ID", connx._ID)

        ' تنفيذ الاستعلام وقراءة النتيجة
        connx.rdr = cmd.ExecuteReader()
        If connx.rdr.Read() Then
            ' تعيين السعر في الحقل النصي

            txt_pr_price.Text = connx.rdr("item_Price").ToString()
        End If

        connx.rdr.Close()
        connx.Con.Close()

        ' فتح النموذج Frm_Qty
        If TxtOrder_No.Text <> "" Then


            With Frm_Qty
                .Show()
            End With
        End If
    End Sub

    Public Sub Load_Items_by_cat()
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from item_tbl where cat_id =@cat_id", connx.Con)
        connx.cmd.Parameters.AddWithValue("@cat_id", _filter)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185
                MainPanel.Height = 200
            Else
                MainPanel.Width = 91 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "السعر :" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)
            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
            AddHandler LblPrice.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Public Sub get_Info_Print()
        ' التأكد من أن الاتصال مغلق وفتحه
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' إنشاء DataTable لتخزين النتائج
        Dim MyDt As New DataTable

        ' إعداد أمر SQL للقراءة من قاعدة البيانات
        Dim cmd As New SqlCommand("SELECT DISTINCT depID as 'القسم', DepName as 'اسم القسم', PrinterName as 'اسم الطابعة', Status as 'الحالة' FROM View_prtBDep WHERE Order_No=@Order_No", connx.Con)
        cmd.Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)

        ' استخدام SqlDataAdapter لملء البيانات في MyDt
        Dim adptr As New SqlDataAdapter(cmd)
        adptr.Fill(MyDt)

        ' تعيين مصدر البيانات مباشرةً إلى DataGridView بناءً على النتائج
        If MyDt.Rows.Count > 0 Then
            Dgv_print.DataSource = MyDt
        Else
            ' إذا لم توجد بيانات، إفراغ DataGridView عن طريق تعيين DataSource إلى Nothing
            Dgv_print.DataSource = Nothing
        End If

        ' إغلاق الاتصال
        connx.Con.Close()
    End Sub
    Public Sub filter_click(sender As Object, e As EventArgs)
        _filter = sender.Tag.ToString()
        'loadall_Items_Button_By_CaTName()'////////////////لو عاوز اعمل الكاتجوري بالبوتون
        Load_Items_by_cat()
    End Sub
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        loadall_Items_Button()
    End Sub

    Private Sub CheckBox1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox1.CheckedChanged
        Load_Items()
    End Sub

    Private Sub TxtSearchPrd_TextChanged(sender As Object, e As EventArgs) Handles TxtSearchPrd.TextChanged
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from item_Tbl where itemname like '%" & TxtSearchPrd.Text & "%'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185 + (2 * TrackBar1.Value)
                MainPanel.Height = 200 + (2 * TrackBar1.Value)
            Else
                MainPanel.Width = 91 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "السعر :" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)
            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
            AddHandler LblPrice.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Sub loadall_Items_Button()
        FlowLayoutPanel2.AutoScroll = True
        FlowLayoutPanel2.Controls.Clear()

        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' Modify the query to join item_Tbl with Cat_Tbl to get the category color
        Dim cmd As New SqlCommand("SELECT i.itemName, i.item_Price,i.item_qun, i.item_ID, c.CatColor FROM item_Tbl i JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID", connx.Con)
        connx.rdr = cmd.ExecuteReader()
        While connx.rdr.Read()
            ' Create item button
            Dim btnPrd As New Button
            btnPrd.Width = 91 + (2 * TrackBar1.Value)
            btnPrd.Height = 100 + (2 * TrackBar1.Value)
            btnPrd.Text = connx.rdr("itemName").ToString() & vbNewLine & "السعر : " & connx.rdr("item_Price").ToString() & vbNewLine & "الكمية : " & connx.rdr("Item_qun").ToString()
            btnPrd.Tag = connx.rdr("item_ID").ToString()
            btnPrd.FlatStyle = FlatStyle.Flat
            btnPrd.FlatAppearance.BorderSize = 0
            btnPrd.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            ' Set the background color of the item button to the category color
            btnPrd.BackColor = Color.FromArgb(Val(connx.rdr("CatColor").ToString()))  ' Assuming Cat_Color is stored as an integer value representing the color
            btnPrd.ForeColor = Color.White
            btnPrd.Cursor = Cursors.Hand
            btnPrd.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btnPrd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btnPrd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer))

            ' Add the button to FlowLayoutPanel
            FlowLayoutPanel2.Controls.Add(btnPrd)
            AddHandler btnPrd.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
        TrackBar1.Value = My.Settings.size
    End Sub
    Sub loadall_Items_Button_By_CaTName()
        FlowLayoutPanel2.AutoScroll = True
        FlowLayoutPanel2.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' تعديل جملة SQL لعرض المنتجات بناءً على فئة محددة
        connx.cmd = New SqlCommand("SELECT i.itemName, i.item_Price, i.item_ID, c.CatColor FROM item_Tbl i JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID WHERE c.Cat_ID = @Cat_ID", connx.Con)
        connx.cmd.Parameters.AddWithValue("@Cat_ID", _filter)

        connx.rdr = connx.cmd.ExecuteReader()

        While connx.rdr.Read()
            ' إنشاء زر لكل منتج
            Dim btnPrd As New Button
            btnPrd.Width = 91 + (2 * TrackBar1.Value)
            btnPrd.Height = 100 + (2 * TrackBar1.Value)
            btnPrd.Text = connx.rdr("itemName").ToString() & vbNewLine & "السعر : " & connx.rdr("item_Price").ToString()
            btnPrd.Tag = connx.rdr("item_ID").ToString()
            btnPrd.FlatStyle = FlatStyle.Flat
            btnPrd.FlatAppearance.BorderSize = 0
            btnPrd.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            ' تعيين لون خلفية الزر حسب لون الفئة
            btnPrd.BackColor = Color.FromArgb(Val(connx.rdr("CatColor").ToString()))
            btnPrd.ForeColor = Color.White
            btnPrd.Cursor = Cursors.Hand
            btnPrd.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btnPrd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btnPrd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer))

            ' إضافة الزر إلى FlowLayoutPanel
            FlowLayoutPanel2.Controls.Add(btnPrd)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Private Sub TrackBar1_Scroll(sender As Object, e As EventArgs) Handles TrackBar1.Scroll

        For Each ctrl In FlowLayoutPanel2.Controls
            ctrl.Width = 180 + (2 * TrackBar1.Value)
            ctrl.Height = 64 + (2 * TrackBar1.Value)
            My.Settings.size = TrackBar1.Value
        Next
        My.Settings.Save()
    End Sub
    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        With frmOrderType
            lblcheck.Text = "0"
            .Show()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.None  ' يعين نمط الفورم
        End With
        clerTXT()
        'With Frm_Select_Table
        '    .loadall_Tables()
        '    .Show()

        '    .TopMost = True  ' يجعل الفورم دائماً في الأعلى
        '    .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
        'End With
    End Sub
    '*********************** Get Order No*******************************

    Public Function Get_Order_No() As String
        Try
            Dim orderDate As String = Now.ToString("yyyyMMdd")
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            Dim cmd As New SqlCommand(" select * from Order_Tbl Where Order_No Like '" & orderDate & "%' order by Order_ID desc", connx.Con)
            connx.rdr = cmd.ExecuteReader
            connx.rdr.Read()
            If connx.rdr.HasRows Then
                Get_Order_No = CLng(connx.rdr("Order_No").ToString) + 1
            Else
                Get_Order_No = orderDate & "0001"
            End If
            connx.rdr.Close()
            connx.Con.Close()
            Return Get_Order_No
        Catch ex As Exception
        End Try
    End Function
    Public Sub getorder()
        Dim found As Boolean
        Dim tno As String
        Dim _FEE As Double = 0
        Dim OrderDate1 As Date
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New SqlCommand(" select * from Order_Tbl Where Table_Name Like '" & TxtTableName.Text & "' and ord_Status='Open'", connx.Con)
        connx.rdr = cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            found = True
            tno = connx.rdr("Order_No").ToString
            OrderDate1 = connx.rdr("OrderDate").ToString
            _FEE = connx.rdr("SERVICE_PRICE").ToString
        Else
            found = False
            tno = Get_Order_No()
        End If
        connx.rdr.Close()
        connx.Con.Close()
        If found = True Then
            TxtOrder_No.Text = tno
            OrderDate.Value = OrderDate1
            txtDeleveryFee.Text = _FEE
        Else

            TxtOrder_No.Text = tno
            OrderDate.Value = Today
        End If

        Load_Order()
        order_total()
    End Sub
    '"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
    Public Sub Load_Order()
        Dgv.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Select * from View_Order Where Order_No like '" & TxtOrder_No.Text & "'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            Dgv.Rows.Add(connx.rdr("Order_ID").ToString, connx.rdr("item_ID").ToString, connx.rdr("itemName").ToString, connx.rdr("ord_Price").ToString, connx.rdr("ord_Qty").ToString, connx.rdr("ord_Total").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()


    End Sub
    Private Sub Label4_Click_1(sender As Object, e As EventArgs) Handles Label4.Click
        Get_Order_No()
    End Sub
    Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
        ' تحقق أن المستخدم نقر على خلية وليس على رأس العمود أو الصف
        If e.RowIndex < 0 Then Exit Sub

        ' إضافة صنف (زيادة الكمية)
        If e.ColumnIndex = 6 Then
            Dim CmdUpdate2 As New SqlCommand
            With CmdUpdate2
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
                .Parameters.AddWithValue("@Order_ID", Dgv.Rows(e.RowIndex).Cells(0).Value)
                .Parameters.AddWithValue("@ord_Qty", 1)
                .Parameters.AddWithValue("@Item_ID", Dgv.Rows(e.RowIndex).Cells(1).Value)
            End With
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate2.ExecuteNonQuery()
            connx.Con.Close()
            getorder()

        End If

        ' تحديث الإجمالي
        Dim CmdUpdate As New SqlCommand
        With CmdUpdate
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate.ExecuteNonQuery()
        connx.Con.Close()
        getorder()


        ' تقليل الكمية (إنقاص الكمية)
        If e.ColumnIndex = 7 Then
            If Dgv.Rows(e.RowIndex).Cells(4).Value > 1 Then
                Dim CmdUpdate2 As New SqlCommand
                With CmdUpdate2
                    .Connection = connx.Con
                    .CommandType = CommandType.Text
                    .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty - @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
                    .Parameters.AddWithValue("@Order_ID", Dgv.Rows(e.RowIndex).Cells(0).Value)
                    .Parameters.AddWithValue("@ord_Qty", 1)
                    .Parameters.AddWithValue("@Item_ID", Dgv.Rows(e.RowIndex).Cells(1).Value)
                End With
                If connx.Con.State = 1 Then connx.Con.Close()
                connx.Con.Open()
                CmdUpdate2.ExecuteNonQuery()
                connx.Con.Close()
                getorder()
            End If
        End If

        ' تحديث الإجمالي
        Dim CmdUpdate1 As New SqlCommand
        With CmdUpdate1
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate1.ExecuteNonQuery()
        connx.Con.Close()
        getorder()

        ' حذف العنصر
        If e.ColumnIndex = 8 Then
            If MessageBox.Show("هل أنت متأكد من الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then
                del_(e.RowIndex)
            Else
                Exit Sub
            End If
            getorder()
        End If

    End Sub

    ' الإجراء الخاص بحذف الصف
    Sub del_(rowIndex As Integer)
        connx.Con.Open()
        Dim cmd As New SqlCommand("Delete from Order_Tbl where Order_ID=@Order_ID AND Item_ID=@Item_ID", connx.Con)
        cmd.Parameters.AddWithValue("@Order_ID", Dgv.Rows(rowIndex).Cells(0).Value)
        cmd.Parameters.AddWithValue("@Item_ID", Dgv.Rows(rowIndex).Cells(1).Value)
        cmd.ExecuteNonQuery()
        connx.Con.Close()

        ' التحقق إذا كانت جميع الصفوف قد تم مسحها
        If Dgv.Rows.Count - 1 = 0 Then
            MessageBox.Show("تم مسح جميع الصفوف!")
            clerTXT()
        End If

    End Sub

    'Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
    '    If e.ColumnIndex = 6 Then
    '        '******************** Add One***************************
    '        Dim CmdUpdate2 As New SqlCommand
    '        With CmdUpdate2
    '            .Connection = connx.Con
    '            .CommandType = CommandType.Text
    '            .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
    '            .Parameters.AddWithValue("@Order_ID", Dgv.CurrentRow.Cells(0).Value)
    '            .Parameters.AddWithValue("@ord_Qty", 1)
    '            .Parameters.AddWithValue("@Item_ID", Dgv.CurrentRow.Cells(1).Value)
    '        End With
    '        If connx.Con.State = 1 Then connx.Con.Close()
    '        connx.Con.Open()
    '        CmdUpdate2.ExecuteNonQuery()
    '        connx.Con.Close()
    '        getorder()
    '        Load_Order()
    '    End If
    '    '*********************************************اضافة المجموع الى الجدول**************************
    '    Dim CmdUpdate As New SqlCommand
    '    With CmdUpdate
    '        .Connection = connx.Con
    '        .CommandType = CommandType.Text
    '        .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_NO"
    '        .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
    '    End With
    '    If connx.Con.State = 1 Then connx.Con.Close()
    '    connx.Con.Open()
    '    CmdUpdate.ExecuteNonQuery()
    '    connx.Con.Close()
    '    'Load_Order()
    '    getorder()
    '    Load_Order()
    '    If e.ColumnIndex = 7 Then
    '        If Dgv.CurrentRow.Cells(4).Value > 1 Then
    '            '******************** min One***************************

    '            Dim CmdUpdate2 As New SqlCommand
    '            With CmdUpdate2
    '                .Connection = connx.Con
    '                .CommandType = CommandType.Text
    '                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty - @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
    '                .Parameters.AddWithValue("@Order_ID", Dgv.CurrentRow.Cells(0).Value)
    '                .Parameters.AddWithValue("@ord_Qty", 1)
    '                .Parameters.AddWithValue("@Item_ID", Dgv.CurrentRow.Cells(1).Value)
    '            End With
    '            If connx.Con.State = 1 Then connx.Con.Close()
    '            connx.Con.Open()
    '            CmdUpdate2.ExecuteNonQuery()
    '            connx.Con.Close()
    '            getorder()
    '            Load_Order()
    '        End If
    '    End If
    '    '*********************************************اضافة المجموع الى الجدول**************************

    '    If e.ColumnIndex = 8 Then
    '        If MessageBox.Show("هل أنت متأكد من الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then
    '            del_()
    '        Else
    '            Exit Sub
    '        End If

    '    End If
    '    Load_Order()
    'End Sub
    'Sub del_()
    '    connx.Con.Open()
    '    Dim cmd As New SqlCommand(" Delete from Order_Tbl where Order_ID=" & Dgv.CurrentRow.Cells(0).Value & " AND Item_ID=" & Dgv.CurrentRow.Cells(1).Value & " ", connx.Con)
    '    ' التحقق إذا كانت جميع الصفوف قد تم مسحها

    '    cmd.ExecuteNonQuery()
    '    connx.Con.Close()

    '    If Dgv.Rows.Count = 0 Then
    '        ' تنفيذ الإجراء المطلوب
    '        MessageBox.Show("تم مسح جميع الصفوف!")
    '        clerTXT() 
    '    End If

    'End Sub

    ''''''Sub order_total()
    ''''''    TxtCount.Text = Dgv.Rows.Count
    ''''''    Try
    ''''''        If My.Settings.Tax_Status = True Then
    ''''''            connx.Get_Tax_Value()
    ''''''        Else
    ''''''            _Tax_VALUE = 0.0
    ''''''        End If
    ''''''        Dim Total1, _Total_Tax As Double
    ''''''        For Each row As DataGridViewRow In Dgv.Rows
    ''''''            Total1 += row.Cells(5).Value
    ''''''        Next
    ''''''        TxtTotal.Text = Total1
    ''''''        _Total_Tax = Total1 * _Tax_VALUE / 100
    ''''''        LblTax.Text = _Total_Tax
    ''''''        TxtFinalTotal.Text = Total1 + _Total_Tax + txtDeleveryFee.Text
    ''''''        TxtCount.Text = Dgv.Rows.Count
    ''''''    Catch ex As Exception

    ''''''    End Try
    ''''''End Sub
    Public Sub order_total()
        TxtCount.Text = Dgv.Rows.Count
        Try
            If My.Settings.Tax_Status = True Then
                connx.Get_Tax_Value()
            Else
                _Tax_VALUE = 0.0
            End If
            Dim Total1 As Decimal = 0D, _Total_Tax As Decimal = 0D
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Total1 += Convert.ToDecimal(row.Cells(5).Value)
                End If
            Next
            TxtTotal.Text = Total1
            _Total_Tax = Total1 * _Tax_VALUE / 100
            LblTax.Text = _Total_Tax
            TxtFinalTotal.Text = Total1 + _Total_Tax + Convert.ToDecimal(txtDeleveryFee.Text)
            TxtCount.Text = Dgv.Rows.Count
        Catch ex As Exception
            ' معالجة الخطأ إذا حدث
        End Try
    End Sub
    Private Sub BtnPaid_Click(sender As Object, e As EventArgs) Handles BtnPaid.Click
        get_Info_Print()
        With frm_paid
            _Order_No = TxtOrder_No.Text
            _finalTotal = Val(TxtFinalTotal.Text)
            _TaxTotal = Val(LblTax.Text)
            _Order_Total = Val(TxtTotal.Text)
            .TxtFinalTotal_.Text = TxtFinalTotal.Text
            .TxtDiscount.Text = "0.0"
            connx.Get_Tax_Value()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
            .Show()
        End With
    End Sub

    Public Sub Get_Info()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand(" Select * from comSetting_Tbl ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then

            _CompanyName = connx.rdr("CompanyName").ToString
            _vat_No = connx.rdr("Vat_No").ToString
        End If

        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    '**********************************************************************الايصال الالكتروني المصري*********************************************


    Public Function CreateReceiptJson() As String
        Try
            ' تحقق من تهيئة الكائنات الأساسية
            If OrderDate Is Nothing Then
                MessageBox.Show("OrderDate is not initialized.")
                Return String.Empty
            End If

            If TxtOrder_No Is Nothing Then
                MessageBox.Show("TxtOrder_No is not initialized.")
                Return String.Empty
            End If

            If AddressISSUR Is Nothing Then
                MessageBox.Show("AddressISSUR is not initialized.")
                Return String.Empty
            End If

            If receiver1 Is Nothing Then
                MessageBox.Show("receiver1 is not initialized.")
                Return String.Empty
            End If

            ' إعداد الهيدر
            Dim header1 As New Dictionary(Of String, Object) From {
            {"dateTimeIssued", OrderDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")},
            {"receiptNumber", "ERece-" & TxtOrder_No.Text},
            {"uuid", ""},
            {"previousUUID", My.Settings.previousUUID},
            {"referenceOldUUID", ""},
            {"currency", "EGP"},
            {"exchangeRate", 0.0},
            {"sOrderNameCode", ""},
            {"orderdeliveryMode", "FC"}
        }

            ' إعداد documentType
            Dim documentType1 As New Dictionary(Of String, Object) From {
            {"receiptType", "SC"},
            {"typeVersion", "1.2"}
        }

            ' إعداد معلومات المرسل (seller)
            Dim branchAddress1 As New Dictionary(Of String, Object) From {
            {"country", "EG"},
            {"governate", If(AddressISSUR.governate, "")},
            {"regionCity", If(AddressISSUR.regionCity, "")},
            {"street", If(AddressISSUR.street, "")},
            {"buildingNumber", If(AddressISSUR.buildingNumber, "")},
            {"postalCode", If(AddressISSUR.postalCode, "")},
            {"floor", If(AddressISSUR.floor, "")},
            {"room", If(AddressISSUR.room, "")},
            {"landmark", If(AddressISSUR.landmark, "")},
            {"additionalInformation", If(AddressISSUR.additionalInformation, "")}
        }

            Dim seller1 As New Dictionary(Of String, Object) From {
            {"rin", My.Settings.RegistrationNumber},
            {"companyTradeName", If(_CompanyName, "")},
            {"branchCode", If(AddressISSUR.branchID, "")},
            {"branchAddress", branchAddress1},
            {"deviceSerialNumber", My.Settings.serialDevice},
            {"activityCode", My.Settings.activitycode}
        }

            Dim Buyer1 As New Dictionary(Of String, Object) From {
            {"type", If(receiver1.type, "")},
            {"id", If(receiver1.id, "")},
            {"name", If(receiver1.name, "")},
            {"mobileNumber", ""},
            {"paymentNumber", ""}
        }

            ' إعداد العناصر (المنتجات)
            Dim itemData As New List(Of Dictionary(Of String, Object))
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Dim item As New Dictionary(Of String, Object) From {
                    {"internalCode", If(row.Cells(0).Value IsNot Nothing, row.Cells(0).Value.ToString(), "")},
                    {"description", If(row.Cells(2).Value IsNot Nothing, row.Cells(2).Value.ToString(), "")},
                    {"itemType", "GS1"},
                    {"itemCode", "037000401629"},
                    {"unitType", "EA"},
                   {"quantity", If(Decimal.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDecimal(row.Cells(4).Value), 0D)},
                    {"unitPrice", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value), 0.0)},
                    {"netSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value), 0.0)},
                    {"totalSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11, 0.0)},
                    {"total", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11 * 1.024, 0.0)},
                    {"commercialDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 867.86}, {"description", "XYZ"}, {"rate", 2.3}}
                    }},
                    {"itemDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "ABC"}, {"rate", 2.3}},
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "XYZ"}, {"rate", 4.0}}
                    }},
                    {"additionalCommercialDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "ABC"}, {"rate", 10.0}
                    }},
                    {"additionalItemDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "XYZ"}, {"rate", 10.0}
                    }},
                    {"valueDifference", 20},
                    {"taxableItems", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}, {"subType", "V009"}, {"rate", 14}}
                    }}
                }
                    itemData.Add(item)
                End If
            Next

            ' تجميع الهيكل الكامل للإيصال
            Dim receipt As New Dictionary(Of String, Object) From {
            {"header", header1},
            {"documentType", documentType1},
            {"seller", seller1},
            {"buyer", Buyer1},
            {"itemData", itemData},
            {"totalSales", 8678.6},
            {"totalCommercialDiscount", 867.86},
            {"totalItemsDiscount", 20},
            {"extraReceiptDiscountData", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"amount", 0}, {"description", "ABC"}}
            }},
            {"netAmount", 7810.74},
            {"feesAmount", 0},
            {"totalAmount", 8887.0436},
            {"taxTotals", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}}
            }},
            {"paymentMethod", "C"},
            {"adjustment", 0}
        }

            ' تجميع القائمة النهائية
            Dim finalReceipt As New Dictionary(Of String, Object) From {
            {"receipts", New List(Of Dictionary(Of String, Object)) From {receipt}}
        }

            ' تحويل إلى JSON
            Dim json As String = JsonConvert.SerializeObject(finalReceipt, Formatting.Indented)
            Return json

        Catch ex As Exception
            MessageBox.Show("Error creating JSON: " & ex.Message)
            Return String.Empty
        End Try
    End Function


    Public Sub SaveJsonToFile()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJson()

        ' تحديد مسار الملف
        Dim filePath As String = "D:\Basseet_SYS -ok\design\EGY_RECIPT\EInvoicing\SourceDocumentJson.json"

        ' التحقق من وجود المجلد، إذا لم يكن موجودًا، نقوم بإنشائه
        Dim folderPath As String = Path.GetDirectoryName(filePath)
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If

        ' حفظ ملف JSON على القرص
        File.WriteAllText(filePath, receiptJson)

        ' إعلام المستخدم بنجاح العملية
        MessageBox.Show("تم حفظ الإيصال كملف JSON في: " & filePath)
    End Sub


    Public Sub SendReceipt()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJson()

        ' إعداد RestClient
        Dim client As New RestClient("https://api.invoice-system.com/sendReceipt") ' تأكد من تعديل الرابط ليتناسب مع API الإيصالات
        Dim request As New RestRequest(Method.POST)
        request.AddHeader("Authorization", "Bearer your_token_here") ' ضع التوكن الخاص بك هنا
        request.AddHeader("Content-Type", "application/json")
        request.AddParameter("application/json", receiptJson, ParameterType.RequestBody)

        ' إرسال الطلب
        Dim response As IRestResponse = client.Execute(request)

        ' معالجة الرد
        If response.IsSuccessful Then
            MessageBox.Show("تم إرسال الإيصال بنجاح!")
        Else
            MessageBox.Show("حدث خطأ أثناء إرسال الإيصال: " & response.Content)
        End If
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        CreateReceiptJson()
        SaveJsonToFile()
        '''' ConvertFullContent()
        click_on_bat()
        System.Threading.Thread.Sleep(3000)
        ProcessReceiptFile()
        ConvertFileToSHA256()
        SaveJsonToFileFinal()
    End Sub
    'Public Sub ConvertFullContent()
    '    ' Load data from the file
    '    Dim jsonData As String = File.ReadAllText("D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt.json", System.Text.Encoding.UTF8)

    '    ' Parse the JSON text
    '    Dim data As JObject = JObject.Parse(jsonData)

    '    ' Initialize a StringBuilder for the output
    '    Dim result As New StringBuilder()

    '    ' Process each receipt
    '    For Each receipt As JObject In data("receipts")
    '        ProcessJsonObject(receipt, result)
    '        result.AppendLine() ' Add a newline after each receipt
    '    Next

    '    ' Save the resulting file
    '    Dim outputPath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt2.json"
    '    File.WriteAllText(outputPath, result.ToString(), System.Text.Encoding.UTF8)

    '    Console.WriteLine("Data converted successfully.")
    'End Sub

    ' Recursive function to process each JSON object
    Private Sub ProcessJsonObject(jObject As JObject, result As StringBuilder)
        For Each property1 In jObject.Properties()
            If property1.Value.Type = JTokenType.Object Then
                result.Append("""" & property1.Name & """")
                ProcessJsonObject(property1.Value, result)
            ElseIf property1.Value.Type = JTokenType.Array Then
                result.Append("""" & property1.Name & """: [")
                For Each item As JObject In property1.Value
                    ProcessJsonObject(item, result)
                Next
                result.Append("],")
            Else
                result.Append("""" & property1.Name & """")
                result.Append("""" & property1.Value.ToString() & """")
                result.AppendLine()
            End If
        Next
    End Sub


    Public Sub ConvertFileToSHA256()
        ' قراءة محتوى الملف كـ نص
        Dim filePath As String = "D:\Basseet_SYS -ok\EGY_RECIPT\receipt3.json" ' ضع مسار الملف هنا
        Dim fileContent As String = File.ReadAllText(filePath, Encoding.UTF8)

        ' تحويل النص إلى SHA-256
        Dim sha256Hash As String = GetSHA256Hash(fileContent)

        ' عرض أو حفظ التجزئة
        Console.WriteLine("SHA-256 Hash: " & sha256Hash)
        Dim xxz As String
        xxz = sha256Hash
        Txthash.Text = xxz
    End Sub

    Private Function GetSHA256Hash(input As String) As String
        ' إنشاء كائن SHA-256
        Using sha256 As SHA256 = SHA256.Create()
            ' تحويل النص إلى بايتات وتجزئته
            Dim bytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(input))

            ' تحويل البايتات إلى سلسلة نصية بالنظام السادس عشر
            Dim sb As New StringBuilder()
            For Each b As Byte In bytes
                sb.Append(b.ToString("x2"))
            Next
            Return sb.ToString()
        End Using
    End Function
    Public Function CreateReceiptJsonfinal() As String
        Try
            ' تحقق من تهيئة الكائنات الأساسية
            If OrderDate Is Nothing Then
                MessageBox.Show("OrderDate is not initialized.")
                Return String.Empty
            End If

            If TxtOrder_No Is Nothing Then
                MessageBox.Show("TxtOrder_No is not initialized.")
                Return String.Empty
            End If

            If AddressISSUR Is Nothing Then
                MessageBox.Show("AddressISSUR is not initialized.")
                Return String.Empty
            End If

            If receiver1 Is Nothing Then
                MessageBox.Show("receiver1 is not initialized.")
                Return String.Empty
            End If

            ' إعداد الهيدر
            Dim header1 As New Dictionary(Of String, Object) From {
            {"dateTimeIssued", OrderDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")},
            {"receiptNumber", "ERece-" & TxtOrder_No.Text},
            {"uuid", Txthash.Text},
            {"previousUUID", My.Settings.previousUUID},
            {"referenceOldUUID", ""},
            {"currency", "EGP"},
            {"exchangeRate", 0.0},
            {"sOrderNameCode", ""},
            {"orderdeliveryMode", "FC"}
        }

            ' إعداد documentType
            Dim documentType1 As New Dictionary(Of String, Object) From {
            {"receiptType", "SC"},
            {"typeVersion", "1.2"}
        }

            ' إعداد معلومات المرسل (seller)
            Dim branchAddress1 As New Dictionary(Of String, Object) From {
            {"country", "EG"},
            {"governate", If(AddressISSUR.governate, "")},
            {"regionCity", If(AddressISSUR.regionCity, "")},
            {"street", If(AddressISSUR.street, "")},
            {"buildingNumber", If(AddressISSUR.buildingNumber, "")},
            {"postalCode", If(AddressISSUR.postalCode, "")},
            {"floor", If(AddressISSUR.floor, "")},
            {"room", If(AddressISSUR.room, "")},
            {"landmark", If(AddressISSUR.landmark, "")},
            {"additionalInformation", If(AddressISSUR.additionalInformation, "")}
        }

            Dim seller1 As New Dictionary(Of String, Object) From {
            {"rin", My.Settings.RegistrationNumber},
            {"companyTradeName", If(_CompanyName, "")},
            {"branchCode", If(AddressISSUR.branchID, "")},
            {"branchAddress", branchAddress1},
            {"deviceSerialNumber", My.Settings.serialDevice},
            {"activityCode", My.Settings.activitycode}
        }

            Dim Buyer1 As New Dictionary(Of String, Object) From {
            {"type", If(receiver1.type, "")},
            {"id", If(receiver1.id, "")},
            {"name", If(receiver1.name, "")},
            {"mobileNumber", ""},
            {"paymentNumber", ""}
        }

            ' إعداد العناصر (المنتجات)
            Dim itemData As New List(Of Dictionary(Of String, Object))
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Dim item As New Dictionary(Of String, Object) From {
                    {"internalCode", If(row.Cells(0).Value IsNot Nothing, row.Cells(0).Value.ToString(), "")},
                    {"description", If(row.Cells(2).Value IsNot Nothing, row.Cells(2).Value.ToString(), "")},
                    {"itemType", "GS1"},
                    {"itemCode", "037000401629"},
                    {"unitType", "EA"},
                    {"quantity", If(Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToInt32(row.Cells(4).Value), 0)},
                    {"unitPrice", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value), 0.0)},
                    {"netSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value), 0.0)},
                    {"totalSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11, 0.0)},
                    {"total", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11 * 1.024, 0.0)},
                    {"commercialDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 867.86}, {"description", "XYZ"}, {"rate", 2.3}}
                    }},
                    {"itemDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "ABC"}, {"rate", 2.3}},
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "XYZ"}, {"rate", 4.0}}
                    }},
                    {"additionalCommercialDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "ABC"}, {"rate", 10.0}
                    }},
                    {"additionalItemDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "XYZ"}, {"rate", 10.0}
                    }},
                    {"valueDifference", 20},
                    {"taxableItems", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}, {"subType", "V009"}, {"rate", 14}}
                    }}
                }
                    itemData.Add(item)
                End If
            Next

            ' تجميع الهيكل الكامل للإيصال
            Dim receipt As New Dictionary(Of String, Object) From {
            {"header", header1},
            {"documentType", documentType1},
            {"seller", seller1},
            {"buyer", Buyer1},
            {"itemData", itemData},
            {"totalSales", 8678.6},
            {"totalCommercialDiscount", 867.86},
            {"totalItemsDiscount", 20},
            {"extraReceiptDiscountData", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"amount", 0}, {"description", "ABC"}}
            }},
            {"netAmount", 7810.74},
            {"feesAmount", 0},
            {"totalAmount", 8887.0436},
            {"taxTotals", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}}
            }},
            {"paymentMethod", "C"},
            {"adjustment", 0}
        }

            ' تجميع القائمة النهائية
            Dim finalReceipt As New Dictionary(Of String, Object) From {
            {"receipts", New List(Of Dictionary(Of String, Object)) From {receipt}}
        }

            ' تحويل إلى JSON
            Dim json As String = JsonConvert.SerializeObject(finalReceipt, Formatting.Indented)
            Return json

        Catch ex As Exception
            MessageBox.Show("Error creating JSON: " & ex.Message)
            Return String.Empty
        End Try
    End Function
    Public Sub SaveJsonToFileFinal()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJsonfinal()

        ' تحديد مسار الملف
        Dim filePath As String = "D:\Basseet_SYS -ok\EGY_RECIPT\receiptf.json"

        ' التحقق من وجود المجلد، إذا لم يكن موجودًا، نقوم بإنشائه
        Dim folderPath As String = Path.GetDirectoryName(filePath)
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If

        ' حفظ ملف JSON على القرص
        File.WriteAllText(filePath, receiptJson)

        '' إعلام المستخدم بنجاح العملية
        'MessageBox.Show("تم حفظ الإيصال كملف JSON في: " & filePath)
    End Sub

    'Public Function FormatReceiptData(filePath As String) As String
    '    Try
    '        ' قراءة محتوى الملف
    '        Dim input As String = File.ReadAllText(filePath)

    '        ' إزالة الرموز غير المرغوب فيها وتوحيد السطور
    '        Dim lines = input.Split(New String() {Environment.NewLine}, StringSplitOptions.RemoveEmptyEntries)
    '        Dim result = String.Join(" ", lines)

    '        ' إزالة "RECEIPTS""RECEIPTS" من بداية النص
    '        If result.StartsWith("""RECEIPTS""""RECEIPTS""") Then
    '            result = result.Substring("""RECEIPTS""""RECEIPTS""".Length)
    '        End If

    '        ' معالجة باقي النص كما هو
    '        Dim fields As String() = Regex.Split(result, """(?=\w)")
    '        Dim formattedResult As New StringBuilder()

    '        For Each field As String In fields
    '            If Not String.IsNullOrEmpty(field) Then
    '                formattedResult.Append($"""{field}""")
    '            End If
    '        Next

    '        Return formattedResult.ToString()

    '    Catch ex As FileNotFoundException
    '        Return "Error: File not found"
    '    Catch ex As Exception
    '        Return "Error processing file: " & ex.Message
    '    End Try
    'End Function
    Public Function RemoveReceiptsHeader(input As String) As String
        Try
            ' إزالة "RECEIPTS""RECEIPTS" من النص
            Return input.Replace("""RECEIPTS""""RECEIPTS""", "")
        Catch ex As Exception
            Return "Error: " & ex.Message
        End Try
    End Function

    ' مثال على الاستخدام
    Public Sub ProcessReceiptFile()
        '    ' مسار الملف - قم بتغييره حسب موقع الملف لديك
        '    Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\EInvoicing\CanonicalString.txt"

        '    ' معالجة الملف
        '    Dim formattedData = FormatReceiptData(filePath)

        '    ' طباعة النتيجة
        '    Console.WriteLine(formattedData)

        '    ' حفظ النتيجة في ملف جديد (اختياري)
        '    Dim outputPath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt3.json"
        '    File.WriteAllText(outputPath, formattedData)
        'End Sub
        Try
            ' قراءة النص من ملف
            Dim filePath As String = "D:\Basseet_SYS -ok\EGY_RECIPT\EInvoicing\CanonicalString.txt"
            Dim fileContent As String = System.IO.File.ReadAllText(filePath)

            ' استدعاء الدالة
            Dim result As String = RemoveReceiptsHeader(fileContent)

            ' حفظ النتيجة في ملف جديد
            System.IO.File.WriteAllText("D:\Basseet_SYS -ok\EGY_RECIPT\receipt3.json", result)

            'MessageBox.Show("معالجة إرسال الإيصال")

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        End Try
    End Sub
    Sub click_on_bat()
        Try
            ' تحديد مسار الملف
            Dim batFilePath As String = "D:\Basseet_SYS -ok\EGY_RECIPT\EInvoicing\SubmitInvoices.bat"

            ' التحقق من وجود الملف
            If System.IO.File.Exists(batFilePath) Then
                Process.Start(batFilePath)
                System.Threading.Thread.Sleep(3000)
                'MessageBox.Show("تم تشغيل الملف بنجاح")
            Else
                MessageBox.Show("الملف غير موجود!")
            End If

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        End Try
    End Sub

    Sub clerTXT()
        TxtCount.Text = 0.0
        TxtTotal.Text = 0.0
        TxtFinalTotal.Text = 0.0
        txtDiscountV.Text = 0.0
        LblTax.Text = 0.0
        txtDeleveryFee.Text = 0.0
    End Sub

    Private Sub istlam_Click(sender As Object, e As EventArgs) Handles istlam.Click
        With frm_istlam
            .txt_total.Text = 0.0
            '''''''''''''''''''''''''''''.txt_cashierBalance.Text = _cashier_balance
            .ShowDialog()
        End With
    End Sub

    Private Sub taslim_Click(sender As Object, e As EventArgs) Handles taslim.Click
        With frm_taslim
            .get_cash()
            .get_visa()
            .get_start()
            .txt_total_total.Text = Val(.txt_total_cash.Text) + Val(.txt_total_visa.Text) + Val(.txt_start.Text)
            .ShowDialog()
        End With
    End Sub
    Public Sub ExportInvoiceToUBL()
        Try
            ' Namespaces
            Dim ns As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            Dim cac As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            Dim cbc As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"

            ' البيانات الأساسية
            Dim orderId As String = TxtOrder_No.Text.Trim()
            Dim issueDate As String = OrderDate.Value.ToString("yyyy-MM-dd")
            Dim issueTime As String = DateTime.Now.ToString("HH:mm:ss")
            Dim currency As String = "SAR"
            Dim lineCount As Integer = Dgv.Rows.Count

            If String.IsNullOrWhiteSpace(orderId) OrElse lineCount = 0 Then
                MessageBox.Show("لا توجد بيانات فاتورة لتصديرها.")
                Exit Sub
            End If

            ' الإجماليات
            Dim totalBeforeTax As Decimal = 0, taxValue As Decimal = 0
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Dim price As Decimal = CDec(row.Cells(3).Value)
                    Dim qty As Decimal = CDec(row.Cells(4).Value)
                    totalBeforeTax += price * qty
                    taxValue += Math.Round(price * qty * 0.15D, 2)
                End If
            Next
            Dim totalWithTax As Decimal = totalBeforeTax + taxValue

            ' QR و PIH
            Dim qrData As String = GenerateZATCA_QR_Base64(_CompanyName, _vat_No, issueDate, totalWithTax, taxValue)
            Dim qrImage As Bitmap = GenerateQRCodeImage(qrData)
            Dim qrBase64 As String = ImageToBase64(qrImage)
            Dim uuid As String = Guid.NewGuid().ToString()

            Dim previousInvoiceHash As String = My.Settings.previousUUID()

            ' عناصر البنود
            Dim invoiceLines As New List(Of XElement)
            For i As Integer = 0 To Dgv.Rows.Count - 1
                Dim row = Dgv.Rows(i)
                If row.IsNewRow Then Continue For
                Dim itemName As String = row.Cells(2).Value?.ToString()
                Dim price As Decimal = CDec(row.Cells(3).Value)
                Dim qty As Decimal = CDec(row.Cells(4).Value)
                Dim total As Decimal = Math.Round(price * qty, 2)
                Dim taxAmount As Decimal = Math.Round(total * 0.15D, 2)

                invoiceLines.Add(
                New XElement(cac + "InvoiceLine",
                    New XElement(cbc + "ID", (i + 1).ToString()),
                    New XElement(cbc + "InvoicedQuantity", qty),
                    New XElement(cbc + "LineExtensionAmount", New XAttribute("currencyID", currency), total),
                    New XElement(cac + "TaxTotal",
                        New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxAmount),
                        New XElement(cac + "TaxSubtotal",
                            New XElement(cbc + "TaxableAmount", New XAttribute("currencyID", currency), total),
                            New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxAmount),
                            New XElement(cac + "TaxCategory",
                                New XElement(cbc + "ID", "S"),
                                New XElement(cbc + "Percent", "15"),
                                New XElement(cac + "TaxScheme", New XElement(cbc + "ID", "VAT"))
                            )
                        )
                    ),
                    New XElement(cac + "Item", New XElement(cbc + "Name", itemName)),
                    New XElement(cac + "Price",
                        New XElement(cbc + "PriceAmount", New XAttribute("currencyID", currency), price),
                        New XElement(cbc + "BaseQuantity", "1.00")
                    )
                )
            )
            Next

            ' إنشاء عنصر الفاتورة
            Dim invoice As XElement =
            New XElement(ns + "Invoice",
                New XAttribute(XNamespace.Xmlns + "cac", cac),
                New XAttribute(XNamespace.Xmlns + "cbc", cbc),
                New XElement(cbc + "UBLVersionID", "2.1"),
                New XElement(cbc + "CustomizationID", "urn:cen.eu:en16931:2017#conformant#urn:sdsa:Invoice:sa:CI_1.0"),
                New XElement(cbc + "ProfileID", "simplified:reporting:1.0"),
                New XElement(cbc + "ID", orderId),
                New XElement(cbc + "UUID", uuid),
                New XElement(cbc + "IssueDate", issueDate),
                New XElement(cbc + "IssueTime", issueTime),
                New XElement(cbc + "InvoiceTypeCode", New XAttribute("name", "0200000"), "388"),
                New XElement(cbc + "DocumentCurrencyCode", currency),
                New XElement(cbc + "TaxCurrencyCode", currency),
                New XElement(cbc + "LineCountNumeric", lineCount),
                New XElement(cac + "AdditionalDocumentReference",
                    New XElement(cbc + "ID", "PIH"),
                    New XElement(cac + "Attachment",
                        New XElement(cbc + "EmbeddedDocumentBinaryObject",
                            New XAttribute("mimeCode", "text/plain"),
                            New XAttribute("encodingCode", "Base64"),
                            Convert.ToBase64String(Encoding.UTF8.GetBytes(previousInvoiceHash))
                        )
                    )
                ),
                New XElement(cac + "AccountingSupplierParty",
                    New XElement(cac + "Party",
                        New XElement(cac + "PartyIdentification",
                            New XElement(cbc + "ID", New XAttribute("schemeID", "CRN"), "**********")
                        ),
                        New XElement(cac + "PartyName",
                            New XElement(cbc + "Name", _CompanyName)
                        ),
                        New XElement(cac + "PostalAddress",
                            New XElement(cbc + "StreetName", "شارع الملك فهد"),
                            New XElement(cbc + "BuildingNumber", "0045"),
                            New XElement(cbc + "CityName", "الرياض"),
                            New XElement(cbc + "PostalZone", "12345"),
                            New XElement(cbc + "CitySubdivisionName", "حي النخيل"),
                            New XElement(cac + "Country", New XElement(cbc + "IdentificationCode", "SA"))
                        ),
                        New XElement(cac + "PartyTaxScheme",
                            New XElement(cbc + "CompanyID", _vat_No),
                            New XElement(cac + "TaxScheme", New XElement(cbc + "ID", "VAT"))
                        ),
                        New XElement(cac + "PartyLegalEntity",
                            New XElement(cbc + "RegistrationName", _CompanyName)
                        )
                    )
                ),
                New XElement(cac + "AccountingCustomerParty",
                    New XElement(cac + "Party",
                        New XElement(cbc + "Name", "عميل تيك أواي"),
                        New XElement(cbc + "Note", "بيع مباشر بدون بيانات المشتري")
                    )
                ),
                invoiceLines,
                New XElement(cac + "TaxTotal",
                    New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxValue),
                    New XElement(cac + "TaxSubtotal",
                        New XElement(cbc + "TaxableAmount", New XAttribute("currencyID", currency), totalBeforeTax),
                        New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxValue),
                        New XElement(cac + "TaxCategory",
                            New XElement(cbc + "ID", "S"),
                            New XElement(cbc + "Percent", "15"),
                            New XElement(cac + "TaxScheme", New XElement(cbc + "ID", "VAT"))
                        )
                    )
                ),
                New XElement(cac + "LegalMonetaryTotal",
                    New XElement(cbc + "LineExtensionAmount", New XAttribute("currencyID", currency), totalBeforeTax),
                    New XElement(cbc + "TaxExclusiveAmount", New XAttribute("currencyID", currency), totalBeforeTax),
                    New XElement(cbc + "TaxInclusiveAmount", New XAttribute("currencyID", currency), totalWithTax),
                    New XElement(cbc + "AllowanceTotalAmount", New XAttribute("currencyID", currency), 0),
                    New XElement(cbc + "ChargeTotalAmount", New XAttribute("currencyID", currency), 0),
                    New XElement(cbc + "PayableAmount", New XAttribute("currencyID", currency), totalWithTax)
                ),
                New XElement(cac + "AdditionalDocumentReference",
                    New XElement(cbc + "ID", "QR"),
                    New XElement(cac + "Attachment",
                        New XElement(cbc + "EmbeddedDocumentBinaryObject",
                            New XAttribute("mimeCode", "text/plain"),
                            New XAttribute("encodingCode", "Base64"),
                            qrBase64)
                    )
                )
            )

            ' الحفظ
            Dim folderPath As String = "D:\Basseet_SYS -ok\ZATICA\XML_INV"
            If Not Directory.Exists(folderPath) Then Directory.CreateDirectory(folderPath)
            Dim filePath As String = Path.Combine(folderPath, "INVOICETOZATIKA.xml")
            Dim doc As New XDocument(New XDeclaration("1.0", "UTF-8", "yes"), invoice)
            doc.Save(filePath)

            '      MessageBox.Show("✅ تم إنشاء الفاتورة UBL بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("❌ خطأ أثناء التصدير: " & ex.Message)
        End Try
    End Sub
    ' دالة إنشاء بيانات QR Code
    Private Function GenerateQRCodeData(companyName As String, vatNo As String, issueDate As String, totalWithTax As Decimal, taxAmount As Decimal, invoiceNumber As String) As String
        Return $"|{companyName}|{vatNo}|{issueDate}|{totalWithTax}|{taxAmount}|{invoiceNumber}|"
    End Function

    Private Function GenerateQRCodeImage(data As String) As Bitmap
        Dim writer As New ZXing.BarcodeWriterPixelData() With {
        .Format = ZXing.BarcodeFormat.QR_CODE,
        .Options = New ZXing.QrCode.QrCodeEncodingOptions() With {
            .Width = 150,
            .Height = 150,
            .Margin = 1
        }
    }

        Dim pixelData = writer.Write(data)
        Dim bitmap As New Bitmap(pixelData.Width, pixelData.Height, Imaging.PixelFormat.Format32bppRgb)
        Dim bitmapData = bitmap.LockBits(New Rectangle(0, 0, pixelData.Width, pixelData.Height), Imaging.ImageLockMode.WriteOnly, Imaging.PixelFormat.Format32bppRgb)
        System.Runtime.InteropServices.Marshal.Copy(pixelData.Pixels, 0, bitmapData.Scan0, pixelData.Pixels.Length)
        bitmap.UnlockBits(bitmapData)
        Return bitmap
    End Function

    Private Function GenerateQRCodeImage1(data As String) As Bitmap
        Dim writer As New BarcodeWriterPixelData() With {
            .Format = BarcodeFormat.QR_CODE,
            .Options = New QrCodeEncodingOptions() With {
                .Width = 200,
                .Height = 200,
                .Margin = 1
            }
        }
        Dim pixelData = writer.Write(data)
        Dim bitmap As New Bitmap(pixelData.Width, pixelData.Height, PixelFormat.Format32bppRgb)
        Dim bitmapData = bitmap.LockBits(New Rectangle(0, 0, pixelData.Width, pixelData.Height), ImageLockMode.WriteOnly, PixelFormat.Format32bppRgb)
        System.Runtime.InteropServices.Marshal.Copy(pixelData.Pixels, 0, bitmapData.Scan0, pixelData.Pixels.Length)
        bitmap.UnlockBits(bitmapData)
        Return bitmap
    End Function

    ' دالة تحويل الصورة إلى Base64
    Private Function ImageToBase64(image As Image) As String
        Using ms As New MemoryStream()
            image.Save(ms, ImageFormat.Png)
            Return Convert.ToBase64String(ms.ToArray())
        End Using
    End Function
    Public Sub selectall_cuvn(dgv_amil As DataGridView)
        Try
            ' التأكد من أن الاتصال مفتوح
            If connx.Con.State = ConnectionState.Closed Then
                connx.Con.Open()
            End If

            ' إنشاء DataTable وتنظيفها
            If dt_cuvn Is Nothing Then
                dt_cuvn = New DataTable()
            Else
                dt_cuvn.Clear()
            End If

            ' إعداد الاستعلام وSqlCommand
            Dim cmd As New SqlCommand("SELECT * FROM v_customer_area", connx.Con)

            ' إعداد SqlDataAdapter وتعيين SelectCommand
            Dim adp As New SqlDataAdapter()
            adp.SelectCommand = cmd

            ' ملء DataTable
            adp.Fill(dt_cuvn)

            ' ربط DataGridView بـ DataTable
            dgv_amil.DataSource = dt_cuvn

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        Finally
            ' إغلاق الاتصال إذا كان مفتوحًا
            If connx.Con.State = ConnectionState.Open Then
                connx.Con.Close()
            End If
        End Try
    End Sub
    Public Sub selectall_delev(dgvdelev As DataGridView)
        Try
            ' التأكد من أن الاتصال مفتوح
            If connx.Con.State = ConnectionState.Closed Then
                connx.Con.Open()
            End If

            ' إنشاء DataTable وتنظيفها
            If dt_delev Is Nothing Then
                dt_delev = New DataTable()
            Else
                dt_delev.Clear()
            End If

            ' إعداد الاستعلام وSqlCommand
            Dim cmd As New SqlCommand("SELECT * FROM TBL_DELEVERYMAN", connx.Con)

            ' إعداد SqlDataAdapter وتعيين SelectCommand
            Dim adp As New SqlDataAdapter()
            adp.SelectCommand = cmd

            ' ملء DataTable
            adp.Fill(dt_delev)

            ' ربط DataGridView بـ DataTable
            dgvdelev.DataSource = dt_delev

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        Finally
            ' إغلاق الاتصال إذا كان مفتوحًا
            If connx.Con.State = ConnectionState.Open Then
                connx.Con.Close()
            End If
        End Try
    End Sub
    Private Sub CBOCUSTOMER_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CBOCUSTOMER.SelectedIndexChanged
        Try
            Dim dv1 As DataView = dt_cuvn.DefaultView
            dv1.RowFilter = "CustomerName   like '%" & Trim$(CBOCUSTOMER.Text) & "%'"
            'like'" & Trim$(txtBillNumber.Text) &
        Catch ex As Exception

        End Try
        TxtTableName.Text = CBOCUSTOMER.Text
    End Sub

    Private Sub dgv_amil_CellEnter(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_amil.CellEnter
        Try
            With dgv_amil
                Me.TXT_CUSTOMER_ID.Text = .CurrentRow.Cells("CustomerID").Value.ToString()
                Me.CBOCUSTOMER.Text = .CurrentRow.Cells("CustomerName").Value.ToString()
                Me.txtAddress.Text = .CurrentRow.Cells("Address").Value.ToString()
                Me.txtMobNu.Text = .CurrentRow.Cells("Mobile").Value.ToString()
                Me.txtDeleveryFee.Text = .CurrentRow.Cells("area_price").Value.ToString()
            End With
        Catch ex As Exception

        End Try
    End Sub
    Sub fillcmb_tblCUSTOMERS1()
        connx.FillComboBox(CBOCUSTOMER, "CustomerID", "CustomerName", "tblCustomers")
    End Sub
    Sub fillcmb_tbldelev()
        connx.FillComboBox(cboDelev, "DELEVERYMAN_ID", "DELEVERYMAN_Name", "TBL_DELEVERYMAN")
    End Sub
    Private Sub txtsrch_TextChanged(sender As Object, e As EventArgs) Handles txtsrch.TextChanged

        Try
            Dim dv As DataView = dt_cuvn.DefaultView
            dv.RowFilter = "CustomerName   like '%" & txtsrch.Text & "%'"
            dv.RowFilter = "Mobile   like '%" & txtsrch.Text & "%'"
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Label3_Click(sender As Object, e As EventArgs) Handles Label3.Click
        frm_whats.Show()
    End Sub

    Private Sub dgvdelev_CellEnter(sender As Object, e As DataGridViewCellEventArgs) Handles dgvdelev.CellEnter
        Try
            With dgvdelev

            End With
        Catch ex As Exception

        End Try
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboDelev.SelectedIndexChanged
        Try
            Dim dv1 As DataView = dt_delev.DefaultView
            dv1.RowFilter = "DELEVERYMAN_NAME   like '%" & Trim$(cboDelev.Text) & "%'"
            'like'" & Trim$(txtBillNumber.Text) &
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btn_manage_t_Click(sender As Object, e As EventArgs) Handles btn_manage_t.Click
        _Order_Type = "on_resturant"
        With Frm_Select_Table
            .Label6.Text = "الطاولات المشغولة"
            .LoadOpenTablesWithBills()
            .Show()
            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
        End With

    End Sub

    Private Sub btn_add_cstmr_Click(sender As Object, e As EventArgs) Handles btn_add_cstmr.Click
        With frmSettings
            ' حفظ التبويبات غير المطلوبة مؤقتًا
            Dim hiddenTabs As New List(Of TabPage)

            For Each tab As TabPage In .TabControl1.TabPages
                If tab IsNot .TabPage4 Then
                    hiddenTabs.Add(tab)
                End If
            Next

            ' إزالة التبويبات غير المطلوبة
            For Each tab As TabPage In hiddenTabs
                .TabControl1.TabPages.Remove(tab)
            Next

            ' عرض التبويب المطلوب
            .TabControl1.SelectedTab = .TabPage4
            .ShowDialog()

            ' إعادة التبويبات بعد الإغلاق
            'For Each tab As TabPage In hiddenTabs
            '    .TabControl1.TabPages.Add(tab)
            'Next
        End With
    End Sub

    'qr_zatica
    Function EncodeTLV(tag As Byte, value As String) As Byte()
        Dim valueBytes = Encoding.UTF8.GetBytes(value)
        Dim result As New List(Of Byte)
        result.Add(tag)
        result.Add(CByte(valueBytes.Length))
        result.AddRange(valueBytes)
        Return result.ToArray()
    End Function

    Function GenerateZATCA_QR_Base64(sellerName As String, vatNumber As String, invoiceDate As DateTime, invoiceTotal As Decimal, taxTotal As Decimal) As String
        Dim fullTLV As New List(Of Byte)

        fullTLV.AddRange(EncodeTLV(1, sellerName))
        fullTLV.AddRange(EncodeTLV(2, vatNumber))
        fullTLV.AddRange(EncodeTLV(3, invoiceDate.ToString("yyyy-MM-ddTHH:mm:ssZ")))
        fullTLV.AddRange(EncodeTLV(4, invoiceTotal.ToString("0.00")))
        fullTLV.AddRange(EncodeTLV(5, taxTotal.ToString("0.00")))

        Return Convert.ToBase64String(fullTLV.ToArray())
    End Function
    Private Sub BtnGenerateQR_Click(sender As Object, e As EventArgs) Handles BtnGenerateQR.Click
        ' بيانات الفاتورة
        Dim sellerName As String = _CompanyName
        Dim vatNumber As String = _vat_No
        Dim invoiceDate As DateTime = Now
        Dim invoiceTotal As Decimal = Val(TxtFinalTotal.Text)
        Dim taxTotal As Decimal = Val(LblTax.Text)

        ' توليد QR بصيغة TLV
        Dim qrBase64 As String = GenerateZATCA_QR_Base64(sellerName, vatNumber, invoiceDate, invoiceTotal, taxTotal)

        ' توليد صورة QR
        Dim qrImage As Bitmap = GenerateQRCodeImage(qrBase64)

        ' عرض في PictureBox
        PictureBox_QR.Image = qrImage
    End Sub




    'Public Sub ExportInvoiceToUBL()
    '    Try
    '        ' UBL namespaces
    '        Dim ns As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
    '        Dim cac As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    '        Dim cbc As XNamespace = "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"

    '        ' قراءة البيانات الأساسية
    '        Dim orderId As String = TxtOrder_No.Text.Trim()
    '        Dim issueDate As String = OrderDate.Value.ToString("yyyy-MM-dd")
    '        Dim currency As String = "SAR"
    '        Dim lineCount As Integer = Dgv.Rows.Count

    '        ' التأكد من وجود بيانات
    '        If String.IsNullOrWhiteSpace(orderId) OrElse lineCount = 0 Then
    '            MessageBox.Show("لا توجد بيانات فاتورة لتصديرها.")
    '            Exit Sub
    '        End If

    '        ' حساب القيم الإجمالية من DataGridView
    '        Dim totalBeforeTax As Decimal = 0
    '        Dim taxValue As Decimal = 0
    '        Dim totalWithTax As Decimal = 0

    '        For Each row As DataGridViewRow In Dgv.Rows
    '            If Not row.IsNewRow Then
    '                Dim price As Decimal = CDec(row.Cells(3).Value)
    '                Dim qty As Decimal = CDec(row.Cells(4).Value)
    '                totalBeforeTax += price * qty
    '                taxValue += Math.Round(price * qty * 0.15D, 2)
    '            End If
    '        Next

    '        totalWithTax = totalBeforeTax + taxValue

    '        ' إنشاء بيانات QR Code
    '        Dim qrData As String = GenerateQRCodeData(_CompanyName, _vat_No, issueDate, totalWithTax, taxValue, orderId)
    '        Dim qrCodeImage As Bitmap = GenerateQRCodeImage(qrData)
    '        Dim qrCodeBase64 As String = ImageToBase64(qrCodeImage)
    '        Dim uuid As String = Guid.NewGuid().ToString()
    '        Dim previousInvoiceHash As String = My.Settings.previousUUID()

    '        ' بناء هيكل الفاتورة
    '        Dim invoice As XElement = New XElement(ns + "Invoice",
    'New XAttribute(XNamespace.Xmlns + "cac", cac),
    'New XAttribute(XNamespace.Xmlns + "cbc", cbc),
    'New XElement(cbc + "UBLVersionID", "2.1"),
    'New XElement(cbc + "CustomizationID", "urn:cen.eu:en16931:2017#conformant#urn:sdsa:Invoice:sa:CI_1.0"),
    'New XElement(cbc + "ProfileID", "simplified:reporting:1.0"),
    'New XElement(cbc + "ID", orderId),
    'New XElement(cbc + "UUID", uuid),
    'New XElement(cbc + "IssueDate", issueDate),
    'New XElement(cbc + "InvoiceTypeCode", "0100000"),
    'New XElement(cbc + "DocumentCurrencyCode", currency),
    'New XElement(cbc + "LineCountNumeric", lineCount),
    '   New XElement(cac + "AdditionalDocumentReference",
    '    New XElement(cbc + "ID", "PIH"),
    '    New XElement(cac + "Attachment",
    '        New XElement(cbc + "EmbeddedDocumentBinaryObject",
    '            New XAttribute("mimeCode", "text/plain"),
    '            New XAttribute("encodingCode", "Base64"),
    '            Convert.ToBase64String(Encoding.UTF8.GetBytes(previousInvoiceHash)))
    '    )
    '),
    '                          New XElement(cac + "AccountingSupplierParty",'البائع
    '                New XElement(cac + "Party",
    '                    New XElement(cbc + "Name", _CompanyName),
    '                    New XElement(cac + "PostalAddress",
    '                        New XElement(cbc + "StreetName", "شارع الملك فهد"),
    '                        New XElement(cbc + "CityName", "الرياض"),
    '                        New XElement(cbc + "BuildingNumber", "45")
    '                    ),
    '                    New XElement(cac + "PartyTaxScheme",
    '                        New XElement(cbc + "CompanyID", _vat_No),
    '                        New XElement(cac + "TaxScheme",
    '                            New XElement(cbc + "ID", "VAT")
    '                        )
    '                    ),
    '                    New XElement(cac + "Contact",
    '                        New XElement(cbc + "Telephone", "+************")
    '                    )
    '                )
    '            ),' المشتري
    '                            New XElement(cac + "AccountingCustomerParty",
    '                New XElement(cac + "Party",
    '                    New XElement(cbc + "Name", "عميل تيك أواي"),
    '                    New XElement(cbc + "Note", "بيع مباشر بدون بيانات المشتري")
    '                )
    '            ),'تفاصيل العناصر
    '                            New XElement(cac + "InvoiceLine",
    '                From i As Integer In Enumerable.Range(0, Dgv.Rows.Count)
    '                Let row = Dgv.Rows(i)
    '                Let itemName = row.Cells(2).Value?.ToString()
    '                Let qty = CDec(row.Cells(4).Value)
    '                Let price = CDec(row.Cells(3).Value)
    '                Let total = Math.Round(price * qty, 2)
    '                Let taxAmount = Math.Round(price * qty * 0.15D, 2)
    '                Select New XElement(cac + "InvoiceLine",
    '                    New XElement(cbc + "ID", (i + 1).ToString()),
    '                    New XElement(cbc + "InvoicedQuantity", qty),
    '                    New XElement(cbc + "LineExtensionAmount", New XAttribute("currencyID", currency), total),
    '                    New XElement(cac + "TaxTotal",
    '                        New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxAmount),
    '                        New XElement(cac + "TaxSubtotal",
    '                            New XElement(cbc + "TaxableAmount", New XAttribute("currencyID", currency), total),
    '                            New XElement(cac + "TaxCategory",
    '                                New XElement(cbc + "ID", "S"),
    '                                New XElement(cbc + "Percent", "15"),
    '                                New XElement(cac + "TaxScheme",
    '                                    New XElement(cbc + "ID", "VAT")
    '                                )
    '                            )
    '                        )
    '                    ),
    '                    New XElement(cac + "Item",
    '                        New XElement(cbc + "Name", itemName)
    '                    ),
    '                    New XElement(cac + "Price",
    '                        New XElement(cbc + "PriceAmount", New XAttribute("currencyID", currency), price),
    '                        New XElement(cbc + "BaseQuantity", "1.00")
    '                    )
    '                )
    '            ),  ' الضرائب والإجماليات
    '                                        New XElement(cac + "TaxTotal",
    '                New XElement(cbc + "TaxAmount", New XAttribute("currencyID", currency), taxValue)
    '            ),
    '            New XElement(cac + "LegalMonetaryTotal",
    '                New XElement(cbc + "LineExtensionAmount", New XAttribute("currencyID", currency), totalBeforeTax),
    '                New XElement(cbc + "TaxExclusiveAmount", New XAttribute("currencyID", currency), totalBeforeTax),
    '                New XElement(cbc + "TaxInclusiveAmount", New XAttribute("currencyID", currency), totalWithTax),
    '                New XElement(cbc + "PayableAmount", New XAttribute("currencyID", currency), totalWithTax)
    '            ),   ' QR Code كمرفق
    '                                      New XElement(cac + "AdditionalDocumentReference",
    '                New XElement(cbc + "ID", "QR"),
    '                New XElement(cac + "Attachment",
    '                    New XElement(cbc + "EmbeddedDocumentBinaryObject", qrCodeBase64),
    '                    New XElement(cbc + "MimeCode", "image/png"),
    '                    New XElement(cbc + "EncodingCode", "Base64")
    '                )
    '            )
    '        )

    '        ' حفظ الملف
    '        'Dim folderPath As String = "D:\UBL-Invoices"
    '        'If Not Directory.Exists(folderPath) Then Directory.CreateDirectory(folderPath)
    '        'Dim filePath As String = Path.Combine(folderPath, $"UBL_Invoice_{orderId}.xml")
    '        'Dim doc As New XDocument(New XDeclaration("1.0", "UTF-8", "yes"), invoice)
    '        'doc.Save(filePath)
    '        Dim folderPath As String = "D:\Basseet_SYS\XML_INV"
    '        If Not Directory.Exists(folderPath) Then Directory.CreateDirectory(folderPath)

    '        Dim filePath As String = Path.Combine(folderPath, "INVOICETOZATIKA.xml")
    '        Dim doc As New XDocument(New XDeclaration("1.0", "UTF-8", "yes"), invoice)

    '        doc.Save(filePath)

    '        MessageBox.Show("✅ تم تصدير الفاتورة بصيغة UBL إلى: " & filePath, "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

    '    Catch ex As Exception
    '        MessageBox.Show("❌ خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '    End Try
    'End Sub




End Class
