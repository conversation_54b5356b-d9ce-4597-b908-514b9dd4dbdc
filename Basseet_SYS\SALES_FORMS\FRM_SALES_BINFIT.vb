﻿Imports System.Data.SqlClient
Imports org.apache.xerces.impl

Public Class FRM_SALES_BINFIT
    Dim connex As New CLS_CON
    Public Sub LoadSalesProfit()
        DGV_Profit.Rows.Clear()
        lblTotal_penfit.Text = "0" ' تصفير الليبل قبل البدء

        Dim totalProfit As Decimal = 0 ' متغير لتجميع الربح
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from V_INV_Profit", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            DGV_Profit.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Cashier").ToString,
                                dr("final_total").ToString, dr("ItemName").ToString, dr("cost_price").ToString,
                                 dr("Item_Price").ToString, dr("ord_Qty").ToString, dr("Profit").ToString)
            totalProfit += Convert.ToDecimal(dr("Profit"))
        End While
        dr.Close()
        connex.Con.Close()
        ' عرض مجموع الأرباح
        lblTotal_penfit.Text = totalProfit.ToString("N2") ' تنسيقه بفاصلة عشرية مثلا 1,234.56
    End Sub
    Private Sub FRM_SALES_BINFIT_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadSalesProfit()
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        Search_Date_TODAY()
    End Sub
    Public Sub Search_Date_TODAY()

        Dim sdate As String = Today.Now.ToString("yyyy-MM-dd")
        DGV_Profit.Rows.Clear()
        lblTotal_penfit.Text = "0" ' تصفير الليبل قبل البدء

        Dim totalProfit As Decimal = 0 ' متغير لتجميع الربح
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from V_INV_Profit where SalesDate like '" & sdate & "'", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            DGV_Profit.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Cashier").ToString,
                                dr("final_total").ToString, dr("ItemName").ToString, dr("cost_price").ToString,
                                 dr("Item_Price").ToString, dr("ord_Qty").ToString, dr("Profit").ToString)
            ' إضافة الربح إلى المجموع
            totalProfit += Convert.ToDecimal(dr("Profit"))
        End While
        dr.Close()
        connex.Con.Close()
        ' عرض مجموع الأرباح
        lblTotal_penfit.Text = totalProfit.ToString("N2") ' تنسيقه بفاصلة عشرية مثلا 1,234.56
    End Sub
    Public Sub Search_By_Selected_Month()
        ' الحصول على الشهر المحدد من DateTimePicker
        Dim selectedMonth As String = DateTimePicker1.Value.ToString("yyyy-MM") ' صيغة السنة-الشهر
        DGV_Profit.Rows.Clear()
        lblTotal_penfit.Text = "0" ' تصفير الليبل قبل البدء

        Dim totalProfit As Decimal = 0 ' متغير لتجميع الربح

        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand("select * from V_INV_Profit WHERE FORMAT(SalesDate, 'yyyy-MM') = @selectedMonth", connex.Con)
        cmd.Parameters.AddWithValue("@selectedMonth", selectedMonth) ' استخدم الباراميتر لتجنب مشاكل SQL Injection
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader()
        While dr.Read
            DGV_Profit.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Cashier").ToString,
                            dr("final_total").ToString, dr("ItemName").ToString, dr("cost_price").ToString,
                            dr("Item_Price").ToString, dr("ord_Qty").ToString, dr("Profit").ToString)

            ' إضافة الربح إلى المجموع
            totalProfit += Convert.ToDecimal(dr("Profit"))
        End While
        dr.Close()
        connex.Con.Close()

        ' عرض مجموع الأرباح
        lblTotal_penfit.Text = totalProfit.ToString("N2") ' تنسيقه بفاصلة عشرية مثلا 1,234.56
    End Sub


    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Search_By_Selected_Month()
    End Sub
    Public Sub Search_Date()

        Dim sdate As String = DateTimePicker1.Value.ToString("yyyy-MM-dd")
        DGV_Profit.Rows.Clear()
        lblTotal_penfit.Text = "0" ' تصفير الليبل قبل البدء

        Dim totalProfit As Decimal = 0 ' متغير لتجميع الربح
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from V_INV_Profit where SalesDate like '" & sdate & "'", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            DGV_Profit.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Cashier").ToString,
                            dr("final_total").ToString, dr("ItemName").ToString, dr("cost_price").ToString,
                            dr("Item_Price").ToString, dr("ord_Qty").ToString, dr("Profit").ToString)

            ' إضافة الربح إلى المجموع
            totalProfit += Convert.ToDecimal(dr("Profit"))
        End While
        dr.Close()
        connex.Con.Close()

        ' عرض مجموع الأرباح
        lblTotal_penfit.Text = totalProfit.ToString("N2") ' تنسيقه بفاصلة عشرية مثلا 1,234.56
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        Search_Date()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        LoadSalesProfit()
    End Sub
End Class