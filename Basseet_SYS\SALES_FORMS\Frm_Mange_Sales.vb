﻿Imports Net.Pkcs11Interop.Common
Imports System.Data.SqlClient

Public Class Frm_Mange_Sales
    Dim connex As New CLS_CON
    Public Sub LoadSales()
        Dgv.Rows.Clear()
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from Sales_Tbl", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            Dgv.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Total").ToString, dr("SalesDate").ToString, dr("Cashier").ToString, dr("PaidType").ToString)
        End While
        dr.Close()
        connex.Con.Close()
    End Sub
    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        Search_Date_TODAY()
    End Sub

    Private Sub Frm_Mange_Sales_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadSales()
    End Sub

    Private Sub Label2_Click(sender As Object, e As EventArgs) Handles Label2.Click
        Me.Close()
    End Sub
    Public Sub Search_Date()

        Dim sdate As String = DateTimePicker1.Value.ToString("yyyy-MM-dd")
        Dgv.Rows.Clear()
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from Sales_Tbl where SalesDate like '" & sdate & "'", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            Dgv.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Total").ToString, dr("SalesDate").ToString, dr("Cashier").ToString, dr("PaidType").ToString)
        End While
        dr.Close()
        connex.Con.Close()
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs) Handles BtnUpdate.Click
        Search_Date()
    End Sub

    Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
        If e.ColumnIndex = 6 Then

            With Frm_Show_Inv
                .DgvShow.Rows.Clear()
                If connex.Con.State = 1 Then connex.Con.Close()
                connex.Con.Open()
                Dim cmd As New SqlCommand(" select * from View_Order where Order_No =@Order_No ", connex.Con)
                cmd.Parameters.AddWithValue("@Order_No", Dgv.CurrentRow.Cells(1).Value)
                Dim dr As SqlDataReader
                dr = cmd.ExecuteReader
                While dr.Read
                    .DgvShow.Rows.Add(dr("ItemName").ToString, dr("ord_Price").ToString, dr("ord_Qty").ToString, dr("ord_Total").ToString)

                End While
                dr.Close()
                connex.Con.Close()
                Dim total2 As Decimal = 0.00
                For Each row As DataGridViewRow In Frm_Show_Inv.DgvShow.Rows
                    total2 += row.Cells(3).Value
                Next
                .Lbl_Inv_total.Text = total2

                .ShowDialog()
            End With
        End If
    End Sub
    Public Sub Search_Date_TODAY()

        Dim sdate As String = Today.Now.ToString("yyyy-MM-dd")
        Dgv.Rows.Clear()
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand(" select * from Sales_Tbl where SalesDate like '" & sdate & "'", connex.Con)
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader
        While dr.Read
            Dgv.Rows.Add(dr("Sale_ID").ToString, dr("Order_No").ToString, dr("Total").ToString, dr("SalesDate").ToString, dr("Cashier").ToString, dr("PaidType").ToString)
        End While
        dr.Close()
        connex.Con.Close()
    End Sub

    Public Sub Search_By_Selected_Month()
        ' الحصول على الشهر المحدد من DateTimePicker
        Dim selectedMonth As String = DateTimePicker1.Value.ToString("yyyy-MM") ' صيغة السنة-الشهر
        Dgv.Rows.Clear()
        If connex.Con.State = 1 Then connex.Con.Close()
        connex.Con.Open()
        Dim cmd As New SqlCommand("SELECT * FROM Sales_Tbl WHERE FORMAT(SalesDate, 'yyyy-MM') = @selectedMonth", connex.Con)
        cmd.Parameters.AddWithValue("@selectedMonth", selectedMonth) ' استخدم الباراميتر لتجنب مشاكل SQL Injection
        Dim dr As SqlDataReader
        dr = cmd.ExecuteReader()
        While dr.Read()
            Dgv.Rows.Add(dr("Sale_ID").ToString(), dr("Order_No").ToString(), dr("Total").ToString(), dr("SalesDate").ToString(), dr("Cashier").ToString(), dr("PaidType").ToString())
        End While
        dr.Close()
        connex.Con.Close()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Search_By_Selected_Month()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        LoadSales()
    End Sub


End Class