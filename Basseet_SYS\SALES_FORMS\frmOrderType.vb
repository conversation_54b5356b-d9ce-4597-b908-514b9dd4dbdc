﻿Public Class frmOrderType
    Private Sub btnTakeAway_Click(sender As Object, e As EventArgs) Handles btnTakeAway.Click
        _Order_Type = "on_TakeAway"
        With Frm_Select_Table
            .loadall_Tables()
            .Show()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
        End With
        Me.Close()
    End Sub

    Private Sub btnTable_Click(sender As Object, e As EventArgs) Handles btnTable.Click
        _Order_Type = "on_resturant"
        With Frm_Select_Table
            .loadall_Tables()
            .Show()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
        End With

        Me.Close()

    End Sub

    Private Sub btnDelivery_Click(sender As Object, e As EventArgs) Handles btnDelivery.Click
        ' إغلاق الفورم الحالي
        Me.Close()

        ' تعيين نوع الطلب
        _Order_Type = "on_Delivery"
        With Frm_pos
            .OrderDate.Value = Today
            .TxtTableName.Text = "توصيل" ' تغيير النص إلى اسم الطاولة
            .TxtOrder_No.Text = .Get_Order_No ' تغيير النص إلى قيمة أخرى
            .txtAddress.Visible = True
            .CBOCUSTOMER.Visible = True
            .cboDelev.Visible = True
            .txtMobNu.Visible = True
            .txtsrch.Visible = True
            .Label10.Visible = True
            .Label11.Visible = True
            .Label7.Visible = True
            .Label23.Visible = True
            .fillcmb_tblCUSTOMERS1()
            .fillcmb_tbldelev()
            .selectall_cuvn(.dgv_amil)
            .getorder()
            .Load_Order()
        End With

        ' إغلاق النموذج الحالي
        Me.Close()

        'التعامل مع الفورم Frm_Select_Table
        'With Frm_Select_Table
        '    .loadall_Tables() ' تحميل الطولات
        '    .TopMost = True  ' الفورم دائماً في الأعلى
        '    .FormBorderStyle = FormBorderStyle.FixedDialog  ' تغيير نمط الفورم
        '    .ShowDialog()  ' عرض الفورم
        'End With

        '' إزالة التاب باجز قبل عرض frmSettings
        'frmSettings.TabControl1.TabPages.Remove(frmSettings.TabPage1)
        'frmSettings.TabControl1.TabPages.Remove(frmSettings.TabPage2)
        'frmSettings.TabControl1.TabPages.Remove(frmSettings.TabPage3)

        '' عرض الفورم frmSettings
        'frmSettings.ShowDialog()
    End Sub

End Class