﻿Imports System.Data.SqlClient
Imports System.Net

Public Class Frm_Qty
    Dim connx As New CLS_CON
    Dim id As String, Price As Double
    Dim FianlT As Double

    Public Sub Insert_Order(ByVal id_ As String, ByVal Price_ As Double)

        Me.id = id_
        Me.Price = Price_

    End Sub

    Private Sub Frm_Qty_Invalidated(sender As Object, e As InvalidateEventArgs) Handles Me.Invalidated
        Me.KeyPreview = True
    End Sub

    Public Function Check_item(ByVal Order_No As String, ByVal Item_ID As String) As Boolean
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim Cmd As New SqlCommand("select count(*) from Order_Tbl where Order_No=@Order_No and Item_ID=@Item_ID", connx.Con)
        With Cmd
            .Parameters.AddWithValue("@Order_No", Order_No)
            .Parameters.AddWithValue("@Item_ID", Item_ID)
        End With
        Dim i As Integer = CInt(Cmd.ExecuteScalar)
        If i > 0 Then Return True Else Return False

    End Function

    Private Sub Frm_Qty_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.TopMost = True
    End Sub

    Private Sub Label4_Click(sender As Object, e As EventArgs) Handles Label4.Click
        TxtQty.Text &= "3"
    End Sub

    Private Sub Label7_Click(sender As Object, e As EventArgs) Handles Label7.Click
        TxtQty.Text &= "4"
    End Sub

    Private Sub Label1_Click(sender As Object, e As EventArgs) Handles Label1.Click
        TxtQty.Text &= "1"
    End Sub

    Private Sub Label3_Click(sender As Object, e As EventArgs) Handles Label3.Click
        TxtQty.Text &= "2"
    End Sub

    Private Sub Label6_Click(sender As Object, e As EventArgs) Handles Label6.Click
        TxtQty.Text &= "5"
    End Sub

    Private Sub Label5_Click(sender As Object, e As EventArgs) Handles Label5.Click
        TxtQty.Text &= "6"
    End Sub

    Private Sub Label10_Click(sender As Object, e As EventArgs) Handles Label10.Click
        TxtQty.Text &= "7"
    End Sub

    Private Sub Label9_Click(sender As Object, e As EventArgs) Handles Label9.Click
        TxtQty.Text &= "8"
    End Sub

    Private Sub Label8_Click(sender As Object, e As EventArgs) Handles Label8.Click
        TxtQty.Text &= "9"
    End Sub

    Private Sub Label11_Click(sender As Object, e As EventArgs) Handles Label11.Click
        TxtQty.Text &= "0"
    End Sub

    Private Sub Label12_Click(sender As Object, e As EventArgs) Handles Label12.Click
        TxtQty.Text = ""
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Dim OrderDate As String = Now.ToString("yyyy-MM-dd")
        '*************************************المنتج غير موجود يضاف لاول مرة**********************************

        If Check_item(Frm_pos.TxtOrder_No.Text, connx._ID) = False Then
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()

            ' تأكد من استخدام .Text للحصول على القيمة النصية من الحقول النصية
            connx.cmd = New SqlCommand("INSERT INTO Order_Tbl (Order_no, OrderDate, item_ID, ord_Price, ord_Qty, ord_Total, Table_Name, ord_Status, UserName,service_price) " &
                                   "VALUES (@Order_no, @OrderDate, @item_ID, @ord_Price, @ord_Qty, @ord_Total, @Table_Name, @ord_Status, @UserName,@service_price)", connx.Con)

            connx.cmd.Parameters.AddWithValue("@Order_no", Frm_pos.TxtOrder_No.Text)
            connx.cmd.Parameters.AddWithValue("@OrderDate", Frm_pos.OrderDate.Value)
            connx.cmd.Parameters.AddWithValue("@item_ID", Frm_pos.txt_pr_id.Text)

            ' تأكد من أنك تأخذ القيمة النصية للسعر وليس الكائن مباشرة
            connx.cmd.Parameters.AddWithValue("@ord_Price", CDec(Frm_pos.txt_pr_price.Text))
            '

            connx.cmd.Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text)) 'ممكن تحويلها لقيمة دوبل علشان تقبل الكسور

            ' حساب المجموع الكلي
            Dim MyTotal As Double = CDec(Frm_pos.txt_pr_price.Text) * Val(CDec(TxtQty.Text))
            connx.cmd.Parameters.AddWithValue("@ord_Total", MyTotal)

            connx.cmd.Parameters.AddWithValue("@Table_Name", Frm_pos.TxtTableName.Text)
            connx.cmd.Parameters.AddWithValue("@ord_Status", "Open")
            connx.cmd.Parameters.AddWithValue("@UserName", "Admin") ' اسم المستخدم يمكن أن يكون ديناميكيًا
            connx.cmd.Parameters.AddWithValue("@service_price", Frm_pos.txtDeleveryFee.Text)
            Try
                connx.cmd.ExecuteNonQuery()
                connx.Con.Close()
            Catch ex As Exception
                connx.Con.Close()
                MsgBox(Err.Description, MsgBoxStyle.Information)
            Finally
                If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            End Try

        Else
            '******************************************المنتج موجود تعديل الكمية*****************************

            Dim CmdUpdate2 As New SqlCommand
            With CmdUpdate2
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_No=@Order_No and Item_ID=@Item_ID"
                .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
                .Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text))
                .Parameters.AddWithValue("@Item_ID", id)
            End With
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate2.ExecuteNonQuery()
            connx.Con.Close()

        End If

        '*********************************************اضافة المجموع الى الجدول**************************
        Dim CmdUpdate As New SqlCommand
        With CmdUpdate
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate.ExecuteNonQuery()
        connx.Con.Close()

        TxtQty.Text = ""
        Me.Close()
        With Frm_pos
            .Load_Order()
            .order_total()
        End With
        '    End Sub
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        Dim OrderDate As String = Now.ToString("yyyy-MM-dd")
        '*************************************المنتج غير موجود يضاف لاول مرة**********************************

        If Check_item(Frm_pos.TxtOrder_No.Text, connx._ID) = False Then
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()

            ' تأكد من استخدام .Text للحصول على القيمة النصية من الحقول النصية
            connx.cmd = New SqlCommand("INSERT INTO Order_Tbl (Order_no, OrderDate, item_ID, ord_Price, ord_Qty, ord_Total, Table_Name, ord_Status, UserName,service_price) " &
                                   "VALUES (@Order_no, @OrderDate, @item_ID, @ord_Price, @ord_Qty, @ord_Total, @Table_Name, @ord_Status, @UserName,@service_price)", connx.Con)

            connx.cmd.Parameters.AddWithValue("@Order_no", Frm_pos.TxtOrder_No.Text)
            connx.cmd.Parameters.AddWithValue("@OrderDate", Frm_pos.OrderDate.Value)
            connx.cmd.Parameters.AddWithValue("@item_ID", Frm_pos.txt_pr_id.Text)

            ' تأكد من أنك تأخذ القيمة النصية للسعر وليس الكائن مباشرة
            connx.cmd.Parameters.AddWithValue("@ord_Price", CDec(Frm_pos.txt_pr_price.Text))
            '

            connx.cmd.Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text)) 'ممكن تحويلها لقيمة دوبل علشان تقبل الكسور

            ' حساب المجموع الكلي
            Dim MyTotal As Double = CDec(Frm_pos.txt_pr_price.Text) * Val(CDec(TxtQty.Text))
            connx.cmd.Parameters.AddWithValue("@ord_Total", MyTotal)

            connx.cmd.Parameters.AddWithValue("@Table_Name", Frm_pos.TxtTableName.Text)
            connx.cmd.Parameters.AddWithValue("@ord_Status", "Open")
            connx.cmd.Parameters.AddWithValue("@UserName", "Admin") ' اسم المستخدم يمكن أن يكون ديناميكيًا
            connx.cmd.Parameters.AddWithValue("@service_price", Frm_pos.txtDeleveryFee.Text)
            Try
                connx.cmd.ExecuteNonQuery()
                connx.Con.Close()
            Catch ex As Exception
                connx.Con.Close()
                MsgBox(Err.Description, MsgBoxStyle.Information)
            Finally
                If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            End Try

        Else
            '******************************************المنتج موجود تعديل الكمية*****************************

            Dim CmdUpdate2 As New SqlCommand
            With CmdUpdate2
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_No=@Order_No and Item_ID=@Item_ID"
                .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
                .Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text))
                .Parameters.AddWithValue("@Item_ID", id)
            End With
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate2.ExecuteNonQuery()
            connx.Con.Close()

        End If

        '*********************************************اضافة المجموع الى الجدول**************************
        Dim CmdUpdate As New SqlCommand
        With CmdUpdate
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate.ExecuteNonQuery()
        connx.Con.Close()

        TxtQty.Text = ""
        Me.Close()
        With Frm_pos
            .Load_Order()
            .order_total()
        End With
        '    End Sub
    End Sub

    Private Sub Frm_Qty_KeyDown(sender As Object, e As KeyEventArgs) Handles Me.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim OrderDate As String = Now.ToString("yyyy-MM-dd")
            '*************************************المنتج غير موجود يضاف لاول مرة**********************************

            If Check_item(Frm_pos.TxtOrder_No.Text, Frm_pos.txt_pr_id.Text) = False Then
                If connx.Con.State = 1 Then connx.Con.Close()
                connx.Con.Open()

                ' تأكد من استخدام .Text للحصول على القيمة النصية من الحقول النصية
                connx.cmd = New SqlCommand("INSERT INTO Order_Tbl (Order_no, OrderDate, item_ID, ord_Price, ord_Qty, ord_Total, Table_Name, ord_Status, UserName,service_price) " &
                                   "VALUES (@Order_no, @OrderDate, @item_ID, @ord_Price, @ord_Qty, @ord_Total, @Table_Name, @ord_Status, @UserName,@service_price)", connx.Con)

                connx.cmd.Parameters.AddWithValue("@Order_no", Frm_pos.TxtOrder_No.Text)
                connx.cmd.Parameters.AddWithValue("@OrderDate", Frm_pos.OrderDate.Value)
                connx.cmd.Parameters.AddWithValue("@item_ID", Frm_pos.txt_pr_id.Text)

                ' تأكد من أنك تأخذ القيمة النصية للسعر وليس الكائن مباشرة
                connx.cmd.Parameters.AddWithValue("@ord_Price", CDec(Frm_pos.txt_pr_price.Text))
                If TxtQty.Text = "" Then
                    MessageBox.Show("الرجاء وضع كمية")
                Else
                    connx.cmd.Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text)) 'ممكن تحويلها لقيمة دوبل علشان تقبل الكسور
                End If
                ' حساب المجموع الكلي
                Dim MyTotal As Double = CDec(Frm_pos.txt_pr_price.Text) * Val(TxtQty.Text)
                connx.cmd.Parameters.AddWithValue("@ord_Total", MyTotal)

                connx.cmd.Parameters.AddWithValue("@Table_Name", Frm_pos.TxtTableName.Text)
                connx.cmd.Parameters.AddWithValue("@ord_Status", "Open")
                connx.cmd.Parameters.AddWithValue("@UserName", "Admin") ' اسم المستخدم يمكن أن يكون ديناميكيًا
                connx.cmd.Parameters.AddWithValue("@service_price", Frm_pos.txtDeleveryFee.Text)
                Try
                    connx.cmd.ExecuteNonQuery()
                    connx.Con.Close()
                Catch ex As Exception
                    connx.Con.Close()
                    MsgBox(Err.Description, MsgBoxStyle.Information)
                Finally
                    If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
                End Try

            Else
                '******************************************المنتج موجود تعديل الكمية*****************************

                Dim CmdUpdate2 As New SqlCommand
                With CmdUpdate2
                    .Connection = connx.Con
                    .CommandType = CommandType.Text
                    .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_No=@Order_No and Item_ID=@Item_ID"
                    .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
                    If TxtQty.Text = "" Then
                        MessageBox.Show("يجب وضع كمية")
                    Else
                        .Parameters.AddWithValue("@ord_Qty", CDec(TxtQty.Text))
                    End If
                    .Parameters.AddWithValue("@Item_ID", Frm_pos.txt_pr_id.Text)
                End With
                If connx.Con.State = 1 Then connx.Con.Close()
                connx.Con.Open()
                CmdUpdate2.ExecuteNonQuery()
                connx.Con.Close()

            End If

            '*********************************************اضافة المجموع الى الجدول**************************
            Dim CmdUpdate As New SqlCommand
            With CmdUpdate
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
                .Parameters.AddWithValue("@Order_No", Frm_pos.TxtOrder_No.Text)
            End With
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate.ExecuteNonQuery()
            connx.Con.Close()

            TxtQty.Text = ""
            Me.Close()
            With Frm_pos
                .Load_Order()
                .order_total()

            End With
        End If
    End Sub

End Class
