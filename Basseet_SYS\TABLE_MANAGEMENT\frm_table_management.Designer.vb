﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frm_table_management
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TabControl1 = New System.Windows.Forms.TabControl()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.BtnRefresh = New System.Windows.Forms.Button()
        Me.DgvActiveTables = New System.Windows.Forms.DataGridView()
        Me.Table_Name = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Order_No = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Total_Amount = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Items_Count = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Last_Order_Date = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ord_Status = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Table_Type = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Customer_Name = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Customer_Phone = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.LblSelectedTable = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.TxtSplitCount = New System.Windows.Forms.TextBox()
        Me.BtnSplitTable = New System.Windows.Forms.Button()
        Me.TabPage3 = New System.Windows.Forms.TabPage()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.TxtGroupName = New System.Windows.Forms.TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.TxtSelectedTables = New System.Windows.Forms.TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TxtGroupNotes = New System.Windows.Forms.TextBox()
        Me.BtnMergeTables = New System.Windows.Forms.Button()
        Me.TabPage4 = New System.Windows.Forms.TabPage()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.BtnCloseGroup = New System.Windows.Forms.Button()
        Me.DgvTableGroups = New System.Windows.Forms.DataGridView()
        Me.Group_ID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Group_Name = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Group_Status = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Total_Amount_Group = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Created_Date = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Created_By = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Tables_Count = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Tables_List = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPage5 = New System.Windows.Forms.TabPage()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.DgvSplitOperations = New System.Windows.Forms.DataGridView()
        Me.Split_ID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Original_Table_Name = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Original_Order_No = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Split_Count = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Split_Date = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Split_By = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Original_Total = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Status = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BtnClose = New System.Windows.Forms.Button()
        Me.TabControl1.SuspendLayout()
        Me.TabPage1.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        CType(Me.DgvActiveTables, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPage2.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.TabPage3.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.TabPage4.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.DgvTableGroups, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPage5.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        CType(Me.DgvSplitOperations, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'TabControl1
        '
        Me.TabControl1.Controls.Add(Me.TabPage1)
        Me.TabControl1.Controls.Add(Me.TabPage2)
        Me.TabControl1.Controls.Add(Me.TabPage3)
        Me.TabControl1.Controls.Add(Me.TabPage4)
        Me.TabControl1.Controls.Add(Me.TabPage5)
        Me.TabControl1.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.TabControl1.Location = New System.Drawing.Point(12, 12)
        Me.TabControl1.Name = "TabControl1"
        Me.TabControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.TabControl1.RightToLeftLayout = True
        Me.TabControl1.SelectedIndex = 0
        Me.TabControl1.Size = New System.Drawing.Size(1000, 600)
        Me.TabControl1.TabIndex = 0
        '
        'TabPage1
        '
        Me.TabPage1.Controls.Add(Me.GroupBox1)
        Me.TabPage1.Location = New System.Drawing.Point(4, 26)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage1.Size = New System.Drawing.Size(992, 570)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "الطاولات النشطة"
        Me.TabPage1.UseVisualStyleBackColor = True
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.BtnRefresh)
        Me.GroupBox1.Controls.Add(Me.DgvActiveTables)
        Me.GroupBox1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupBox1.Location = New System.Drawing.Point(3, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(986, 564)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "الطاولات النشطة"
        '
        'BtnRefresh
        '
        Me.BtnRefresh.Location = New System.Drawing.Point(20, 25)
        Me.BtnRefresh.Name = "BtnRefresh"
        Me.BtnRefresh.Size = New System.Drawing.Size(100, 35)
        Me.BtnRefresh.TabIndex = 1
        Me.BtnRefresh.Text = "تحديث"
        Me.BtnRefresh.UseVisualStyleBackColor = True
        '
        'DgvActiveTables
        '
        Me.DgvActiveTables.AllowUserToAddRows = False
        Me.DgvActiveTables.AllowUserToDeleteRows = False
        Me.DgvActiveTables.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DgvActiveTables.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DgvActiveTables.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.Table_Name, Me.Order_No, Me.Total_Amount, Me.Items_Count, Me.Last_Order_Date, Me.ord_Status, Me.Table_Type, Me.Customer_Name, Me.Customer_Phone})
        Me.DgvActiveTables.Location = New System.Drawing.Point(20, 70)
        Me.DgvActiveTables.MultiSelect = False
        Me.DgvActiveTables.Name = "DgvActiveTables"
        Me.DgvActiveTables.ReadOnly = True
        Me.DgvActiveTables.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DgvActiveTables.Size = New System.Drawing.Size(950, 480)
        Me.DgvActiveTables.TabIndex = 0
        '
        'Table_Name
        '
        Me.Table_Name.HeaderText = "اسم الطاولة"
        Me.Table_Name.Name = "Table_Name"
        Me.Table_Name.ReadOnly = True
        '
        'Order_No
        '
        Me.Order_No.HeaderText = "رقم الطلب"
        Me.Order_No.Name = "Order_No"
        Me.Order_No.ReadOnly = True
        '
        'Total_Amount
        '
        Me.Total_Amount.HeaderText = "الإجمالي"
        Me.Total_Amount.Name = "Total_Amount"
        Me.Total_Amount.ReadOnly = True
        '
        'Items_Count
        '
        Me.Items_Count.HeaderText = "عدد الأصناف"
        Me.Items_Count.Name = "Items_Count"
        Me.Items_Count.ReadOnly = True
        '
        'Last_Order_Date
        '
        Me.Last_Order_Date.HeaderText = "آخر طلب"
        Me.Last_Order_Date.Name = "Last_Order_Date"
        Me.Last_Order_Date.ReadOnly = True
        '
        'ord_Status
        '
        Me.ord_Status.HeaderText = "الحالة"
        Me.ord_Status.Name = "ord_Status"
        Me.ord_Status.ReadOnly = True
        '
        'Table_Type
        '
        Me.Table_Type.HeaderText = "نوع الطاولة"
        Me.Table_Type.Name = "Table_Type"
        Me.Table_Type.ReadOnly = True
        '
        'Customer_Name
        '
        Me.Customer_Name.HeaderText = "اسم العميل"
        Me.Customer_Name.Name = "Customer_Name"
        Me.Customer_Name.ReadOnly = True
        '
        'Customer_Phone
        '
        Me.Customer_Phone.HeaderText = "رقم الهاتف"
        Me.Customer_Phone.Name = "Customer_Phone"
        Me.Customer_Phone.ReadOnly = True
        '
        'TabPage2
        '
        Me.TabPage2.Controls.Add(Me.GroupBox2)
        Me.TabPage2.Location = New System.Drawing.Point(4, 26)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage2.Size = New System.Drawing.Size(992, 570)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "فصل طاولة"
        Me.TabPage2.UseVisualStyleBackColor = True
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.LblSelectedTable)
        Me.GroupBox2.Controls.Add(Me.Label1)
        Me.GroupBox2.Controls.Add(Me.TxtSplitCount)
        Me.GroupBox2.Controls.Add(Me.BtnSplitTable)
        Me.GroupBox2.Location = New System.Drawing.Point(50, 50)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(500, 300)
        Me.GroupBox2.TabIndex = 0
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "فصل طاولة"
        '
        'LblSelectedTable
        '
        Me.LblSelectedTable.AutoSize = True
        Me.LblSelectedTable.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold)
        Me.LblSelectedTable.Location = New System.Drawing.Point(50, 50)
        Me.LblSelectedTable.Name = "LblSelectedTable"
        Me.LblSelectedTable.Size = New System.Drawing.Size(134, 19)
        Me.LblSelectedTable.TabIndex = 0
        Me.LblSelectedTable.Text = "لم يتم اختيار طاولة"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(350, 120)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(100, 17)
        Me.Label1.TabIndex = 1
        Me.Label1.Text = "عدد الأجزاء:"
        '
        'TxtSplitCount
        '
        Me.TxtSplitCount.Location = New System.Drawing.Point(200, 117)
        Me.TxtSplitCount.Name = "TxtSplitCount"
        Me.TxtSplitCount.Size = New System.Drawing.Size(100, 24)
        Me.TxtSplitCount.TabIndex = 2
        Me.TxtSplitCount.Text = "2"
        Me.TxtSplitCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'BtnSplitTable
        '
        Me.BtnSplitTable.Location = New System.Drawing.Point(200, 180)
        Me.BtnSplitTable.Name = "BtnSplitTable"
        Me.BtnSplitTable.Size = New System.Drawing.Size(120, 40)
        Me.BtnSplitTable.TabIndex = 3
        Me.BtnSplitTable.Text = "فصل الطاولة"
        Me.BtnSplitTable.UseVisualStyleBackColor = True
        '
        'TabPage3
        '
        Me.TabPage3.Controls.Add(Me.GroupBox3)
        Me.TabPage3.Location = New System.Drawing.Point(4, 26)
        Me.TabPage3.Name = "TabPage3"
        Me.TabPage3.Size = New System.Drawing.Size(992, 570)
        Me.TabPage3.TabIndex = 2
        Me.TabPage3.Text = "ضم طاولات"
        Me.TabPage3.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.Label2)
        Me.GroupBox3.Controls.Add(Me.TxtGroupName)
        Me.GroupBox3.Controls.Add(Me.Label3)
        Me.GroupBox3.Controls.Add(Me.TxtSelectedTables)
        Me.GroupBox3.Controls.Add(Me.Label4)
        Me.GroupBox3.Controls.Add(Me.TxtGroupNotes)
        Me.GroupBox3.Controls.Add(Me.BtnMergeTables)
        Me.GroupBox3.Location = New System.Drawing.Point(50, 50)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(600, 400)
        Me.GroupBox3.TabIndex = 0
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "ضم طاولات"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(450, 60)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(100, 17)
        Me.Label2.TabIndex = 0
        Me.Label2.Text = "اسم المجموعة:"
        '
        'TxtGroupName
        '
        Me.TxtGroupName.Location = New System.Drawing.Point(200, 57)
        Me.TxtGroupName.Name = "TxtGroupName"
        Me.TxtGroupName.Size = New System.Drawing.Size(200, 24)
        Me.TxtGroupName.TabIndex = 1
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(450, 110)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(140, 17)
        Me.Label3.TabIndex = 2
        Me.Label3.Text = "أسماء الطاولات (مفصولة بفاصلة):"
        '
        'TxtSelectedTables
        '
        Me.TxtSelectedTables.Location = New System.Drawing.Point(50, 107)
        Me.TxtSelectedTables.Multiline = True
        Me.TxtSelectedTables.Name = "TxtSelectedTables"
        Me.TxtSelectedTables.Size = New System.Drawing.Size(350, 80)
        Me.TxtSelectedTables.TabIndex = 3
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(450, 220)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(65, 17)
        Me.Label4.TabIndex = 4
        Me.Label4.Text = "ملاحظات:"
        '
        'TxtGroupNotes
        '
        Me.TxtGroupNotes.Location = New System.Drawing.Point(50, 217)
        Me.TxtGroupNotes.Multiline = True
        Me.TxtGroupNotes.Name = "TxtGroupNotes"
        Me.TxtGroupNotes.Size = New System.Drawing.Size(350, 80)
        Me.TxtGroupNotes.TabIndex = 5
        '
        'BtnMergeTables
        '
        Me.BtnMergeTables.Location = New System.Drawing.Point(200, 330)
        Me.BtnMergeTables.Name = "BtnMergeTables"
        Me.BtnMergeTables.Size = New System.Drawing.Size(120, 40)
        Me.BtnMergeTables.TabIndex = 6
        Me.BtnMergeTables.Text = "ضم الطاولات"
        Me.BtnMergeTables.UseVisualStyleBackColor = True
        '
        'TabPage4
        '
        Me.TabPage4.Controls.Add(Me.GroupBox4)
        Me.TabPage4.Location = New System.Drawing.Point(4, 26)
        Me.TabPage4.Name = "TabPage4"
        Me.TabPage4.Size = New System.Drawing.Size(992, 570)
        Me.TabPage4.TabIndex = 3
        Me.TabPage4.Text = "مجموعات الطاولات"
        Me.TabPage4.UseVisualStyleBackColor = True
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.BtnCloseGroup)
        Me.GroupBox4.Controls.Add(Me.DgvTableGroups)
        Me.GroupBox4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupBox4.Location = New System.Drawing.Point(0, 0)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(992, 570)
        Me.GroupBox4.TabIndex = 0
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "مجموعات الطاولات"
        '
        'BtnCloseGroup
        '
        Me.BtnCloseGroup.Location = New System.Drawing.Point(20, 25)
        Me.BtnCloseGroup.Name = "BtnCloseGroup"
        Me.BtnCloseGroup.Size = New System.Drawing.Size(120, 35)
        Me.BtnCloseGroup.TabIndex = 1
        Me.BtnCloseGroup.Text = "إغلاق المجموعة"
        Me.BtnCloseGroup.UseVisualStyleBackColor = True
        '
        'DgvTableGroups
        '
        Me.DgvTableGroups.AllowUserToAddRows = False
        Me.DgvTableGroups.AllowUserToDeleteRows = False
        Me.DgvTableGroups.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DgvTableGroups.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DgvTableGroups.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.Group_ID, Me.Group_Name, Me.Group_Status, Me.Total_Amount_Group, Me.Created_Date, Me.Created_By, Me.Tables_Count, Me.Tables_List})
        Me.DgvTableGroups.Location = New System.Drawing.Point(20, 70)
        Me.DgvTableGroups.MultiSelect = False
        Me.DgvTableGroups.Name = "DgvTableGroups"
        Me.DgvTableGroups.ReadOnly = True
        Me.DgvTableGroups.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DgvTableGroups.Size = New System.Drawing.Size(950, 480)
        Me.DgvTableGroups.TabIndex = 0
        '
        'Group_ID
        '
        Me.Group_ID.HeaderText = "رقم المجموعة"
        Me.Group_ID.Name = "Group_ID"
        Me.Group_ID.ReadOnly = True
        '
        'Group_Name
        '
        Me.Group_Name.HeaderText = "اسم المجموعة"
        Me.Group_Name.Name = "Group_Name"
        Me.Group_Name.ReadOnly = True
        '
        'Group_Status
        '
        Me.Group_Status.HeaderText = "الحالة"
        Me.Group_Status.Name = "Group_Status"
        Me.Group_Status.ReadOnly = True
        '
        'Total_Amount_Group
        '
        Me.Total_Amount_Group.HeaderText = "الإجمالي"
        Me.Total_Amount_Group.Name = "Total_Amount_Group"
        Me.Total_Amount_Group.ReadOnly = True
        '
        'Created_Date
        '
        Me.Created_Date.HeaderText = "تاريخ الإنشاء"
        Me.Created_Date.Name = "Created_Date"
        Me.Created_Date.ReadOnly = True
        '
        'Created_By
        '
        Me.Created_By.HeaderText = "أنشئ بواسطة"
        Me.Created_By.Name = "Created_By"
        Me.Created_By.ReadOnly = True
        '
        'Tables_Count
        '
        Me.Tables_Count.HeaderText = "عدد الطاولات"
        Me.Tables_Count.Name = "Tables_Count"
        Me.Tables_Count.ReadOnly = True
        '
        'Tables_List
        '
        Me.Tables_List.HeaderText = "قائمة الطاولات"
        Me.Tables_List.Name = "Tables_List"
        Me.Tables_List.ReadOnly = True
        '
        'TabPage5
        '
        Me.TabPage5.Controls.Add(Me.GroupBox5)
        Me.TabPage5.Location = New System.Drawing.Point(4, 26)
        Me.TabPage5.Name = "TabPage5"
        Me.TabPage5.Size = New System.Drawing.Size(992, 570)
        Me.TabPage5.TabIndex = 4
        Me.TabPage5.Text = "عمليات الفصل"
        Me.TabPage5.UseVisualStyleBackColor = True
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.DgvSplitOperations)
        Me.GroupBox5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupBox5.Location = New System.Drawing.Point(0, 0)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(992, 570)
        Me.GroupBox5.TabIndex = 0
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "عمليات الفصل"
        '
        'DgvSplitOperations
        '
        Me.DgvSplitOperations.AllowUserToAddRows = False
        Me.DgvSplitOperations.AllowUserToDeleteRows = False
        Me.DgvSplitOperations.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DgvSplitOperations.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DgvSplitOperations.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.Split_ID, Me.Original_Table_Name, Me.Original_Order_No, Me.Split_Count, Me.Split_Date, Me.Split_By, Me.Original_Total, Me.Status})
        Me.DgvSplitOperations.Location = New System.Drawing.Point(20, 30)
        Me.DgvSplitOperations.MultiSelect = False
        Me.DgvSplitOperations.Name = "DgvSplitOperations"
        Me.DgvSplitOperations.ReadOnly = True
        Me.DgvSplitOperations.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DgvSplitOperations.Size = New System.Drawing.Size(950, 520)
        Me.DgvSplitOperations.TabIndex = 0
        '
        'Split_ID
        '
        Me.Split_ID.HeaderText = "رقم العملية"
        Me.Split_ID.Name = "Split_ID"
        Me.Split_ID.ReadOnly = True
        '
        'Original_Table_Name
        '
        Me.Original_Table_Name.HeaderText = "الطاولة الأصلية"
        Me.Original_Table_Name.Name = "Original_Table_Name"
        Me.Original_Table_Name.ReadOnly = True
        '
        'Original_Order_No
        '
        Me.Original_Order_No.HeaderText = "رقم الطلب الأصلي"
        Me.Original_Order_No.Name = "Original_Order_No"
        Me.Original_Order_No.ReadOnly = True
        '
        'Split_Count
        '
        Me.Split_Count.HeaderText = "عدد الأجزاء"
        Me.Split_Count.Name = "Split_Count"
        Me.Split_Count.ReadOnly = True
        '
        'Split_Date
        '
        Me.Split_Date.HeaderText = "تاريخ الفصل"
        Me.Split_Date.Name = "Split_Date"
        Me.Split_Date.ReadOnly = True
        '
        'Split_By
        '
        Me.Split_By.HeaderText = "فصل بواسطة"
        Me.Split_By.Name = "Split_By"
        Me.Split_By.ReadOnly = True
        '
        'Original_Total
        '
        Me.Original_Total.HeaderText = "الإجمالي الأصلي"
        Me.Original_Total.Name = "Original_Total"
        Me.Original_Total.ReadOnly = True
        '
        'Status
        '
        Me.Status.HeaderText = "الحالة"
        Me.Status.Name = "Status"
        Me.Status.ReadOnly = True
        '
        'BtnClose
        '
        Me.BtnClose.Location = New System.Drawing.Point(932, 630)
        Me.BtnClose.Name = "BtnClose"
        Me.BtnClose.Size = New System.Drawing.Size(80, 35)
        Me.BtnClose.TabIndex = 1
        Me.BtnClose.Text = "إغلاق"
        Me.BtnClose.UseVisualStyleBackColor = True
        '
        'frm_table_management
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1024, 680)
        Me.Controls.Add(Me.BtnClose)
        Me.Controls.Add(Me.TabControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.Name = "frm_table_management"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "إدارة فصل وضم الطاولات"
        Me.TabControl1.ResumeLayout(False)
        Me.TabPage1.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        CType(Me.DgvActiveTables, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPage2.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.TabPage3.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.TabPage4.ResumeLayout(False)
        Me.GroupBox4.ResumeLayout(False)
        CType(Me.DgvTableGroups, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPage5.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        CType(Me.DgvSplitOperations, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents TabControl1 As TabControl
    Friend WithEvents TabPage1 As TabPage
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents BtnRefresh As Button
    Friend WithEvents DgvActiveTables As DataGridView
    Friend WithEvents Table_Name As DataGridViewTextBoxColumn
    Friend WithEvents Order_No As DataGridViewTextBoxColumn
    Friend WithEvents Total_Amount As DataGridViewTextBoxColumn
    Friend WithEvents Items_Count As DataGridViewTextBoxColumn
    Friend WithEvents Last_Order_Date As DataGridViewTextBoxColumn
    Friend WithEvents ord_Status As DataGridViewTextBoxColumn
    Friend WithEvents Table_Type As DataGridViewTextBoxColumn
    Friend WithEvents Customer_Name As DataGridViewTextBoxColumn
    Friend WithEvents Customer_Phone As DataGridViewTextBoxColumn
    Friend WithEvents TabPage2 As TabPage
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents LblSelectedTable As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents TxtSplitCount As TextBox
    Friend WithEvents BtnSplitTable As Button
    Friend WithEvents TabPage3 As TabPage
    Friend WithEvents GroupBox3 As GroupBox
    Friend WithEvents Label2 As Label
    Friend WithEvents TxtGroupName As TextBox
    Friend WithEvents Label3 As Label
    Friend WithEvents TxtSelectedTables As TextBox
    Friend WithEvents Label4 As Label
    Friend WithEvents TxtGroupNotes As TextBox
    Friend WithEvents BtnMergeTables As Button
    Friend WithEvents TabPage4 As TabPage
    Friend WithEvents GroupBox4 As GroupBox
    Friend WithEvents BtnCloseGroup As Button
    Friend WithEvents DgvTableGroups As DataGridView
    Friend WithEvents Group_ID As DataGridViewTextBoxColumn
    Friend WithEvents Group_Name As DataGridViewTextBoxColumn
    Friend WithEvents Group_Status As DataGridViewTextBoxColumn
    Friend WithEvents Total_Amount_Group As DataGridViewTextBoxColumn
    Friend WithEvents Created_Date As DataGridViewTextBoxColumn
    Friend WithEvents Created_By As DataGridViewTextBoxColumn
    Friend WithEvents Tables_Count As DataGridViewTextBoxColumn
    Friend WithEvents Tables_List As DataGridViewTextBoxColumn
    Friend WithEvents TabPage5 As TabPage
    Friend WithEvents GroupBox5 As GroupBox
    Friend WithEvents DgvSplitOperations As DataGridView
    Friend WithEvents Split_ID As DataGridViewTextBoxColumn
    Friend WithEvents Original_Table_Name As DataGridViewTextBoxColumn
    Friend WithEvents Original_Order_No As DataGridViewTextBoxColumn
    Friend WithEvents Split_Count As DataGridViewTextBoxColumn
    Friend WithEvents Split_Date As DataGridViewTextBoxColumn
    Friend WithEvents Split_By As DataGridViewTextBoxColumn
    Friend WithEvents Original_Total As DataGridViewTextBoxColumn
    Friend WithEvents Status As DataGridViewTextBoxColumn
    Friend WithEvents BtnClose As Button
End Class