﻿﻿Imports System.Data.SqlClient

Public Class frm_table_management
    Dim connx As New CLS_CON
    Private SelectedTableName As String = ""
    Private SelectedOrderNo As String = ""

    Private Sub frm_table_management_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadActiveTables()
        LoadTableGroups()
        LoadSplitOperations()
    End Sub

    ' تحميل الطاولات النشطة
    Private Sub LoadActiveTables()
        Try
            DgvActiveTables.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("
                SELECT DISTINCT 
                    o.Table_Name,
                    o.Order_No,
                    SUM(o.ord_Total) AS Total_Amount,
                    COUNT(*) AS Items_Count,
                    MAX(o.OrderDate) AS Last_Order_Date,
                    o.ord_Status,
                    CASE 
                        WHEN o.Group_ID IS NOT NULL THEN 'مجمعة'
                        WHEN o.Split_ID IS NOT NULL THEN 'مفصولة'
                        ELSE 'عادية'
                    END AS Table_Type,
                    o.Customer_Name,
                    o.Customer_Phone
                FROM Order_Tbl o
                WHERE o.ord_Status IN ('open', 'split')
                GROUP BY o.Table_Name, o.Order_No, o.ord_Status, o.Group_ID, o.Split_ID, o.Customer_Name, o.Customer_Phone
                ORDER BY o.Table_Name", connx.Con)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                DgvActiveTables.Rows.Add(
                    reader("Table_Name").ToString(),
                    reader("Order_No").ToString(),
                    Convert.ToDecimal(reader("Total_Amount")).ToString("F2"),
                    reader("Items_Count").ToString(),
                    Convert.ToDateTime(reader("Last_Order_Date")).ToString("yyyy-MM-dd HH:mm"),
                    reader("ord_Status").ToString(),
                    reader("Table_Type").ToString(),
                    If(IsDBNull(reader("Customer_Name")), "", reader("Customer_Name").ToString()),
                    If(IsDBNull(reader("Customer_Phone")), "", reader("Customer_Phone").ToString())
                )
            End While
            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل مجموعات الطاولات
    Private Sub LoadTableGroups()
        Try
            DgvTableGroups.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("
                SELECT 
                    tg.Group_ID,
                    tg.Group_Name,
                    tg.Group_Status,
                    tg.Total_Amount,
                    tg.Created_Date,
                    tg.Created_By,
                    COUNT(tgd.Detail_ID) as Tables_Count,
                    STRING_AGG(tgd.Table_Name, ', ') as Tables_List
                FROM TableGroups_Tbl tg
                LEFT JOIN TableGroupDetails_Tbl tgd ON tg.Group_ID = tgd.Group_ID AND tgd.Is_Active = 1
                GROUP BY tg.Group_ID, tg.Group_Name, tg.Group_Status, tg.Total_Amount, tg.Created_Date, tg.Created_By
                ORDER BY tg.Created_Date DESC", connx.Con)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                DgvTableGroups.Rows.Add(
                    reader("Group_ID").ToString(),
                    reader("Group_Name").ToString(),
                    reader("Group_Status").ToString(),
                    Convert.ToDecimal(reader("Total_Amount")).ToString("F2"),
                    Convert.ToDateTime(reader("Created_Date")).ToString("yyyy-MM-dd HH:mm"),
                    reader("Created_By").ToString(),
                    reader("Tables_Count").ToString(),
                    If(IsDBNull(reader("Tables_List")), "", reader("Tables_List").ToString())
                )
            End While
            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل مجموعات الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل عمليات الفصل
    Private Sub LoadSplitOperations()
        Try
            DgvSplitOperations.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("
                SELECT 
                    tso.Split_ID,
                    tso.Original_Table_Name,
                    tso.Original_Order_No,
                    tso.Split_Count,
                    tso.Split_Date,
                    tso.Split_By,
                    tso.Original_Total,
                    tso.Status
                FROM TableSplitOperations_Tbl tso
                ORDER BY tso.Split_Date DESC", connx.Con)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                DgvSplitOperations.Rows.Add(
                    reader("Split_ID").ToString(),
                    reader("Original_Table_Name").ToString(),
                    reader("Original_Order_No").ToString(),
                    reader("Split_Count").ToString(),
                    Convert.ToDateTime(reader("Split_Date")).ToString("yyyy-MM-dd HH:mm"),
                    reader("Split_By").ToString(),
                    Convert.ToDecimal(reader("Original_Total")).ToString("F2"),
                    reader("Status").ToString()
                )
            End While
            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل عمليات الفصل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' اختيار طاولة من الجدول
    Private Sub DgvActiveTables_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvActiveTables.CellClick
        If e.RowIndex >= 0 Then
            SelectedTableName = DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value.ToString()
            SelectedOrderNo = DgvActiveTables.Rows(e.RowIndex).Cells("Order_No").Value.ToString()
            
            LblSelectedTable.Text = "الطاولة المختارة: " & SelectedTableName
            TxtSelectedTables.Text = SelectedTableName
        End If
    End Sub

    ' فصل طاولة
    Private Sub BtnSplitTable_Click(sender As Object, e As EventArgs) Handles BtnSplitTable.Click
        If String.IsNullOrEmpty(SelectedTableName) Then
            MessageBox.Show("يرجى اختيار طاولة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim splitCount As Integer
        If Not Integer.TryParse(TxtSplitCount.Text, splitCount) OrElse splitCount <= 1 Then
            MessageBox.Show("يرجى إدخال عدد صحيح أكبر من 1", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SP_SplitTableSimple", connx.Con)
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Parameters.AddWithValue("@Original_Table_Name", SelectedTableName)
            cmd.Parameters.AddWithValue("@Original_Order_No", SelectedOrderNo)
            cmd.Parameters.AddWithValue("@Split_Count", splitCount)
            cmd.Parameters.AddWithValue("@Split_By", Environment.UserName)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                MessageBox.Show(reader("Message").ToString(), "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            reader.Close()
            connx.Con.Close()

            ' تحديث البيانات
            LoadActiveTables()
            LoadSplitOperations()
            ClearSelection()

        Catch ex As Exception
            MessageBox.Show("خطأ في فصل الطاولة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' ضم طاولات
    Private Sub BtnMergeTables_Click(sender As Object, e As EventArgs) Handles BtnMergeTables.Click
        If String.IsNullOrEmpty(TxtSelectedTables.Text.Trim()) Then
            MessageBox.Show("يرجى إدخال أسماء الطاولات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If String.IsNullOrEmpty(TxtGroupName.Text.Trim()) Then
            MessageBox.Show("يرجى إدخال اسم المجموعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SP_MergeTables", connx.Con)
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Parameters.AddWithValue("@Group_Name", TxtGroupName.Text.Trim())
            cmd.Parameters.AddWithValue("@Table_Names", TxtSelectedTables.Text.Trim())
            cmd.Parameters.AddWithValue("@Created_By", Environment.UserName)
            cmd.Parameters.AddWithValue("@Notes", TxtGroupNotes.Text.Trim())

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                MessageBox.Show(reader("Message").ToString() & vbNewLine & "الإجمالي: " & reader("Total_Amount").ToString(), "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            reader.Close()
            connx.Con.Close()

            ' تحديث البيانات
            LoadActiveTables()
            LoadTableGroups()
            ClearMergeForm()

        Catch ex As Exception
            MessageBox.Show("خطأ في ضم الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إغلاق مجموعة طاولات
    Private Sub BtnCloseGroup_Click(sender As Object, e As EventArgs) Handles BtnCloseGroup.Click
        If DgvTableGroups.CurrentRow Is Nothing Then
            MessageBox.Show("يرجى اختيار مجموعة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim groupId As Integer = Convert.ToInt32(DgvTableGroups.CurrentRow.Cells("Group_ID").Value)
        Dim groupName As String = DgvTableGroups.CurrentRow.Cells("Group_Name").Value.ToString()

        If MessageBox.Show($"هل تريد إغلاق المجموعة '{groupName}'؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Return
        End If

        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("SP_CloseTableGroup", connx.Con)
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Parameters.AddWithValue("@Group_ID", groupId)
            cmd.Parameters.AddWithValue("@Closed_By", Environment.UserName)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            If reader.Read() Then
                MessageBox.Show(reader("Message").ToString(), "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            reader.Close()
            connx.Con.Close()

            ' تحديث البيانات
            LoadActiveTables()
            LoadTableGroups()

        Catch ex As Exception
            MessageBox.Show("خطأ في إغلاق المجموعة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحديث البيانات
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs) Handles BtnRefresh.Click
        LoadActiveTables()
        LoadTableGroups()
        LoadSplitOperations()
    End Sub

    ' مسح التحديد
    Private Sub ClearSelection()
        SelectedTableName = ""
        SelectedOrderNo = ""
        LblSelectedTable.Text = "لم يتم اختيار طاولة"
        TxtSplitCount.Text = "2"
    End Sub

    ' مسح نموذج الضم
    Private Sub ClearMergeForm()
        TxtGroupName.Text = ""
        TxtSelectedTables.Text = ""
        TxtGroupNotes.Text = ""
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub
End Class
