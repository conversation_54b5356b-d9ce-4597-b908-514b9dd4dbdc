Imports System.Data
Imports System.Data.SqlClient

Public Class frm_table_management
    Dim connx As New CLS_CON
    Private SelectedTableName As String = ""
    Private SelectedOrderNo As String = ""

    Private Sub frm_table_management_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Me.Text = "Table Management System"
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            LoadActiveTables()
            LoadTableGroups()
            LoadSplitOperations()

        Catch ex As Exception
            MessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CheckTablesExist() As Boolean
        Try
            Using cmd As New System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Order_Tbl'", connx.Con)
                If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
                Dim result As Integer = CInt(cmd.ExecuteScalar())
                connx.Con.Close()
                Return result > 0
            End Using
        Catch
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            Return False
        End Try
    End Function

    Private Sub LoadActiveTables()
        Try
            If DgvActiveTables Is Nothing Then Return

            DgvActiveTables.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Using cmd As New System.Data.SqlClient.SqlCommand("SELECT DISTINCT Table_Name, Order_No, ord_Total, ord_Status FROM Order_Tbl WHERE ord_Status = 'open' ORDER BY Table_Name", connx.Con)
                Using reader As System.Data.SqlClient.SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        DgvActiveTables.Rows.Add(
                            reader("Table_Name").ToString(),
                            reader("Order_No").ToString(),
                            Convert.ToDecimal(reader("ord_Total")).ToString("F2"),
                            "1",
                            DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                            reader("ord_Status").ToString(),
                            "Normal",
                            "",
                            ""
                        )
                    End While
                End Using
            End Using
            connx.Con.Close()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("Error loading tables: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTableGroups()
        Try
            If DgvTableGroups Is Nothing Then Return
            DgvTableGroups.Rows.Clear()

            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            ' تحميل مجموعات الطاولات من جدول Table_Groups إذا كان موجوداً
            Dim checkTableCmd As New SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Table_Groups'", connx.Con)
            Dim tableExists As Integer = CInt(checkTableCmd.ExecuteScalar())

            If tableExists > 0 Then
                Using cmd As New SqlCommand("SELECT Group_ID, Group_Name, Table_Names, Total_Amount, Status, Created_Date, Created_By, Notes FROM Table_Groups WHERE Status = 'active' ORDER BY Created_Date DESC", connx.Con)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            DgvTableGroups.Rows.Add(
                                reader("Group_ID").ToString(),
                                reader("Group_Name").ToString(),
                                reader("Table_Names").ToString(),
                                Convert.ToDecimal(reader("Total_Amount")).ToString("F2"),
                                reader("Status").ToString(),
                                Convert.ToDateTime(reader("Created_Date")).ToString("yyyy-MM-dd HH:mm"),
                                reader("Created_By").ToString(),
                                reader("Notes").ToString()
                            )
                        End While
                    End Using
                End Using
            End If

            connx.Con.Close()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("خطأ في تحميل مجموعات الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadSplitOperations()
        Try
            If DgvSplitOperations Is Nothing Then Return
            DgvSplitOperations.Rows.Clear()

            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            ' تحميل عمليات الفصل من جدول Table_Split_Operations إذا كان موجوداً
            Dim checkTableCmd As New SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Table_Split_Operations'", connx.Con)
            Dim tableExists As Integer = CInt(checkTableCmd.ExecuteScalar())

            If tableExists > 0 Then
                Using cmd As New SqlCommand("SELECT TOP 50 Split_ID, Original_Table_Name, Split_Count, Split_Type, Created_Date, Created_By, Notes FROM Table_Split_Operations ORDER BY Created_Date DESC", connx.Con)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            DgvSplitOperations.Rows.Add(
                                reader("Split_ID").ToString(),
                                reader("Original_Table_Name").ToString(),
                                reader("Split_Count").ToString(),
                                reader("Split_Type").ToString(),
                                Convert.ToDateTime(reader("Created_Date")).ToString("yyyy-MM-dd HH:mm"),
                                reader("Created_By").ToString(),
                                reader("Notes").ToString()
                            )
                        End While
                    End Using
                End Using
            End If

            connx.Con.Close()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("خطأ في تحميل عمليات الفصل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvActiveTables_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvActiveTables.CellClick
        Try
            If e.RowIndex >= 0 AndAlso DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value IsNot Nothing Then
                SelectedTableName = DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value.ToString()
                SelectedOrderNo = DgvActiveTables.Rows(e.RowIndex).Cells("Order_No").Value.ToString()

                If LblSelectedTable IsNot Nothing Then
                    LblSelectedTable.Text = "Selected Table: " & SelectedTableName
                End If
                If TxtSelectedTables IsNot Nothing Then
                    TxtSelectedTables.Text = SelectedTableName
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub BtnSplitTable_Click(sender As Object, e As EventArgs) Handles BtnSplitTable.Click
        Try
            ' التحقق من اختيار طاولة
            If String.IsNullOrEmpty(SelectedTableName) Then
                MessageBox.Show("يرجى اختيار طاولة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' التحقق من عدد الأقسام
            Dim splitCount As Integer
            If TxtSplitCount IsNot Nothing AndAlso Integer.TryParse(TxtSplitCount.Text, splitCount) AndAlso splitCount >= 2 AndAlso splitCount <= 10 Then
                ' تنفيذ عملية الفصل
                SplitTableOperation(SelectedTableName, SelectedOrderNo, splitCount)
            Else
                MessageBox.Show("يرجى إدخال عدد صحيح للأقسام (2-10)", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("خطأ في فصل الطاولة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SplitTableOperation(tableName As String, orderNo As String, splitCount As Integer)
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            ' استخدام الإجراء المخزن SP_SplitTable
            Using cmd As New SqlCommand("SP_SplitTable", connx.Con)
                cmd.CommandType = CommandType.StoredProcedure
                cmd.Parameters.AddWithValue("@Table_Name", tableName)
                cmd.Parameters.AddWithValue("@Order_No", orderNo)
                cmd.Parameters.AddWithValue("@Split_Count", splitCount)
                cmd.Parameters.AddWithValue("@Split_Type", "equal") ' تقسيم متساوي
                cmd.Parameters.AddWithValue("@Created_By", Environment.UserName)
                cmd.Parameters.AddWithValue("@Notes", $"تم فصل الطاولة إلى {splitCount} أقسام")

                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        Dim message As String = reader("Message").ToString()
                        Dim success As Boolean = Convert.ToBoolean(reader("Success"))

                        If success Then
                            MessageBox.Show(message, "نجح الفصل", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            ' تحديث البيانات
                            reader.Close()
                            connx.Con.Close()
                            LoadActiveTables()
                            LoadSplitOperations()
                            ClearSelection()
                        Else
                            MessageBox.Show(message, "خطأ في الفصل", MessageBoxButtons.OK, MessageBoxIcon.Error)
                        End If
                    End If
                End Using
            End Using

            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("خطأ في تنفيذ عملية الفصل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnMergeTables_Click(sender As Object, e As EventArgs) Handles BtnMergeTables.Click
        Try
            ' التحقق من إدخال اسم المجموعة والطاولات
            If TxtGroupName Is Nothing OrElse String.IsNullOrEmpty(TxtGroupName.Text.Trim()) Then
                MessageBox.Show("يرجى إدخال اسم المجموعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If TxtSelectedTables Is Nothing OrElse String.IsNullOrEmpty(TxtSelectedTables.Text.Trim()) Then
                MessageBox.Show("يرجى اختيار الطاولات المراد ضمها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' تأكيد العملية
            If MessageBox.Show($"هل تريد ضم الطاولات: {TxtSelectedTables.Text} في مجموعة '{TxtGroupName.Text}'؟", "تأكيد الضم", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Return
            End If

            ' تنفيذ عملية الضم
            MergeTablesOperation()

        Catch ex As Exception
            MessageBox.Show("خطأ في ضم الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub MergeTablesOperation()
        Try
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            ' استخدام الإجراء المخزن SP_MergeTables
            Using cmd As New SqlCommand("SP_MergeTables", connx.Con)
                cmd.CommandType = CommandType.StoredProcedure
                cmd.Parameters.AddWithValue("@Group_Name", TxtGroupName.Text.Trim())
                cmd.Parameters.AddWithValue("@Table_Names", TxtSelectedTables.Text.Trim())
                cmd.Parameters.AddWithValue("@Created_By", Environment.UserName)
                cmd.Parameters.AddWithValue("@Notes", If(TxtGroupNotes IsNot Nothing, TxtGroupNotes.Text.Trim(), ""))

                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        Dim message As String = reader("Message").ToString()
                        Dim totalAmount As String = ""

                        ' محاولة قراءة المبلغ الإجمالي إذا كان متوفراً
                        Try
                            totalAmount = reader("Total_Amount").ToString()
                            message += vbNewLine & "الإجمالي: " & totalAmount
                        Catch
                            ' تجاهل إذا لم يكن العمود متوفراً
                        End Try

                        MessageBox.Show(message, "نجح الضم", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    End If
                End Using
            End Using

            connx.Con.Close()

            ' تحديث البيانات
            LoadActiveTables()
            LoadTableGroups()
            ClearMergeForm()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("خطأ في تنفيذ عملية الضم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnCloseGroup_Click(sender As Object, e As EventArgs) Handles BtnCloseGroup.Click
        Try
            MessageBox.Show("Close group function will be implemented soon", "Under Development", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs) Handles BtnRefresh.Click
        Try
            LoadActiveTables()
            LoadTableGroups()
            LoadSplitOperations()
        Catch ex As Exception
            MessageBox.Show("Error refreshing: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearSelection()
        Try
            SelectedTableName = ""
            SelectedOrderNo = ""
            If LblSelectedTable IsNot Nothing Then
                LblSelectedTable.Text = "No table selected"
            End If
            If TxtSplitCount IsNot Nothing Then
                TxtSplitCount.Text = "2"
            End If
        Catch
        End Try
    End Sub

    Private Sub ClearMergeForm()
        Try
            If TxtGroupName IsNot Nothing Then TxtGroupName.Text = ""
            If TxtSelectedTables IsNot Nothing Then TxtSelectedTables.Text = ""
            If TxtGroupNotes IsNot Nothing Then TxtGroupNotes.Text = ""
        Catch
        End Try
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub
End Class
