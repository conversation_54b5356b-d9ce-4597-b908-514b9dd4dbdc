Imports System.Data
Imports System.Data.SqlClient

Public Class frm_table_management
    Dim connx As New CLS_CON
    Private SelectedTableName As String = ""
    Private SelectedOrderNo As String = ""

    Private Sub frm_table_management_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Me.Text = "Table Management System"
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            LoadActiveTables()
            LoadTableGroups()
            LoadSplitOperations()

        Catch ex As Exception
            MessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CheckTablesExist() As Boolean
        Try
            Using cmd As New System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Order_Tbl'", connx.Con)
                If connx.Con.State = ConnectionState.Closed Then connx.Con.Open()
                Dim result As Integer = CInt(cmd.ExecuteScalar())
                connx.Con.Close()
                Return result > 0
            End Using
        Catch
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            Return False
        End Try
    End Function

    Private Sub LoadActiveTables()
        Try
            If DgvActiveTables Is Nothing Then Return

            DgvActiveTables.Rows.Clear()
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Using cmd As New System.Data.SqlClient.SqlCommand("SELECT DISTINCT Table_Name, Order_No, ord_Total, ord_Status FROM Order_Tbl WHERE ord_Status = 'open' ORDER BY Table_Name", connx.Con)
                Using reader As System.Data.SqlClient.SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        DgvActiveTables.Rows.Add(
                            reader("Table_Name").ToString(),
                            reader("Order_No").ToString(),
                            Convert.ToDecimal(reader("ord_Total")).ToString("F2"),
                            "1",
                            DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                            reader("ord_Status").ToString(),
                            "Normal",
                            "",
                            ""
                        )
                    End While
                End Using
            End Using
            connx.Con.Close()

        Catch ex As Exception
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            MessageBox.Show("Error loading tables: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTableGroups()
        Try
            If DgvTableGroups Is Nothing Then Return
            DgvTableGroups.Rows.Clear()
        Catch ex As Exception
            MessageBox.Show("Error loading table groups: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadSplitOperations()
        Try
            If DgvSplitOperations Is Nothing Then Return
            DgvSplitOperations.Rows.Clear()
        Catch ex As Exception
            MessageBox.Show("Error loading split operations: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvActiveTables_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvActiveTables.CellClick
        Try
            If e.RowIndex >= 0 AndAlso DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value IsNot Nothing Then
                SelectedTableName = DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value.ToString()
                SelectedOrderNo = DgvActiveTables.Rows(e.RowIndex).Cells("Order_No").Value.ToString()

                If LblSelectedTable IsNot Nothing Then
                    LblSelectedTable.Text = "Selected Table: " & SelectedTableName
                End If
                If TxtSelectedTables IsNot Nothing Then
                    TxtSelectedTables.Text = SelectedTableName
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub BtnSplitTable_Click(sender As Object, e As EventArgs) Handles BtnSplitTable.Click
        Try
            MessageBox.Show("Split table function will be implemented soon", "Under Development", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnMergeTables_Click(sender As Object, e As EventArgs) Handles BtnMergeTables.Click
        Try
            MessageBox.Show("Merge tables function will be implemented soon", "Under Development", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnCloseGroup_Click(sender As Object, e As EventArgs) Handles BtnCloseGroup.Click
        Try
            MessageBox.Show("Close group function will be implemented soon", "Under Development", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs) Handles BtnRefresh.Click
        Try
            LoadActiveTables()
            LoadTableGroups()
            LoadSplitOperations()
        Catch ex As Exception
            MessageBox.Show("Error refreshing: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearSelection()
        Try
            SelectedTableName = ""
            SelectedOrderNo = ""
            If LblSelectedTable IsNot Nothing Then
                LblSelectedTable.Text = "No table selected"
            End If
            If TxtSplitCount IsNot Nothing Then
                TxtSplitCount.Text = "2"
            End If
        Catch
        End Try
    End Sub

    Private Sub ClearMergeForm()
        Try
            If TxtGroupName IsNot Nothing Then TxtGroupName.Text = ""
            If TxtSelectedTables IsNot Nothing Then TxtSelectedTables.Text = ""
            If TxtGroupNotes IsNot Nothing Then TxtGroupNotes.Text = ""
        Catch
        End Try
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub
End Class
