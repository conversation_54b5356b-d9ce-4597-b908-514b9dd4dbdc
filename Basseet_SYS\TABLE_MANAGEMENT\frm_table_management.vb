﻿﻿Imports System.Data.SqlClient

Public Class frm_table_management
    Dim connx As New CLS_CON
    Private SelectedTableName As String = ""
    Private SelectedOrderNo As String = ""

    Private Sub frm_table_management_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Me.Text = "إدارة الطاولات - فصل وضم"
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            ' تحميل البيانات فقط إذا كانت الجداول موجودة
            If CheckTablesExist() Then
                LoadActiveTables()
                LoadTableGroups()
                LoadSplitOperations()
            Else
                MessageBox.Show("يرجى تطبيق سكريبت قاعدة البيانات أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CheckTablesExist() As Boolean
        Try
            Dim cmd As New SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Table_Split_Operations'", connx.con)
            connx.con.Open()
            Dim result As Integer = cmd.ExecuteScalar()
            connx.con.Close()
            Return result > 0
        Catch
            If connx.con.State = ConnectionState.Open Then connx.con.Close()
            Return False
        End Try
    End Function

    ' تحميل الطاولات النشطة
    Private Sub LoadActiveTables()
        Try
            If DgvActiveTables Is Nothing Then Return

            DgvActiveTables.Rows.Clear()
            If connx.con.State = ConnectionState.Open Then connx.con.Close()
            connx.con.Open()

            ' استعلام مبسط للطاولات النشطة
            Dim cmd As New SqlCommand("SELECT DISTINCT Table_Name, Order_No, ord_Total, ord_Status FROM Order_Tbl WHERE ord_Status = 'open' ORDER BY Table_Name", connx.con)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                DgvActiveTables.Rows.Add(
                    reader("Table_Name").ToString(),
                    reader("Order_No").ToString(),
                    Convert.ToDecimal(reader("ord_Total")).ToString("F2"),
                    "1",
                    DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                    reader("ord_Status").ToString(),
                    "عادية",
                    "",
                    ""
                )
            End While
            reader.Close()
            connx.con.Close()

        Catch ex As Exception
            If connx.con.State = ConnectionState.Open Then connx.con.Close()
            MessageBox.Show("خطأ في تحميل الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل مجموعات الطاولات
    Private Sub LoadTableGroups()
        Try
            If DgvTableGroups Is Nothing Then Return
            DgvTableGroups.Rows.Clear()
            ' مؤقتاً - سيتم تحميل البيانات عند توفر الجداول
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل مجموعات الطاولات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل عمليات الفصل
    Private Sub LoadSplitOperations()
        Try
            If DgvSplitOperations Is Nothing Then Return
            DgvSplitOperations.Rows.Clear()
            ' مؤقتاً - سيتم تحميل البيانات عند توفر الجداول
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
            connx.Con.Open()

            Dim cmd As New SqlCommand("
                SELECT 
                    tso.Split_ID,
                    tso.Original_Table_Name,
                    tso.Original_Order_No,
                    tso.Split_Count,
                    tso.Split_Date,
                    tso.Split_By,
                    tso.Original_Total,
                    tso.Status
                FROM TableSplitOperations_Tbl tso
                ORDER BY tso.Split_Date DESC", connx.Con)

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                DgvSplitOperations.Rows.Add(
                    reader("Split_ID").ToString(),
                    reader("Original_Table_Name").ToString(),
                    reader("Original_Order_No").ToString(),
                    reader("Split_Count").ToString(),
                    Convert.ToDateTime(reader("Split_Date")).ToString("yyyy-MM-dd HH:mm"),
                    reader("Split_By").ToString(),
                    Convert.ToDecimal(reader("Original_Total")).ToString("F2"),
                    reader("Status").ToString()
                )
            End While
            reader.Close()
            connx.Con.Close()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل عمليات الفصل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' اختيار طاولة من الجدول
    Private Sub DgvActiveTables_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvActiveTables.CellClick
        Try
            If e.RowIndex >= 0 AndAlso DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value IsNot Nothing Then
                SelectedTableName = DgvActiveTables.Rows(e.RowIndex).Cells("Table_Name").Value.ToString()
                SelectedOrderNo = DgvActiveTables.Rows(e.RowIndex).Cells("Order_No").Value.ToString()

                If LblSelectedTable IsNot Nothing Then
                    LblSelectedTable.Text = "الطاولة المختارة: " & SelectedTableName
                End If
                If TxtSelectedTables IsNot Nothing Then
                    TxtSelectedTables.Text = SelectedTableName
                End If
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحديد
        End Try
    End Sub

    ' فصل طاولة
    Private Sub BtnSplitTable_Click(sender As Object, e As EventArgs) Handles BtnSplitTable.Click
        Try
            MessageBox.Show("سيتم تطبيق وظيفة فصل الطاولة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' ضم طاولات
    Private Sub BtnMergeTables_Click(sender As Object, e As EventArgs) Handles BtnMergeTables.Click
        Try
            MessageBox.Show("سيتم تطبيق وظيفة ضم الطاولات قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    ' إغلاق مجموعة طاولات
    Private Sub BtnCloseGroup_Click(sender As Object, e As EventArgs) Handles BtnCloseGroup.Click
        Try
            MessageBox.Show("سيتم تطبيق وظيفة إغلاق المجموعة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحديث البيانات
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs) Handles BtnRefresh.Click
        Try
            LoadActiveTables()
            LoadTableGroups()
            LoadSplitOperations()
        Catch ex As Exception
            MessageBox.Show("خطأ في التحديث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' مسح التحديد
    Private Sub ClearSelection()
        Try
            SelectedTableName = ""
            SelectedOrderNo = ""
            If LblSelectedTable IsNot Nothing Then
                LblSelectedTable.Text = "لم يتم اختيار طاولة"
            End If
            If TxtSplitCount IsNot Nothing Then
                TxtSplitCount.Text = "2"
            End If
        Catch
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' مسح نموذج الضم
    Private Sub ClearMergeForm()
        Try
            If TxtGroupName IsNot Nothing Then TxtGroupName.Text = ""
            If TxtSelectedTables IsNot Nothing Then TxtSelectedTables.Text = ""
            If TxtGroupNotes IsNot Nothing Then TxtGroupNotes.Text = ""
        Catch
            ' تجاهل الأخطاء
        End Try
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub
End Class
