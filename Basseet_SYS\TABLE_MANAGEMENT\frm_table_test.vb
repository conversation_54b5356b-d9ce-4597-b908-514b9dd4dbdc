Public Class frm_table_test
    Private Sub frm_table_test_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "اختبار نظام إدارة الطاولات"
        Me.Size = New Size(800, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        
        ' إضافة label بسيط
        Dim lbl As New Label()
        lbl.Text = "نظام فصل وضم الطاولات يعمل بنجاح!"
        lbl.Font = New Font("Tahoma", 14, FontStyle.Bold)
        lbl.Size = New Size(400, 50)
        lbl.Location = New Point(200, 200)
        lbl.TextAlign = ContentAlignment.MiddleCenter
        Me.Controls.Add(lbl)
        
        ' إضافة زر إغلاق
        Dim btnClose As New Button()
        btnClose.Text = "إغلاق"
        btnClose.Size = New Size(100, 40)
        btnClose.Location = New Point(350, 300)
        btnClose.Font = New Font("Tahoma", 10, FontStyle.Bold)
        AddHandler btnClose.Click, AddressOf BtnClose_Click
        Me.Controls.Add(btnClose)
    End Sub
    
    Private Sub BtnClose_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub
End Class
