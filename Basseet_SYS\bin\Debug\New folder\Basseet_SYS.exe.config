﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Basseet_SYS.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="Basseet_SYS.My.MySettings.smart_rest1ConnectionString" connectionString="Data Source=DESKTOP-QI6H2EA;Initial Catalog=smart_rest1;Integrated Security=True;Encrypt=False" providerName="System.Data.SqlClient" />
    </connectionStrings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <userSettings>
        <Basseet_SYS.My.MySettings>
            <setting name="PrinterName" serializeAs="String">
                <value />
            </setting>
            <setting name="Tax_Status" serializeAs="String">
                <value>true</value>
            </setting>
            <setting name="SalePrint" serializeAs="String">
                <value />
            </setting>
            <setting name="size" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="previousUUID" serializeAs="String">
                <value />
            </setting>
            <setting name="activitycode" serializeAs="String">
                <value />
            </setting>
            <setting name="name1" serializeAs="String">
                <value />
            </setting>
            <setting name="RegistrationNumber" serializeAs="String">
                <value />
            </setting>
            <setting name="serialDevice" serializeAs="String">
                <value />
            </setting>
        </Basseet_SYS.My.MySettings>
    </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.5.0.0" newVersion="4.5.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>