﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Basseet_SYS
</name>
</assembly>
<members>
<member name="T:Basseet_SYS.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources._111">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources._1111">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources._18_06_2022_08_15_58_م">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources._18_06_2022_08_16_59_م">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources._when">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.Actions_application_exit_icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.alert">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.Apps_Zoom_In_icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.bundle_48x48x32b">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.croak">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.exit_2_32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.icons8_add_32px">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.Pen_icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.printer_321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.save_32_blue">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.save_322">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.Settings_5_icon1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Basseet_SYS.My.Resources.Resources.suc">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.comSetting_TblDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.Sales_TblDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.View_OrderDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.comSetting_TblRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.Sales_TblRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.View_OrderRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.comSetting_TblRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.Sales_TblRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Basseet_SYS.DataSet1.View_OrderRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
</members>
</doc>
