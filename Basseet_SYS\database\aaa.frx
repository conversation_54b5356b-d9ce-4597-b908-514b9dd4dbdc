﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="10/20/2024 16:52:09" ReportInfo.Modified="04/26/2025 16:00:44" ReportInfo.CreatorVersion="2024.2.0.0">
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGiDb8DbfAluRQe2kAdJ1efhYV5DjXfHFwmEtJWiyR5ZtBLmDcVJUtwjDZhPc+NPzP0f5GBn3kGBe67l6/8EePjgN6L8k/SkbMVvm5UFBUKus0NNehj2mzre9ZOvksqvOTnzKL9ue+5oNeiZRwhOTH06JxwSVDbVHRHY1ZQghjXl3e3HZQtNODjQM5iv4EmGkng==">
      <TableDataSource Name="comSetting_Tbl" DataType="System.Int32" Enabled="true" TableName="comSetting_Tbl">
        <Column Name="Company_ID" DataType="System.Int32"/>
        <Column Name="CompanyName" DataType="System.String"/>
        <Column Name="Address" DataType="System.String"/>
        <Column Name="Phone" DataType="System.String"/>
        <Column Name="Mobile" DataType="System.String"/>
        <Column Name="CompanyLogo" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="Phone1" DataType="System.String"/>
        <Column Name="Phone2" DataType="System.String"/>
        <Column Name="TELEGRAM" DataType="System.String"/>
        <Column Name="WHATSAPP" DataType="System.String"/>
        <Column Name="Mobile1" DataType="System.String"/>
        <Column Name="Mobile2" DataType="System.String"/>
        <Column Name="GOOGLE_LOC" DataType="System.String"/>
        <Column Name="PCNAME" DataType="System.String"/>
        <Column Name="COM_NUM" DataType="System.Int32"/>
        <Column Name="commonName" DataType="System.String"/>
        <Column Name="serialNumer" DataType="System.String"/>
        <Column Name="orgnizationIdentifier" DataType="System.String"/>
        <Column Name="orgnizationUnitName" DataType="System.String"/>
        <Column Name="orgnizationName" DataType="System.String"/>
        <Column Name="countryName" DataType="System.String"/>
        <Column Name="invoiceType" DataType="System.String"/>
        <Column Name="location" DataType="System.String"/>
        <Column Name="industry" DataType="System.String"/>
        <Column Name="emailId" DataType="System.Int32"/>
        <Column Name="fax" DataType="System.String"/>
        <Column Name="city" DataType="System.String"/>
        <Column Name="vat_no" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="Sales_Tbl" DataType="System.Int32" Enabled="true" TableName="Sales_Tbl">
        <Column Name="Sale_ID" DataType="System.Int32"/>
        <Column Name="Order_No" DataType="System.String"/>
        <Column Name="Total" DataType="System.Decimal"/>
        <Column Name="SalesDate" DataType="System.DateTime"/>
        <Column Name="SalesTime" DataType="System.String"/>
        <Column Name="Cashier" DataType="System.String"/>
        <Column Name="PaidType" DataType="System.String"/>
        <Column Name="UserName" DataType="System.String"/>
        <Column Name="tax_value" DataType="System.Decimal"/>
        <Column Name="tax_total" DataType="System.Decimal"/>
        <Column Name="final_total" DataType="System.Decimal"/>
        <Column Name="disc_total" DataType="System.Decimal"/>
        <Column Name="QrCode_Pic" DataType="System.Byte[]" BindableControl="Picture"/>
      </TableDataSource>
      <TableDataSource Name="View_Order" DataType="System.Int32" Enabled="true" TableName="View_Order">
        <Column Name="Order_ID" DataType="System.Int32"/>
        <Column Name="Order_No" DataType="System.String"/>
        <Column Name="Item_ID" DataType="System.Int32"/>
        <Column Name="ord_Price" DataType="System.Decimal"/>
        <Column Name="ord_Qty" DataType="System.Int32"/>
        <Column Name="ord_Total" DataType="System.Decimal"/>
        <Column Name="OrderDate" DataType="System.DateTime"/>
        <Column Name="Table_Name" DataType="System.String"/>
        <Column Name="ord_Status" DataType="System.String"/>
        <Column Name="UserName" DataType="System.String"/>
        <Column Name="ItemName" DataType="System.String"/>
        <Column Name="Itembarcode" DataType="System.String"/>
        <Column Name="Cat_ID" DataType="System.Int32"/>
        <Column Name="CatName" DataType="System.String"/>
      </TableDataSource>
    </MsSqlDataConnection>
  </Dictionary>
  <ReportPage Name="Page1" PaperWidth="80" PaperHeight="200" LeftMargin="2" TopMargin="2" RightMargin="2" BottomMargin="5" Watermark.Font="Arial, 60pt" UnlimitedHeight="true">
    <ReportTitleBand Name="ReportTitle1" Width="287.28" Height="113.4">
      <TextObject Name="Text1" Left="113.4" Top="56.7" Width="179.55" Height="18.9" Text="[comSetting_Tbl.CompanyName]" Font="Arial, 10pt"/>
      <PictureObject Name="Picture1" Left="9.45" Top="37.8" Width="94.5" Height="75.6" DataColumn="comSetting_Tbl.CompanyLogo"/>
      <TextObject Name="Text10" Left="113.4" Top="85.05" Width="151.2" Height="18.9" Text="[comSetting_Tbl.Phone]" Font="Arial, 10pt"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="117.4" Width="287.28" Height="37.8">
      <TextObject Name="Text3" Left="217.35" Top="9.45" Width="66.15" Height="18.9" Text="اسم الصنف" HorzAlign="Center" Font="Arial, 8pt, style=Bold"/>
      <TextObject Name="Text5" Left="122.85" Top="9.45" Width="66.15" Height="18.9" Text="الكمية" HorzAlign="Center" Font="Arial, 7pt, style=Bold"/>
      <TextObject Name="Text7" Top="9.45" Width="94.5" Height="18.9" Text="السعر" HorzAlign="Center" Font="Arial, 7pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="159.2" Width="287.28" Height="56.7" CanGrow="true" CanShrink="true" DataSource="View_Order">
      <TextObject Name="Text2" Left="189" Top="18.9" Width="94.5" Height="18.9" Text="[View_Order.ItemName]" HorzAlign="Center" Font="Arial, 7pt, style=Bold"/>
      <TextObject Name="Text4" Left="85.05" Top="18.9" Width="94.5" Height="18.9" Text="[View_Order.ord_Qty]" HorzAlign="Center" Font="Arial, 7pt, style=Bold"/>
      <TextObject Name="Text6" Top="18.9" Width="85.05" Height="18.9" Text="[View_Order.ord_Price]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Center" WordWrap="false" Font="Arial, 7pt, style=Bold" Trimming="EllipsisCharacter"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="219.9" Width="287.28" Height="179.55">
      <TextObject Name="Text8" Left="151.2" Top="56.7" Width="132.3" Height="18.9" Text="[Sales_Tbl.final_total]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 7pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text9" Left="151.2" Top="28.35" Width="132.3" Height="18.9" Text="[Sales_Tbl.tax_total]" Format="Currency" Format.UseLocale="true" Format.DecimalDigits="2" HorzAlign="Right" WordWrap="false" Font="Arial, 7pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text11" Left="151.2" Top="103.95" Width="113.4" Height="18.9" Text="[View_Order.Table_Name]" Font="Arial, 10pt"/>
    </PageFooterBand>
  </ReportPage>
</Report>
