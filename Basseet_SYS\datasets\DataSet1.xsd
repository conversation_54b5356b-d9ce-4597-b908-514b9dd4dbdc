﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet1" targetNamespace="http://tempuri.org/DataSet1.xsd" xmlns:mstns="http://tempuri.org/DataSet1.xsd" xmlns="http://tempuri.org/DataSet1.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="smart_rest1ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="smart_rest1ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Basseet_SYS.My.MySettings.GlobalReference.Default.smart_rest1ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet1" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DataSet1" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet1">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="comSetting_Tbl" msprop:Generator_RowClassName="comSetting_TblRow" msprop:Generator_RowEvHandlerName="comSetting_TblRowChangeEventHandler" msprop:Generator_RowDeletedName="comSetting_TblRowDeleted" msprop:Generator_RowDeletingName="comSetting_TblRowDeleting" msprop:Generator_RowEvArgName="comSetting_TblRowChangeEvent" msprop:Generator_TablePropName="comSetting_Tbl" msprop:Generator_RowChangedName="comSetting_TblRowChanged" msprop:Generator_UserTableName="comSetting_Tbl" msprop:Generator_RowChangingName="comSetting_TblRowChanging" msprop:Generator_TableClassName="comSetting_TblDataTable" msprop:Generator_TableVarName="tablecomSetting_Tbl">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Company_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_UserColumnName="Company_ID" msprop:Generator_ColumnPropNameInTable="Company_IDColumn" msprop:Generator_ColumnPropNameInRow="Company_ID" msprop:Generator_ColumnVarNameInTable="columnCompany_ID" type="xs:int" />
              <xs:element name="CompanyName" msprop:Generator_UserColumnName="CompanyName" msprop:Generator_ColumnPropNameInTable="CompanyNameColumn" msprop:Generator_ColumnPropNameInRow="CompanyName" msprop:Generator_ColumnVarNameInTable="columnCompanyName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_UserColumnName="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone" msprop:Generator_UserColumnName="Phone" msprop:Generator_ColumnPropNameInTable="PhoneColumn" msprop:Generator_ColumnPropNameInRow="Phone" msprop:Generator_ColumnVarNameInTable="columnPhone" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Mobile" msprop:Generator_UserColumnName="Mobile" msprop:Generator_ColumnPropNameInTable="MobileColumn" msprop:Generator_ColumnPropNameInRow="Mobile" msprop:Generator_ColumnVarNameInTable="columnMobile" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CompanyLogo" msprop:Generator_UserColumnName="CompanyLogo" msprop:Generator_ColumnPropNameInTable="CompanyLogoColumn" msprop:Generator_ColumnPropNameInRow="CompanyLogo" msprop:Generator_ColumnVarNameInTable="columnCompanyLogo" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="Phone1" msprop:Generator_UserColumnName="Phone1" msprop:Generator_ColumnPropNameInTable="Phone1Column" msprop:Generator_ColumnPropNameInRow="Phone1" msprop:Generator_ColumnVarNameInTable="columnPhone1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone2" msprop:Generator_UserColumnName="Phone2" msprop:Generator_ColumnPropNameInTable="Phone2Column" msprop:Generator_ColumnPropNameInRow="Phone2" msprop:Generator_ColumnVarNameInTable="columnPhone2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TELEGRAM" msprop:Generator_UserColumnName="TELEGRAM" msprop:Generator_ColumnPropNameInTable="TELEGRAMColumn" msprop:Generator_ColumnPropNameInRow="TELEGRAM" msprop:Generator_ColumnVarNameInTable="columnTELEGRAM" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="WHATSAPP" msprop:Generator_UserColumnName="WHATSAPP" msprop:Generator_ColumnPropNameInTable="WHATSAPPColumn" msprop:Generator_ColumnPropNameInRow="WHATSAPP" msprop:Generator_ColumnVarNameInTable="columnWHATSAPP" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Mobile1" msprop:Generator_UserColumnName="Mobile1" msprop:Generator_ColumnPropNameInTable="Mobile1Column" msprop:Generator_ColumnPropNameInRow="Mobile1" msprop:Generator_ColumnVarNameInTable="columnMobile1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Mobile2" msprop:Generator_UserColumnName="Mobile2" msprop:Generator_ColumnPropNameInTable="Mobile2Column" msprop:Generator_ColumnPropNameInRow="Mobile2" msprop:Generator_ColumnVarNameInTable="columnMobile2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GOOGLE_LOC" msprop:Generator_UserColumnName="GOOGLE_LOC" msprop:Generator_ColumnPropNameInTable="GOOGLE_LOCColumn" msprop:Generator_ColumnPropNameInRow="GOOGLE_LOC" msprop:Generator_ColumnVarNameInTable="columnGOOGLE_LOC" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PCNAME" msprop:Generator_UserColumnName="PCNAME" msprop:Generator_ColumnPropNameInTable="PCNAMEColumn" msprop:Generator_ColumnPropNameInRow="PCNAME" msprop:Generator_ColumnVarNameInTable="columnPCNAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="COM_NUM" msprop:Generator_UserColumnName="COM_NUM" msprop:Generator_ColumnPropNameInTable="COM_NUMColumn" msprop:Generator_ColumnPropNameInRow="COM_NUM" msprop:Generator_ColumnVarNameInTable="columnCOM_NUM" type="xs:int" minOccurs="0" />
              <xs:element name="commonName" msprop:Generator_UserColumnName="commonName" msprop:Generator_ColumnPropNameInTable="commonNameColumn" msprop:Generator_ColumnPropNameInRow="commonName" msprop:Generator_ColumnVarNameInTable="columncommonName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="serialNumer" msprop:Generator_UserColumnName="serialNumer" msprop:Generator_ColumnPropNameInTable="serialNumerColumn" msprop:Generator_ColumnPropNameInRow="serialNumer" msprop:Generator_ColumnVarNameInTable="columnserialNumer" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="orgnizationIdentifier" msprop:Generator_UserColumnName="orgnizationIdentifier" msprop:Generator_ColumnPropNameInTable="orgnizationIdentifierColumn" msprop:Generator_ColumnPropNameInRow="orgnizationIdentifier" msprop:Generator_ColumnVarNameInTable="columnorgnizationIdentifier" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="orgnizationUnitName" msprop:Generator_UserColumnName="orgnizationUnitName" msprop:Generator_ColumnPropNameInTable="orgnizationUnitNameColumn" msprop:Generator_ColumnPropNameInRow="orgnizationUnitName" msprop:Generator_ColumnVarNameInTable="columnorgnizationUnitName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="orgnizationName" msprop:Generator_UserColumnName="orgnizationName" msprop:Generator_ColumnPropNameInTable="orgnizationNameColumn" msprop:Generator_ColumnPropNameInRow="orgnizationName" msprop:Generator_ColumnVarNameInTable="columnorgnizationName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="countryName" msprop:Generator_UserColumnName="countryName" msprop:Generator_ColumnPropNameInTable="countryNameColumn" msprop:Generator_ColumnPropNameInRow="countryName" msprop:Generator_ColumnVarNameInTable="columncountryName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="invoiceType" msprop:Generator_UserColumnName="invoiceType" msprop:Generator_ColumnPropNameInTable="invoiceTypeColumn" msprop:Generator_ColumnPropNameInRow="invoiceType" msprop:Generator_ColumnVarNameInTable="columninvoiceType" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="location" msprop:Generator_UserColumnName="location" msprop:Generator_ColumnPropNameInTable="locationColumn" msprop:Generator_ColumnPropNameInRow="location" msprop:Generator_ColumnVarNameInTable="columnlocation" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="industry" msprop:Generator_UserColumnName="industry" msprop:Generator_ColumnPropNameInTable="industryColumn" msprop:Generator_ColumnPropNameInRow="industry" msprop:Generator_ColumnVarNameInTable="columnindustry" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="emailId" msprop:Generator_UserColumnName="emailId" msprop:Generator_ColumnPropNameInTable="emailIdColumn" msprop:Generator_ColumnPropNameInRow="emailId" msprop:Generator_ColumnVarNameInTable="columnemailId" type="xs:int" minOccurs="0" />
              <xs:element name="fax" msprop:Generator_UserColumnName="fax" msprop:Generator_ColumnPropNameInTable="faxColumn" msprop:Generator_ColumnPropNameInRow="fax" msprop:Generator_ColumnVarNameInTable="columnfax" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="city" msprop:Generator_UserColumnName="city" msprop:Generator_ColumnPropNameInTable="cityColumn" msprop:Generator_ColumnPropNameInRow="city" msprop:Generator_ColumnVarNameInTable="columncity" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="vat_no" msprop:Generator_UserColumnName="vat_no" msprop:Generator_ColumnPropNameInTable="vat_noColumn" msprop:Generator_ColumnPropNameInRow="vat_no" msprop:Generator_ColumnVarNameInTable="columnvat_no" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Sales_Tbl" msprop:Generator_RowClassName="Sales_TblRow" msprop:Generator_RowEvHandlerName="Sales_TblRowChangeEventHandler" msprop:Generator_RowDeletedName="Sales_TblRowDeleted" msprop:Generator_RowDeletingName="Sales_TblRowDeleting" msprop:Generator_RowEvArgName="Sales_TblRowChangeEvent" msprop:Generator_TablePropName="Sales_Tbl" msprop:Generator_RowChangedName="Sales_TblRowChanged" msprop:Generator_UserTableName="Sales_Tbl" msprop:Generator_RowChangingName="Sales_TblRowChanging" msprop:Generator_TableClassName="Sales_TblDataTable" msprop:Generator_TableVarName="tableSales_Tbl">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Sale_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_UserColumnName="Sale_ID" msprop:Generator_ColumnPropNameInTable="Sale_IDColumn" msprop:Generator_ColumnPropNameInRow="Sale_ID" msprop:Generator_ColumnVarNameInTable="columnSale_ID" type="xs:int" />
              <xs:element name="Order_No" msprop:Generator_UserColumnName="Order_No" msprop:Generator_ColumnPropNameInTable="Order_NoColumn" msprop:Generator_ColumnPropNameInRow="Order_No" msprop:Generator_ColumnVarNameInTable="columnOrder_No" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInTable="TotalColumn" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" type="xs:decimal" minOccurs="0" />
              <xs:element name="SalesDate" msprop:Generator_UserColumnName="SalesDate" msprop:Generator_ColumnPropNameInTable="SalesDateColumn" msprop:Generator_ColumnPropNameInRow="SalesDate" msprop:Generator_ColumnVarNameInTable="columnSalesDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SalesTime" msprop:Generator_UserColumnName="SalesTime" msprop:Generator_ColumnPropNameInTable="SalesTimeColumn" msprop:Generator_ColumnPropNameInRow="SalesTime" msprop:Generator_ColumnVarNameInTable="columnSalesTime" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Cashier" msprop:Generator_UserColumnName="Cashier" msprop:Generator_ColumnPropNameInTable="CashierColumn" msprop:Generator_ColumnPropNameInRow="Cashier" msprop:Generator_ColumnVarNameInTable="columnCashier" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PaidType" msprop:Generator_UserColumnName="PaidType" msprop:Generator_ColumnPropNameInTable="PaidTypeColumn" msprop:Generator_ColumnPropNameInRow="PaidType" msprop:Generator_ColumnVarNameInTable="columnPaidType" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="UserName" msprop:Generator_UserColumnName="UserName" msprop:Generator_ColumnPropNameInTable="UserNameColumn" msprop:Generator_ColumnPropNameInRow="UserName" msprop:Generator_ColumnVarNameInTable="columnUserName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tax_value" msprop:Generator_UserColumnName="tax_value" msprop:Generator_ColumnPropNameInTable="tax_valueColumn" msprop:Generator_ColumnPropNameInRow="tax_value" msprop:Generator_ColumnVarNameInTable="columntax_value" type="xs:decimal" minOccurs="0" />
              <xs:element name="tax_total" msprop:Generator_UserColumnName="tax_total" msprop:Generator_ColumnPropNameInTable="tax_totalColumn" msprop:Generator_ColumnPropNameInRow="tax_total" msprop:Generator_ColumnVarNameInTable="columntax_total" type="xs:decimal" minOccurs="0" />
              <xs:element name="final_total" msprop:Generator_UserColumnName="final_total" msprop:Generator_ColumnPropNameInTable="final_totalColumn" msprop:Generator_ColumnPropNameInRow="final_total" msprop:Generator_ColumnVarNameInTable="columnfinal_total" type="xs:decimal" minOccurs="0" />
              <xs:element name="disc_total" msprop:Generator_UserColumnName="disc_total" msprop:Generator_ColumnPropNameInTable="disc_totalColumn" msprop:Generator_ColumnPropNameInRow="disc_total" msprop:Generator_ColumnVarNameInTable="columndisc_total" type="xs:decimal" minOccurs="0" />
              <xs:element name="QrCode_Pic" msprop:Generator_UserColumnName="QrCode_Pic" msprop:Generator_ColumnPropNameInTable="QrCode_PicColumn" msprop:Generator_ColumnPropNameInRow="QrCode_Pic" msprop:Generator_ColumnVarNameInTable="columnQrCode_Pic" type="xs:base64Binary" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="View_Order" msprop:Generator_RowClassName="View_OrderRow" msprop:Generator_RowEvHandlerName="View_OrderRowChangeEventHandler" msprop:Generator_RowDeletedName="View_OrderRowDeleted" msprop:Generator_RowDeletingName="View_OrderRowDeleting" msprop:Generator_RowEvArgName="View_OrderRowChangeEvent" msprop:Generator_TablePropName="View_Order" msprop:Generator_RowChangedName="View_OrderRowChanged" msprop:Generator_UserTableName="View_Order" msprop:Generator_RowChangingName="View_OrderRowChanging" msprop:Generator_TableClassName="View_OrderDataTable" msprop:Generator_TableVarName="tableView_Order">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Order_ID" msprop:Generator_UserColumnName="Order_ID" msprop:Generator_ColumnPropNameInTable="Order_IDColumn" msprop:Generator_ColumnPropNameInRow="Order_ID" msprop:Generator_ColumnVarNameInTable="columnOrder_ID" type="xs:int" />
              <xs:element name="Order_No" msprop:Generator_UserColumnName="Order_No" msprop:Generator_ColumnPropNameInTable="Order_NoColumn" msprop:Generator_ColumnPropNameInRow="Order_No" msprop:Generator_ColumnVarNameInTable="columnOrder_No" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Item_ID" msprop:Generator_UserColumnName="Item_ID" msprop:Generator_ColumnPropNameInTable="Item_IDColumn" msprop:Generator_ColumnPropNameInRow="Item_ID" msprop:Generator_ColumnVarNameInTable="columnItem_ID" type="xs:int" />
              <xs:element name="ord_Price" msprop:Generator_UserColumnName="ord_Price" msprop:Generator_ColumnPropNameInTable="ord_PriceColumn" msprop:Generator_ColumnPropNameInRow="ord_Price" msprop:Generator_ColumnVarNameInTable="columnord_Price" type="xs:decimal" minOccurs="0" />
              <xs:element name="ord_Qty" msprop:Generator_UserColumnName="ord_Qty" msprop:Generator_ColumnPropNameInTable="ord_QtyColumn" msprop:Generator_ColumnPropNameInRow="ord_Qty" msprop:Generator_ColumnVarNameInTable="columnord_Qty" type="xs:int" minOccurs="0" />
              <xs:element name="ord_Total" msprop:Generator_UserColumnName="ord_Total" msprop:Generator_ColumnPropNameInTable="ord_TotalColumn" msprop:Generator_ColumnPropNameInRow="ord_Total" msprop:Generator_ColumnVarNameInTable="columnord_Total" type="xs:decimal" minOccurs="0" />
              <xs:element name="OrderDate" msprop:Generator_UserColumnName="OrderDate" msprop:Generator_ColumnPropNameInTable="OrderDateColumn" msprop:Generator_ColumnPropNameInRow="OrderDate" msprop:Generator_ColumnVarNameInTable="columnOrderDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Table_Name" msprop:Generator_UserColumnName="Table_Name" msprop:Generator_ColumnPropNameInTable="Table_NameColumn" msprop:Generator_ColumnPropNameInRow="Table_Name" msprop:Generator_ColumnVarNameInTable="columnTable_Name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ord_Status" msprop:Generator_UserColumnName="ord_Status" msprop:Generator_ColumnPropNameInTable="ord_StatusColumn" msprop:Generator_ColumnPropNameInRow="ord_Status" msprop:Generator_ColumnVarNameInTable="columnord_Status" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="UserName" msprop:Generator_UserColumnName="UserName" msprop:Generator_ColumnPropNameInTable="UserNameColumn" msprop:Generator_ColumnPropNameInRow="UserName" msprop:Generator_ColumnVarNameInTable="columnUserName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ItemName" msprop:Generator_UserColumnName="ItemName" msprop:Generator_ColumnPropNameInTable="ItemNameColumn" msprop:Generator_ColumnPropNameInRow="ItemName" msprop:Generator_ColumnVarNameInTable="columnItemName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Itembarcode" msprop:Generator_UserColumnName="Itembarcode" msprop:Generator_ColumnPropNameInTable="ItembarcodeColumn" msprop:Generator_ColumnPropNameInRow="Itembarcode" msprop:Generator_ColumnVarNameInTable="columnItembarcode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Cat_ID" msprop:Generator_UserColumnName="Cat_ID" msprop:Generator_ColumnPropNameInTable="Cat_IDColumn" msprop:Generator_ColumnPropNameInRow="Cat_ID" msprop:Generator_ColumnVarNameInTable="columnCat_ID" type="xs:int" />
              <xs:element name="CatName" msprop:Generator_UserColumnName="CatName" msprop:Generator_ColumnPropNameInTable="CatNameColumn" msprop:Generator_ColumnPropNameInRow="CatName" msprop:Generator_ColumnVarNameInTable="columnCatName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:comSetting_Tbl" />
      <xs:field xpath="mstns:Company_ID" />
    </xs:unique>
    <xs:unique name="Sales_Tbl_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Sales_Tbl" />
      <xs:field xpath="mstns:Sale_ID" />
    </xs:unique>
    <xs:unique name="View_Order_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:View_Order" />
      <xs:field xpath="mstns:Order_ID" />
    </xs:unique>
  </xs:element>
</xs:schema>