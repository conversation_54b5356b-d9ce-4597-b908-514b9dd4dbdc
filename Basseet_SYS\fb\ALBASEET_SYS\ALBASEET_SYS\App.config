﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="ALBASEET_SYS.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <userSettings>
    <ALBASEET_SYS.My.MySettings>
      <setting name="SIZE" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="Tax_Status" serializeAs="String">
        <value />
      </setting>
      <setting name="previousUUID" serializeAs="String">
        <value />
      </setting>
      <setting name="AddressISSUR" serializeAs="String">
        <value />
      </setting>
      <setting name="RegistrationNumber" serializeAs="String">
        <value />
      </setting>
      <setting name="serialDevice" serializeAs="String">
        <value />
      </setting>
      <setting name="activitycode" serializeAs="String">
        <value />
      </setting>
    </ALBASEET_SYS.My.MySettings>
  </userSettings>
</configuration>