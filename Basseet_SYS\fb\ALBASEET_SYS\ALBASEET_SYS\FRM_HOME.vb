﻿Public Class FRM_HOME
    Private Sub ButtonX1_Click(sender As Object, e As EventArgs) Handles ButtonX1.Click
        Panel1.Width = 135
        SendFormToPanel(New FRM_DEP_CAT)
    End Sub
    Public Sub SendFormToPanel(ByVal Sform As Object)

        If MainPanel.Controls.Count > 0 Then MainPanel.Controls.RemoveAt(0)
        Dim frm As Form = TryCast(Sform, Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        MainPanel.Controls.Add(frm)
        MainPanel.Tag = frm
        frm.Show()

    End Sub

    Private Sub ButtonX2_Click(sender As Object, e As EventArgs) Handles ButtonX2.Click

        'Dim frm As New frm_manage_product
        'frm.TopLevel = False
        'MAINPanel.Controls.Add(frm)

        '' ضبط الموقع ليكون في منتصف الـ Panel
        'frm.Left = (MAINPanel.Width - frm.Width) \ 2
        'frm.Top = (MAINPanel.Height - frm.Height) \ 2

        'frm.BringToFront()
        'frm.Show()
        Panel1.Width = 135
        SendFormToPanel(New frm_manage_product)

    End Sub

    Private Sub ButtonX4_Click(sender As Object, e As EventArgs) Handles ButtonX4.Click
        Panel1.Width = 137
        Frm_pos.Show()
    End Sub
End Class
