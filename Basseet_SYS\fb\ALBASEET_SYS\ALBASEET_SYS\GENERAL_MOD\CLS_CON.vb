﻿Imports FirebirdSql.Data.FirebirdClient
Imports System.Data.Common
Imports System.IO
Public Class CLS_CON

    Private FbCon As FbConnection

    ' تعريف متغير الاتصال العامة
    Public ReadOnly Property Con As FbConnection

    ' Constructor
    Public Sub New()
        ' قراءة سلسلة الاتصال من الملف وتخزينها في FbCon
        FbCon = New FbConnection(File.ReadAllText("D:\ALBASEET_SYS\ALBASEET_SYS\bin\Debug\CONN.txt"))

        ' تهيئة متغير الاتصال العامة
        Con = FbCon
    End Sub

    Private ds As New DataSet
    Private da As New FbDataAdapter
    Public cmd As New FbCommand
    Private dv As New DataView
    Public cur As CurrencyManager
    Private sdv As New DataView
    Public scur As CurrencyManager
    Private ddv As New DataView
    Public dcur As CurrencyManager
    Public frm As New Form
    Private TRANC As FbTransaction
    Public rdr As FbDataReader
    Public adp As FbDataAdapter
    Public dt As DataTable
    Public _ID As Integer = 0
    Public _PrdName As String = ""
    Public _SalePrice As Double = 0.0

    ' الحصول على قيمة الضريبة
    Public Sub Get_Tax_Value()
        If FbCon.State = ConnectionState.Open Then FbCon.Close()
        FbCon.Open()
        cmd = New FbCommand("SELECT * FROM Tax_Tbl", FbCon)
        rdr = cmd.ExecuteReader()
        If rdr.Read() Then
            _Tax_VALUE = Convert.ToDouble(rdr("Tax_Value"))
        Else
            _Tax_VALUE = 0.0
        End If
        rdr.Close()
        FbCon.Close()
    End Sub

    ' فتح الاتصال
    Public Sub Connect()
        If FbCon.State <> ConnectionState.Open Then
            FbCon.Open()
        End If
    End Sub

    ' إغلاق الاتصال
    Public Sub Disconnect()
        If FbCon.State <> ConnectionState.Closed Then
            FbCon.Close()
        End If
    End Sub

    ' البحث باستخدام DataSet
    Public Function SearchDataSet(SQL As String, Optional TableName As String = "0") As DataView
        ds = New DataSet
        adp = New FbDataAdapter(SQL, FbCon)
        adp.Fill(ds, TableName)
        sdv = New DataView(ds.Tables(TableName))
        scur = CType(frm.BindingContext(sdv), CurrencyManager)
        Return sdv
    End Function

    ' تعبئة ComboBox
    Public Sub FillComboBox(cbo As ComboBox, FirstCol As String, SecondCol As String, TableName As String)
        Dim SQL As String = $"SELECT {FirstCol}, {SecondCol} FROM {TableName}"
        cbo.DataSource = SearchDataSet(SQL, TableName)
        cbo.ValueMember = FirstCol
        cbo.DisplayMember = SecondCol
    End Sub

    ' الحصول على الرقم التالي للعمود
    Public Function GetNextID(tablename As String, columnname As String) As Integer
        Dim dt As New DataTable
        Dim adp As New FbDataAdapter
        dt.Clear()
        adp = New FbDataAdapter($"SELECT MAX({columnname}) FROM {tablename}", Con)
        adp.Fill(dt)
        Dim MYID As Integer
        If IsDBNull(dt(0)(0)) Then
            MYID = 1
        Else
            MYID = Convert.ToInt32(dt(0)(0)) + 1
        End If
        Return MYID
    End Function
End Class
