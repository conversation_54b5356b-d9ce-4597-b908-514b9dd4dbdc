﻿Imports FirebirdSql.Data.FirebirdClient
Imports System.Drawing.Printing
Imports System.Management
Imports System.IO
Imports System.Data.SqlClient

Public Class FRM_DEP_CAT
    Dim CONNX As New CLS_CON

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        If TXT_DepName.Text = vbNullString Or CmbPrinter.Text = vbNullString Then
            MessageBox.Show("رجاء ، قم بتعبئة الكل", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Insert_Dep_tbl()
        Load_Dep()


    End Sub

    Public Sub Insert_Dep_tbl()
        Dim Cmd As New FbCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "INSERT INTO Deptbl (DepName, PrinterName, Status) VALUES (@DepName, @PrinterName, @Status)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DepName", TXT_DepName.Text)
            .Parameters.AddWithValue("@PrinterName", CmbPrinter.Text)
            .Parameters.AddWithValue("@Status", Check_active.Checked)
        End With
        If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX.Con.Close()
        MsgBox("تم إضافة البرنتر بنجاح", MsgBoxStyle.Information, "حفظ")
    End Sub



    Public Sub Update_Dep_tbl()
        Dim Cmd As New FbCommand
        With Cmd
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "UPDATE Deptbl SET DepName = @DepName, PrinterName = @PrinterName, Status = @Status WHERE DepID = @DepID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@DepName", TXT_DepName.Text)
            .Parameters.AddWithValue("@PrinterName", CmbPrinter.Text)
            .Parameters.AddWithValue("@Status", Check_active.Checked)
            .Parameters.AddWithValue("@DepID", Txt_ID.Text)
        End With
        If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        CONNX.Con.Open()
        Cmd.ExecuteNonQuery()
        CONNX.Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
    End Sub

    Private Sub BtnSaveCat_Click(sender As Object, e As EventArgs) Handles BtnSaveCat.Click
        InsertCat()
        Load_Data_Cat()
    End Sub

    ' Add other methods (ClearText, PrintersCombo, etc.) with similar modifications for Firebird.
    Public Sub Load_Data_Cat()
        DgvCat.Rows.Clear()
        If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        CONNX.Con.Open()
        Dim Cmd As New FbCommand("SELECT Cat_Tbl.Cat_ID, Cat_Tbl.CatName, Deptbl.DepName FROM Cat_Tbl INNER JOIN Deptbl ON Cat_Tbl.DepID = Deptbl.DepID", CONNX.Con)
        CONNX.rdr = Cmd.ExecuteReader()
        While CONNX.rdr.Read()
            DgvCat.Rows.Add(CONNX.rdr("Cat_ID").ToString(), CONNX.rdr("CatName").ToString(), CONNX.rdr("DepName").ToString())
        End While
        CONNX.rdr.Close()
        CONNX.Con.Close()
    End Sub
    Public Sub Load_Dep()
        Dgv_Dep.Rows.Clear()
        If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        CONNX.Con.Open()
        Dim Cmd As New FbCommand("SELECT * FROM Deptbl", CONNX.Con)
        CONNX.rdr = Cmd.ExecuteReader()
        While CONNX.rdr.Read()
            Dim MY_Status As String = If(CONNX.rdr("Status"), "فعالة", "غير فعالة")
            Dgv_Dep.Rows.Add(CONNX.rdr("DepID").ToString(), CONNX.rdr("DepName").ToString(), CONNX.rdr("PrinterName").ToString(), MY_Status)
        End While
        CONNX.rdr.Close()
        CONNX.Con.Close()
    End Sub
    Public Sub InsertCat()
        Dim CmdInsert As New FbCommand
        With CmdInsert
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "INSERT INTO Cat_Tbl (CatName, DepID, CatColor) VALUES (@CatName, @DepID, @CatColor)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CatName", TxtCatName.Text)
            .Parameters.AddWithValue("@DepID", CmbDep.SelectedValue)
            .Parameters.AddWithValue("@CatColor", btnColor.BackColor.ToArgb())
        End With
        Try
            If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
            CONNX.Con.Open()
            CmdInsert.ExecuteNonQuery()
            CONNX.Con.Close()
            MsgBox("تمت الإضافة بنجاح", MsgBoxStyle.Information, "حفظ")
        Catch ex As Exception
            CONNX.Con.Close()
            MsgBox(ex.Message, MsgBoxStyle.Information)
        End Try
    End Sub
    Public Sub UpdateCat()
        Dim CmdUpdate As New FbCommand
        With CmdUpdate
            .Connection = CONNX.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Cat_Tbl Set CatName = @CatName,DepID=@DepID,CatColor=@CatColor Where Cat_ID = @Cat_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@CatName", SqlDbType.VarChar).Value = TxtCatName.Text
            .Parameters.AddWithValue("@DepID", SqlDbType.Int).Value = CmbDep.SelectedValue
            .Parameters.AddWithValue("@CatColor", SqlDbType.VarChar).Value = btnColor.BackColor.ToArgb()
            .Parameters.AddWithValue("@Cat_ID", SqlDbType.Int).Value = TxtCat_ID.Text
        End With
        Try
            If CONNX.Con.State = 1 Then CONNX.Con.Close()
            CONNX.Con.Open()
            CmdUpdate.ExecuteNonQuery()
            CONNX.Con.Close()
            MsgBox("تم تحديث السجل بنجاح", MsgBoxStyle.Information, "تحديث")
            CmdUpdate = Nothing
        Catch ex As Exception
            CONNX.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If CONNX.Con.State = ConnectionState.Open Then CONNX.Con.Close()
        End Try
    End Sub
    Public Sub ClearCat()
        TxtCatName.Text = ""
        CmbDep.Text = ""
        TxtCatName.Focus()
        BtnSaveCat.Enabled = True
        BtnEditCat.Enabled = False
        BtnDeleteCat.Enabled = False
        BtnNewCat.Enabled = False
    End Sub
    Private Sub PrintersCombo()
        Try
            Dim i As Integer
            Dim pkInstalledPrinters As String

            ' تفريغ العناصر الحالية في القائمة
            CmbPrinter.Items.Clear()

            ' إضافة الطابعات المثبتة محلياً
            For i = 0 To PrinterSettings.InstalledPrinters.Count - 1
                pkInstalledPrinters = PrinterSettings.InstalledPrinters.Item(i)
                CmbPrinter.Items.Add(pkInstalledPrinters)
            Next

            ' إضافة طابعات الشبكة باستخدام WMI
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_Printer WHERE Network = TRUE")
            For Each printer As ManagementObject In searcher.Get()
                CmbPrinter.Items.Add(printer("Name").ToString())
            Next

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.[Error])
        End Try
    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Load_Data_Cat()
    End Sub

    Private Sub PanelEx3_Click(sender As Object, e As EventArgs) Handles PanelEx3.Click


    End Sub

    Private Sub FRM_DEP_CAT_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Load_Data_Cat()
        PrintersCombo()
        Load_Dep()
        Load_Dep_Tbl()
    End Sub
    Public Sub Load_Dep_Tbl()
        CONNX.dt = New DataTable
        CmbDep.DataSource = Nothing
        CmbDep.Items.Clear()
        CmbDep.Text = vbNullString
        CONNX.dt.Clear()
        CONNX.cmd = New FbCommand("Select DepID,DepName From DepTbl ", CONNX.Con) '
        Try
            If CONNX.Con.State = 1 Then CONNX.Con.Close()
            CONNX.Con.Open()
            CONNX.dt.Load(CONNX.cmd.ExecuteReader)
            CONNX.Con.Close()
            CONNX.cmd = Nothing
        Catch ex As Exception
            MessageBox.Show(ex.Message)
            CONNX.Con.Close()
        End Try
        If CONNX.dt.Rows.Count <> 0 Then
            With CmbDep
                .DataSource = CONNX.dt
                .DisplayMember = "DepName"
                .ValueMember = "DepID"
            End With
        End If
        If CmbDep.Items.Count > 0 Then CmbDep.SelectedIndex = -1
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs) Handles BtnEdit.Click
        Update_Dep_tbl()
        Load_Dep()
    End Sub

    Private Sub Dgv_Dep_Click(sender As Object, e As EventArgs) Handles Dgv_Dep.Click
        Txt_ID.Text = Dgv_Dep.CurrentRow.Cells(0).Value
        TXT_DepName.Text = Dgv_Dep.CurrentRow.Cells(1).Value
        CmbPrinter.Text = Dgv_Dep.CurrentRow.Cells(2).Value
        If Dgv_Dep.CurrentRow.Cells(3).Value = "فعالة" Then
            Check_active.Checked = True
        Else
            Check_active.Checked = False
        End If
        BtnSave.Enabled = False
        BtnEdit.Enabled = True

        BtnNew.Enabled = True
    End Sub

    Private Sub DgvCat_Click(sender As Object, e As EventArgs) Handles DgvCat.Click
        TxtCat_ID.Text = DgvCat.CurrentRow.Cells(0).Value
        TxtCatName.Text = DgvCat.CurrentRow.Cells(1).Value
        CmbDep.Text = DgvCat.CurrentRow.Cells(2).Value
        BtnSaveCat.Enabled = False
        BtnEditCat.Enabled = True
        BtnDeleteCat.Enabled = True
        BtnNewCat.Enabled = True

    End Sub

    Private Sub btnSelectColor_Click(sender As Object, e As EventArgs) Handles btnSelectColor.Click
        Try
            ' Show the color dialog.
            Dim result As DialogResult = ColorDialog1.ShowDialog()
            ' See if user pressed ok.
            If result = DialogResult.OK Then
                ' Set form background to the selected color.
                Me.btnColor.BackColor = ColorDialog1.Color
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnEditCat_Click(sender As Object, e As EventArgs) Handles BtnEditCat.Click
        UpdateCat()
        Load_Data_Cat()
    End Sub
    Public Sub Cleardep()
        TXT_DepName.Text = ""
        CmbPrinter.Text = ""
        TxtCatName.Focus()
        BtnSaveCat.Enabled = True
        BtnEditCat.Enabled = False
        BtnDeleteCat.Enabled = False
        BtnNewCat.Enabled = False
    End Sub
    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        Cleardep()
    End Sub

    Private Sub BtnNewCat_Click(sender As Object, e As EventArgs) Handles BtnNewCat.Click
        ClearCat()
    End Sub
End Class
