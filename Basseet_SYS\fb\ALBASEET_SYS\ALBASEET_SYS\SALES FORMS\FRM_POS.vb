﻿Imports System.Data.FBClient
Imports FirebirdSql.Data.FirebirdClient
Imports System.IO
Imports ALBasseet_SYS.DocumentR
Imports Newtonsoft.Json
Imports Newtonsoft.Json.Linq
Imports RestSharp
Imports System.ComponentModel
Imports System.Runtime.Serialization.Json
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports Svg
Imports Net.Pkcs11Interop.Common
'Imports Net.Pkcs11Interop.Common
'Imports System.Xml
'******************************************************************************************************************
Public Class Frm_pos
    Dim connx As New CLS_CON
    Dim btncat As New Button
    Dim btnPrd As New Button
    Dim LblName As New Label
    Dim LblPrice As New Label
    Dim picPanel As New Panel
    Dim MainPanel As New Panel
    Dim _filter As String = ""
    Sub loadall_cat()
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New FbCommand(" Select * from Cat_Tbl", connx.Con)
        Dim adp As New FbDataAdapter(cmd)
        connx.rdr = cmd.ExecuteReader
        While connx.rdr.Read
            btncat = New Button
            btncat.Width = 195
            btncat.Height = 40
            btncat.Text = connx.rdr("CatName").ToString
            btncat.Tag = connx.rdr("Cat_ID").ToString
            btncat.FlatStyle = FlatStyle.Flat
            btncat.FlatAppearance.BorderSize = 0
            btncat.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            btncat.BackColor = Color.FromArgb(Val(connx.rdr.GetValue(2)))
            btncat.ForeColor = Color.White
            btncat.Cursor = Cursors.Hand
            btncat.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btncat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btncat.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            FlowLayoutPanel1.Controls.Add(btncat)
            AddHandler btncat.Click, AddressOf filter_click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Public Sub SendFormToPanel(ByVal Sform As Object)

        If Panel11.Controls.Count > 0 Then Panel11.Controls.RemoveAt(0)
        Dim frm As Form = TryCast(Sform, Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        Panel11.Controls.Add(frm)
        Panel11.Tag = frm
        frm.Show()

    End Sub

    Private Sub Frm_pos_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        loadall_cat()
        Load_Items()
        Get_Info()
        Dgv.Parent = Panel5
        Dgv.Dock = DockStyle.Top
        Dgv.Height = 650 ' تعيين ارتفاع للجريد فيو

        ' تعيين Panel الفرعية داخل Panel الرئيسي
        Panel10.Parent = Panel5
        Panel10.Dock = DockStyle.Fill
        'If _cashier_status = False Then
        '    frm_istlam.ShowDialog()
        'End If

    End Sub

    Private Sub TxtSearchCat_TextChanged(sender As Object, e As EventArgs) Handles TxtSearchCat.TextChanged
        FlowLayoutPanel1.AutoScroll = True
        FlowLayoutPanel1.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New FbCommand(" Select * from Cat_Tbl Where CatName Like '%" & TxtSearchCat.Text & "%'", connx.Con)
        Dim adp As New FbDataAdapter(cmd)
        connx.rdr = cmd.ExecuteReader
        While connx.rdr.Read
            btncat = New Button
            btncat.Width = 195
            btncat.Height = 40
            btncat.Text = connx.rdr("CatName").ToString
            btncat.Tag = connx.rdr("Cat_ID").ToString
            btncat.FlatStyle = FlatStyle.Flat
            btncat.FlatAppearance.BorderSize = 0
            btncat.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            btncat.BackColor = Color.FromArgb(Val(connx.rdr.GetValue(2)))
            btncat.ForeColor = Color.White
            btncat.Cursor = Cursors.Hand
            btncat.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btncat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btncat.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            FlowLayoutPanel1.Controls.Add(btncat)

            AddHandler btncat.Click, AddressOf filter_click

        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Public Sub Load_Items()
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand("Select * from item_Tbl", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185
                MainPanel.Height = 200
            Else
                MainPanel.Width = 89 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "سعر:" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 8.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)
            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
            AddHandler LblPrice.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
        TrackBar1.Value = My.Settings.SIZE
    End Sub
    Public Sub Select_Click(sender As Object, e As EventArgs)
        connx._ID = sender.Tag.ToString()
        txt_pr_id.Text = connx._ID
        ' فتح الاتصال مع قاعدة البيانات
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' جلب بيانات الصنف باستخدام ID
        Dim cmd As New FbCommand("SELECT item_Price FROM item_Tbl WHERE item_ID=@item_ID", connx.Con)
        cmd.Parameters.AddWithValue("@item_ID", connx._ID)

        ' تنفيذ الاستعلام وقراءة النتيجة
        connx.rdr = cmd.ExecuteReader()
        If connx.rdr.Read() Then
            ' تعيين السعر في الحقل النصي

            txt_pr_price.Text = connx.rdr("item_Price").ToString()
        End If

        connx.rdr.Close()
        connx.Con.Close()

        ' فتح النموذج Frm_Qty
        With Frm_Qty
            .Show()
        End With
    End Sub

    Public Sub Load_Items_by_cat()
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand("Select * from item_tbl where cat_id =@cat_id", connx.Con)
        connx.cmd.Parameters.AddWithValue("@cat_id", _filter)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185
                MainPanel.Height = 200
            Else
                MainPanel.Width = 91 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "السعر :" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)
            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
            AddHandler LblPrice.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Public Sub get_Info_Print()
        ' التأكد من أن الاتصال مغلق وفتحه
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' إنشاء DataTable لتخزين النتائج
        Dim MyDt As New DataTable

        ' إعداد أمر FB للقراءة من قاعدة البيانات
        Dim cmd As New FbCommand("SELECT DISTINCT depID as 'القسم', DepName as 'اسم القسم', PrinterName as 'اسم الطابعة', Status as 'الحالة' FROM View_prtBDep WHERE Order_No=@Order_No", connx.Con)
        cmd.Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)

        ' استخدام FBDataAdapter لملء البيانات في MyDt
        Dim adptr As New FbDataAdapter(cmd)
        adptr.Fill(MyDt)

        ' تعيين مصدر البيانات مباشرةً إلى DataGridView بناءً على النتائج
        If MyDt.Rows.Count > 0 Then
            Dgv_print.DataSource = MyDt
        Else
            ' إذا لم توجد بيانات، إفراغ DataGridView عن طريق تعيين DataSource إلى Nothing
            Dgv_print.DataSource = Nothing
        End If

        ' إغلاق الاتصال
        connx.Con.Close()
    End Sub
    Public Sub filter_click(sender As Object, e As EventArgs)
        _filter = sender.Tag.ToString()
        'loadall_Items_Button_By_CaTName()'////////////////لو عاوز اعمل الكاتجوري بالبوتون
        Load_Items_by_cat()
    End Sub
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        loadall_Items_Button()
    End Sub

    Private Sub CheckBox1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox1.CheckedChanged
        Load_Items()
    End Sub

    Private Sub TxtSearchPrd_TextChanged(sender As Object, e As EventArgs) Handles TxtSearchPrd.TextChanged
        FlowLayoutPanel2.Controls.Clear()
        FlowLayoutPanel2.AutoScroll = True
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand("Select * from item_Tbl where itemname like '%" & TxtSearchPrd.Text & "%'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            MainPanel = New Panel
            If CheckBox1.Checked = True Then
                MainPanel.Width = 185 + (2 * TrackBar1.Value)
                MainPanel.Height = 200 + (2 * TrackBar1.Value)
            Else
                MainPanel.Width = 91 + (2 * TrackBar1.Value)
                MainPanel.Height = 100 + (2 * TrackBar1.Value)
            End If
            MainPanel.BorderStyle = BorderStyle.FixedSingle
            '**********************************************************
            LblName = New Label
            LblName.BackColor = Color.FromArgb(45, 45, 48)
            LblName.ForeColor = Color.White
            LblName.Text = connx.rdr("itemName").ToString
            LblName.Tag = connx.rdr("item_ID").ToString
            LblName.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblName.TextAlign = ContentAlignment.MiddleCenter
            LblName.AutoSize = False
            LblName.BorderStyle = BorderStyle.FixedSingle
            LblName.Dock = DockStyle.Top
            '**********************************************************
            LblPrice = New Label
            LblPrice.BackColor = Color.FromArgb(245, 246, 250)
            LblPrice.ForeColor = Color.FromArgb(232, 65, 24)
            LblPrice.Text = "السعر :" & Space(4) & connx.rdr("item_Price").ToString
            LblPrice.Tag = connx.rdr("item_ID").ToString
            LblPrice.Font = New System.Drawing.Font("Tajawal", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            LblPrice.TextAlign = ContentAlignment.MiddleCenter
            LblPrice.AutoSize = False
            LblPrice.BorderStyle = BorderStyle.FixedSingle
            LblPrice.Dock = DockStyle.Bottom
            '**********************************************************
            picPanel = New Panel

            '**********************************************************
            Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
            Dim ms As New MemoryStream(data)
            Dim bitamp As New System.Drawing.Bitmap(ms)
            picPanel = New Panel
            picPanel.Tag = connx.rdr("item_ID").ToString
            picPanel.BackColor = Color.Transparent
            picPanel.BackgroundImageLayout = ImageLayout.Stretch
            picPanel.BackgroundImage = bitamp
            picPanel.Dock = DockStyle.Fill
            '**********************************************************
            MainPanel.Controls.Add(LblName)
            MainPanel.Controls.Add(LblPrice)
            MainPanel.Controls.Add(picPanel)
            '**********************************************************
            FlowLayoutPanel2.Controls.Add(MainPanel)
            AddHandler picPanel.Click, AddressOf Select_Click
            AddHandler LblName.Click, AddressOf Select_Click
            AddHandler LblPrice.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Sub loadall_Items_Button()
        FlowLayoutPanel2.AutoScroll = True
        FlowLayoutPanel2.Controls.Clear()

        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' Modify the query to join item_Tbl with Cat_Tbl to get the category color
        Dim cmd As New FbCommand("SELECT i.itemName, i.item_Price,i.item_qun, i.item_ID, c.CatColor FROM item_Tbl i JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID", connx.Con)
        connx.rdr = cmd.ExecuteReader()
        While connx.rdr.Read()
            ' Create item button
            Dim btnPrd As New Button
            btnPrd.Width = 91 + (2 * TrackBar1.Value)
            btnPrd.Height = 100 + (2 * TrackBar1.Value)
            btnPrd.Text = connx.rdr("itemName").ToString() & vbNewLine & "السعر : " & connx.rdr("item_Price").ToString() & vbNewLine & "الكمية : " & connx.rdr("Item_qun").ToString()
            btnPrd.Tag = connx.rdr("item_ID").ToString()
            btnPrd.FlatStyle = FlatStyle.Flat
            btnPrd.FlatAppearance.BorderSize = 0
            btnPrd.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            ' Set the background color of the item button to the category color
            btnPrd.BackColor = Color.FromArgb(Val(connx.rdr("CatColor").ToString()))  ' Assuming Cat_Color is stored as an integer value representing the color
            btnPrd.ForeColor = Color.White
            btnPrd.Cursor = Cursors.Hand
            btnPrd.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btnPrd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btnPrd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer))

            ' Add the button to FlowLayoutPanel
            FlowLayoutPanel2.Controls.Add(btnPrd)
            AddHandler btnPrd.Click, AddressOf Select_Click
        End While
        connx.rdr.Close()
        connx.Con.Close()
        TrackBar1.Value = My.Settings.SIZE
    End Sub
    Sub loadall_Items_Button_By_CaTName()
        FlowLayoutPanel2.AutoScroll = True
        FlowLayoutPanel2.Controls.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' تعديل جملة FB لعرض المنتجات بناءً على فئة محددة
        connx.cmd = New FbCommand("SELECT i.itemName, i.item_Price, i.item_ID, c.CatColor FROM item_Tbl i JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID WHERE c.Cat_ID = @Cat_ID", connx.Con)
        connx.cmd.Parameters.AddWithValue("@Cat_ID", _filter)

        connx.rdr = connx.cmd.ExecuteReader()

        While connx.rdr.Read()
            ' إنشاء زر لكل منتج
            Dim btnPrd As New Button
            btnPrd.Width = 91 + (2 * TrackBar1.Value)
            btnPrd.Height = 100 + (2 * TrackBar1.Value)
            btnPrd.Text = connx.rdr("itemName").ToString() & vbNewLine & "السعر : " & connx.rdr("item_Price").ToString()
            btnPrd.Tag = connx.rdr("item_ID").ToString()
            btnPrd.FlatStyle = FlatStyle.Flat
            btnPrd.FlatAppearance.BorderSize = 0
            btnPrd.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            ' تعيين لون خلفية الزر حسب لون الفئة
            btnPrd.BackColor = Color.FromArgb(Val(connx.rdr("CatColor").ToString()))
            btnPrd.ForeColor = Color.White
            btnPrd.Cursor = Cursors.Hand
            btnPrd.Font = New System.Drawing.Font("Tajawal", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            btnPrd.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            btnPrd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer), CType(CType(45, Byte), Integer))

            ' إضافة الزر إلى FlowLayoutPanel
            FlowLayoutPanel2.Controls.Add(btnPrd)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    Private Sub TrackBar1_Scroll(sender As Object, e As EventArgs) Handles TrackBar1.Scroll

        For Each ctrl In FlowLayoutPanel2.Controls
            ctrl.Width = 180 + (2 * TrackBar1.Value)
            ctrl.Height = 64 + (2 * TrackBar1.Value)
            My.Settings.SIZE = TrackBar1.Value

        Next
        My.Settings.Save()
    End Sub
    Private Sub BtnNew_Click(sender As Object, e As EventArgs) Handles BtnNew.Click
        With frmOrderType
            .Show()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.None  ' يعين نمط الفورم
        End With
        clerTXT()
        'With Frm_Select_Table
        '    .loadall_Tables()
        '    .Show()

        '    .TopMost = True  ' يجعل الفورم دائماً في الأعلى
        '    .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
        'End With
    End Sub
    '*********************** Get Order No*******************************

    Public Function Get_Order_No() As String
        Try
            Dim orderDate As String = Now.ToString("yyyyMMdd")
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            Dim cmd As New FbCommand(" select * from Order_Tbl Where Order_No Like '" & orderDate & "%' order by Order_ID desc", connx.Con)
            connx.rdr = cmd.ExecuteReader
            connx.rdr.Read()
            If connx.rdr.HasRows Then
                Get_Order_No = CLng(connx.rdr("Order_No").ToString) + 1
            Else
                Get_Order_No = orderDate & "0001"
            End If
            connx.rdr.Close()
            connx.Con.Close()
            Return Get_Order_No
        Catch ex As Exception
        End Try
    End Function
    Public Sub getorder()
        Dim found As Boolean
        Dim tno As String
        Dim _FEE As Double = 0
        Dim OrderDate1 As Date
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Dim cmd As New FbCommand(" select * from Order_Tbl Where Table_Name Like '" & TxtTableName.Text & "' and ord_Status='Open'", connx.Con)
        connx.rdr = cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then
            found = True
            tno = connx.rdr("Order_No").ToString
            OrderDate1 = connx.rdr("OrderDate").ToString
            _FEE = connx.rdr("SERVICE_PRICE").ToString
        Else
            found = False
            tno = Get_Order_No()
        End If
        connx.rdr.Close()
        connx.Con.Close()
        If found = True Then
            TxtOrder_No.Text = tno
            OrderDate.Value = OrderDate1
            txtDeleveryFee.Text = _FEE
        Else

            TxtOrder_No.Text = tno
            OrderDate.Value = Today
        End If

        Load_Order()
        order_total()
    End Sub
    '"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
    Public Sub Load_Order()
        Dgv.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand("Select * from View_Order Where Order_No like '" & TxtOrder_No.Text & "'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            Dgv.Rows.Add(connx.rdr("Order_ID").ToString, connx.rdr("item_ID").ToString, connx.rdr("itemName").ToString, connx.rdr("ord_Price").ToString, connx.rdr("ord_Qty").ToString, connx.rdr("ord_Total").ToString)
        End While
        connx.rdr.Close()
        connx.Con.Close()


    End Sub
    Private Sub Label4_Click_1(sender As Object, e As EventArgs) Handles Label4.Click
        Get_Order_No()
    End Sub
    Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
        ' تحقق أن المستخدم نقر على خلية وليس على رأس العمود أو الصف
        If e.RowIndex < 0 Then Exit Sub

        ' إضافة صنف (زيادة الكمية)
        If e.ColumnIndex = 6 Then
            Dim CmdUpdate2 As New FbCommand
            With CmdUpdate2
                .Connection = connx.Con
                .CommandType = CommandType.Text
                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
                .Parameters.AddWithValue("@Order_ID", Dgv.Rows(e.RowIndex).Cells(0).Value)
                .Parameters.AddWithValue("@ord_Qty", 1)
                .Parameters.AddWithValue("@Item_ID", Dgv.Rows(e.RowIndex).Cells(1).Value)
            End With
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdUpdate2.ExecuteNonQuery()
            connx.Con.Close()
            getorder()

        End If

        ' تحديث الإجمالي
        Dim CmdUpdate As New FbCommand
        With CmdUpdate
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate.ExecuteNonQuery()
        connx.Con.Close()
        getorder()


        ' تقليل الكمية (إنقاص الكمية)
        If e.ColumnIndex = 7 Then
            If Dgv.Rows(e.RowIndex).Cells(4).Value > 1 Then
                Dim CmdUpdate2 As New FbCommand
                With CmdUpdate2
                    .Connection = connx.Con
                    .CommandType = CommandType.Text
                    .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty - @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
                    .Parameters.AddWithValue("@Order_ID", Dgv.Rows(e.RowIndex).Cells(0).Value)
                    .Parameters.AddWithValue("@ord_Qty", 1)
                    .Parameters.AddWithValue("@Item_ID", Dgv.Rows(e.RowIndex).Cells(1).Value)
                End With
                If connx.Con.State = 1 Then connx.Con.Close()
                connx.Con.Open()
                CmdUpdate2.ExecuteNonQuery()
                connx.Con.Close()
                getorder()
            End If
        End If

        ' تحديث الإجمالي
        Dim CmdUpdate1 As New FbCommand
        With CmdUpdate1
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_No"
            .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        CmdUpdate1.ExecuteNonQuery()
        connx.Con.Close()
        getorder()

        ' حذف العنصر
        If e.ColumnIndex = 8 Then
            If MessageBox.Show("هل أنت متأكد من الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then
                del_(e.RowIndex)
            Else
                Exit Sub
            End If
            getorder()
        End If

    End Sub

    ' الإجراء الخاص بحذف الصف
    Sub del_(rowIndex As Integer)
        connx.Con.Open()
        Dim cmd As New FbCommand("Delete from Order_Tbl where Order_ID=@Order_ID AND Item_ID=@Item_ID", connx.Con)
        cmd.Parameters.AddWithValue("@Order_ID", Dgv.Rows(rowIndex).Cells(0).Value)
        cmd.Parameters.AddWithValue("@Item_ID", Dgv.Rows(rowIndex).Cells(1).Value)
        cmd.ExecuteNonQuery()
        connx.Con.Close()

        ' التحقق إذا كانت جميع الصفوف قد تم مسحها
        If Dgv.Rows.Count - 1 = 0 Then
            MessageBox.Show("تم مسح جميع الصفوف!")
            clerTXT()
        End If

    End Sub

    'Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
    '    If e.ColumnIndex = 6 Then
    '        '******************** Add One***************************
    '        Dim CmdUpdate2 As New FBCommand
    '        With CmdUpdate2
    '            .Connection = connx.Con
    '            .CommandType = CommandType.Text
    '            .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty + @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
    '            .Parameters.AddWithValue("@Order_ID", Dgv.CurrentRow.Cells(0).Value)
    '            .Parameters.AddWithValue("@ord_Qty", 1)
    '            .Parameters.AddWithValue("@Item_ID", Dgv.CurrentRow.Cells(1).Value)
    '        End With
    '        If connx.Con.State = 1 Then connx.Con.Close()
    '        connx.Con.Open()
    '        CmdUpdate2.ExecuteNonQuery()
    '        connx.Con.Close()
    '        getorder()
    '        Load_Order()
    '    End If
    '    '*********************************************اضافة المجموع الى الجدول**************************
    '    Dim CmdUpdate As New FBCommand
    '    With CmdUpdate
    '        .Connection = connx.Con
    '        .CommandType = CommandType.Text
    '        .CommandText = "Update Order_Tbl Set ord_Total = ord_Price * ord_Qty Where Order_No=@Order_NO"
    '        .Parameters.AddWithValue("@Order_No", TxtOrder_No.Text)
    '    End With
    '    If connx.Con.State = 1 Then connx.Con.Close()
    '    connx.Con.Open()
    '    CmdUpdate.ExecuteNonQuery()
    '    connx.Con.Close()
    '    'Load_Order()
    '    getorder()
    '    Load_Order()
    '    If e.ColumnIndex = 7 Then
    '        If Dgv.CurrentRow.Cells(4).Value > 1 Then
    '            '******************** min One***************************

    '            Dim CmdUpdate2 As New FBCommand
    '            With CmdUpdate2
    '                .Connection = connx.Con
    '                .CommandType = CommandType.Text
    '                .CommandText = "Update Order_Tbl Set ord_Qty = ord_Qty - @ord_Qty Where Order_ID=@Order_ID and Item_ID=@Item_ID"
    '                .Parameters.AddWithValue("@Order_ID", Dgv.CurrentRow.Cells(0).Value)
    '                .Parameters.AddWithValue("@ord_Qty", 1)
    '                .Parameters.AddWithValue("@Item_ID", Dgv.CurrentRow.Cells(1).Value)
    '            End With
    '            If connx.Con.State = 1 Then connx.Con.Close()
    '            connx.Con.Open()
    '            CmdUpdate2.ExecuteNonQuery()
    '            connx.Con.Close()
    '            getorder()
    '            Load_Order()
    '        End If
    '    End If
    '    '*********************************************اضافة المجموع الى الجدول**************************

    '    If e.ColumnIndex = 8 Then
    '        If MessageBox.Show("هل أنت متأكد من الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then
    '            del_()
    '        Else
    '            Exit Sub
    '        End If

    '    End If
    '    Load_Order()
    'End Sub
    'Sub del_()
    '    connx.Con.Open()
    '    Dim cmd As New FBCommand(" Delete from Order_Tbl where Order_ID=" & Dgv.CurrentRow.Cells(0).Value & " AND Item_ID=" & Dgv.CurrentRow.Cells(1).Value & " ", connx.Con)
    '    ' التحقق إذا كانت جميع الصفوف قد تم مسحها

    '    cmd.ExecuteNonQuery()
    '    connx.Con.Close()

    '    If Dgv.Rows.Count = 0 Then
    '        ' تنفيذ الإجراء المطلوب
    '        MessageBox.Show("تم مسح جميع الصفوف!")
    '        clerTXT() 
    '    End If

    'End Sub

    Sub order_total()
        TxtCount.Text = Dgv.Rows.Count
        Try
            If My.Settings.Tax_Status = True Then
                connx.Get_Tax_Value()
            Else
                _Tax_VALUE = 0.0
            End If
            Dim Total1, _Total_Tax As Double
            For Each row As DataGridViewRow In Dgv.Rows
                Total1 += row.Cells(5).Value
            Next
            TxtTotal.Text = Total1
            _Total_Tax = Total1 * _Tax_VALUE / 100
            LblTax.Text = _Total_Tax
            TxtFinalTotal.Text = Total1 + _Total_Tax + txtDeleveryFee.Text
            TxtCount.Text = Dgv.Rows.Count
        Catch ex As Exception

        End Try
    End Sub

    Private Sub BtnPaid_Click(sender As Object, e As EventArgs) Handles BtnPaid.Click
        get_Info_Print()
        With frm_paid
            _Order_No = TxtOrder_No.Text
            _finalTotal = Val(TxtFinalTotal.Text)
            _TaxTotal = Val(LblTax.Text)
            _Order_Total = Val(TxtTotal.Text)
            .TxtFinalTotal_.Text = TxtFinalTotal.Text
            .TxtDiscount.Text = "0.0"
            connx.Get_Tax_Value()

            .TopMost = True  ' يجعل الفورم دائماً في الأعلى
            .FormBorderStyle = FormBorderStyle.FixedDialog  ' يعين نمط الفورم
            .Show()
        End With
    End Sub

    Public Sub Get_Info()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand(" Select * from comSetting_Tbl ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        connx.rdr.Read()
        If connx.rdr.HasRows Then

            _CompanyName = connx.rdr("CompanyName").ToString
            _vat_No = connx.rdr("Vat_No").ToString
        End If

        connx.rdr.Close()
        connx.Con.Close()
    End Sub
    '**********************************************************************الايصال الالكتروني المصري*********************************************


    Public Function CreateReceiptJson() As String
        Try
            ' تحقق من تهيئة الكائنات الأساسية
            If OrderDate Is Nothing Then
                MessageBox.Show("OrderDate is not initialized.")
                Return String.Empty
            End If

            If TxtOrder_No Is Nothing Then
                MessageBox.Show("TxtOrder_No is not initialized.")
                Return String.Empty
            End If

            If AddressISSUR Is Nothing Then
                MessageBox.Show("AddressISSUR is not initialized.")
                Return String.Empty
            End If

            If receiver1 Is Nothing Then
                MessageBox.Show("receiver1 is not initialized.")
                Return String.Empty
            End If

            ' إعداد الهيدر
            Dim header1 As New Dictionary(Of String, Object) From {
            {"dateTimeIssued", OrderDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")},
            {"receiptNumber", "ERece-" & TxtOrder_No.Text},
            {"uuid", ""},
            {"previousUUID", My.Settings.previousUUID},
            {"referenceOldUUID", ""},
            {"currency", "EGP"},
            {"exchangeRate", 0.0},
            {"sOrderNameCode", ""},
            {"orderdeliveryMode", "FC"}
        }

            ' إعداد documentType
            Dim documentType1 As New Dictionary(Of String, Object) From {
            {"receiptType", "SC"},
            {"typeVersion", "1.2"}
        }

            ' إعداد معلومات المرسل (seller)
            Dim branchAddress1 As New Dictionary(Of String, Object) From {
            {"country", "EG"},
            {"governate", If(AddressISSUR.governate, "")},
            {"regionCity", If(AddressISSUR.regionCity, "")},
            {"street", If(AddressISSUR.street, "")},
            {"buildingNumber", If(AddressISSUR.buildingNumber, "")},
            {"postalCode", If(AddressISSUR.postalCode, "")},
            {"floor", If(AddressISSUR.floor, "")},
            {"room", If(AddressISSUR.room, "")},
            {"landmark", If(AddressISSUR.landmark, "")},
            {"additionalInformation", If(AddressISSUR.additionalInformation, "")}
        }

            Dim seller1 As New Dictionary(Of String, Object) From {
            {"rin", My.Settings.RegistrationNumber},
            {"companyTradeName", If(_CompanyName, "")},
            {"branchCode", If(AddressISSUR.branchID, "")},
            {"branchAddress", branchAddress1},
            {"deviceSerialNumber", My.Settings.serialDevice},
            {"activityCode", My.Settings.activitycode}
        }

            Dim Buyer1 As New Dictionary(Of String, Object) From {
            {"type", If(receiver1.type, "")},
            {"id", If(receiver1.id, "")},
            {"name", If(receiver1.name, "")},
            {"mobileNumber", ""},
            {"paymentNumber", ""}
        }

            ' إعداد العناصر (المنتجات)
            Dim itemData As New List(Of Dictionary(Of String, Object))
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Dim item As New Dictionary(Of String, Object) From {
                    {"internalCode", If(row.Cells(0).Value IsNot Nothing, row.Cells(0).Value.ToString(), "")},
                    {"description", If(row.Cells(2).Value IsNot Nothing, row.Cells(2).Value.ToString(), "")},
                    {"itemType", "GS1"},
                    {"itemCode", "037000401629"},
                    {"unitType", "EA"},
                    {"quantity", If(Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToInt32(row.Cells(4).Value), 0)},
                    {"unitPrice", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value), 0.0)},
                    {"netSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value), 0.0)},
                    {"totalSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11, 0.0)},
                    {"total", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11 * 1.024, 0.0)},
                    {"commercialDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 867.86}, {"description", "XYZ"}, {"rate", 2.3}}
                    }},
                    {"itemDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "ABC"}, {"rate", 2.3}},
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "XYZ"}, {"rate", 4.0}}
                    }},
                    {"additionalCommercialDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "ABC"}, {"rate", 10.0}
                    }},
                    {"additionalItemDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "XYZ"}, {"rate", 10.0}
                    }},
                    {"valueDifference", 20},
                    {"taxableItems", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}, {"subType", "V009"}, {"rate", 14}}
                    }}
                }
                    itemData.Add(item)
                End If
            Next

            ' تجميع الهيكل الكامل للإيصال
            Dim receipt As New Dictionary(Of String, Object) From {
            {"header", header1},
            {"documentType", documentType1},
            {"seller", seller1},
            {"buyer", Buyer1},
            {"itemData", itemData},
            {"totalSales", 8678.6},
            {"totalCommercialDiscount", 867.86},
            {"totalItemsDiscount", 20},
            {"extraReceiptDiscountData", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"amount", 0}, {"description", "ABC"}}
            }},
            {"netAmount", 7810.74},
            {"feesAmount", 0},
            {"totalAmount", 8887.0436},
            {"taxTotals", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}}
            }},
            {"paymentMethod", "C"},
            {"adjustment", 0}
        }

            ' تجميع القائمة النهائية
            Dim finalReceipt As New Dictionary(Of String, Object) From {
            {"receipts", New List(Of Dictionary(Of String, Object)) From {receipt}}
        }

            ' تحويل إلى JSON
            Dim json As String = JsonConvert.SerializeObject(finalReceipt, Formatting.Indented)
            Return json

        Catch ex As Exception
            MessageBox.Show("Error creating JSON: " & ex.Message)
            Return String.Empty
        End Try
    End Function


    Public Sub SaveJsonToFile()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJson()

        ' تحديد مسار الملف
        Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\EInvoicing\SourceDocumentJson.json"

        ' التحقق من وجود المجلد، إذا لم يكن موجودًا، نقوم بإنشائه
        Dim folderPath As String = Path.GetDirectoryName(filePath)
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If

        ' حفظ ملف JSON على القرص
        File.WriteAllText(filePath, receiptJson)

        ' إعلام المستخدم بنجاح العملية
        MessageBox.Show("تم حفظ الإيصال كملف JSON في: " & filePath)
    End Sub


    Public Sub SendReceipt()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJson()

        ' إعداد RestClient
        Dim client As New RestClient("https://api.invoice-system.com/sendReceipt") ' تأكد من تعديل الرابط ليتناسب مع API الإيصالات
        Dim request As New RestRequest(Method.POST)
        request.AddHeader("Authorization", "Bearer your_token_here") ' ضع التوكن الخاص بك هنا
        request.AddHeader("Content-Type", "application/json")
        request.AddParameter("application/json", receiptJson, ParameterType.RequestBody)

        ' إرسال الطلب
        Dim response As RestResponse = client.Execute(request)

        ' معالجة الرد
        If response.IsSuccessful Then
            MessageBox.Show("تم إرسال الإيصال بنجاح!")
        Else
            MessageBox.Show("حدث خطأ أثناء إرسال الإيصال: " & response.Content)
        End If
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        CreateReceiptJson()
        SaveJsonToFile()
        '''' ConvertFullContent()
        click_on_bat()
        System.Threading.Thread.Sleep(3000)
        ProcessReceiptFile()
        ConvertFileToSHA256()
        SaveJsonToFileFinal()
    End Sub
    'Public Sub ConvertFullContent()
    '    ' Load data from the file
    '    Dim jsonData As String = File.ReadAllText("D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt.json", System.Text.Encoding.UTF8)

    '    ' Parse the JSON text
    '    Dim data As JObject = JObject.Parse(jsonData)

    '    ' Initialize a StringBuilder for the output
    '    Dim result As New StringBuilder()

    '    ' Process each receipt
    '    For Each receipt As JObject In data("receipts")
    '        ProcessJsonObject(receipt, result)
    '        result.AppendLine() ' Add a newline after each receipt
    '    Next

    '    ' Save the resulting file
    '    Dim outputPath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt2.json"
    '    File.WriteAllText(outputPath, result.ToString(), System.Text.Encoding.UTF8)

    '    Console.WriteLine("Data converted successfully.")
    'End Sub

    ' Recursive function to process each JSON object
    Private Sub ProcessJsonObject(jObject As JObject, result As StringBuilder)
        For Each property1 In jObject.Properties()
            If property1.Value.Type = JTokenType.Object Then
                result.Append("""" & property1.Name & """")
                ProcessJsonObject(property1.Value, result)
            ElseIf property1.Value.Type = JTokenType.Array Then
                result.Append("""" & property1.Name & """: [")
                For Each item As JObject In property1.Value
                    ProcessJsonObject(item, result)
                Next
                result.Append("],")
            Else
                result.Append("""" & property1.Name & """")
                result.Append("""" & property1.Value.ToString() & """")
                result.AppendLine()
            End If
        Next
    End Sub


    Public Sub ConvertFileToSHA256()
        ' قراءة محتوى الملف كـ نص
        Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt3.json" ' ضع مسار الملف هنا
        Dim fileContent As String = File.ReadAllText(filePath, Encoding.UTF8)

        ' تحويل النص إلى SHA-256
        Dim sha256Hash As String = GetSHA256Hash(fileContent)

        ' عرض أو حفظ التجزئة
        Console.WriteLine("SHA-256 Hash: " & sha256Hash)
        Dim xxz As String
        xxz = sha256Hash
        Txthash.Text = xxz
    End Sub

    Private Function GetSHA256Hash(input As String) As String
        ' إنشاء كائن SHA-256
        Using sha256 As SHA256 = SHA256.Create()
            ' تحويل النص إلى بايتات وتجزئته
            Dim bytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(input))

            ' تحويل البايتات إلى سلسلة نصية بالنظام السادس عشر
            Dim sb As New StringBuilder()
            For Each b As Byte In bytes
                sb.Append(b.ToString("x2"))
            Next
            Return sb.ToString()
        End Using
    End Function
    Public Function CreateReceiptJsonfinal() As String
        Try
            ' تحقق من تهيئة الكائنات الأساسية
            If OrderDate Is Nothing Then
                MessageBox.Show("OrderDate is not initialized.")
                Return String.Empty
            End If

            If TxtOrder_No Is Nothing Then
                MessageBox.Show("TxtOrder_No is not initialized.")
                Return String.Empty
            End If

            If AddressISSUR Is Nothing Then
                MessageBox.Show("AddressISSUR is not initialized.")
                Return String.Empty
            End If

            If receiver1 Is Nothing Then
                MessageBox.Show("receiver1 is not initialized.")
                Return String.Empty
            End If

            ' إعداد الهيدر
            Dim header1 As New Dictionary(Of String, Object) From {
            {"dateTimeIssued", OrderDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")},
            {"receiptNumber", "ERece-" & TxtOrder_No.Text},
            {"uuid", Txthash.Text},
            {"previousUUID", My.Settings.previousUUID},
            {"referenceOldUUID", ""},
            {"currency", "EGP"},
            {"exchangeRate", 0.0},
            {"sOrderNameCode", ""},
            {"orderdeliveryMode", "FC"}
        }

            ' إعداد documentType
            Dim documentType1 As New Dictionary(Of String, Object) From {
            {"receiptType", "SC"},
            {"typeVersion", "1.2"}
        }

            ' إعداد معلومات المرسل (seller)
            Dim branchAddress1 As New Dictionary(Of String, Object) From {
            {"country", "EG"},
            {"governate", If(AddressISSUR.governate, "")},
            {"regionCity", If(AddressISSUR.regionCity, "")},
            {"street", If(AddressISSUR.street, "")},
            {"buildingNumber", If(AddressISSUR.buildingNumber, "")},
            {"postalCode", If(AddressISSUR.postalCode, "")},
            {"floor", If(AddressISSUR.floor, "")},
            {"room", If(AddressISSUR.room, "")},
            {"landmark", If(AddressISSUR.landmark, "")},
            {"additionalInformation", If(AddressISSUR.additionalInformation, "")}
        }

            Dim seller1 As New Dictionary(Of String, Object) From {
            {"rin", My.Settings.RegistrationNumber},
            {"companyTradeName", If(_CompanyName, "")},
            {"branchCode", If(AddressISSUR.branchID, "")},
            {"branchAddress", branchAddress1},
            {"deviceSerialNumber", My.Settings.serialDevice},
            {"activityCode", My.Settings.activitycode}
        }

            Dim Buyer1 As New Dictionary(Of String, Object) From {
            {"type", If(receiver1.type, "")},
            {"id", If(receiver1.id, "")},
            {"name", If(receiver1.name, "")},
            {"mobileNumber", ""},
            {"paymentNumber", ""}
        }

            ' إعداد العناصر (المنتجات)
            Dim itemData As New List(Of Dictionary(Of String, Object))
            For Each row As DataGridViewRow In Dgv.Rows
                If Not row.IsNewRow Then
                    Dim item As New Dictionary(Of String, Object) From {
                    {"internalCode", If(row.Cells(0).Value IsNot Nothing, row.Cells(0).Value.ToString(), "")},
                    {"description", If(row.Cells(2).Value IsNot Nothing, row.Cells(2).Value.ToString(), "")},
                    {"itemType", "GS1"},
                    {"itemCode", "037000401629"},
                    {"unitType", "EA"},
                    {"quantity", If(Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToInt32(row.Cells(4).Value), 0)},
                    {"unitPrice", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value), 0.0)},
                    {"netSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value), 0.0)},
                    {"totalSale", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11, 0.0)},
                    {"total", If(Double.TryParse(row.Cells(3).Value?.ToString(), Nothing) AndAlso Integer.TryParse(row.Cells(4).Value?.ToString(), Nothing), Convert.ToDouble(row.Cells(3).Value) * Convert.ToInt32(row.Cells(4).Value) * 1.11 * 1.024, 0.0)},
                    {"commercialDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 867.86}, {"description", "XYZ"}, {"rate", 2.3}}
                    }},
                    {"itemDiscountData", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "ABC"}, {"rate", 2.3}},
                        New Dictionary(Of String, Object) From {{"amount", 10}, {"description", "XYZ"}, {"rate", 4.0}}
                    }},
                    {"additionalCommercialDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "ABC"}, {"rate", 10.0}
                    }},
                    {"additionalItemDiscount", New Dictionary(Of String, Object) From {
                        {"amount", 9456.1404}, {"description", "XYZ"}, {"rate", 10.0}
                    }},
                    {"valueDifference", 20},
                    {"taxableItems", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}, {"subType", "V009"}, {"rate", 14}}
                    }}
                }
                    itemData.Add(item)
                End If
            Next

            ' تجميع الهيكل الكامل للإيصال
            Dim receipt As New Dictionary(Of String, Object) From {
            {"header", header1},
            {"documentType", documentType1},
            {"seller", seller1},
            {"buyer", Buyer1},
            {"itemData", itemData},
            {"totalSales", 8678.6},
            {"totalCommercialDiscount", 867.86},
            {"totalItemsDiscount", 20},
            {"extraReceiptDiscountData", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"amount", 0}, {"description", "ABC"}}
            }},
            {"netAmount", 7810.74},
            {"feesAmount", 0},
            {"totalAmount", 8887.0436},
            {"taxTotals", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"taxType", "T1"}, {"amount", 1096.3036}}
            }},
            {"paymentMethod", "C"},
            {"adjustment", 0}
        }

            ' تجميع القائمة النهائية
            Dim finalReceipt As New Dictionary(Of String, Object) From {
            {"receipts", New List(Of Dictionary(Of String, Object)) From {receipt}}
        }

            ' تحويل إلى JSON
            Dim json As String = JsonConvert.SerializeObject(finalReceipt, Formatting.Indented)
            Return json

        Catch ex As Exception
            MessageBox.Show("Error creating JSON: " & ex.Message)
            Return String.Empty
        End Try
    End Function
    Public Sub SaveJsonToFileFinal()
        ' إنشاء JSON للإيصال
        Dim receiptJson As String = CreateReceiptJsonfinal()

        ' تحديد مسار الملف
        Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receiptf.json"

        ' التحقق من وجود المجلد، إذا لم يكن موجودًا، نقوم بإنشائه
        Dim folderPath As String = Path.GetDirectoryName(filePath)
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If

        ' حفظ ملف JSON على القرص
        File.WriteAllText(filePath, receiptJson)

        '' إعلام المستخدم بنجاح العملية
        'MessageBox.Show("تم حفظ الإيصال كملف JSON في: " & filePath)
    End Sub

    'Public Function FormatReceiptData(filePath As String) As String
    '    Try
    '        ' قراءة محتوى الملف
    '        Dim input As String = File.ReadAllText(filePath)

    '        ' إزالة الرموز غير المرغوب فيها وتوحيد السطور
    '        Dim lines = input.Split(New String() {Environment.NewLine}, StringSplitOptions.RemoveEmptyEntries)
    '        Dim result = String.Join(" ", lines)

    '        ' إزالة "RECEIPTS""RECEIPTS" من بداية النص
    '        If result.StartsWith("""RECEIPTS""""RECEIPTS""") Then
    '            result = result.Substring("""RECEIPTS""""RECEIPTS""".Length)
    '        End If

    '        ' معالجة باقي النص كما هو
    '        Dim fields As String() = Regex.Split(result, """(?=\w)")
    '        Dim formattedResult As New StringBuilder()

    '        For Each field As String In fields
    '            If Not String.IsNullOrEmpty(field) Then
    '                formattedResult.Append($"""{field}""")
    '            End If
    '        Next

    '        Return formattedResult.ToString()

    '    Catch ex As FileNotFoundException
    '        Return "Error: File not found"
    '    Catch ex As Exception
    '        Return "Error processing file: " & ex.Message
    '    End Try
    'End Function
    Public Function RemoveReceiptsHeader(input As String) As String
        Try
            ' إزالة "RECEIPTS""RECEIPTS" من النص
            Return input.Replace("""RECEIPTS""""RECEIPTS""", "")
        Catch ex As Exception
            Return "Error: " & ex.Message
        End Try
    End Function

    ' مثال على الاستخدام
    Public Sub ProcessReceiptFile()
        '    ' مسار الملف - قم بتغييره حسب موقع الملف لديك
        '    Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\EInvoicing\CanonicalString.txt"

        '    ' معالجة الملف
        '    Dim formattedData = FormatReceiptData(filePath)

        '    ' طباعة النتيجة
        '    Console.WriteLine(formattedData)

        '    ' حفظ النتيجة في ملف جديد (اختياري)
        '    Dim outputPath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt3.json"
        '    File.WriteAllText(outputPath, formattedData)
        'End Sub
        Try
            ' قراءة النص من ملف
            Dim filePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\EInvoicing\CanonicalString.txt"
            Dim fileContent As String = System.IO.File.ReadAllText(filePath)

            ' استدعاء الدالة
            Dim result As String = RemoveReceiptsHeader(fileContent)

            ' حفظ النتيجة في ملف جديد
            System.IO.File.WriteAllText("D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\receipt3.json", result)

            'MessageBox.Show("معالجة إرسال الإيصال")

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        End Try
    End Sub
    Sub click_on_bat()
        Try
            ' تحديد مسار الملف
            Dim batFilePath As String = "D:\Basseet_SYS\Basseet_SYS\EGY_RECIPT\EInvoicing\SubmitInvoices.bat"

            ' التحقق من وجود الملف
            If System.IO.File.Exists(batFilePath) Then
                Process.Start(batFilePath)
                System.Threading.Thread.Sleep(3000)
                'MessageBox.Show("تم تشغيل الملف بنجاح")
            Else
                MessageBox.Show("الملف غير موجود!")
            End If

        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        End Try
    End Sub

    Sub clerTXT()
        TxtCount.Text = 0.0
        TxtTotal.Text = 0.0
        TxtFinalTotal.Text = 0.0
        txtDiscountV.Text = 0.0
        LblTax.Text = 0.0
        txtDeleveryFee.Text = 0.0
    End Sub

    Private Sub istlam_Click(sender As Object, e As EventArgs) Handles istlam.Click
        With frm_istlam
            .txt_total.Text = 0.0
            '''''''''''''''''''''''''''''.txt_cashierBalance.Text = _cashier_balance
            .ShowDialog()
        End With
    End Sub

    Private Sub taslim_Click(sender As Object, e As EventArgs) Handles taslim.Click
        With frm_taslim
            .get_cash()
            .get_visa()
            .get_start()
            .txt_total_total.Text = Val(.txt_total_cash.Text) + Val(.txt_total_visa.Text) + Val(.txt_start.Text)
            .ShowDialog()
        End With
    End Sub
End Class
