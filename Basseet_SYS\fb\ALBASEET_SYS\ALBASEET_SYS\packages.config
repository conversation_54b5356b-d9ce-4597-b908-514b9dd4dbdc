﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="net48" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net48" />
  <package id="Newton.JsonMediaTypeFormatter" version="1.0.6" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net48" />
  <package id="RestSharp" version="112.1.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.Serialization.Json" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.4" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WebApiContrib.Formatting.Jsonp" version="3.0.2" targetFramework="net48" />
</packages>