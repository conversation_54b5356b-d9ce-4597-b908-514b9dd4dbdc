﻿Imports FirebirdSql.Data.FirebirdClient
Imports System.IO

Public Class frm_manage_product
    Dim connx As New CLS_CON
    Dim status_0 As Boolean
    Dim status_1 As Boolean
    Private Sub BtnNewPrd_Click(sender As Object, e As EventArgs) Handles BtnNewPrd.Click
        With Frm_Add_Update_Prod
            .Label1.Text = "شاشة اضافة منتج جديد"
            .ClearItems()
            .Load_Cat_Tbl()
            .ShowDialog()
        End With
    End Sub



    Public Sub Load_Prd()
        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()
        connx.FillComboBox(CmbCat, "cat_id", "catName", "cat_tbl")
        DgvPrd.Rows.Clear()
        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()

        connx.cmd = New FbCommand("Select * from VIEW_PROD", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader

        While connx.rdr.Read
            Dim MY_Status As String = ""

            ' Change the condition based on the actual data type in the database
            If connx.rdr("ITEM_STATUS").ToString() = "True" OrElse connx.rdr("ITEM_STATUS").ToString() = "true" Then
                MY_Status = "متوفر"
            Else
                MY_Status = "غير متوفر"
            End If

            DgvPrd.Rows.Add(connx.rdr("Item_ID").ToString(),
                            connx.rdr("Itembarcode").ToString(),
                            connx.rdr("ItemName").ToString(),
                            connx.rdr("cost_price").ToString(),
                            connx.rdr("Item_Price").ToString(),
                            connx.rdr("CatName").ToString(),
                            MY_Status)
        End While

        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub frm_manage_product_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Load_Prd()
    End Sub
    'اقلب ده فيو
    Private Sub DgvPrd_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DgvPrd.CellContentClick
        If e.ColumnIndex = 7 Then
            With Frm_Add_Update_Prod

                If connx.Con.State = 1 Then connx.Con.Close()
                connx.Con.Open()
                connx.cmd = New FBCommand("Select * from View_prod where item_ID=@item_ID", connx.Con)
                connx.cmd.Parameters.AddWithValue("@Item_ID", DgvPrd.CurrentRow.Cells(0).Value)

                connx.rdr = connx.cmd.ExecuteReader
                connx.rdr.Read()
                If connx.rdr.HasRows Then
                    .TxtPrd_ID.Text = connx.rdr("item_ID").ToString
                    .TxtPrd_Barcode.Text = connx.rdr("itemBarcode").ToString
                    .TxtPrdName.Text = connx.rdr("ItemName").ToString
                    .txtCost.Text = connx.rdr("cost_price").ToString
                    Frm_Add_Update_Prod.TxtPrdPrice.Text = connx.rdr("item_Price").ToString
                    Frm_Add_Update_Prod.Cmb_Cat.Text = connx.rdr("catname").ToString
                    Dim data As Byte() = DirectCast(connx.rdr("item_Image"), Byte())
                    Dim ms As New MemoryStream(data)
                    Dim bitamp As New System.Drawing.Bitmap(ms)
                    .Prd_Image.Image = bitamp
                    If connx.rdr("Item_Status") = True Then
                        .CheckStatus.Checked = True
                    Else
                        .CheckStatus.Checked = False
                    End If

                    connx.rdr.Close()
                    connx.Con.Close()
                    .Label1.Text = "تعديل بيانات المنتج " & DgvPrd.CurrentRow.Cells(2).Value
                    .BtnSave.Enabled = False
                    ' Load_Prd()
                    .BtnEdit.Enabled = True
                    .Show()

                End If
            End With
        ElseIf e.ColumnIndex = 8 Then
            If MessageBox.Show("هل أنت متأكد من مواصلة عملية الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
                Exit Sub
            Else
                Delete_Prd_Tbl(DgvPrd)
            End If
            Load_Prd()
        End If
    End Sub
    Public Sub Delete_Prd_Tbl(ByVal dgv_Prd_Tbl As DataGridView)
        Dim Position As Integer = dgv_Prd_Tbl.CurrentRow.Index
        Dim ID_Position As Integer = dgv_Prd_Tbl.Rows(Position).Cells(0).Value
        Dim Cmd As New FBCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Delete  From item_Tbl Where item_ID = @item_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@item_ID", FbDbType.Integer).Value = ID_Position
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        MsgBox("تم حذف الصنف.", MsgBoxStyle.Information, "حذف")
        Cmd = Nothing
    End Sub
    Private Sub BtnAllPrd_Click(sender As Object, e As EventArgs) Handles BtnAllPrd.Click
        Load_Prd()
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs) Handles TxtSearch.TextChanged

        DgvPrd.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FBCommand("Select * from View_Prod Where Itembarcode Like '%" & TxtSearch.Text & "%' Or itemName Like '%" & TxtSearch.Text & "%'", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            Dim MY_Status As String = ""
            If connx.rdr("item_Status") = "True" Then
                MY_Status = "متوفر"
            Else
                MY_Status = "غير متوفر"
            End If
            DgvPrd.Rows.Add(connx.rdr("Item_ID").ToString, connx.rdr("Itembarcode").ToString, connx.rdr("itemName").ToString, connx.rdr("cost_Price").ToString, connx.rdr("Item_Price").ToString, connx.rdr("CatName").ToString, MY_Status)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub CmbCat_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CmbCat.SelectedIndexChanged
        DgvPrd.Rows.Clear()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New FbCommand("Select * from VIEW_PROD Where CatName Like '" & CmbCat.Text & "' ", connx.Con)
        connx.rdr = connx.cmd.ExecuteReader
        While connx.rdr.Read
            Dim MY_Status As String = ""
            If connx.rdr("item_Status") = "True" Then
                MY_Status = "متوفر"
            Else
                MY_Status = "غير متوفر"
            End If
            DgvPrd.Rows.Add(connx.rdr("Item_ID").ToString, connx.rdr("Itembarcode").ToString, connx.rdr("itemName").ToString, connx.rdr("cost_Price").ToString, connx.rdr("Item_Price").ToString, connx.rdr("CatName").ToString, MY_Status)
        End While
        connx.rdr.Close()
        connx.Con.Close()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click

    End Sub

    '                                                                                        /
    ' End Sub: load products                                                               /
    '/////////////////////////////////////////////////////////////////////////////////////////
End Class