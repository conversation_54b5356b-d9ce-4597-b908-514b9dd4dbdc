﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>從位址讀取資料單元，或將資料單元寫入位址時，若資料單元不是資料大小的倍數，就會擲回例外狀況 (Exception)。此類別無法被繼承。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>初始化 <see cref="T:System.DataMisalignedException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息來初始化 <see cref="T:System.DataMisalignedException" /> 類別的新執行個體。</summary>
      <param name="message">描述錯誤的 <see cref="T:System.String" /> 物件。<paramref name="message" /> 的內容必須能讓人了解。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和基礎例外狀況來初始化 <see cref="T:System.DataMisalignedException" /> 類別的新執行個體。</summary>
      <param name="message">描述錯誤的 <see cref="T:System.String" /> 物件。<paramref name="message" /> 的內容必須能讓人了解。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
      <param name="innerException">導致目前 <see cref="T:System.DataMisalignedException" /> 的例外狀況。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>DLL 匯入中所指定的 DLL 找不到時所擲回的例外狀況。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>使用預設屬性來初始化 <see cref="T:System.DllNotFoundException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 <see cref="T:System.DllNotFoundException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況的內部例外參考，初始化 <see cref="T:System.DllNotFoundException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>表示遺漏的 <see cref="T:System.Object" />。此類別無法被繼承。</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>代表 <see cref="T:System.Reflection.Missing" /> 類別的唯一執行個體。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>封裝陣列和在特定陣列中的位移 (Offset)。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 結構的新執行個體。</summary>
      <param name="array">Managed 陣列。</param>
      <param name="offset">位移 (以位元組為單位)，屬於透過平台叫用傳遞的元素。</param>
      <exception cref="T:System.ArgumentException">陣列大於 2 GB。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>指出指定的物件是否符合目前的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。</summary>
      <returns>如果物件符合這個 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />，則為 true，否則為 false。</returns>
      <param name="obj">與這個執行個體相互比較的物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>指出指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件是否符合目前的執行個體。</summary>
      <returns>如果指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件符合目前的執行個體，則為 true，否則為 false。</returns>
      <param name="obj">要與這個執行個體相比較的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>傳回這個 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 所參考的 Managed 陣列。</summary>
      <returns>這個執行個體所參考的 Managed 陣列。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>傳回這個實值型別 (Value Type) 的雜湊程式碼。</summary>
      <returns>這個執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>當這個 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 建構完成時，傳回提供的位移。</summary>
      <returns>這個執行個體的位移。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>判斷兩個指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件是否具有相同的值。</summary>
      <returns>如果 <paramref name="a" /> 的值與 <paramref name="b" /> 的值相同，則為 true，否則為 false。</returns>
      <param name="a">
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件，要與 <paramref name="b" /> 參數比較。</param>
      <param name="b">
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件，要與 <paramref name="a" /> 參數比較。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>判斷兩個指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件是否具有相同的值。</summary>
      <returns>如果 <paramref name="a" /> 的值與 <paramref name="b" /> 的值不同，則為 true，否則為 false。</returns>
      <param name="a">
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件，要與 <paramref name="b" /> 參數比較。</param>
      <param name="b">
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件，要與 <paramref name="a" /> 參數比較。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>控制 Unicode 字元是否要轉換成最相近的 ANSI 字元。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> 類別的新執行個體，這個執行個體設定為 <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" /> 屬性的值。</summary>
      <param name="BestFitMapping">true 指示啟用了最適合對應，否則為 false。預設為 true。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>將 Unicode 字元轉換為 ANSI 字元時，取得最適合對應行為。</summary>
      <returns>如果已啟用最適合對應，則為 true，否則為 false。預設為 true。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>在無法對應的 Unicode 字元轉換為 ANSI '?' 字元時，啟用或停用擲回例外狀況。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>將 VT_BSTR 型別的資料從 Managed 封送處理成 Unmanaged 程式碼。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>使用指定的 <see cref="T:System.Object" /> 物件，初始化 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 類別的新執行個體。</summary>
      <param name="value">要包裝並當做 VT_BSTR 封送處理的物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>使用指定的 <see cref="T:System.String" /> 物件，初始化 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 類別的新執行個體。</summary>
      <param name="value">要包裝並當做 VT_BSTR 封送處理的物件。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>取得要當做型別 VT_BSTR 封送處理的已包裝 <see cref="T:System.String" /> 物件。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 所包裝的物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>指定需要呼叫在 Unmanaged 程式碼中實作之方法的呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>呼叫端會清除堆疊。這會啟用有 varargs 的呼叫函式，就可用於接受各種數目參數的方法，如 Printf。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>被呼叫端會清除堆疊。這是針對用平台叫用呼叫 Unmanaged 函式的預設慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>第一個參數為 this 指標且儲存在 register ECX 中。其他參數會被推入至堆疊。這個呼叫慣例是用來呼叫從 Unmanaged DLL 匯出之類別上的方法。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>此成員並不真的是呼叫慣例，但會使用預設平台呼叫慣例。例如，在 Windows 上的預設值為 <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />，在 Windows CE.NET 上則為 <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>指示如果完全沒有產生介面時，要向 COM 公開的介面所產生的類別介面型別。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 列舉值，初始化 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 類別的新執行個體。</summary>
      <param name="classInterfaceType">描述為類別所產生的介面型別。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 列舉型別 (Enumeration) 成員，初始化 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 類別的新執行個體。</summary>
      <param name="classInterfaceType">其中一個 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值，描述為類別產生的介面型別。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>取得描述應該產生哪個型別之類別介面的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值，描述應該產生哪個型別的類別介面。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>描述為類別所產生的類別介面型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>指示類別對 COM 用戶端只支援晚期繫結 (Late Binding)。類別的 dispinterface 應要求會自動地公開至 COM 用戶端。Tlbexp.exe (類型程式庫匯出工具) 產生的型別程式庫不包含 dispinterface 的型別資訊，以避免用戶端快取介面的 DISPID。dispinterface 不會出現 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 中所描述的版本控制問題，因為用戶端只能晚期繫結到介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>指示雙重類別介面自動地產生給類別並公開至 COM。型別資訊產生給類別介面並發行至型別程式庫。由於 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 所描述的版本控制限制，我們非常不鼓勵使用 AutoDual。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>指示沒有類別介面產生給類別。如果沒有明確實作介面，類別只能透過 IDispatch 介面提供晚期繫結存取。這是 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 的建議預設值。使用 ClassInterfaceType.None 是唯一透過類別明確實作的介面公開功能的方法。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>指定從型別程式庫匯入的 Coclass 的類別識別項。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>使用原始 Coclass 的類別識別項，初始化 <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> 的新執行個體。</summary>
      <param name="coClass">
        <see cref="T:System.Type" />，包含原始 Coclass 的類別識別項。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>取得原始 Coclass 的類別識別項。</summary>
      <returns>
        <see cref="T:System.Type" />，包含原始 Coclass 的類別識別項。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>允許事件處理常式的晚期繫結註冊。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>使用指定的型別以及型別上事件的名稱，初始化 <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> 類別的新執行個體。</summary>
      <param name="type">物件的型別。</param>
      <param name="eventName">
        <paramref name="type" /> 上事件的名稱。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>將事件處理常式附加至 COM 物件。</summary>
      <param name="target">事件委派應該繫結的目標物件。</param>
      <param name="handler">事件委派。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>取得這個事件的屬性。</summary>
      <returns>這個事件的唯讀屬性。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>取得宣告這個成員的類別。</summary>
      <returns>宣告這個成員之類別的 <see cref="T:System.Type" /> 物件。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>取得目前成員的名稱。</summary>
      <returns>這個成員的名稱。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>中斷事件處理常式和 COM 物件的連結。</summary>
      <param name="target">與事件委派繫結的目標物件。</param>
      <param name="handler">事件委派。</param>
      <exception cref="T:System.InvalidOperationException">此事件沒有公用 remove 存取子。</exception>
      <exception cref="T:System.ArgumentException">傳入的處理常式無法使用。</exception>
      <exception cref="T:System.Reflection.TargetException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.Exception" />。<paramref name="target" /> 參數為 null 且事件不是靜態的。-或-<see cref="T:System.Reflection.EventInfo" /> 未在目標上宣告。</exception>
      <exception cref="T:System.MethodAccessException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.MemberAccessException" />。呼叫端沒有存取成員的使用權限。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>指定要公開 (Expose) 至 COM 的預設介面。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>使用指定的 <see cref="T:System.Type" /> 物件做為公開至 COM 的預設介面，初始化 <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> 類別的新執行個體。</summary>
      <param name="defaultInterface">
        <see cref="T:System.Type" /> 值，表示要公開至 COM 的預設介面。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>取得 <see cref="T:System.Type" /> 物件，其指定要公開至 COM 的預設介面。</summary>
      <returns>
        <see cref="T:System.Type" /> 物件，其指定要公開至 COM 的預設介面。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>辨識實作事件介面 (從 COM 型別程式庫匯入 Coclass 時所產生) 的方法的來源介面和類別。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>使用來源介面和事件提供者類別，初始化 <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> 類別的新執行個體。</summary>
      <param name="SourceInterface">
        <see cref="T:System.Type" />，包含來自型別程式庫的原始來源介面。COM 使用這個介面回呼至 Managed 類別。</param>
      <param name="EventProvider">
        <see cref="T:System.Type" />，包含實作事件介面之方法的類別。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>取得實作事件介面的方法的類別。</summary>
      <returns>
        <see cref="T:System.Type" />，包含實作事件介面之方法的類別。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>從型別程式庫取得原始來源介面。</summary>
      <returns>包含來源介面的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>提供方法，以便對 COM 物件加入及移除處理事件的 .NET Framework 委派。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>將委派加入至源自 COM 物件的事件引動過程清單。</summary>
      <param name="rcw">COM 物件，觸發呼叫端想要回應的事件。</param>
      <param name="iid">COM 物件用來觸發事件之來源介面的識別項。</param>
      <param name="dispid">來源介面上方法的分派識別項。</param>
      <param name="d">引發 COM 事件時要叫用的委派。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>從源自 COM 物件的事件引動過程清單移除委派。</summary>
      <returns>已從引動過程清單移除的委派。</returns>
      <param name="rcw">委派附加至的 COM 物件。</param>
      <param name="iid">COM 物件用來觸發事件之來源介面的識別項。</param>
      <param name="dispid">來源介面上方法的分派識別項。</param>
      <param name="d">要從引動過程清單移除的委派。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>當無法辨認的 HRESULT 從 COM 方法呼叫傳回時所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>使用預設值，初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>使用指定的訊息，初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 類別的新執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外參考，初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>使用指定的訊息和錯誤碼，初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 類別的新執行個體。</summary>
      <param name="message">訊息，指出例外狀況發生的原因。</param>
      <param name="errorCode">與這個例外狀況相關聯的錯誤碼 (HRESULT) 值。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>指示屬性型別之前已經定義在 COM 中。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.ComImportAttribute" /> 的新執行個體。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>指示公開介面至 COM 的方式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>表示介面已做為雙重介面 公開給 COM，同時啟用早期和晚期繫結。<see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" /> 是預設值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>指出介面已做為分配介面 (Dispinterface) 公開至 COM，僅啟用晚期繫結。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>指示介面公開給 COM 做為 Windows 執行階段 介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>指出介面已做為衍生自 IUnknown 的介面公開給 COM，僅啟用早期繫結。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>描述 COM 成員的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>該成員為一般方法。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>該成員取得屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>該成員設定屬性。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>辨識為屬性類別公開為 COM 事件資源的介面清單。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>使用事件來源介面的名稱，初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 類別的新執行個體。</summary>
      <param name="sourceInterfaces">完整事件來源介面名稱之以 null 分隔的清單。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>使用要當做來源介面使用的型別，初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 類別的新執行個體。</summary>
      <param name="sourceInterface">來源介面的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>使用要當做來源介面使用的型別，初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 類別的新執行個體。</summary>
      <param name="sourceInterface1">預設來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">來源介面的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>使用要當做來源介面使用的型別，初始化 ComSourceInterfacesAttribute 類別的新執行個體。</summary>
      <param name="sourceInterface1">預設來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">來源介面的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>使用要當做來源介面使用的型別，初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 類別的新執行個體。</summary>
      <param name="sourceInterface1">預設來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">來源介面的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface4">來源介面的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>取得事件來源介面的完整名稱。</summary>
      <returns>事件來源介面的完整名稱。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>包裝封送處理器應將其當做 VT_CY 來封送處理的物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>使用要被包裝並當做型別 VT_CY 封送處理的 Decimal，初始化 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 類別的新執行個體。</summary>
      <param name="obj">要被包裝並當做 VT_CY 封送處理的 Decimal。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>使用物件 (包含要被包裝並當做型別 VT_CY 封送處理的 Decimal)，初始化 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 類別的新執行個體。</summary>
      <param name="obj">物件，包含要被包裝並當做 VT_CY 封送處理的 Decimal。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 參數不是 <see cref="T:System.Decimal" /> 型別。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>取得要當做型別 VT_CY 封送處理的已包裝物件。</summary>
      <returns>要當做型別 VT_CY 封送處理的已包裝物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>表示 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 方法的 IUnknown::QueryInterface 呼叫是否會使用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>IUnknown::QueryInterface 方法呼叫可以使用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 介面。當您使用這個值時，<see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 方法多載會如同 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" /> 多載般運作。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>IUnknown::QueryInterface 方法呼叫應忽略 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 介面。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>提供 <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 方法的傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>特定介面 ID 的介面無法使用。在此例中，傳回的介面為 null。對 IUnknown::QueryInterface 的呼叫端則傳回 E_NOINTERFACE。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 方法傳回的介面指標可以當做 IUnknown::QueryInterface 的結果使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>未使用自訂 QueryInterface，而應使用 IUnknown::QueryInterface 的預設實作。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>指定 <see cref="T:System.Runtime.InteropServices.CharSet" /> 列舉型別的值。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 值，初始化 <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> 類別的新執行個體。</summary>
      <param name="charSet">其中一個 <see cref="T:System.Runtime.InteropServices.CharSet" /> 值。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>取得所有 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 呼叫的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 預設值。</summary>
      <returns>所有 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 呼叫的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 預設值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>指定用來搜尋提供平台叫用函式的 DLL 的路徑。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>指定在搜尋平台叫用目標時所使用的路徑，初始化 <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 類別的新執行個體。</summary>
      <param name="paths">列舉值的位元組合，這些值會指定 LoadLibraryEx 函式在平台叫用期間的搜尋路徑。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>取得列舉值的位元組合，這些值會指定 LoadLibraryEx 函式在平台叫用期間的搜尋路徑。</summary>
      <returns>列舉值的位元組合，這些值會指定平台叫用的搜尋路徑。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>從支援預設參數的語言呼叫時，設定參數的預設值。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>使用參數的預設值，初始化 <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> 類別的新執行個體。</summary>
      <param name="value">物件，表示參數的預設值。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>取得參數的預設值。</summary>
      <returns>物件，表示參數的預設值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>包裝封送處理器應將其當做 VT_DISPATCH 來封送處理的物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>使用被包裝的物件，來初始化 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 類別的新執行個體。</summary>
      <param name="obj">要被包裝和轉換成 <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" /> 的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 不是類別或陣列。-或-<paramref name="obj" /> 並不支援 IDispatch。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="obj" /> 參數是以 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 屬性 (Attribute) 標記，該屬性傳遞的值為 false。-或-<paramref name="obj" /> 參數繼承自以 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 屬性 (Attribute) 標記的型別，該屬性傳遞的值為 false。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>取得 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 所包裝的物件。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 所包裝的物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>指定方法、欄位或屬性的 COM 分派識別項 (DISPID)。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>使用指定的 DISPID，初始化 DispIdAttribute 類別的新執行個體。</summary>
      <param name="dispId">成員的 DISPID。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>取得成員的 DISPID。</summary>
      <returns>成員的 DISPID。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>指出由 Unmanaged 動態連結程式庫 (DLL) 公開做為靜態進入點的屬性化方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>使用含有要匯入方法的 DLL 名稱，來初始化 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 類別的新執行個體。</summary>
      <param name="dllName">包含 Unmanaged 方法的 DLL 名稱。如果 DLL 包含在組件中，這個名稱可能會包括組件顯示名稱。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>將 Unicode 字元轉換成 ANSI 字元時，啟用或停用最適合對應行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>指示進入點的呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>指示如何將字串參數封送處理到方法和控制項函式名稱改變 (Name Mangling)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>指示要呼叫的 DLL 進入點 (Entry Point) 的名稱或序數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>控制 <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> 欄位是否會導致 Common Language Runtime 搜尋 Unmanaged DLL 以取得不是指定名稱的進入點名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>指出是否直接轉譯具有 HRESULT 或 retval 傳回值的 Unmanaged 方法，或者是否將 HRESULT 或 retval 傳回值自動轉換成例外狀況。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>指示自屬性方法傳回之前，被呼叫端是否呼叫 SetLastError Win32 應用程式開發介面函式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>在無法對應的 Unicode 字元轉換為 ANSI "?" 字元時，啟用或停用例外狀況的擲回。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>取得包含進入點的 DLL 檔案名稱。</summary>
      <returns>包含進入點的 DLL 檔案名稱。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>指定用來搜尋提供平台叫用函式的 DLL 的路徑。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>將應用程式目錄包含在 DLL 搜尋路徑中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>當搜尋組件相依性時，會包括內含組件本身的目錄，並優先搜尋該目錄。在路徑傳遞至 Win32 LoadLibraryEx 函式之前，.NET Framework 會使用這個值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>搜尋應用程式目錄，然後再呼叫含 LOAD_WITH_ALTERED_SEARCH_PATH 旗標的Win32 LoadLibraryEx 函式如果沒有指定其他值，會忽略此值。不支援 <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 屬性的作業系統會使用此值，而忽略其他值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>將應用程式目錄、%WinDir%\System32 目錄和使用者目錄包含在 DLL 搜尋路徑中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>將 %WinDir%\System32 目錄包含在 DLL 搜尋路徑中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>搜尋其他資料夾之前，先在 DLL 所在的資料夾中搜尋 DLL 的相依性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>包含使用 Win32 AddDllDirectory 函式已明確加入至整個處理序搜尋路徑的任何路徑。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>包裝封送處理器應將其當做 VT_ERROR 來封送處理的物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>使用與所提供之例外狀況 (Exception) 對應的 HRESULT，初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 類別的新執行個體。</summary>
      <param name="e">要轉換為錯誤碼的例外狀況。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>使用錯誤的 HRESULT 來初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 類別的新執行個體。</summary>
      <param name="errorCode">錯誤的 HRESULT。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>使用包含錯誤的 HRESULT 的物件，初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 類別的新執行個體。</summary>
      <param name="errorCode">包含錯誤的 HRESULT 的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="errorCode" /> 參數不是 <see cref="T:System.Int32" /> 型別。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>取得包裝函式的錯誤碼。</summary>
      <returns>錯誤的 HRESULT。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>提供從 Unmanaged 記憶體存取 Managed 物件的方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>擷取在 <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" /> 控制代碼中的物件位址。</summary>
      <returns>當做 <see cref="T:System.IntPtr" /> 的釘選物件位址。</returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>配置 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> 控制代碼給指定的物件。</summary>
      <returns>保護物件不被記憶體回收的新 <see cref="T:System.Runtime.InteropServices.GCHandle" />。當不再需要時，這個 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 必須使用 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 來釋放。</returns>
      <param name="value">使用 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的物件。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>配置指定類型的控制代碼給指定的物件。</summary>
      <returns>指定類型的新 <see cref="T:System.Runtime.InteropServices.GCHandle" />。當不再需要時，這個 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 必須使用 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 來釋放。</returns>
      <param name="value">使用 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的物件。</param>
      <param name="type">
        <see cref="T:System.Runtime.InteropServices.GCHandleType" /> 值的其中之一，指出要建立的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 類型。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件是否等於目前的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</summary>
      <returns>如果指定的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件等於目前的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件，則為 true，否則為 false。</returns>
      <param name="o">要與目前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件比較的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>釋放 <see cref="T:System.Runtime.InteropServices.GCHandle" />。</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>傳回新的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件，而此物件是從 Managed 物件的控制代碼所建立。</summary>
      <returns>新的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件，其對應於實值參數。</returns>
      <param name="value">Managed 物件的 <see cref="T:System.IntPtr" /> 控制代碼，從此控制代碼可建立 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>傳回目前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件的識別項。</summary>
      <returns>目前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件的識別項。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>取得值，指出控制代碼是否已配置。</summary>
      <returns>如果已配置控制代碼，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>傳回值，這個值表示兩個 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件是否相等。</summary>
      <returns>如果 <paramref name="a" /> 和 <paramref name="b" /> 參數相等，則為 true，否則為 false。</returns>
      <param name="a">要與 <paramref name="b" /> 參數比較的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</param>
      <param name="b">要與 <paramref name="a" /> 參數比較的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 是使用內部整數表示來儲存。</summary>
      <returns>使用內部整數表示儲存的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</returns>
      <param name="value">
        <see cref="T:System.IntPtr" />，指出需要轉換的控制代碼。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 是使用內部整數表示來儲存。</summary>
      <returns>整數值。</returns>
      <param name="value">需要整數的 <see cref="T:System.Runtime.InteropServices.GCHandle" />。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>傳回值，這個值表示兩個 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件是否不相等。</summary>
      <returns>如果 <paramref name="a" /> 和 <paramref name="b" /> 參數不相等，則為 true，否則為 false。</returns>
      <param name="a">要與 <paramref name="b" /> 參數比較的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。</param>
      <param name="b">要與 <paramref name="a" /> 參數比較的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>取得或設定這個控制代碼表示的物件。</summary>
      <returns>這個控制代碼表示的物件。</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>傳回 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件的內部整數表示。</summary>
      <returns>
        <see cref="T:System.IntPtr" /> 物件，代表 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件。 </returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 物件，從此物件可擷取內部整數表示。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 類別可以配置的控制代碼的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>這個控制代碼型別表示不透明的控制代碼，意指您無法透過控制代碼解析固定物件的位址。您可以使用這個型別來追蹤物件，避免它被記憶體回收行程回收。當 Unmanaged 用戶端持有 Managed 物件的唯一參考，而且參考無法從記憶體回收行程偵測時，這個列舉成員就很有用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>這個控制代碼型別類似於 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />，但是讓 Pin 物件的位址可以被取得。這會避免記憶體回收行程移動物件，並因而減低記憶體回收行程的效能。使用 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 方法儘快釋放配置的控制代碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>這個控制代碼型別被用來追蹤物件，但允許物件被回收。當收集某個物件時，會將 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的內容歸零。Weak 參考會先歸零後，完成項才會執行，所以即使完成項重新啟動該物件，Weak 參考仍然會歸零。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>這個控制代碼型別和 <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" /> 類似，但如果物件在結束期間重新恢復的話，控制代碼不會歸零。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>不要自動 GUID 時，提供明確 <see cref="T:System.Guid" />。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>使用指定的 GUID，初始化 <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> 類別的新執行個體。</summary>
      <param name="guid">要指派的 <see cref="T:System.Guid" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>取得類別的 <see cref="T:System.Guid" />。</summary>
      <returns>類別的 <see cref="T:System.Guid" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>達到指定的臨界值時，追蹤未完成的控制代碼並強制進行記憶體回收。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>使用名稱以及要開始進行控制代碼回收的臨界值，初始化 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 類別的新執行個體。</summary>
      <param name="name">回收行程的名稱。這項參數可讓您分別針對追蹤控制代碼型別的回收行程加以命名。</param>
      <param name="initialThreshold">值，其指定要開始進行回收的點。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 參數小於 0。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>使用名稱、要開始進行控制代碼回收的臨界值以及必須執行控制代碼回收的臨界值，初始化 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 類別的新執行個體。</summary>
      <param name="name">回收行程的名稱。這項參數可讓您分別針對追蹤控制代碼型別的回收行程加以命名。</param>
      <param name="initialThreshold">值，其指定要開始進行回收的點。</param>
      <param name="maximumThreshold">值，其指定必須進行回收的點。這個值應該設定為可用控制代碼的最大數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 參數小於 0。-或-<paramref name="maximumThreshold" /> 參數小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="maximumThreshold" /> 參數小於 <paramref name="initialThreshold" /> 參數。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>遞增目前的控制代碼計數。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 屬性小於 0。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>取得已回收之控制代碼的數目。</summary>
      <returns>已回收之控制代碼的數目。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>取得值，其指定要開始進行回收的點。</summary>
      <returns>值，其指定要開始進行回收的點。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>取得值，其指定必須進行回收的點。</summary>
      <returns>值，其指定必須進行回收的點。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>取得 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 物件的名稱。</summary>
      <returns>這項 <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> 屬性可讓您分別針對追蹤控制代碼型別的回收行程加以命名。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>遞減目前的控制代碼計數。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 屬性小於 0。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>提供方法，讓用戶端存取實質物件，而不是自訂封送處理器所分配的配接器物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>提供對基礎物件的存取，由自訂封送處理器包裝。</summary>
      <returns>配接器物件所包含的物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>讓開發人員提供 IUnknown::QueryInterface(REFIID riid, void **ppvObject) (英文) 方法的自訂 Managed 實作。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>根據指定的介面 ID，傳回介面。</summary>
      <returns>其中一個列舉值，表示是否已使用 IUnknown::QueryInterface 的自訂實作。</returns>
      <param name="iid">要求之介面的 GUID。</param>
      <param name="ppv">這個方法傳回時，要求之介面的參考。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>指示資料應從呼叫端封送處理到被呼叫端，但不會封送處理回呼叫端。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.InAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>指示 Managed 介面公開給 COM 時是否為雙重、僅分派或 IUnknown。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 列舉型別成員，初始化 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="interfaceType">描述介面應如何公開給 COM 用戶端。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 列舉型別成員，初始化 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 類別的新執行個體。</summary>
      <param name="interfaceType">其中一個 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值，描述介面應如何公開給 COM 用戶端。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>取得描述介面應如何公開給 COM 的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值。</summary>
      <returns>描述介面應如何公開給 COM 的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>使用無效的 COM 物件時便擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>使用預設屬性，初始化 InvalidComObjectException 的執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>使用訊息，初始化 InvalidComObjectException 的執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>封送處理器碰到無法封送處理至 Managed 程式碼的 Variant 型別引數時，所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>使用預設值，初始化 InvalidOleVariantTypeException 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>使用指定的訊息，初始化 InvalidOleVariantTypeException 類別的新執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>提供方法的集合，方法用於配置 Unmanaged 記憶體、複製 Unmanaged 記憶體區塊和將 Managed 類型轉換為 Unmanaged 類型，也包括其他和 Unmanaged 程式碼互動時使用的方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>遞增指定介面的參考次數 (Reference Count)。</summary>
      <returns>
        <paramref name="pUnk" /> 參數上的參考次數的新值。</returns>
      <param name="pUnk">要遞增的介面參考次數。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>從 COM 工作記憶體配置器 (Allocator) 配置指定大小的記憶體區塊。</summary>
      <returns>表示配置的記憶體區塊位址的整數。這個記憶體必須使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> 來釋放。</returns>
      <param name="cb">要配置的記憶體區塊的大小。</param>
      <exception cref="T:System.OutOfMemoryException">沒有充足的記憶體來滿足要求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>使用指定的位元組數目，從處理序的 Unmanaged 記憶體中配置記憶體。</summary>
      <returns>新配置的記憶體的指標。這個記憶體必須使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 方法加以釋放。</returns>
      <param name="cb">記憶體中需要的位元組數目。</param>
      <exception cref="T:System.OutOfMemoryException">沒有充足的記憶體來滿足要求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>使用指定數目的位元組指標，從處理序的 Unmanaged 記憶體中配置記憶體。</summary>
      <returns>新配置的記憶體的指標。這個記憶體必須使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 方法加以釋放。</returns>
      <param name="cb">記憶體中需要的位元組數目。</param>
      <exception cref="T:System.OutOfMemoryException">沒有充足的記憶體來滿足要求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>表示是否有位在任何內容中的執行階段可呼叫包裝函式 (RCW) 可以清除。</summary>
      <returns>如果有可以清除的執行階段可呼叫包裝函式，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 8 位元不帶正負號的整數 (Unsigned Integer) 陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 字元陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 雙精確度浮點數陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 16 位元帶正負號的整數陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 32 位元帶正負號的整數陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> 或 <paramref name="length" /> 是 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 64 位元帶正負號的整數陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 8 位元不帶正負號的整數 (Unsigned Integer) 陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 字元陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到Managed 雙精確度浮點數陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 16 位元帶正負號的整數陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 32 位元帶正負號的整數陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 64 位元帶正負號的整數陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed <see cref="T:System.IntPtr" /> 陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>從 Unmanaged 記憶體指標將資料複製到 Managed 單精確度浮點數陣列。</summary>
      <param name="source">要複製的來源記憶體指標。</param>
      <param name="destination">要複製到其中的陣列。</param>
      <param name="startIndex">複製應該在此處開始之目的地陣列中以零起始的索引。</param>
      <param name="length">要複製的陣列元素數目。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed <see cref="T:System.IntPtr" /> 陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>從一維、Managed 單精確度浮點數陣列將資料複製到 Unmanaged 記憶體指標。</summary>
      <param name="source">要複製的一維陣列。</param>
      <param name="startIndex">複製應該在此處開始之來源陣列中以零起始的索引。</param>
      <param name="destination">要複製到的記憶體指標。</param>
      <param name="length">要複製的陣列元素數目。 </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 不是有效的。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>將 Managed 物件與指定的 COM 物件進行彙總。</summary>
      <returns>Managed 物件的內部 IUnknown 指標。</returns>
      <param name="pOuter">外部 IUnknown 指標。</param>
      <param name="o">要彙總的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 是 Windows 執行階段 物件。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將所指定類型的 Managed 物件與指定的 COM 物件進行彙總。</summary>
      <returns>Managed 物件的內部 IUnknown 指標。 </returns>
      <param name="pOuter">外部 IUnknown 指標。</param>
      <param name="o">要彙總的 Managed 物件。</param>
      <typeparam name="T">要彙總的 Managed 物件類型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 是 Windows 執行階段 物件。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>將指定 COM 物件包裝在指定類型的物件中。</summary>
      <returns>為想要類型之執行個體的新包裝物件。</returns>
      <param name="o">要被包裝的物件。</param>
      <param name="t">要建立的包裝函式類型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 必須要衍生自 __ComObject。-或-<paramref name="t" /> 是 Windows 執行階段 型別。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 參數為 null。</exception>
      <exception cref="T:System.InvalidCastException">因為不支援所有必要的介面，<paramref name="o" /> 不能轉換為目的類型。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將指定 COM 物件包裝在指定類型的物件中。</summary>
      <returns>新包裝的物件。 </returns>
      <param name="o">要被包裝的物件。</param>
      <typeparam name="T">要包裝的物件類型。</typeparam>
      <typeparam name="TWrapper">要傳回的物件類型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 必須要衍生自 __ComObject。-或-<paramref name="T" /> 是 Windows 執行階段 型別。</exception>
      <exception cref="T:System.InvalidCastException">因為不支援所有必要的介面，<paramref name="o" /> 不能轉換為 <paramref name="TWrapper" />。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]釋放指定之 Unmanaged 記憶體區塊指向的所有指定類型的子結構。</summary>
      <param name="ptr">Unmanaged 記憶體區塊的指標。 </param>
      <typeparam name="T">格式化結構的類型。這提供刪除 <paramref name="ptr" /> 中的緩衝區時所必須的配置資訊。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 有自動配置。使用循序或明確取代。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>釋放指定之 Unmanaged 記憶體區塊指向的所有子結構。</summary>
      <param name="ptr">Unmanaged 記憶體區塊的指標。</param>
      <param name="structuretype">格式化類別的類型。這提供刪除 <paramref name="ptr" /> 中的緩衝區時所必須的配置資訊。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> 有自動配置。使用循序或明確取代。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>藉由將執行階段可呼叫包裝函式 (RCW) 的參考次數設定為 0，釋放對 RCW 的所有參考。</summary>
      <returns>與 <paramref name="o" />參數關聯之 RCW 參考次數的新值，如果釋放成功則為 0 (零)。</returns>
      <param name="o">要釋放的 RCW。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 不是一個有效的 COM 物件。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>使用 COM SysFreeString 函式釋放 BSTR。</summary>
      <param name="ptr">要釋放的 BSTR 的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>釋放 Unmanaged COM 工作記憶體配置器所配置的記憶體區塊。</summary>
      <param name="ptr">要釋放的記憶體的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>從處理序的 Unmanaged 記憶體釋放先前配置的記憶體。</summary>
      <param name="hglobal">由對 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 的原始相應呼叫傳回的控制代碼。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>傳回 IUnknown 介面指標，表示指定物件上的指定介面。自訂查詢介面存取預設為啟用。</summary>
      <returns>介面指標，表示物件的指定介面。</returns>
      <param name="o">提供介面的物件。</param>
      <param name="T">所要求之介面的類型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 參數不是介面。-或-類型對 COM 為不可見的。-或-<paramref name="T" /> 參數為泛型類型。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 參數不支援要求的介面。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 參數為 null。-或-<paramref name="T" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>傳回 IUnknown 介面指標，表示指定物件上的指定介面。自訂查詢介面存取是由指定的自訂模式所控制。</summary>
      <returns>介面指標，表示物件的介面。</returns>
      <param name="o">提供介面的物件。</param>
      <param name="T">所要求之介面的類型。</param>
      <param name="mode">其中一個列舉值，指出是否要套用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 所提供的 IUnknown::QueryInterface 自訂。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 參數不是介面。-或-類型對 COM 為不可見的。-或-<paramref name="T" /> 參數為泛型類型。</exception>
      <exception cref="T:System.InvalidCastException">物件 <paramref name="o" /> 不支援要求的介面。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 參數為 null。-或-<paramref name="T" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]傳回 IUnknown 介面指標，表示指定類型的物件上的指定介面。自訂查詢介面存取預設為啟用。</summary>
      <returns>表示 <paramref name="TInterface" /> 介面的介面指標。</returns>
      <param name="o">提供介面的物件。</param>
      <typeparam name="T">
        <paramref name="o" /> 的型別。</typeparam>
      <typeparam name="TInterface">要傳回的介面類型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TInterface" /> 參數不是介面。-或-類型對 COM 為不可見的。-或-<paramref name="T" /> 參數是開放式泛型類型。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 參數不支援 <paramref name="TInterface" /> 介面。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將 Unmanaged 函式指標轉換成所指定類型的委派。</summary>
      <returns>指定之委派類型的執行個體。</returns>
      <param name="ptr">要轉換的 Unmanaged 函式指標。</param>
      <typeparam name="TDelegate">要傳回的委派型別。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 泛型參數不是委派，否則就是開放式泛型類型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>將 Unmanaged 函式指標轉換成委派。</summary>
      <returns>委派執行個體，可轉型成適當的委派類型。</returns>
      <param name="ptr">要轉換的 Unmanaged 函式指標。</param>
      <param name="t">要傳回的委派型别。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 參數不是委派，或者是泛型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 參數為 null。-或-<paramref name="t" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>擷取可辨識發生的例外狀況 (Exception) 類型的代碼。</summary>
      <returns>例外狀況的類型。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>將指定的 HRESULT 錯誤碼轉換成對應的 <see cref="T:System.Exception" /> 物件。</summary>
      <returns>物件，表示轉換的 HRESULT。</returns>
      <param name="errorCode">要轉換的 HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>將指定的 HRESULT 錯誤碼轉換成對應的 <see cref="T:System.Exception" /> 物件，其額外的錯誤資訊傳入此例外狀況物件的 IErrorInfo 介面。</summary>
      <returns>物件，表示已轉換的 HRESULT 以及從 <paramref name="errorInfo" /> 取得的資訊。</returns>
      <param name="errorCode">要轉換的 HRESULT。</param>
      <param name="errorInfo">IErrorInfo 介面的指標，此介面提供關於這個錯誤的詳細資訊。您可以指定 IntPtr(0) 以使用目前的 IErrorInfo 介面，或指定 IntPtr(-1) 忽略目前的 IErrorInfo 介面，並且只從錯誤碼建構例外狀況。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>將委派轉換成可從 Unmanaged 程式碼呼叫的函式指標。</summary>
      <returns>可傳遞至 Unmanaged 程式碼的值，然後程式碼就可以用它來呼叫基礎 Managed 委派。</returns>
      <param name="d">要傳遞至 Unmanaged 程式碼的委派。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="d" /> 參數為泛型類型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將所指定類型的委派轉換成可從 Unmanaged 程式碼呼叫的函式指標。</summary>
      <returns>可傳遞至 Unmanaged 程式碼的值，然後程式碼就可以用它來呼叫基礎 Managed 委派。</returns>
      <param name="d">要傳遞至 Unmanaged 程式碼的委派。</param>
      <typeparam name="TDelegate">要轉換的委派類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>將指定的例外狀況轉換成 HRESULT。</summary>
      <returns>對應於提供的例外狀況的 HRESULT。</returns>
      <param name="e">要轉換成 HRESULT 的例外狀況。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>傳回 HRESULT，它對應於使用 <see cref="T:System.Runtime.InteropServices.Marshal" /> 執行的 Win32 程式碼所引發的最後一個錯誤。</summary>
      <returns>對應於最後一個 Win32 錯誤碼的 HRESULT。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>從 Managed 物件傳回 IUnknown 介面。</summary>
      <returns>
        <paramref name="o" /> 參數的 IUnknown 指標。</returns>
      <param name="o">其 IUnknown 介面受要求的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>傳回使用平台叫用 (已設定 <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> 旗標) 來呼叫的最後 Unmanaged 函式所傳回的錯誤碼。</summary>
      <returns>藉由呼叫 Win32 SetLastError 函式設定的最後錯誤碼。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>將物件轉換為 COM VARIANT。</summary>
      <param name="obj">要為其取得 COM VARIANT 的物件。</param>
      <param name="pDstNativeVariant">指標，用來接收對應於 <paramref name="obj" /> 參數的 VARIANT。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 參數為泛型類型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將所指定類型的物件轉換為 COM VARIANT。</summary>
      <param name="obj">要為其取得 COM VARIANT 的物件。</param>
      <param name="pDstNativeVariant">指標，用來接收對應於 <paramref name="obj" /> 參數的 VARIANT。</param>
      <typeparam name="T">要轉換的物件類型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>傳回類型的執行個體，這個執行個體透過 IUnknown 介面指標來表示 COM 物件。</summary>
      <returns>表示指定之 Unmanaged COM 物件的物件。</returns>
      <param name="pUnk">IUnknown 介面的指標。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>將 COM VARIANT 轉換為物件。</summary>
      <returns>對應於 <paramref name="pSrcNativeVariant" /> 參數的物件。</returns>
      <param name="pSrcNativeVariant">COM VARIANT 的指標。</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> 不是有效的 VARIANT 類型。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> 有不受支援的類型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將 COM VARIANT 轉換為所指定類型的物件。</summary>
      <returns>對應於 <paramref name="pSrcNativeVariant" /> 參數、指定之類型的物件。 </returns>
      <param name="pSrcNativeVariant">COM VARIANT 的指標。</param>
      <typeparam name="T">要將 COM VARIANT 轉換成的類型。</typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> 不是有效的 VARIANT 類型。 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> 有不受支援的類型。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>將 COM VARIANTs 的陣列轉換為物件陣列。</summary>
      <returns>對應於 <paramref name="aSrcNativeVariant" /> 的物件陣列。</returns>
      <param name="aSrcNativeVariant">COM VARIANT 的陣列中第一個元素的指標。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 中 COM VARIANT 的計數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> 為負數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]將 COM VARIANT 的陣列轉換為所指定類型的陣列。</summary>
      <returns>對應於 <paramref name="aSrcNativeVariant" /> 的 <paramref name="T" /> 物件陣列。 </returns>
      <param name="aSrcNativeVariant">COM VARIANT 的陣列中第一個元素的指標。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 中 COM VARIANT 的計數。</param>
      <typeparam name="T">要傳回的陣列類型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> 為負數。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>取得虛擬函式表 (v-table 或 VTBL) 中包含使用者定義之方法的第一個位置。</summary>
      <returns>包含使用者定義之方法的第一個 VTBL 位置。如果介面是以 IUnknown 為基礎，第一個位置為 3，如果介面是以 IDispatch 為基礎則為 7。</returns>
      <param name="t">表示介面的類型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 對於 COM 不是可見的。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>傳回與指定的類別識別項 (CLSID) 關聯的類型。</summary>
      <returns>System.__ComObject，不論 CLSID 是否有效。</returns>
      <param name="clsid">要傳回之類型的 CLSID。 </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>擷取以 ITypeInfo 物件表示的類型名稱。</summary>
      <returns>
        <paramref name="typeInfo" /> 參數所指向的類型名稱。</returns>
      <param name="typeInfo">表示 ITypeInfo 指標的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeInfo" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>為指定的 IUnknown 介面建立唯一執行階段可呼叫包裝函式 (RCW) 物件。</summary>
      <returns>指定之 IUnknown 介面的唯一 RCW。</returns>
      <param name="unknown">IUnknown 介面的 Managed 指標。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>指示指定物件是否表示 COM 物件。</summary>
      <returns>如果 <paramref name="o" /> 參數為 COM 型別，則為 true，否則為 false。</returns>
      <param name="o">要檢查的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]傳回所指定 Managed 類別之 Unmanaged 表單的欄位位移 (Offset)。</summary>
      <returns>平台叫用所宣告的指定類別內 <paramref name="fieldName" /> 參數的位移 (以位元組為單位)。 </returns>
      <param name="fieldName">
        <paramref name="T" /> 類型中的欄位名稱。</param>
      <typeparam name="T">Managed 實值類型或格式化的參考類型。您必須將 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 屬性套用到類別。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>傳回 Managed 類別之 Unmanaged 表單的欄位位移 (Offset)。</summary>
      <returns>平台叫用所宣告的指定類別內 <paramref name="fieldName" /> 參數的位移 (以位元組為單位)。</returns>
      <param name="t">指定 Managed 類別的實值類型或格式化參考類型。您必須將 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 套用到類別。</param>
      <param name="fieldName">
        <paramref name="t" /> 參數中的欄位。</param>
      <exception cref="T:System.ArgumentException">無法匯出類別，因為結構或欄位是非公用的。從 .NET Framework 2.0 版開始，這個欄位可能是私用的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>從 Unmanaged ANSI 字串將直到第一個 null 字元的所有字元複製到 Managed <see cref="T:System.String" />，並且將每個 ANSI 字元擴展為 Unicode。</summary>
      <returns>存有 Unmanaged ANSI 字串複本的 Managed 字串。如果 <paramref name="ptr" /> 為 null，方法就會傳回 null 字串。</returns>
      <param name="ptr">Unmanaged 字串第一個字元的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>配置 Managed <see cref="T:System.String" />、從 Unmanaged ANSI 字串將指定數目的字元複製到其中，並將每一個 ANSI 字元擴展為 Unicode。</summary>
      <returns>如果 <paramref name="ptr" /> 參數的值不是 null，則為包含原生 ANSI 字串複本的 Managed 字串，否則這個方法會傳回 null。</returns>
      <param name="ptr">Unmanaged 字串第一個字元的位址。</param>
      <param name="len">要複製的輸入字串的位元組計數。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> 小於零。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>配置 Managed <see cref="T:System.String" />，並將儲存在 Unmanaged 記憶體的 BSTR 字串複製到其中。</summary>
      <returns>如果 <paramref name="ptr" /> 參數的值不是 null，則為包含 Unmanaged 字串複本的 Managed 字串，否則這個方法會傳回 null。</returns>
      <param name="ptr">Unmanaged 字串第一個字元的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>配置 Managed <see cref="T:System.String" />，並從 Unmanaged Unicode 字串將直到第一個 Null 字元的所有字元複製到其中。</summary>
      <returns>如果 <paramref name="ptr" /> 參數的值不是 null，則為包含 Unmanaged 字串複本的 Managed 字串，否則這個方法會傳回 null。</returns>
      <param name="ptr">Unmanaged 字串第一個字元的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>配置 Managed <see cref="T:System.String" />，並從 Unmanaged Unicode 字串將指定數目的字元複製到其中。</summary>
      <returns>如果 <paramref name="ptr" /> 參數的值不是 null，則為包含 Unmanaged 字串複本的 Managed 字串，否則這個方法會傳回 null。</returns>
      <param name="ptr">Unmanaged 字串第一個字元的位址。</param>
      <param name="len">要複製的 Unicode 字元數。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]從 Unmanaged 記憶體區塊封送處理資料到新配置的指定類型的 Managed 物件 (其類型是由泛型類型參數所指定)。</summary>
      <returns>包含 <paramref name="ptr" /> 參數所指向的資料的 Managed 物件。</returns>
      <param name="ptr">Unmanaged 記憶體區塊的指標。</param>
      <typeparam name="T">複製資料所到的物件類型。這必須是格式化類別或結構。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 的配置不是循序或明確的。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="T" /> 指定的類別沒有可存取的預設建構函式。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>從 Unmanaged 記憶體區塊封送處理資料到 Managed 物件。</summary>
      <param name="ptr">Unmanaged 記憶體區塊的指標。</param>
      <param name="structure">複製資料所到的物件。這必須是格式化類別的執行個體。</param>
      <exception cref="T:System.ArgumentException">結構配置不是循序或明確的。-或-結構為 Boxed 實值類型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>從 Unmanaged 記憶體區塊封送處理資料到新配置的指定類型的 Managed 物件。</summary>
      <returns>包含 <paramref name="ptr" /> 參數所指向的資料的 Managed 物件。</returns>
      <param name="ptr">Unmanaged 記憶體區塊的指標。</param>
      <param name="structureType">要建立的物件類型。這個物件必須表示格式化類別或結構。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> 參數配置不是循序或明確的。-或-<paramref name="structureType" /> 參數為泛型類型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> 為 null。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="structureType" /> 指定的類別沒有可存取的預設建構函式。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]從 Unmanaged 記憶體區塊封送處理資料到指定類型的 Managed 物件。</summary>
      <param name="ptr">Unmanaged 記憶體區塊的指標。</param>
      <param name="structure">複製資料所到的物件。</param>
      <typeparam name="T">
        <paramref name="structure" /> 的型別。這必須是格式化類別。</typeparam>
      <exception cref="T:System.ArgumentException">結構配置不是循序或明確的。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>從 COM 物件要求指定介面的指標。</summary>
      <returns>表示呼叫成功或失敗的 HRESULT。</returns>
      <param name="pUnk">要被查詢的介面。</param>
      <param name="iid">所要求介面的介面識別項 (IID)。</param>
      <param name="ppv">這個方法傳回時，會包含到傳回介面的參考。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>從 Unmanaged 記憶體讀取單一位元組。</summary>
      <returns>從 Unmanaged 記憶體讀取的位元組。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的位址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>從 Unmanaged 記憶體讀取在指定位移 (或索引) 的單一位元組。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的位元組。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的基底位址 (Base Address)。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>從 Unmanaged 記憶體讀取在指定位移 (或索引) 的單一位元組。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的位元組。</returns>
      <param name="ptr">來源物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>從 Unmanaged 記憶體讀取 16 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體讀取的 16 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的位址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 16 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的 16 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的基底位址 (Base Address)。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 16 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的 16 位元帶正負號的整數。</returns>
      <param name="ptr">來源物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>從 Unmanaged 記憶體讀取 32 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體讀取的 32 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的位址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 32 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體讀取的 32 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的基底位址 (Base Address)。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 32 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的 32 位元帶正負號的整數。</returns>
      <param name="ptr">來源物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>從 Unmanaged 記憶體讀取 64 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體讀取的 64 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的位址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 64 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的 64 位元帶正負號的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的基底位址 (Base Address)。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取 64 位元帶正負號的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的 64 位元帶正負號的整數。</returns>
      <param name="ptr">來源物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>從 Unmanaged 記憶體讀取處理器原生大小的整數。</summary>
      <returns>從 Unmanaged 記憶體讀取的整數。在 32 位元電腦上會傳回 32 位元整數，而在 64 位元電腦上則會傳回 64 位元整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的位址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>從 Unmanaged 記憶體中指定位移處讀取處理器原生大小的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的整數。</returns>
      <param name="ptr">從 Unmanaged 記憶體中讀取的基底位址 (Base Address)。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>從 Unmanaged 記憶體讀取處理器原生大小的整數。</summary>
      <returns>從 Unmanaged 記憶體中指定位移處讀取的整數。</returns>
      <param name="ptr">來源物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行讀取。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>調整先前用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> 所配置的記憶體區塊的大小。</summary>
      <returns>表示重新配置的記憶體區塊位址的整數。這個記憶體必須使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> 來釋放。</returns>
      <param name="pv">用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> 所配置之記憶體的指標。</param>
      <param name="cb">配置的區塊的新大小。</param>
      <exception cref="T:System.OutOfMemoryException">沒有充足的記憶體來滿足要求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>調整先前用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 所配置的記憶體區塊的大小。</summary>
      <returns>重新配置的記憶體的指標。這個記憶體必須使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 加以釋放。</returns>
      <param name="pv">用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 所配置之記憶體的指標。</param>
      <param name="cb">配置的區塊的新大小。這不是指標；它是您所要求、轉型為類型 <see cref="T:System.IntPtr" /> 的位元組計數。如果您傳遞指標，它會被視為大小。</param>
      <exception cref="T:System.OutOfMemoryException">沒有充足的記憶體來滿足要求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>遞減指定介面上的參考次數。</summary>
      <returns>介面上由 <paramref name="pUnk" /> 參數指定的參考次數的新值。</returns>
      <param name="pUnk">要釋放的介面。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>遞減與指定之 COM 物件相關特定執行階段可呼叫包裝函式 (RCW) 的參考次數。</summary>
      <returns>與 <paramref name="o" /> 相關的 RCW 之參考次數的新值。這個值通常為零，因為不論呼叫的 Managed 用戶端數目，RCW 只保留一個包裝的 COM 物件參考。</returns>
      <param name="o">要釋放的 COM 物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 不是一個有效的 COM 物件。</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]傳回 Unmanaged 類型的大小 (以位元組為單位)。</summary>
      <returns>
        <paramref name="T" /> 泛型類型參數所指定之類型的大小，以位元組為單位。</returns>
      <typeparam name="T">要傳回其大小的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>傳回物件的 Unmanaged 大小 (以位元組為單位)。</summary>
      <returns>Unmanaged 程式碼中指定之物件的大小。</returns>
      <param name="structure">其大小要被傳回的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>傳回 Unmanaged 類型的大小 (以位元組為單位)。</summary>
      <returns>Unmanaged 程式碼中指定之類型的大小。</returns>
      <param name="t">要傳回其大小的類型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 參數為泛型類型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 參數為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]傳回指定類型之物件的 Unmanaged 大小，以位元組為單位。</summary>
      <returns>Unmanaged 程式碼中指定之物件的大小，以位元組為單位。</returns>
      <param name="structure">其大小要被傳回的物件。</param>
      <typeparam name="T">
        <paramref name="structure" /> 參數的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>配置 BSTR，並將 Managed <see cref="T:System.String" /> 的內容複製到其中。</summary>
      <returns>BSTR 的 Unmanaged 指標，如果 <paramref name="s" /> 為 null 則為 0。</returns>
      <param name="s">要被複製的 Managed 字串。</param>
      <exception cref="T:System.OutOfMemoryException">沒有足夠的記憶體可用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 的長度超出範圍。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>複製 Managed <see cref="T:System.String" /> 的內容到從 Unmanaged COM 工作配置器配置的記憶體區塊。</summary>
      <returns>整數，表示配置給字串的記憶體區塊指標，如果 <paramref name="s" /> 為 null 則為 0。</returns>
      <param name="s">要複製的 Managed 字串。</param>
      <exception cref="T:System.OutOfMemoryException">沒有足夠的記憶體可用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 參數超過作業系統所允許的最大長度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>複製 Managed <see cref="T:System.String" /> 的內容到從 Unmanaged COM 工作配置器配置的記憶體區塊。</summary>
      <returns>整數，表示配置給字串的記憶體區塊指標，如果 s 為 null 則為 0。</returns>
      <param name="s">要複製的 Managed 字串。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 參數超過作業系統所允許的最大長度。</exception>
      <exception cref="T:System.OutOfMemoryException">沒有足夠的記憶體可用。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>複製 Managed <see cref="T:System.String" /> 的內容到 Unmanaged 記憶體中，在它複製時轉換成 ANSI 格式。</summary>
      <returns>Unmanaged 記憶體中複製 <paramref name="s" /> 的目的位址，如果 <paramref name="s" /> 為 null 則為 0。</returns>
      <param name="s">要複製的 Managed 字串。</param>
      <exception cref="T:System.OutOfMemoryException">沒有足夠的記憶體可用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 參數超過作業系統所允許的最大長度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>將 Managed <see cref="T:System.String" /> 的內容複製到 Unmanaged 記憶體。</summary>
      <returns>Unmanaged 記憶體中複製 <paramref name="s" /> 的目的位址，如果 <paramref name="s" /> 為 null 則為 0。</returns>
      <param name="s">要複製的 Managed 字串。</param>
      <exception cref="T:System.OutOfMemoryException">方法無法配置足夠的原生堆積記憶體。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 參數超過作業系統所允許的最大長度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>從 Managed 物件封送處理資料到 Unmanaged 記憶體區塊。</summary>
      <param name="structure">存有要封送處理之資料的 Managed 物件。這個物件必須是格式化類別的結構或執行個體。</param>
      <param name="ptr">Unmanaged 記憶體區塊的指標，這記憶體必須在呼叫這個方法之前被配置。</param>
      <param name="fDeleteOld">true 表示在 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> 方法複製資料之前，在 <paramref name="ptr" /> 參數上呼叫此方法。區塊必須包含有效的資料。請注意，當記憶體區塊已經包含資料時傳遞 false 會造成記憶體遺漏。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" />是未格式化的類別的參考類型。-或-<paramref name="structure" /> 是泛型類型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]從指定類型的 Managed 物件封送處理資料到 Unmanaged 記憶體區塊。</summary>
      <param name="structure">存有要封送處理之資料的 Managed 物件。物件必須是格式化類別的結構或執行個體。</param>
      <param name="ptr">Unmanaged 記憶體區塊的指標，這記憶體必須在呼叫這個方法之前被配置。 </param>
      <param name="fDeleteOld">true 表示在 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> 方法複製資料之前，在 <paramref name="ptr" /> 參數上呼叫此方法。區塊必須包含有效的資料。請注意，當記憶體區塊已經包含資料時傳遞 false 會造成記憶體遺漏。</param>
      <typeparam name="T">Managed 物件的類型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" />是未格式化的類別的參考類型。</exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>表示系統上的預設字元大小；Unicode 系統的預設值為 2，ANSI 系統為 1。此欄位為唯讀。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>表示用於目前作業系統雙位元組字元集 (DBCS) 大小的最大值，以位元組為單位。此欄位為唯讀。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>擲回具有特定錯誤 HRESULT 值的例外狀況。</summary>
      <param name="errorCode">對應於希望的例外狀況的 HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>根據指定的 IErrorInfo 介面，擲回具有特定失敗 HRESULT 的例外狀況。</summary>
      <param name="errorCode">對應於希望的例外狀況的 HRESULT。</param>
      <param name="errorInfo">IErrorInfo 介面的指標，此介面提供關於這個錯誤的詳細資訊。您可以指定 IntPtr(0) 以使用目前的 IErrorInfo 介面，或指定 IntPtr(-1) 忽略目前的 IErrorInfo 介面，並且只從錯誤碼建構例外狀況。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>取得指定陣列內指定索引處的元素的位址。</summary>
      <returns>
        <paramref name="arr" /> 內部的 <paramref name="index" /> 位址。</returns>
      <param name="arr">包含所需元素的陣列。</param>
      <param name="index">希望的元素之 <paramref name="arr" /> 參數中的索引。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[在 .NET Framework 4.5.1 及更新版本中支援]在指定類型的陣列中取得位於指定索引處的元素的位址。</summary>
      <returns>
        <paramref name="arr" /> 內部的 <paramref name="index" /> 位址。</returns>
      <param name="arr">包含所需元素的陣列。</param>
      <param name="index">
        <paramref name="arr" /> 陣列中所需項目的索引。</param>
      <typeparam name="T">陣列類型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>將單一位元組值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>將單一位元組值寫入 Unmanaged 記憶體中的指定位移。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>將單一位元組值寫入 Unmanaged 記憶體中的指定位移。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>將字元當做 16 位元整數值寫入 Unmanaged 記憶體中。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>將 16 位元整數值寫入 Unmanaged 記憶體中。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>在指定的位移，將 16 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">原生堆積中要寫入的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>將 16 位元帶正負號的整數值寫入 Unmanaged 記憶體中的指定位移。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>在指定的位移，將 16 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>在指定的位移，將 16 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>將 32 位元帶正負號的整數值寫入 Unmanaged 記憶體中。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>將 32 位元帶正負號的整數值寫入 Unmanaged 記憶體中的指定位移。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>在指定的位移，將 32 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>在指定的位移，將 64 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">要寫入之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>將 64 位元帶正負號的整數值寫入 Unmanaged 記憶體中。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>在指定的位移，將 64 位元帶正負號的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>將處理器原生大小的整數值寫入 Unmanaged 記憶體中的指定位移。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>將處理器原來大小的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">Unmanaged 記憶體中要寫入的位址。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是可辨認的格式。-或-<paramref name="ptr" /> 為 null。-或-<paramref name="ptr" /> 無效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>將處理器原來大小的整數值寫入 Unmanaged 記憶體。</summary>
      <param name="ptr">目標物件之 Unmanaged 記憶體中的基底位址。</param>
      <param name="ofs">額外的位元組位移，會先加入至參數 <paramref name="ptr" />，然後再進行寫入。</param>
      <param name="val">要寫入的值。</param>
      <exception cref="T:System.AccessViolationException">基底位址 (<paramref name="ptr" />) 加上位移位元組 (<paramref name="ofs" />) 會產生 Null 或無效的位址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 物件。這個方法不會接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 參數。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>釋放之前使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" /> 方法配置的 BSTR 指標。</summary>
      <param name="s">要釋放的 BSTR 的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>釋放之前使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" /> 方法配置的 Unmanaged 字串指標。</summary>
      <param name="s">要釋放的 Unmanaged 字串的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>釋放之前使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" /> 方法配置的 Unmanaged 字串指標。</summary>
      <param name="s">要釋放的 Unmanaged 字串的位址。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>釋放之前使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" /> 方法配置的 Unmanaged 字串指標。</summary>
      <param name="s">要釋放的 Unmanaged 字串的位址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>釋放之前使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" /> 方法配置的 Unmanaged 字串指標。</summary>
      <param name="s">要釋放的 Unmanaged 字串的位址。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>指示如何在 Managed 和 Unmanaged 程式碼之間封送處理資料。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值，初始化 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 類別的新執行個體。</summary>
      <param name="unmanagedType">要將資料封送處理成的值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 列舉型別 (Enumeration) 成員，初始化 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 類別的新執行個體。</summary>
      <param name="unmanagedType">要將資料封送處理成的值。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>指定 Unmanaged <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> 或 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> 的元素型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>指定 COM 所使用之 Unmanaged iid_is 屬性的參數索引。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>提供其他資訊給自訂封送處理器。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>指定自訂封送處理器的完整名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>實作 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> 為型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>指示 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 的元素型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>指示 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 的使用者定義的元素型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>指示固定長度陣列中的元素數目或要匯出之字串中的字元數目 (非位元組)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>指示哪一個以零起始的參數含有陣列元素的計數，類似 COM 中的 size_is。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>取得資料要封送處理成的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值。</summary>
      <returns>資料要封送處理成的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>例外狀況，由封送處理器擲回於遭遇不支援的 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 時。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>使用預設屬性來初始化 MarshalDirectiveException 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 MarshalDirectiveException 類別的新執行個體。</summary>
      <param name="message">指定例外狀況原因的錯誤訊息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外參考，初始化 <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>指示參數為選擇性的。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>使用預設值，初始化 OptionalAttribute 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>指示應該要抑制 COM Interop 呼叫期間發生的 HRESULT 或 retval 簽章轉換。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>當連入 SAFEARRAY 的順序不符合 Managed 簽章中指定的順序時，所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>使用預設值，初始化 SafeArrayTypeMismatchException 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>使用指定的訊息來初始化 SafeArrayRankMismatchException 類別的新執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>當連入 SAFEARRAY 的型別不符合 Managed 簽章中指定的型別時，所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>使用預設值，初始化 SafeArrayTypeMismatchException 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>使用指定的訊息來初始化 SafeArrayTypeMismatchException 類別的新執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>提供可用於讀取和寫入的受控制記憶體緩衝區。嘗試存取受控制緩衝區之外的記憶體 (不足和滿溢) 會引發例外狀況。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 類別的新執行個體，並且指定是否要確實地釋放緩衝區控制代碼。</summary>
      <param name="ownsHandle">true 表示在結束階段確實地釋放控制代碼，而 false 表示不要確實地釋放 (不建議)。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>從 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 物件取得記憶體區塊的指標。</summary>
      <param name="pointer">位元組指標 (以傳址方式傳遞)，用來從 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 物件中接收指標。您必須先將這個指標設定為 null，再呼叫這個方法。</param>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>取得緩衝區的大小，以位元組為單位。</summary>
      <returns>記憶體緩衝區中的位元組數目。</returns>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>指定實值型別的數目，定義記憶體區域的配置大小。您必須先呼叫這個方法，才能夠使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 執行個體。</summary>
      <param name="numElements">要配置記憶體之實值型別的項目數目。</param>
      <typeparam name="T">要配置記憶體的實值型別。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> 小於零。-或-<paramref name="numElements" /> 乘以每個元素的大小，大於可用的位址空間。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>使用指定的項目數目和項目大小，指定記憶體緩衝區的配置大小。您必須先呼叫這個方法，才能夠使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 執行個體。</summary>
      <param name="numElements">緩衝區中的項目數。</param>
      <param name="sizeOfEachElement">緩衝區中每個項目的大小。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> 小於零。-或-<paramref name="sizeOfEachElement" /> 小於零。-或-<paramref name="numElements" /> 乘以 <paramref name="sizeOfEachElement" /> 大於可用的位址空間。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>定義記憶體區域的配置大小 (以位元組為單位)。您必須先呼叫這個方法，才能夠使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 執行個體。</summary>
      <param name="numBytes">緩衝區中的位元組數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> 小於零。-或-<paramref name="numBytes" /> 大於可用的位址空間。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>從記憶體中指定的位移讀取實值型別。</summary>
      <returns>從記憶體讀取的實值型別。</returns>
      <param name="byteOffset">要從中讀取實值型別的位置。您可能必須考慮對齊問題。</param>
      <typeparam name="T">要讀取的實值型別。</typeparam>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>從記憶體中的位移開始讀取指定數目的實值型別，並從陣列中的索引處開始寫入。</summary>
      <param name="byteOffset">要開始讀取的來源位置。</param>
      <param name="array">要寫入的輸出陣列。</param>
      <param name="index">在輸出陣列中開始寫入的位置。</param>
      <param name="count">從輸入陣列讀取並寫入輸出陣列的實值型別數目。</param>
      <typeparam name="T">要讀取的實值型別。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="count" /> 小於零。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">陣列的長度減去索引會小於 <paramref name="count" />。</exception>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>釋放 <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" /> 方法所取得的指標。</summary>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>將實值型別寫入至記憶體中指定的位置。</summary>
      <param name="byteOffset">要開始寫入的位置。您可能必須考慮對齊問題。</param>
      <param name="value">要寫入的值。</param>
      <typeparam name="T">要寫入的實值型別。</typeparam>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>先從輸入陣列中指定的位置開始讀取位元組，再將指定數目的實值型別寫入至記憶體位置。</summary>
      <param name="byteOffset">要寫入的記憶體中的位置。</param>
      <param name="array">輸入陣列。</param>
      <param name="index">陣列中要開始讀取的位移。</param>
      <param name="count">要寫入的實值型別數目。</param>
      <typeparam name="T">要寫入的實值型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 或 <paramref name="count" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">輸入陣列的長度減去 <paramref name="index" /> 小於 <paramref name="count" />。</exception>
      <exception cref="T:System.InvalidOperationException">尚未呼叫 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>代表結構化例外狀況處理 (SEH) 錯誤。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>使用指定的訊息，初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 類別的新執行個體。</summary>
      <param name="message">訊息，表示例外狀況的原因。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外參考，初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="inner">導致目前例外狀況的例外。如果 <paramref name="inner" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>指示例外狀況是否可復原，以及程式碼是否可從擲回例外狀況的地方繼續進行。</summary>
      <returns>因為可復原的例外狀況未實作，所以永遠是 false。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>提供型別對等支援。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>建立 <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>使用指定的範圍和識別項，建立 <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 類別的新執行個體。</summary>
      <param name="scope">第一個型別對等字串。</param>
      <param name="identifier">第二個型別對等字串。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>取得傳遞給 <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 建構函式的 <paramref name="identifier" /> 參數值。</summary>
      <returns>建構函式的 <paramref name="identifier" /> 參數值。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>取得傳遞給 <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 建構函式的 <paramref name="scope" /> 的參數值。</summary>
      <returns>建構函式的 <paramref name="scope" /> 參數值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>包裝封送處理器應將其當做 VT_UNKNOWN 來封送處理的物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>使用要包裝的物件，初始化 <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> 類別的新執行個體。</summary>
      <param name="obj">所包裝的物件。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>取得這個包裝函式所包含的物件。</summary>
      <returns>被包裝的物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>控制當做 Unmanaged 函式指標在 Unmanaged 程式碼之間來回傳遞之委派簽章的封送處理 (Marshaling) 行為。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>使用指定的呼叫慣例，初始化 <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> 類別的新執行個體。</summary>
      <param name="callingConvention">指定的呼叫慣例。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>將 Unicode 字元轉換成 ANSI 字元時，啟用或停用最適合對應行為。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>取得呼叫慣例的值。</summary>
      <returns>呼叫慣例的值，由 <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" /> 建構函式 (Constructor) 指定。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>表示如何將字串參數封送處理到方法，並控制函式名稱改變 (Name Mangling)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>指示自屬性方法傳回之前，被呼叫端是否呼叫 SetLastError Win32 應用程式開發介面函式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>在無法對應的 Unicode 字元轉換為 ANSI "?" 字元時，啟用或停用例外狀況的擲回。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>識別如何封送處理參數或欄位至 Unmanaged 程式碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>固定長度、單一位元組的 ANSI 字元字串。這個成員可用於 <see cref="T:System.String" /> 資料類型上。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>動態 (Dynamic) 類型，在執行階段判斷物件的類型，並封送處理物件為該類型。這個成員僅對平台叫用方法有效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>4 位元組的布林值 (true != 0, false = 0)。這是 Win32 BOOL 類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>固定長度雙位元組的 Unicode 字元字串。您可以在 <see cref="T:System.String" /> 資料類型上使用這個成員，這是 COM 中的預設字串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>當 <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> 屬性設定為 ByValArray 時，必須設定 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 欄位以指示陣列中項目的數目。需要區別字串類型時，<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> 欄位可以選擇性地包含陣列項目的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" />。您只能將這個 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 用於其項目在結構中以欄位出現的陣列。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>使用於出現在結構中的內嵌 (Inline) 固定長度字元陣列。使用於 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> 的字元類型由套用至包含結構之 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 屬性的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 引數所決定。永遠使用 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 欄位以指示陣列大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>貨幣類型。使用於 <see cref="T:System.Decimal" /> 以封送處理十進位值為 COM Currency 類型，而不是 Decimal。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>與 <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> 或 <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> 相關聯的原生類型，而且該類型會使參數匯出為匯出的類型程式庫中的 HRESULT。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>整數，可當做 C-style 函式指標使用。您可以將這個成員用於 <see cref="T:System.Delegate" /> 資料類型或自 <see cref="T:System.Delegate" /> 繼承的類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Windows 執行階段 字串。這個成員可用於 <see cref="T:System.String" /> 資料類型上。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>1 位元帶正負號的整數。您可以使用這個成員以轉換布林值 (Boolean) 為 1 位元組、C-style bool (true = 1、false = 0)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>2 位元帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>4 位元帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>8 位元帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>COM IDispatch 指標 (Microsoft Visual Basic 6.0 中的 Object)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Windows 執行階段 介面指標。這個成員可用於 <see cref="T:System.Object" /> 資料類型上。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>COM 的介面指標。介面的 <see cref="T:System.Guid" /> 是從類別中繼資料 (Metadata) 取得。使用這個成員指定如果套用至類別時的正確介面類型或預設介面類型。套用至 <see cref="T:System.Object" /> 資料類型時，這個成員會產生與 <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> 相同的行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>COM 的 IUnknown 指標。這個成員可用於 <see cref="T:System.Object" /> 資料類型上。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>指向 C-style 陣列第一個項目的指標。當從 Managed 程式碼封送處理至 Unmanaged 程式碼時，陣列的長度是由 Managed 陣列的長度來判斷。從 Unmanaged 程式碼封送處理至 Managed 程式碼時，陣列長度從 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 和 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" /> 欄位判斷，有需要區別字串類型時，也可選擇是否要加上陣列中項目的 Unmanaged 類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>單一位元組、以 Null 結束的 ANSI 字元字串。您可以將這個成員用於 <see cref="T:System.String" /> 和 <see cref="T:System.Text.StringBuilder" /> 資料類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>C-style 結構的指標，您用來封送處理 Managed 格式化類別。這個成員僅對平台叫用方法有效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>平台相依字元字串：在 Windows 98 上為 ANSI，在 Windows NT 和 Windows XP 上為 Unicode。因為不支援匯出 LPTStr 類型的字串，所以這個值只有在平台叫用 (Invoke) 受到支援，在 COM Interop 則不受支援。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>2 位元組、以 Null 結束的 Unicode 字元字串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>4 位元組浮點數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>8 位元組浮點數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>SafeArray，其為自我描述陣列，具有類型、順序以及關聯陣列資料的繫結。您可以使用這個成員和 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> 欄位以覆寫預設項目類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>VARIANT，用來封送處理 Managed 格式化類別和實值類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>平台相依的帶正負號整數：在 32 位元 Windows 上為 4 位元組，在 64 位元 Windows 上為 8 位元組。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>平台相依的不帶正負號整數：在 32 位元 Windows 上為 4 位元組，在 64 位元 Windows 上為 8 位元組。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>平台相依的長度前置 char 字串：在 Windows 98 上為 ANSI，在 Windows NT 上為 Unicode。您很少會使用這個類似 BSTR 的成員。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>1 位元組不帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>2 位元組不帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>4 位元組不帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>8 位元不帶正負號的整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>2 位元組、OLE 定義的 VARIANT_BOOL 類型 (true = -1、false = 0)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>值，這個值可讓 Visual Basic 變更 Unmanaged 程式碼中的字串，並且讓結果反映在 Managed 程式碼中。只有平台叫用支援這個值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>指示從 Managed 至 Unmanaged 程式碼封送處理陣列為 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 時如何封送處理陣列元素。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>指示 SAFEARRAY 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>指示以長度為首碼的位元組。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>指示 BLOB (二進位大型物件) 含有物件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>指示布林值 (Boolean)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>指示 BSTR 字串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>指示數值為參考。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>指示 C 樣式陣列。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>指示剪貼簿格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>指示類別 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>指示貨幣值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>指示 DATE 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>指示 decimal 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>指示 IDispatch 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>指示尚未指定的值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>指示 SCODE。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>指示 FILETIME 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>指示 HRESULT。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>指示 char 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>指示 short 整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>指示 long 整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>指示 64 位元整數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>指示整數值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>指示以 null 結尾的字串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>指示以 null 結尾的寬字串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>指示 null 值，和 SQL 中的 null 值類似。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>指示指標型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>指示 float 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>指示 double 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>指示使用者定義的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>指示 SAFEARRAY。在 VARIANT 中無效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>指示儲存體遵循的名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>指示儲存體含有物件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>指示資料流遵循的名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>指示資料流含有物件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>指示 byte。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>指示 unsignedshort。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>指示 unsignedlong。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>指示 64 位元不帶正負號的整數 (Unsigned Integer)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>指示 unsigned 整數值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>指示 IUnknown 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>指示使用者定義的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>指示 VARIANT far 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>指示簡單計數的陣列。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>指示 C 樣式 void。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>將 VT_VARIANT | VT_BYREF 型別的資料從 Managed 封送處理成 Unmanaged 程式碼。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>為指定的 <see cref="T:System.Object" /> 參數，初始化 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 類別的新執行個體。</summary>
      <param name="obj">要封送處理的物件。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>取得 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 物件所包裝的物件。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 物件所包裝的物件。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>指定設定通知接收或快取物件連接時的要求行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>針對資料諮詢連接，確保資料的存取性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>針對資料諮詢連接 (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> 或 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />)，這個旗標會要求資料物件不要在它呼叫 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 時傳送資料。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>要求物件在刪除連接之前只執行一次變更告知或快取更新。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>要求物件不要等到資料或檢視變更後，才對 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 發出初始呼叫 (針對資料或檢視諮詢連接) 或更新快取區 (Cache) (針對快取連接)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>這個值是由 DLL 物件應用程式和執行物件繪製的物件處理常式 (Object Handler) 所使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>使用較頻繁的 <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" /> 的同義資料表 (Synonym)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>針對快取連接，這個旗標只會在儲存包含快取區的物件時更新快取表示。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>儲存在 Moniker 繫結作業中使用的參數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>以位元組指定 BIND_OPTS 結構的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>指示呼叫端所指定完成繫結作業的時間量 (以毫秒為單位的時間，如 GetTickCount 函式傳回者)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>控制 Moniker 繫結作業的各個層面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>表示開啟含有由 Moniker 所識別之物件的檔案時應該使用的旗標。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>包含繫結至 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構、<see cref="T:System.Runtime.InteropServices.VARDESC" /> 結構或 ITypeComp 介面的指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構的指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" /> 介面的指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.VARDESC" /> 結構的指標。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>辨識 METHODDATA 結構中所描述方法使用的呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>指示在方法中使用 C 宣告 (CDECL) 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>指示在方法中使用 Macintosh Pascal (MACPASCAL) 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>指示 <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" /> 列舉型別的結尾。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>指示在方法中使用 Macintosh Programmers' Workbench (MPW) CDECL 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>指示在方法中使用 Macintosh Programmers' Workbench (MPW) PASCAL 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>指示在方法中使用 MSC Pascal (MSCPASCAL) 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>指示在方法中使用 Pascal 呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>這個值保留作未來使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>指示在方法中使用標準呼叫慣例 (STDCALL)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>指示在方法中使用 SYSCALL 呼叫慣例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>描述指定連接點上存在的連接。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>表示從對 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 的呼叫傳回的連接 Token。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>表示已連接的通知接收 (Advisory Sink) 之 IUnknown 介面的指標。當不再需要 CONNECTDATA 結構時，呼叫端必須在這個指標上呼叫 IUnknown::Release。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 方法的 <paramref name="dwDirection" /> 參數中，指定資料流量的方向。這決定了產生的列舉值可以列舉的格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>要求 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 提供列舉值給可以在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 中指定的格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>要求 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 提供列舉值給可以在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" /> 中指定的格式。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>識別正要繫結至的型別描述。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>指示已傳回 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>指示已傳回 IMPLICITAPPOBJ。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>指示列舉型別 (Enumeration) 資料標記的結尾。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>指示沒有找到相符的比對。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>指示已傳回 TYPECOMP。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>指示已傳回 VARDESC。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>包含 IDispatch::Invoke 傳遞給方法或屬性的引數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>表示引數的計數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>表示具名引數的計數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>表示具名引數的分派 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>表示引數陣列的參考。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>指定繪製或取得資料時所需的資料或物件的檢視外觀。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>物件的表示，可以讓物件顯示成容器 (Container) 內部的內嵌物件。一般會將這個值指定給複合文件物件。可以針對螢幕或印表機提供這個表示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>物件的螢幕表示，就像使用 [檔案] 功能表中的 [列印] 命令將物件列印到印表機一樣。描述的資料可能表示一系列的頁面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>物件的圖示表示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>物件的縮圖表示 (Thumbnail Representation)，可以讓物件顯示在瀏覽工具中。縮圖為約 120 X 120 像素、16 色 (建議)、與裝置無關 (Device-independent) 的點陣圖，而且可能包裝在中繼檔 (Metafile) 中。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>包含型別描述，並處理變數、函式或函式參數的傳輸資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>包含項目的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>辨識項目的型別。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>包含項目的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>包含遠端處理項目的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>包含參數的資訊。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>描述在 IDispatch::Invoke 期間發生的例外狀況。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>描述預定給客戶的錯誤。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>包含說明檔 (含有關於錯誤的詳細資訊) 的完整磁碟機、路徑和檔案名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>指示例外狀況來源的名稱。通常，這是應用程式名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>指示說明檔內的說明主題代碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>表示函式的指標，這函式接受 <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> 結構做為引數，並且傳回 HRESULT 值。如果不希望將填入擱置，這個欄位要設定為 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>這個欄位已保留；必須設定為 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>描述錯誤的傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>表示識別錯誤的錯誤碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>這個欄位已保留；必須設定為 0。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>表示自 1601 年 1 月 1 日以來 100 奈秒 (十億分之一秒) 間隔的數目。這種結構是 64 位元的值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>指定 FILETIME 的高階 32 位元。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>指定 FILETIME 的低階 32 位元。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>表示通用的剪貼簿格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>指定相關的特定剪貼簿格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>指定其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 列舉常數，指出呈現時應該包含多少細節。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>指定必須跨頁面界限分割資料時的外觀部分。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>指定 DVTARGETDEVICE 結構的指標，結構中包含撰寫資料的目標裝置相關資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>指定其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 列舉常數，表示用來傳輸物件資料的存放媒體類型。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>定義函式描述。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>指定函式的呼叫慣例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>計算參數的總數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>計算選擇性參數的數目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>計算允許的傳回值數目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>包含函式的傳回型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>指定函式是虛擬的、靜態的還是僅供分派。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>指定屬性函式的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>指示 <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" /> 的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>存放函式可在 16 位元系統傳回的錯誤計數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>辨識函式成員 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>指定 <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" /> 在 VTBL 中的位移。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>指示函式的 <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" />。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>辨識定義函式屬性的常數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>支援資料繫結的函式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>最能表示物件的函式。型別中只能有一個函式具有這個屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>允許最佳化，其中編譯器會在「abc」型別上尋找名為「xyz」的成員。如果找到這樣的成員，而且是標示為預設集合的項目的存取子 (Accessor) 函式，就會產生對該成員函式的呼叫。在分配介面和介面中的成員受允許；模組上則不受允許。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>對使用者顯示為可繫結的函式。<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" /> 也必須設定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>雖然函式存在而且可繫結，但是不應該向使用者顯示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>對應為可繫結的個別屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>屬性出現在物件瀏覽器中，但不會在屬性瀏覽器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>將介面標記 (Tag) 為具有預設行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>設定之後，任何對設定該屬性之方法的呼叫將會首先呼叫 IPropertyNotifySink::OnRequestEdit。OnRequestEdit 的實作 (Implementation) 會判斷是否允許呼叫設定屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>函式不應該可以自巨集語言存取。旗標是供系統層級的函式或型別瀏覽器不應顯示的函式使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>函式會傳回是事件來源的物件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>型別資訊成員是顯示在使用者介面中的預設成員。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>函式支援 GetLastError。如果錯誤在函式執行期間發生，呼叫端可以呼叫 GetLastError 來擷取錯誤碼。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>定義如何存取函式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>函式只能透過 IDispatch 來存取。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>此函式是透過 static 位址來存取，並接受隱含的 this 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>函式透過虛擬函式表 (Virtual Function Table，VTBL) 來存取，並接受隱含 this 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>此函式是透過 static 位址來存取，且不接受隱含的 this 指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>函式以與 <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" /> 相同的方式存取，但如果此函式具有實作 (Implementation) 則除外。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>提供 IAdviseSink 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>告知所有已登錄的諮詢接收，物件已經從執行狀態變成載入狀態 (Loaded State)。這個方法是由伺服器呼叫。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>告知所有資料物件目前已登錄的諮詢接收，物件中的資料已經變更。</summary>
      <param name="format">以傳址 (By Reference) 方式傳遞的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />，其描述呼叫資料物件的格式、目標裝置、呈現和儲存區資訊。</param>
      <param name="stgmedium">以傳址方式傳遞的 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />，其定義呼叫資料物件的存放媒體 (全域記憶體、磁碟檔、儲存物件、資料流物件 (Stream Object)、繪圖裝置介面 (Graphics Device Interface，GDI) 物件或未定義)，以及該媒體的擁有權。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>告知所有已登錄的諮詢接收，物件已經重新命名。這個方法是由伺服器呼叫。</summary>
      <param name="moniker">IMoniker 介面的指標，該介面位於物件的新完整 Moniker 上。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>告知所有已登錄的諮詢接收，物件已經儲存。這個方法是由伺服器呼叫。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>告知物件的已登錄諮詢接收，物件的檢視已經變更。這個方法是由伺服器呼叫。</summary>
      <param name="aspect">物件的外觀或檢視。包含取自 <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 列舉型別的值。</param>
      <param name="index">已經變更的檢視部分。目前只有 -1 有效。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>提供 IBindCtx 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>列舉字串，其為內容物件參數的內部維護表格之機碼。</summary>
      <param name="ppenum">這個方法傳回時，包含物件參數列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>傳回儲存在目前繫結內容中的目前繫結選項。</summary>
      <param name="pbindopts">要接收繫結選項的結構指標。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>在內容物件參數的內部維護表格中查詢指定的機碼，並傳回對應的物件 (如果存在)。</summary>
      <param name="pszKey">要搜尋的物件名稱。</param>
      <param name="ppunk">這個方法傳回時，包含物件介面指標。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>傳回與這個繫結處理序關聯的執行物件表格 (ROT) 之存取權。</summary>
      <param name="pprot">這個方法傳回時，包含執行物件表格 (ROT) 的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>將傳遞的物件登錄為已在 Moniker 作業中繫結，應該在作業完成時釋放的其中一個物件。</summary>
      <param name="punk">要登錄供釋放的物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>將指定的物件指標登錄在物件指標之內部維護表格中的指定名稱下。</summary>
      <param name="pszKey">要用來登錄 <paramref name="punk" /> 的名稱。</param>
      <param name="punk">要登錄的物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>釋放所有目前使用 <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" /> 方法以繫結內容登錄的物件。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>從需要被釋放的已登錄物件組中移除物件。</summary>
      <param name="punk">要取消登錄供釋放的物件。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>如果目前已登錄任何在內容物件參數之內部維護表格中的指定機碼，撤銷目前在該機碼下找到的物件登錄。</summary>
      <returns>如果指定的機碼成功地從資料表移除則為 S_OKHRESULT 值，否則為 S_FALSEHRESULT 值。</returns>
      <param name="pszKey">要取消登錄的機碼。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>儲存繫結內容中的參數區塊。這些參數會套用至使用這個繫結內容的後續 UCOMIMoniker 作業。</summary>
      <param name="pbindopts">結構含有要設定的繫結選項。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>提供 IConnectionPoint 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>在連接點和呼叫端的接收物件之間建立諮詢連接 (Advisory Connection)。</summary>
      <param name="pUnkSink">接收的參考，讓這個由連接點管理的輸出介面接收呼叫。</param>
      <param name="pdwCookie">這個方法傳回時，包含連接 Cookie。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>建立列舉值物件，以逐一查看存在於這個連接點的連接。</summary>
      <param name="ppEnum">這個方法傳回時，包含新建立的列舉型別。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>傳回這個連接點所管理之輸出介面的 IID。</summary>
      <param name="pIID">這個參數傳回時，包含這個連接點所管理之輸出介面的 IID。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>擷取在概念上擁有這個連接點之可連接物件的 IConnectionPointContainer 介面指標。</summary>
      <param name="ppCPC">這個參數傳回時，包含可連接物件的 IConnectionPointContainer 介面。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>結束先前透過 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 方法所建立的諮詢連接。</summary>
      <param name="dwCookie">先前從 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 方法傳回的連接 Cookie。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>提供 IConnectionPointContainer 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>建立可連接物件 (Connectable Object) 中支援的所有連接點的列舉值，每個 IID 一個連接點。</summary>
      <param name="ppEnum">這個方法傳回時，包含列舉值的介面指標。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>詢問可連接物件其是否具有特定 IID 的連接點，如果有，傳回該連接點的 IConnectionPoint 介面指標。</summary>
      <param name="riid">輸出介面 IID 的參考，要求其連接點。</param>
      <param name="ppCP">這個方法傳回時，包含管理輸出介面 <paramref name="riid" /> 的連接點。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>含有在處理序之間轉換結構項目、參數或函式傳回值所需的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>保留；設定為 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>指示描述型別的 <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> 值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>描述如何在處理序之間傳輸結構項目、參數或函式傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>參數從呼叫端傳遞資訊到被呼叫端。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>參數為用戶端應用程式的區域識別項。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>參數從被呼叫端傳回資訊到呼叫端。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>參數為成員的傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>沒有指定參數是傳送或接收資訊。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>管理 IEnumConnectionPoints 介面的定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別狀態。</summary>
      <param name="ppenum">這個方法傳回時，包含新建列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的 IConnectionPoint 參考的數目。</param>
      <param name="rgelt">這個方法傳回時，包含列舉連接的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">這個方法傳回時，包含 <paramref name="rgelt" /> 中實際列舉連接數目的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的元素數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>管理 IEnumConnections 介面的定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別狀態。</summary>
      <param name="ppenum">這個方法傳回時，包含新建列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的 <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> 結構數目。</param>
      <param name="rgelt">這個方法傳回時，包含列舉連接的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">這個方法傳回時，包含 <paramref name="rgelt" /> 中實際列舉連接數目的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的元素數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>提供 IEnumFORMATETC 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別 (Enumeration) 狀態。</summary>
      <param name="newEnum">這個方法傳回時，包含新建列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 參考的數目。</param>
      <param name="rgelt">當這個方法傳回時，會包含列舉之 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 參考的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">當這個方法傳回時，會包含 <paramref name="rgelt" /> 所列舉之參考實際數目的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
      <returns>HRESULT，擁有值 S_OK。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的元素數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>管理 IEnumMoniker 介面的定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別狀態。</summary>
      <param name="ppenum">這個方法傳回時，包含新建列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的 Moniker 數目。</param>
      <param name="rgelt">這個方法傳回時，包含列舉 Moniker 的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">這個方法傳回時，包含 <paramref name="rgelt" /> 中實際列舉 Moniker 數目的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的元素數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>管理 IEnumString 介面的定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別狀態。</summary>
      <param name="ppenum">這個方法傳回時，包含新建列舉值的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的字串數目。</param>
      <param name="rgelt">這個方法傳回時，包含列舉字串的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">這個方法傳回時，包含 <paramref name="rgelt" /> 列舉字串實際數目的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的元素數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>管理 IEnumVARIANT 介面的定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>建立新的列舉值，其包含與目前列舉值相同的列舉型別狀態。</summary>
      <returns>新建立列舉值的 <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> 參考。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>擷取列舉型別序列中指定的項目數目。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 參數等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中傳回的項目數目。</param>
      <param name="rgVar">這個方法傳回時，包含列舉項目的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pceltFetched">這個方法傳回時，包含 <paramref name="rgelt" /> 中實際列舉項目數目的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>將列舉型別 (Enumeration) 序列重設到開頭。</summary>
      <returns>HRESULT，擁有值 S_OK。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>略過列舉序列中指定的項目數目。</summary>
      <returns>如果略過的項目數目等於 <paramref name="celt" /> 參數，則為 S_OK，否則為 S_FALSE。</returns>
      <param name="celt">列舉型別中要略過的項目數。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>提供 IMoniker 介面的 Managed 定義，具有 IPersist 和 IPersistStream 的 COM 功能。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>使用 Moniker 繫結至其所識別的物件。</summary>
      <param name="pbc">這個繫結作業中所使用繫結內容 (Bind Context) 物件上的 IBindCtx 介面參考。</param>
      <param name="pmkToLeft">如果 Moniker 是複合型 Moniker 的一部分，則為目前 Moniker 左邊的 Moniker 參考。</param>
      <param name="riidResult">用戶端使用於與 Moniker 所識別物件通訊的介面之介面識別項 (IID)。</param>
      <param name="ppvResult">這個方法傳回時，包含 <paramref name="riidResult" /> 要求的介面參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>擷取含有 Moniker 所辨識物件的儲存體的介面指標。</summary>
      <param name="pbc">這個繫結作業期間所使用繫結內容物件上的 IBindCtx 介面參考。</param>
      <param name="pmkToLeft">如果 Moniker 是複合型 Moniker 的一部分，則為目前 Moniker 左邊的 Moniker 參考。</param>
      <param name="riid">要求的儲存體介面的介面識別項 (IID)。</param>
      <param name="ppvObj">這個方法傳回時，包含 <paramref name="riid" /> 要求的介面參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>根據這個 Moniker 與另一個 Moniker 共用的共同首碼建立新 Moniker。</summary>
      <param name="pmkOther">另一個 Moniker 上的 IMoniker 介面參考，用於比較一般前置字元的目前 Moniker。</param>
      <param name="ppmkPrefix">這個方法傳回時，包含為目前 Moniker 和 <paramref name="pmkOther" /> 的共同首碼的 Moniker。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>將目前 Moniker 與另一個 Moniker 組合，以建立新的複合型 Moniker。</summary>
      <param name="pmkRight">要附加到目前 Moniker 結尾的 Moniker 上的 IMoniker 介面參考。</param>
      <param name="fOnlyIfNotGeneric">true 表示呼叫端要求非一般性複合。如果 <paramref name="pmkRight" /> 為目前 Moniker 可以使用某些產生一般複合以外方式組合的 Moniker 類別，作業會繼續進行。false 表示這個方法可以建立一般複合 (如果需要)。</param>
      <param name="ppmkComposite">這個方法傳回時，包含所產生複合型 Moniker 的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>提供列舉值 (可列舉複合型 Moniker 的元件) 的指標。</summary>
      <param name="fForward">如果為 true，從左至右列舉 Moniker。如果為 false，則從右至左列舉。</param>
      <param name="ppenumMoniker">這個方法傳回時，包含 Moniker 的列舉值物件的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>擷取物件的類別識別項 (Class Identifier，CLSID)。</summary>
      <param name="pClassID">這個方法傳回時，包含 CLSID。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>取得顯示名稱，其為目前 Moniker 的使用者可讀取的表示。</summary>
      <param name="pbc">要在這個作業中使用的繫結內容的參考。</param>
      <param name="pmkToLeft">如果 Moniker 是複合型 Moniker 的一部分，則為目前 Moniker 左邊的 Moniker 參考。</param>
      <param name="ppszDisplayName">這個方法傳回時，包含顯示名稱字串。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>傳回儲存物件所需的資料流大小 (以位元組為單位)。</summary>
      <param name="pcbSize">這個方法傳回時，包含 long 值，指出儲存這個物件所需的資料流大小 (以位元組為單位)。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>提供數字，表示這個 Moniker 所識別的物件上次變更的時間。</summary>
      <param name="pbc">要在這個繫結作業中使用的繫結內容的參考。</param>
      <param name="pmkToLeft">如果 Moniker 是複合型 Moniker 的一部分，則為目前 Moniker 左邊的 Moniker 參考。</param>
      <param name="pFileTime">這個方法傳回時，包含上次變更的時間。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>使用 Moniker 的內部狀態來計算 32 位元整數。</summary>
      <param name="pdwHash">這個方法傳回時，包含這個 Moniker 的雜湊值。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>在目前 Moniker 右邊或其中一個相似結構組成時，提供未組成的 Moniker。</summary>
      <param name="ppmk">這個方法傳回時，包含與目前 Moniker 相反的 Moniker。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>檢查物件自從上次儲存以來是否變更。</summary>
      <returns>如果物件已變更則為 S_OKHRESULT 值，否則為 S_FALSEHRESULT 值。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>比較目前的 Moniker 與指定的 Moniker，指出它們是否相同。</summary>
      <returns>如果 Moniker 相同則為 S_OKHRESULT 值，否則為 S_FALSEHRESULT 值。</returns>
      <param name="pmkOtherMoniker">用來比較的 Moniker 的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>決定目前 Moniker 所識別的物件，目前是否已載入並在執行中。</summary>
      <returns>如果 Moniker 正在執行則為 S_OKHRESULT 值，如果 Moniker 未在執行則為 S_FALSEHRESULT 值，或者為 E_UNEXPECTEDHRESULT 值。</returns>
      <param name="pbc">要在這個繫結作業中使用的繫結內容的參考。</param>
      <param name="pmkToLeft">如果目前的 Moniker 是複合的一部分，則為目前 Moniker 左邊的 Moniker 參考。</param>
      <param name="pmkNewlyRunning">最近加入至執行物件表格 (ROT) 的 Moniker 的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>指示這個 Moniker 是否為系統提供的 Moniker 類別的其中之一。</summary>
      <returns>如果 Moniker 是系統 Moniker 則為 S_OKHRESULT 值，否則為 S_FALSEHRESULT 值。</returns>
      <param name="pdwMksys">這個方法傳回時，包含 MKSYS 列舉型別其中一個整數值的指標，並參考到其中一個 COM Moniker 類別。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>從先前儲存物件的資料流來初始化它。</summary>
      <param name="pStm">從中載入物件的資料流。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>以 <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> 所能瞭解的數量來讀取指定顯示名稱的字元，並建置對應於讀取部分的 Moniker。</summary>
      <param name="pbc">要在這個繫結作業中使用的繫結內容的參考。</param>
      <param name="pmkToLeft">到目前為止已經從顯示名稱中建置的 Moniker 的參考。</param>
      <param name="pszDisplayName">含有要剖析的剩餘顯示名稱的字串的參考。</param>
      <param name="pchEaten">這個方法傳回時，包含剖析 <paramref name="pszDisplayName" /> 所使用的字元數目。這個參數會以未初始化的狀態傳遞。</param>
      <param name="ppmkOut">這個方法傳回時，會包含從 <paramref name="pszDisplayName" /> 所建置的 Moniker 之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>傳回縮減的 Moniker，其為與目前 Moniker 參考相同物件的另一個 Moniker，但可以用相同或較高的效率來繫結。</summary>
      <param name="pbc">要在這個繫結作業中使用的繫結內容上的 IBindCtx 介面參考。</param>
      <param name="dwReduceHowFar">指定目前 Moniker 應縮減的值。</param>
      <param name="ppmkToLeft">目前 Moniker 左邊的 Moniker 之參考。</param>
      <param name="ppmkReduced">這個方法傳回時，包含目前 Moniker 縮減形式的參考，如果發生錯誤，或如果這個 Moniker 縮減到無的話，則可以為 null。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>附加到目前 Moniker (或類似結構) 時，提供可產生指定 Moniker 的 Moniker。</summary>
      <param name="pmkOther">應該採用相對路徑的 Moniker 的參考。</param>
      <param name="ppmkRelPath">這個方法傳回時，包含相關 Moniker 的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>儲存物件到指定資料流。</summary>
      <param name="pStm">儲存物件的資料流。</param>
      <param name="fClearDirty">true 表示完成儲存後清除修改的旗標，否則為 false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>定義實作的或繼承的型別介面的屬性 (Attribute)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>介面或分派介面表示來源或接收的預設值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>接收會透過虛擬函式表 (Virtual Function Table，VTBL) 接受事件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>成員應該不可由使用者來顯示或撰寫程式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Coclass 的這個成員是被呼叫的，而不是被實作的。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>指定如何以 IDispatch::Invoke 叫用 (Invoke) 函式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>成員使用一般函式引動過程語法來呼叫。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>函式使用一般屬性存取語法來叫用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>函式使用屬性值指派語法來叫用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>函式使用屬性參考指派語法來叫用。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>提供 IPersistFile 介面的 Managed 定義，具有 IPersist 的功能。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>擷取物件的類別識別項 (Class Identifier，CLSID)。</summary>
      <param name="pClassID">這個方法傳回時，包含 CLSID 的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>擷取物件目前工作檔的絕對路徑，如果沒有目前工作檔，則擷取物件的預設檔案名稱提示。</summary>
      <param name="ppszFileName">這個方法回傳時，包含以零結尾字串的指標位址，這字串含有目前檔案的路徑，或預設檔案名稱提示 (例如 *.txt)。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>檢查物件自從儲存到它目前檔案以來的變更。</summary>
      <returns>如果檔案自從上次儲存以來已經變更，則為 S_OK；如果檔案自從上次儲存以來尚未變更，則為 S_FALSE。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>開啟指定的檔案，並從檔案連接初始化物件。</summary>
      <param name="pszFileName">以零為結尾的字串，含有要開啟的檔案的絕對路徑。</param>
      <param name="dwMode">STGM 列舉型別中值的組合，表示開啟 <paramref name="pszFileName" /> 所用的存取模式。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>儲存物件的複本到指定檔案中。</summary>
      <param name="pszFileName">以零為結尾的字串，含有要儲存物件所至檔案的絕對路徑。</param>
      <param name="fRemember">使用 <paramref name="pszFileName" /> 參數做為目前的工作檔，為 true，否則為 false。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>告知物件，它可以寫入其檔案。</summary>
      <param name="pszFileName">先前儲存物件所在檔案的絕對路徑。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>提供 IRunningObjectTable 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>列舉目前登錄為執行中的物件。</summary>
      <param name="ppenumMoniker">這個方法傳回時，包含執行物件表格 (ROT) 的新列舉值。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>如果提供的物件名稱已登錄為執行中，傳回這個已登錄物件。</summary>
      <returns>HRESULT 值，指出作業成功或失敗。</returns>
      <param name="pmkObjectName">要在執行物件表格 (ROT) 中搜尋之 Moniker 的參考。</param>
      <param name="ppunkObject">這個方法傳回時，包含要求的執行物件。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>在執行物件表格 (ROT) 中搜尋這個 Moniker，並報告變更的記錄時間 (如果有的話)。</summary>
      <returns>HRESULT 值，指出作業成功或失敗。</returns>
      <param name="pmkObjectName">要在執行物件表格 (ROT) 中搜尋之 Moniker 的參考。</param>
      <param name="pfiletime">這個物件傳回時，包含物件上次變更的時間。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>決定指定的 Moniker 目前是否已在執行物件表格 (ROT) 中登錄。</summary>
      <returns>HRESULT 值，指出作業成功或失敗。</returns>
      <param name="pmkObjectName">要在執行物件表格 (ROT) 中搜尋之 Moniker 的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>注意特定物件變更的時間，讓 IMoniker::GetTimeOfLastChange 可以報告正確的變更時間。</summary>
      <param name="dwRegister">已變更物件的執行物件表格 (ROT) 項目。</param>
      <param name="pfiletime">物件的上次變更時間的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>登錄所提供物件已進入執行狀態。</summary>
      <returns>值，在後續呼叫 <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> 或 <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" /> 時可用來識別這個 ROT 項目。</returns>
      <param name="grfFlags">指定執行物件表格 (ROT) 對 <paramref name="punkObject" /> 的參考是弱式還是強式，並且透過它在 ROT 中的項目控制對物件的存取。</param>
      <param name="punkObject">登錄為執行中之物件的參考。</param>
      <param name="pmkObjectName">識別 <paramref name="punkObject" /> 的 Moniker 之參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>從執行物件表格 (ROT) 中移除指定物件的登錄。</summary>
      <param name="dwRegister">要撤銷的執行物件表格 (ROT) 項目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>提供 IStream 介面的 Managed 定義，具有 ISequentialStream 功能。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>使用它自己的搜尋指標 (其參考與原始資料流相同的位元組)，建立新的資料流物件。</summary>
      <param name="ppstm">這個方法傳回時，包含新資料流物件。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>確保異動模式 (Transacted Mode) 開啟的資料流物件的任何變更，都會反映在父代 (Parent) 儲存區中。</summary>
      <param name="grfCommitFlags">控制如何認可資料流物件變更的數值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>從資料流中的目前搜尋指標複製指定數目的位元組到另一個資料流中的目前搜尋指標。</summary>
      <param name="pstm">目的端資料流的參考。</param>
      <param name="cb">要從來源資料流複製的位元組數目。</param>
      <param name="pcbRead">在成功傳回時，含有從來源讀取的實際位元組數目。</param>
      <param name="pcbWritten">在成功傳回時，含有寫入目的端的實際位元組數目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>限制存取資料流中指定的位元組範圍。</summary>
      <param name="libOffset">範圍開頭的位元組位移。</param>
      <param name="cb">要限制的範圍長度，以位元組為單位。</param>
      <param name="dwLockType">對存取範圍要求的限制。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>在目前搜尋指標位置開始，從資料流物件讀取指定數目的位元組到記憶體中。</summary>
      <param name="pv">這個方法傳回時，包含從資料流讀取的資料。這個參數會以未初始化的狀態傳遞。</param>
      <param name="cb">要從資料流物件讀取的位元組數目。</param>
      <param name="pcbRead">ULONG 變數的指標，其接收從資料流物件讀取的實際位元組數目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>捨棄從上次 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" /> 呼叫後對異動資料流的所有變更。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>變更搜尋指標到相對於資料流開頭的新位置、到資料流結尾，或到目前的搜尋指標。</summary>
      <param name="dlibMove">加入至 <paramref name="dwOrigin" /> 的替代。</param>
      <param name="dwOrigin">搜尋的原點。原點可以是檔案的開頭、目前的搜尋指標或是檔案的結尾。</param>
      <param name="plibNewPosition">在成功傳回時，含有搜尋指標從資料流開頭的位移。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>變更資料流物件的大小。</summary>
      <param name="libNewSize">資料流的新大小，為位元組數目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>擷取這個資料流的 <see cref="T:System.Runtime.InteropServices.STATSTG" /> 結構。</summary>
      <param name="pstatstg">這個方法傳回時，包含描述這個資料流物件的 STATSTG 結構。這個參數會以未初始化的狀態傳遞。</param>
      <param name="grfStatFlag">STATSTG 結構中的成員，這個方法未傳回，因此節省了一些記憶體配置作業。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>移除先前以 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" /> 方法限制的位元組範圍之存取限制。</summary>
      <param name="libOffset">範圍開頭的位元組位移。</param>
      <param name="cb">要限制的範圍長度，以位元組為單位。</param>
      <param name="dwLockType">先前對範圍的存取限制。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>在目前搜尋指標位置開始，將指定數目的位元組寫入資料流物件中。</summary>
      <param name="pv">要將這個資料流寫入的緩衝區。</param>
      <param name="cb">要寫入資料流的位元組數目。</param>
      <param name="pcbWritten">在成功傳回時，含有寫入資料流物件的實際位元組數目。如果呼叫端將這個指標設定為 <see cref="F:System.IntPtr.Zero" />，這個方法就不會提供寫入的實際位元組數目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>提供 ITypeComp 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>對應名稱至型別的成員，或繫結包含於型別程式庫中的全域變數和函式。</summary>
      <param name="szName">要繫結的名稱。</param>
      <param name="lHashVal">由 LHashValOfNameSys 所計算的 <paramref name="szName" /> 的雜湊值。</param>
      <param name="wFlags">旗標字組，含有一個或多個定義於 INVOKEKIND 列舉型別的叫用 (Invoke) 旗標。</param>
      <param name="ppTInfo">這個方法傳回時，如果傳回 FUNCDESC 或 VARDESC，會包含其所繫結項目的型別描述之參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pDescKind">這個方法傳回時，包含 DESCKIND 列舉型別的參考，指出繫結的名稱為 VARDESC、FUNCDESC 或 TYPECOMP。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pBindPtr">這個方法傳回時，包含繫結到 VARDESC、FUNCDESC 或 ITypeComp 介面的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>繫結至型別程式庫內所包含的型別描述。</summary>
      <param name="szName">要繫結的名稱。</param>
      <param name="lHashVal">LHashValOfNameSys 所決定的 <paramref name="szName" /> 雜湊值。</param>
      <param name="ppTInfo">這個方法傳回時，包含繫結 <paramref name="szName" /> 的型別 ITypeInfo 的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="ppTComp">這個方法傳回時，包含 ITypeComp 變數的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>提供元件自動化 ITypeInfo 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>擷取靜態 (Static) 函式或變數的位址，例如在 DLL 中定義者。</summary>
      <param name="memid">要擷取的 static 成員位址的成員 ID。</param>
      <param name="invKind">其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值，指定成員是否為屬性 (Property)，如果是，為何種屬性。</param>
      <param name="ppv">這個方法傳回時，包含 static 成員的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>建立描述元件類別 (coclass) 之型別的新執行個體。</summary>
      <param name="pUnkOuter">做為控制 IUnknown 的物件。</param>
      <param name="riid">呼叫端要用來與所產生物件通訊的介面 IID。</param>
      <param name="ppvObj">這個方法傳回時，包含所建立物件的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>擷取型別程式庫，其包含這個型別描述和它在該型別程式庫中的索引。</summary>
      <param name="ppTLB">這個方法傳回時，包含了包含型別程式庫的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pIndex">這個方法傳回時，包含了包含型別程式庫內型別描述索引的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>擷取 DLL 中函式之進入點 (Entry Point) 的描述或規格。</summary>
      <param name="memid">成員函式的 ID，將傳回其 DLL 項目描述。</param>
      <param name="invKind">其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值，指定由 <paramref name="memid" /> 識別的成員類型。</param>
      <param name="pBstrDllName">如果不是 null，函式會將 <paramref name="pBstrDllName" /> 設定為包含 DLL 名稱的 BSTR。</param>
      <param name="pBstrName">如果不是 null，函式會將 <paramref name="lpbstrName" /> 設定為包含進入點名稱的 BSTR。</param>
      <param name="pwOrdinal">如果不是 null，而且函式是由序數所定義，那麼 <paramref name="lpwOrdinal" /> 就會被設定成指向該序數。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>擷取文件字串、完整的說明檔名稱和路徑，以及指定型別描述之說明主題的主題代碼。</summary>
      <param name="index">要傳回其文件的成員 ID。</param>
      <param name="strName">這個方法傳回時，包含項目方法的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strDocString">當這個方法傳回時，會包含指定項目的文件字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="dwHelpContext">這個方法傳回時，包含與指定項目關聯的說明主題之參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strHelpFile">這個方法傳回時，包含說明檔的完整名稱。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>擷取包含所指定函式相關資訊的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構。</summary>
      <param name="index">要傳回的函式描述之索引。</param>
      <param name="ppFuncDesc">這個方法傳回時，包含描述指定函式的 FUNCDESC 結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>成員名稱與成員 ID 之間，以及參數名稱與參數 ID 之間的對應。</summary>
      <param name="rgszNames">要對應的名稱陣列。</param>
      <param name="cNames">要對應的名稱計數。</param>
      <param name="pMemId">這個方法傳回時，包含放置了名稱對應的陣列參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>擷取型別描述中某個已實作之介面或基底介面的 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 值。</summary>
      <param name="index">實作介面或基底介面的索引。</param>
      <param name="pImplTypeFlags">這個方法傳回時，包含 IMPLTYPEFLAGS 列舉型別的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>擷取封送處理 (Marshaling) 資訊。</summary>
      <param name="memid">成員 ID，表示需要哪些封送處理資訊。</param>
      <param name="pBstrMops">這個方法傳回時，包含 opcode 字串的參考，其用於封送處理由參考的型別描述所描述之結構的欄位，如果沒有資訊要傳回，則傳回 null。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>擷取具有對應到所指定函式 ID 之指定成員 ID (或是屬性或方法的名稱及其參數) 的變數。</summary>
      <param name="memid">要傳回其名稱之成員的 ID。</param>
      <param name="rgBstrNames">這個方法傳回時，包含與成員關聯的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 陣列的長度。</param>
      <param name="pcNames">這個方法傳回時，包含 <paramref name="rgBstrNames" /> 陣列中名稱的數目。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>如果類型描述參考其他類型描述，則擷取參考的類型描述。</summary>
      <param name="hRef">要傳回之已參考的類型描述的控制代碼。</param>
      <param name="ppTI">這個方法傳回時，包含參考的類型描述。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>如果類型描述描述了 COM 類別，則擷取實作介面類型的類型描述。</summary>
      <param name="index">已實作類型的索引，這個已實作類型的控制代碼已傳回。</param>
      <param name="href">這個方法傳回時，包含實作介面的處理常式之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>擷取包含型別描述之屬性 (Attribute) 的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 結構。</summary>
      <param name="ppTypeAttr">這個方法傳回時，包含了含有此型別描述屬性的結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>擷取型別描述的 ITypeComp 介面，它可以讓用戶端編譯器 (Compiler) 繫結至型別描述的成員。</summary>
      <param name="ppTComp">這個方法傳回時，包含了包含型別程式庫的 ITypeComp 介面之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>擷取描述指定變數的 VARDESC 結構。</summary>
      <param name="index">要傳回的變數描述之索引。</param>
      <param name="ppVarDesc">這個方法傳回時，包含描述指定變數的 VARDESC 結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>叫用 (Invoke) 實作型別描述所描述之介面的方法，或存取實作型別描述所描述之介面的物件屬性。</summary>
      <param name="pvInstance">這個型別描述所描述之介面的參考。</param>
      <param name="memid">識別介面成員的值。</param>
      <param name="wFlags">描述叫用呼叫內容的旗標。</param>
      <param name="pDispParams">結構的參考，其包含引數的陣列、具名引數之 DISPID 的陣列，以及每一陣列中元素數目的計數。</param>
      <param name="pVarResult">要儲存結果之位置的參考。如果 <paramref name="wFlags" /> 指定 DISPATCH_PROPERTYPUT 或 DISPATCH_PROPERTYPUTREF，<paramref name="pVarResult" /> 便會被忽略。如果沒有想要的結果，請設定為 null。</param>
      <param name="pExcepInfo">例外狀況資訊結構的指標，只有在傳回 DISP_E_EXCEPTION 時才會填入。</param>
      <param name="puArgErr">如果 Invoke 傳回 DISP_E_TYPEMISMATCH，<paramref name="puArgErr" /> 會指出含錯誤型別的引數之 <paramref name="rgvarg" /> 內的索引。如果有一個以上引數傳回錯誤，<paramref name="puArgErr" /> 只會指出第一個有錯誤的引數。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 方法傳回的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構。</summary>
      <param name="pFuncDesc">要釋放之 FUNCDESC 結構的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 方法傳回的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 結構。</summary>
      <param name="pTypeAttr">要釋放之 TYPEATTR 結構的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 方法傳回的 VARDESC 結構。</summary>
      <param name="pVarDesc">要釋放之 VARDESC 結構的參考。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>提供 ITypeInfo2 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>擷取靜態 (Static) 函式或變數的位址，例如在 DLL 中定義者。</summary>
      <param name="memid">要擷取的 static 成員位址的成員 ID。</param>
      <param name="invKind">其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值，指定成員是否為屬性 (Property)，如果是，為何種屬性。</param>
      <param name="ppv">這個方法傳回時，包含 static 成員的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>建立描述元件類別 (coclass) 之型別的新執行個體。</summary>
      <param name="pUnkOuter">做為控制 IUnknown 的物件。</param>
      <param name="riid">呼叫端要用來與所產生物件通訊的介面 IID。</param>
      <param name="ppvObj">這個方法傳回時，包含所建立物件的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>取得程式庫所有的自訂資料項目。</summary>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>取得指定函式的所有自訂資料。</summary>
      <param name="index">用於取得自訂資料的函式索引。</param>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>取得指定實作類型的所有自訂資料。</summary>
      <param name="index">自訂資料的實作類型索引。</param>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>取得指定函式參數的所有自訂資料。</summary>
      <param name="indexFunc">用於取得自訂資料的函式索引。</param>
      <param name="indexParam">用於取得自訂資料的這個函式之參數索引。</param>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>取得自訂資料的變數。</summary>
      <param name="index">用於取得自訂資料的變數索引。</param>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>擷取型別程式庫，其包含這個型別描述和它在該型別程式庫中的索引。</summary>
      <param name="ppTLB">這個方法傳回時，包含了包含型別程式庫的參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pIndex">這個方法傳回時，包含了包含型別程式庫內型別描述索引的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>取得自訂資料。</summary>
      <param name="guid">用來識別資料的 GUID。</param>
      <param name="pVarVal">這個方法傳回時，包含指定所擷取資料放置處的 Object。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>擷取 DLL 中函式之進入點 (Entry Point) 的描述或規格。</summary>
      <param name="memid">成員函式的 ID，將傳回其 DLL 項目描述。</param>
      <param name="invKind">其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值，指定由 <paramref name="memid" /> 識別的成員類型。</param>
      <param name="pBstrDllName">如果不是 null，函式會將 <paramref name="pBstrDllName" /> 設定為包含 DLL 名稱的 BSTR。</param>
      <param name="pBstrName">如果不是 null，函式會將 <paramref name="lpbstrName" /> 設定為包含進入點名稱的 BSTR。</param>
      <param name="pwOrdinal">如果不是 null，而且函式是由序數所定義，那麼 <paramref name="lpwOrdinal" /> 就會被設定成指向該序數。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>擷取文件字串、完整的說明檔名稱和路徑，以及指定型別描述之說明主題的主題代碼。</summary>
      <param name="index">要傳回其文件的成員 ID。</param>
      <param name="strName">這個方法傳回時，包含項目方法的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strDocString">當這個方法傳回時，會包含指定項目的文件字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="dwHelpContext">這個方法傳回時，包含與指定項目關聯的說明主題之參考。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strHelpFile">這個方法傳回時，包含說明檔的完整名稱。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>擷取文件字串、完整說明檔名稱和路徑、要使用的當地語系化內容，以及說明檔中程式庫說明主題的主題代碼。</summary>
      <param name="memid">型別描述的成員識別項。</param>
      <param name="pbstrHelpString">這個方法傳回時，包含 BSTR，其包含指定項目的名稱。如果呼叫端不需要項目名稱，<paramref name="pbstrHelpString" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pdwHelpStringContext">當這個方法傳回時，會包含說明的當地語系化內容。如果呼叫端不需要說明內容，<paramref name="pdwHelpStringContext" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pbstrHelpStringDll">這個方法傳回時，含有 BSTR，其包含了含有用於說明檔之 DLL 的檔案其完整名稱。如果呼叫端不需要檔案名稱，<paramref name="pbstrHelpStringDll" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>從指定函式取得自訂資料。</summary>
      <param name="index">用於取得自訂資料的函式索引。</param>
      <param name="guid">用來識別資料的 GUID。</param>
      <param name="pVarVal">這個方法傳回時，其中包含用於指定擷取資料放置位置的 Object。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>擷取包含所指定函式相關資訊的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構。</summary>
      <param name="index">要傳回的函式描述之索引。</param>
      <param name="ppFuncDesc">這個方法傳回時，包含描述指定函式的 FUNCDESC 結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>根據已知的 DISPID 繫結至特定成員，而成員名稱為未知 (例如，繫結至預設成員時)。</summary>
      <param name="memid">成員識別項。</param>
      <param name="invKind">其中一個 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值，指定由 memid 識別的成員類型。</param>
      <param name="pFuncIndex">這個方法傳回時，其中包含函式的索引。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>成員名稱與成員 ID 之間，以及參數名稱與參數 ID 之間的對應。</summary>
      <param name="rgszNames">要對應的名稱陣列。</param>
      <param name="cNames">要對應的名稱計數。</param>
      <param name="pMemId">這個方法傳回時，包含放置了名稱對應的陣列參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>取得自訂資料的實作類型。</summary>
      <param name="index">自訂資料的實作類型索引。</param>
      <param name="guid">用來識別資料的 GUID。</param>
      <param name="pVarVal">這個方法傳回時，包含指定所擷取資料放置處的 Object。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>擷取型別描述中某個已實作之介面或基底介面的 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 值。</summary>
      <param name="index">實作介面或基底介面的索引。</param>
      <param name="pImplTypeFlags">這個方法傳回時，包含 IMPLTYPEFLAGS 列舉型別的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>擷取封送處理 (Marshaling) 資訊。</summary>
      <param name="memid">成員 ID，表示需要哪些封送處理資訊。</param>
      <param name="pBstrMops">這個方法傳回時，包含 opcode 字串的參考，其用於封送處理由參考的型別描述所描述之結構的欄位，如果沒有資訊要傳回，則傳回 null。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>擷取具有對應到所指定函式 ID 之指定成員 ID (或是屬性或方法的名稱及其參數) 的變數。</summary>
      <param name="memid">要傳回其名稱之成員的 ID。</param>
      <param name="rgBstrNames">這個方法傳回時，包含與成員關聯的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 陣列的長度。</param>
      <param name="pcNames">這個方法傳回時，包含 <paramref name="rgBstrNames" /> 陣列中名稱的數目。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>取得指定的自訂資料參數。</summary>
      <param name="indexFunc">用於取得自訂資料的函式索引。</param>
      <param name="indexParam">用於取得自訂資料的這個函式之參數索引。</param>
      <param name="guid">用來識別資料的 GUID。</param>
      <param name="pVarVal">這個方法傳回時，包含指定所擷取資料放置處的 Object。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>如果型別描述參考其他型別描述，則擷取參考的型別描述。</summary>
      <param name="hRef">要傳回之已參考的類型描述的控制代碼。</param>
      <param name="ppTI">這個方法傳回時，包含參考的類型描述。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>如果型別描述對 COM 類別進行描述，則擷取實作介面型別的型別描述。</summary>
      <param name="index">已實作類型的索引，這個已實作類型的控制代碼已傳回。</param>
      <param name="href">這個方法傳回時，包含實作介面的處理常式之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>擷取包含型別描述之屬性 (Attribute) 的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 結構。</summary>
      <param name="ppTypeAttr">這個方法傳回時，包含了含有此型別描述屬性的結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>擷取型別描述的 ITypeComp 介面，它可以讓用戶端編譯器 (Compiler) 繫結至型別描述的成員。</summary>
      <param name="ppTComp">這個方法傳回時，包含了包含型別程式庫的 ITypeComp 之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>傳回型別旗標，不執行任何配置。這個方法傳回 DWORD 型別旗標，其會擴充型別旗標，但不增加 TYPEATTR (型別屬性)。</summary>
      <param name="pTypeFlags">這個方法傳回時，其中包含 TYPEFLAG 的 DWORD 參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>快速傳回 TYPEKIND 列舉型別，不執行任何配置。</summary>
      <param name="pTypeKind">這個方法傳回時，其中包含 TYPEKIND 列舉型別的參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>取得自訂資料的變數。</summary>
      <param name="index">用於取得自訂資料的變數索引。</param>
      <param name="guid">用來識別資料的 GUID。</param>
      <param name="pVarVal">這個方法傳回時，包含指定所擷取資料放置處的 Object。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>擷取描述指定變數的 VARDESC 結構。</summary>
      <param name="index">要傳回的變數描述之索引。</param>
      <param name="ppVarDesc">這個方法傳回時，包含描述指定變數的 VARDESC 結構之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>根據已知的 DISPID 繫結至指定成員，而成員名稱為未知 (例如，繫結至預設成員時)。</summary>
      <param name="memid">成員識別項。</param>
      <param name="pVarIndex">這個方法傳回時，其中包含 <paramref name="memid" /> 的索引。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>叫用 (Invoke) 實作型別描述所描述之介面的方法，或存取實作型別描述所描述之介面的物件屬性。</summary>
      <param name="pvInstance">這個型別描述所描述之介面的參考。</param>
      <param name="memid">介面成員的識別項。</param>
      <param name="wFlags">描述叫用呼叫之內容的旗標。</param>
      <param name="pDispParams">結構的參考，其包含引數的陣列、具名引數之 DISPID 的陣列，以及每一陣列中元素數目的計數。</param>
      <param name="pVarResult">要儲存結果之位置的參考。如果 <paramref name="wFlags" /> 指定 DISPATCH_PROPERTYPUT 或 DISPATCH_PROPERTYPUTREF，<paramref name="pVarResult" /> 便會被忽略。如果沒有想要的結果，請設定為 null。</param>
      <param name="pExcepInfo">例外狀況資訊結構的指標，只有在傳回 DISP_E_EXCEPTION 時才會填入。</param>
      <param name="puArgErr">如果 Invoke 傳回 DISP_E_TYPEMISMATCH，<paramref name="puArgErr" /> 會指出含錯誤型別的引數索引。如果有一個以上引數傳回錯誤，<paramref name="puArgErr" /> 只會指出第一個有錯誤的引數。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 方法傳回的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 結構。</summary>
      <param name="pFuncDesc">要釋放之 FUNCDESC 結構的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 方法傳回的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 結構。</summary>
      <param name="pTypeAttr">要釋放之 TYPEATTR 結構的參考。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>釋放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 方法傳回的 VARDESC 結構。</summary>
      <param name="pVarDesc">要釋放之 VARDESC 結構的參考。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>提供 ITypeLib 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>尋找型別描述在型別程式庫中的符合項目。</summary>
      <param name="szNameBuf">要搜尋的名稱。這是個 in/out 參數。</param>
      <param name="lHashVal">用來加速搜尋的雜湊值 (Hash Value)，由 LHashValOfNameSys 函式計算而得。如果 <paramref name="lHashVal" /> 為 0，表示值已計算出來。</param>
      <param name="ppTInfo">這個方法傳回時，包含型別描述的指標陣列，其包含 <paramref name="szNameBuf" /> 中指定的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="rgMemId">所找到項目之 MEMBERID 的陣列；<paramref name="rgMemId" />[i] 為 MEMBERID，其索引至由 <paramref name="ppTInfo" />[i] 所指定的型別描述。不可為 null。</param>
      <param name="pcFound">進入時，表示要尋找多少個執行個體。例如，若要尋找第一個符合項目可以呼叫 <paramref name="pcFound" /> = 1。找到一個執行個體時，搜尋便會停止。結束時，表示找到的執行個體數目。如果 <paramref name="pcFound" /> 的 in 和 out 值相同，可能還有更多包含該名稱的型別描述。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>擷取程式庫的文件字串、完整的說明檔名稱和路徑，以及說明檔中程式庫說明主題的主題代碼。</summary>
      <param name="index">要傳回文件的型別描述之索引。</param>
      <param name="strName">這個方法傳回時，包含表示指定項目名稱的字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strDocString">這個方法傳回時，包含表示指定項目的文件字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="dwHelpContext">這個方法傳回時，包含與指定項目關聯的說明主題識別項。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strHelpFile">這個方法傳回時，包含表示說明檔完整名稱的字串。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>擷取包含程式庫之屬性 (Attribute) 的結構。</summary>
      <param name="ppTLibAttr">這個方法傳回時，包含了含有程式庫屬性的結構。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>讓用戶端編譯器 (Compiler) 繫結至程式庫的型別、變數、常數和全域函式。</summary>
      <param name="ppTComp">這個方法傳回時，包含這個 ITypeLib 的 ITypeComp 執行個體的執行個體。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>擷取程式庫中指定的型別描述。</summary>
      <param name="index">要傳回的 ITypeInfo 介面之索引。</param>
      <param name="ppTI">這個方法傳回時，包含描述由 <paramref name="index" /> 所參考之型別的 ITypeInfo。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>傳回型別程式庫中型別描述的數目。</summary>
      <returns>型別程式庫中型別描述的數目。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>擷取對應到所指定 GUID 的型別描述。</summary>
      <param name="guid">要求其型別資訊之介面的 IID 或類別之 CLSID。</param>
      <param name="ppTInfo">這個方法傳回時，包含要求的 ITypeInfo 介面。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>擷取型別描述的型別。</summary>
      <param name="index">型別程式庫中型別描述的索引。</param>
      <param name="pTKind">這個方法傳回時，包含型別描述的 TYPEKIND 列舉型別之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>指示傳入字串是否含有程式庫中所描述型別或成員的名稱。</summary>
      <returns>如果在型別程式庫中找到 <paramref name="szNameBuf" />，則為 true，否則為 false。</returns>
      <param name="szNameBuf">要測試的字串。這是個 in/out 參數。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> 的雜湊值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>釋放原來從 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 方法取得的 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 結構。</summary>
      <param name="pTLibAttr">要釋放的 TLIBATTR 結構。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>提供 ITypeLib2 介面的 Managed 定義。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>尋找型別描述在型別程式庫中的符合項目。</summary>
      <param name="szNameBuf">要搜尋的名稱。</param>
      <param name="lHashVal">用來加速搜尋的雜湊值 (Hash Value)，由 LHashValOfNameSys 函式計算而得。如果 <paramref name="lHashVal" /> 為 0，表示值已計算出來。</param>
      <param name="ppTInfo">這個方法傳回時，包含型別描述的指標陣列，其包含 <paramref name="szNameBuf" /> 中指定的名稱。這個參數會以未初始化的狀態傳遞。</param>
      <param name="rgMemId">這個方法傳回時，會包含所找到項目之 MEMBERID 的陣列；<paramref name="rgMemId" /> [i] 為 MEMBERID，其索引至由 <paramref name="ppTInfo" /> [i] 所指定的型別描述。這個參數不可以是 null。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pcFound">項目上以傳址 (By Reference) 方式傳遞的值，指出要尋找的執行個體 (Instance) 數目。例如，若要尋找第一個符合項目可以呼叫 <paramref name="pcFound" /> = 1。找到一個執行個體時，搜尋便會停止。結束時，表示找到的執行個體數目。如果 <paramref name="pcFound" /> 的 in 和 out 值相同，可能還有更多包含該名稱的型別描述。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>取得程式庫所有的自訂資料項目。</summary>
      <param name="pCustData">CUSTDATA 的指標，其包含所有自訂資料項目。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>取得自訂資料。</summary>
      <param name="guid">以傳址方式傳遞的 <see cref="T:System.Guid" />，用來識別資料。</param>
      <param name="pVarVal">當這個方法傳回時，會包含指定所擷取資料放置處的物件。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>擷取程式庫的文件字串、完整的說明檔名稱和路徑，以及說明檔中程式庫說明主題的主題代碼。</summary>
      <param name="index">要傳回其文件之型別描述的索引。</param>
      <param name="strName">當這個方法傳回時，會包含指定特定項目名稱的字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strDocString">當這個方法傳回時，會包含指定項目的文件字串。這個參數會以未初始化的狀態傳遞。</param>
      <param name="dwHelpContext">這個方法傳回時，包含與指定項目關聯的說明主題識別項。這個參數會以未初始化的狀態傳遞。</param>
      <param name="strHelpFile">當這個方法傳回時，會包含指定說明檔完整名稱的字串。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>擷取程式庫的文件字串、完整的說明檔名稱和路徑，以及說明檔中程式庫說明主題的主題代碼。</summary>
      <param name="index">要傳回其文件之型別描述的索引；如果 <paramref name="index" /> 為 -1，則傳回程式庫的文件。</param>
      <param name="pbstrHelpString">當這個方法傳回時，會包含指定特定項目名稱的 BSTR。如果呼叫端不需要項目名稱，<paramref name="pbstrHelpString" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pdwHelpStringContext">當這個方法傳回時，會包含說明的當地語系化內容。如果呼叫端不需要說明內容，<paramref name="pdwHelpStringContext" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
      <param name="pbstrHelpStringDll">當這個方法傳回時會包含 BSTR，其指定包含用於說明檔的 DLL 的完整檔案名稱。如果呼叫端不需要檔案名稱，<paramref name="pbstrHelpStringDll" /> 可以是 null。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>擷取包含程式庫之屬性 (Attribute) 的結構。</summary>
      <param name="ppTLibAttr">這個方法傳回時，包含了含有程式庫屬性的結構。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>傳回有效調整雜湊資料表大小所需的型別程式庫相關統計資料。</summary>
      <param name="pcUniqueNames">唯一名稱計數的指標。如果呼叫端不需要這項資訊，請設定為 null。</param>
      <param name="pcchUniqueNames">當這方法傳回時，會包含唯一名稱計數器變更的指標。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>讓用戶端編譯器 (Compiler) 繫結至程式庫的型別、變數、常數和全域函式。</summary>
      <param name="ppTComp">當這個方法傳回時，會包含這個 ITypeLib 的 ITypeComp 執行個體。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>擷取程式庫中指定的型別描述。</summary>
      <param name="index">要傳回之 ITypeInfo 介面的索引。</param>
      <param name="ppTI">這個方法傳回時，包含描述由 <paramref name="index" /> 所參考之型別的 ITypeInfo。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>傳回型別程式庫中型別描述的數目。</summary>
      <returns>型別程式庫中型別描述的數目。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>擷取對應到所指定 GUID 的型別描述。</summary>
      <param name="guid">以傳址方式傳遞的 <see cref="T:System.Guid" />，表示已要求其型別資訊之類別的 CLSID 介面 IID。</param>
      <param name="ppTInfo">這個方法傳回時，包含要求的 ITypeInfo 介面。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>擷取型別描述的型別。</summary>
      <param name="index">型別程式庫中型別描述的索引。</param>
      <param name="pTKind">這個方法傳回時，包含型別描述的 TYPEKIND 列舉型別之參考。這個參數會以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>指示傳入字串是否含有程式庫中所描述型別或成員的名稱。</summary>
      <returns>如果在型別程式庫中找到 <paramref name="szNameBuf" />，則為 true，否則為 false。</returns>
      <param name="szNameBuf">要測試的字串。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> 的雜湊值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>釋放原來從 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 方法取得的 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 結構。</summary>
      <param name="pTLibAttr">要釋放的 TLIBATTR 結構。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>定義套用至型別程式庫的旗標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>這個型別程式庫會描述控制項，而且不應該顯示在供非視覺化物件用的型別瀏覽器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>型別程式庫存在於磁碟上保存的表單中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>型別程式庫不應向使用者顯示，不過它的使用並不受限制。這個型別程式庫應該由控制項使用。主應用程式 (Host) 應該建立以擴充屬性包裝控制項的新型別程式庫。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>型別程式庫受限制，而且不應向使用者顯示。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>含有如何在處理序之間傳輸結構項目、參數或函式傳回值的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>表示正在處理序之間傳遞的值的指標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>表示描述結構項目、參數或傳回值的位元遮罩值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>描述如何在處理序之間傳輸結構項目、參數或函式傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>參數具有自訂資料。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>參數具有已定義的預設行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>參數從呼叫端傳遞資訊到被呼叫端。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>參數為用戶端應用程式的區域識別項。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>參數為選擇性 (Optional)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>參數從被呼叫端傳回資訊到呼叫端。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>參數為成員的傳回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>沒有指定參數是傳送或接收資訊。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>提供 STATDATA 結構的 Managed 定義。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> 列舉值，決定告知諮詢接收資料變更的時機。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>表示要接收變更告知的 <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> 介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>表示可唯一識別諮詢連接的語彙基元 (Token)。這個語彙基元是由設定諮詢連接的方法所傳回。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>表示通知接收相關資料的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 結構。通知接收會收到這個 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 結構所指定之資料的變更告知。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>含有關於開放儲存區、資料流或位元組陣列物件的統計資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>指定這個儲存區、資料流或位元組陣列的上次存取時間。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>指定資料流或位元組陣列的大小，以位元組為單位。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>指示儲存物件 (Storage Object) 的類別識別項 (Class Identifier)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>指示這個儲存區、資料流或位元組陣列的建立時間。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>指示資料流或位元組陣列所支援之區域鎖定的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>指示當物件開啟時所指定的存取模式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>指示儲存物件的目前狀態位元 (IStorage::SetStateBits 方法最近設定的值)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>指示這個儲存區、資料流或位元組陣列的上一次修改時間。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>表示以 null 結尾字串的指標，此字串含有這個結構所描述之物件的名稱。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>指示儲存物件的型別，即來自 STGTY 列舉型別的其中一個值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>提供 STGMEDIUM 結構的 Managed 定義。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>表示介面執行個體的指標，這個介面執行個體可以在接收處理序呼叫 ReleaseStgMedium 函式時，讓傳送處理序控制釋放儲存區的方式。如果 <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 是 null，ReleaseStgMedium 會使用預設程序來釋放儲存區，否則 ReleaseStgMedium 便會使用指定的 IUnknown 介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>指定存放媒體的類型。封送處理 (Marshaling) 和解封送處理 (Unmarshaling) 常式會使用這個值來判斷所使用的等位成員。這個值必須是 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 列舉型別的其中一個項目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>表示接收處理序可以用來存取所傳輸之資料的控制代碼、字串或介面指標。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>識別目標作業系統平台。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>型別程式庫的目標作業系統為 Apple Macintosh。根據預設，所有資料欄位都是對齊偶數位元組 (even-byte) 界限。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>型別程式庫的目標作業系統是 16 位元 Windows 系統。根據預設，資料欄位是封裝的。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>型別程式庫的目標作業系統是 32 位元 Windows 系統。根據預設，資料欄位是以自然方式對齊 (例如，2 位元組的整數是對齊偶數位元組界限；4 位元組的整數是對齊四字組界限，依此類推)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>型別程式庫的目標作業系統是 64 位元 Windows 系統。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>提供 TYMED 結構的 Managed 定義。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>存放媒體是加強型中繼檔 (Metafile)。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員為 null，則目的端處理序應該使用 DeleteEnhMetaFile 刪除點陣圖。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>存放媒體是由路徑所識別的磁碟檔。如果 STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員為 null，則目的端處理序應該使用 OpenFile 刪除檔案。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>存放媒體是繪圖裝置介面 (Graphics Device Interface，GDI) 元件 (HBITMAP)。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員為 null，則目的端處理序應該使用 DeleteObject 刪除點陣圖。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>存放媒體是全域記憶體處理 (HGLOBAL)。以 GMEM_SHARE 旗標配置全域控制代碼。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員為 null，則目的端處理序應該使用 GlobalFree 釋放記憶體。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>存放媒體是由 IStorage 指標所識別的儲存區元件。資料位於這個 IStorage 執行個體 (Instance) 所包含的資料流和儲存區中。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員不是 null，則目的端處理序應該使用 IStorage::Release 釋放儲存區元件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>存放媒體是由 IStream 指標所識別的資料流物件。請使用 ISequentialStream::Read 讀取資料。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員不是 null，則目的端處理序應該使用 IStream::Release 釋放資料流元件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>存放媒體是中繼檔 (HMETAFILE)。請使用 Windows 或 WIN32 函式存取中繼檔的資料。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成員為 null，則目的端處理序應該使用 DeleteMetaFile 刪除點陣圖。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>未傳遞任何資料。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>含有 UCOMITypeInfo 的屬性 (Attribute)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>指定這個型別之執行個體的位元組對齊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>這個型別之執行個體的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>這個型別的虛擬方法表 (Virtual Method Table，VTBL) 的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>指示這個結構所描述之介面上的函式數目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>指示這個結構所描述之介面上已實作的介面數目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>指示這個結構所描述之介面上變數和資料欄位的數目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>型別資訊的 GUID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>所描述型別的 IDL 屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>成員名稱和文件字串的地區設定 (Locale)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>保留供將來使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>配合 <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> 和 <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" /> 欄位使用的常數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>建構函式的 ID；如果沒有，則為 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>解構函式的 ID；如果沒有，則為 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>如果 <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />，指出這個型別為其別名的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>
        <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> 值，描述這項資訊所描述的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>主要版本號碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>次要版本號碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>描述這項資訊的 <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> 值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>描述變數的型別、函式的傳回型別 (Return Type) 或函式參數的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>如果變數為 VT_SAFEARRAY 或 VT_PTR，則 lpValue 欄位會含有 TYPEDESC 的指標，用來指定元素型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>指示這個 TYPEDESC 所描述之項目的 Variant 型別。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>定義型別描述的屬性 (Property) 和屬性 (Attribute)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>類別支援彙總 (Aggregation)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>描述 Application 物件的型別描述。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>可以由 ITypeInfo::CreateInstance 建立的型別的執行個體。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>型別是衍生其他型別的控制項，不應該向使用者顯示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>指示介面是從 IDispatch 直接或間接衍生的。這個旗標是計算得來的，沒有這個旗標的物件描述語言 (Object Description Language)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>介面同時提供 IDispatch 和 VTBL 繫結。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>型別不應顯示到瀏覽器。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>型別已經授權。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>介面在執行階段不能加入成員。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>介面中使用的型別與 Automation 完全相容，包括 VTBL 繫結支援。介面的雙重設定為同時設定這個旗標和 <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />。分配介面不允許這個旗標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>型別已預先定義。用戶端應用程式應該自動建立具有這項屬性之物件的單一執行個體。指向該物件的變數名稱與該物件的類別名稱相同。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>指示介面會使用 Proxy/Stub 動態連結程式庫。這個旗標指定當型別程式庫被取消登錄時，型別程式庫 Proxy 不應被取消登錄。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>物件支援 IConnectionPointWithDefault，而且具有預設的行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>應該不可從巨集語言存取。這個旗標是供系統層級的型別或型別瀏覽器不應顯示的型別使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>指示基底介面在檢查子系前應先檢查名稱解析，與預設行為相反。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>指定資料和函式的各種型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>為另一個型別的別名 (Alias) 的型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>一組實作的元件介面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>可透過 IDispatch::Invoke 來存取的一組方法和屬性。預設情況下，介面會傳回 TKIND_DISPATCH。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>一組列舉值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>具有虛擬函式 (Virtual Function) 的型別，其中全都是純虛擬函式 (Pure Virtual Function)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>列舉型別 (Enumeration) 資料標記的結尾。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>只能有靜態函式和資料的模組 (例如，DLL)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>沒有方法的結構。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>所有具有零位移 (Offset) 的成員的等位。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>辨識特定型別程式庫，並提供成員名稱的當地語系化支援。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>全域地表示型別程式庫的唯一程式庫 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>表示型別程式庫的地區設定 ID (Locale ID)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>表示型別程式庫的目標硬體平台。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>表示程式庫旗標。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>表示型別程式庫的主要版本號碼。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>表示型別程式庫的次要版本號碼。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>描述變數、常數或資料成員。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>包含變數的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>含有變數型別。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>這個欄位保留作未來使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>表示變數的成員 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>定義如何封送處理 (Marshal) 變數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>定義變數的屬性。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>包含變數的資訊。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>描述符號常數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>指示執行個體 (Instance) 內這個變數的位移 (Offset)。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>辨識定義變數的屬性的常數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>變數支援資料繫結 (Data Binding)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>變數是最能表示該物件的單一屬性。型別資訊中只能有一個變數具有這個屬性 (Attribute)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>允許最佳化，其中編譯器會在「abc」型別上尋找名為「xyz」的成員。如果找到這種成員，而且是被標示為預設集合之元素的存取子 (Accessor) 函式，就會產生一個對該成員函式的呼叫。在分配介面和介面中的成員受允許；模組上則不受允許。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>變數對使用者顯示為可繫結的。<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" /> 也必須設定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>雖然變數存在而且可繫結，但是不應該在瀏覽器中向使用者顯示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>變數是對應為可繫結的個別屬性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>變數出現在物件瀏覽器中，但不會在屬性瀏覽器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>不允許對變數進行指派 (Assignment)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>將介面標記 (Tag) 為具有預設行為。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>設定後，間接變更屬性的任何嘗試都將產生對 IPropertyNotifySink::OnRequestEdit 的呼叫。OnRequestEdit 的實作 (Implementation) 會判斷是否接受變更。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>變數不應該可自巨集語言存取。這旗標旨在做為系統層級變數或您不希望型別瀏覽器顯示的變數。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>變數會傳回是事件來源的物件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>變數為使用者介面中的預設顯示。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>定義變數的類型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>VARDESC 結構會描述符號常數。沒有與它關聯的記憶體。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>此變數只能透過 IDispatch::Invoke 來存取。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>此變數是型別的欄位或成員。它會存在型別的每個執行個體內的固定位移處。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>此變數只有一個執行個體。</summary>
    </member>
  </members>
</doc>