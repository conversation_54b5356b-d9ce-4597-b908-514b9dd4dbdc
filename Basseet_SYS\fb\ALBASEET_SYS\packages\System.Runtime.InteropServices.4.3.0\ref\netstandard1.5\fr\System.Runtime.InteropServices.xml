﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>Exception levée lors de la lecture ou de l'écriture d'une unité de données dans une adresse qui n'est pas un multiple de la taille de données.Cette classe ne peut pas être héritée.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DataMisalignedException" />. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DataMisalignedException" /> à l'aide du message d'erreur spécifié.</summary>
      <param name="message">Objet <see cref="T:System.String" /> qui décrit l'erreur.Le contenu du <paramref name="message" /> doit être compréhensible pour les utilisateurs.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DataMisalignedException" /> à l'aide du message d'erreur spécifié et de l'exception sous-jacente.</summary>
      <param name="message">Objet <see cref="T:System.String" /> qui décrit l'erreur.Le contenu du <paramref name="message" /> doit être compréhensible pour les utilisateurs.L'appelant de ce constructeur doit vérifier que cette chaîne a été localisée pour la culture du système en cours.</param>
      <param name="innerException">Exception qui constitue la cause de l'actuel <see cref="T:System.DataMisalignedException" />.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>Exception levée lorsqu'une DLL spécifiée dans une importation de DLL est introuvable.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DllNotFoundException" /> avec des propriétés par défaut.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DllNotFoundException" /> avec un message d'erreur spécifié.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.DllNotFoundException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>Représente un <see cref="T:System.Object" /> manquant.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>Représente l'instance unique de la classe <see cref="T:System.Reflection.Missing" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>Encapsule un tableau et un offset dans le tableau spécifié.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>Initialise une nouvelle instance de la structure <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <param name="array">Tableau managé. </param>
      <param name="offset">Offset de l'élément à passer par appel de plateforme, en octets. </param>
      <exception cref="T:System.ArgumentException">La taille du tableau est supérieure à 2 gigaoctets (Go).</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>Indique si l'objet spécifié correspond à l'objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> en cours.</summary>
      <returns>true si l'objet correspond à <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> ; sinon false.</returns>
      <param name="obj">Objet à comparer à cette instance. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Indique si l'objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> spécifié correspond à l'instance actuelle.</summary>
      <returns>true si l'objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> spécifié correspond à l'instance actuelle ; sinon, false.</returns>
      <param name="obj">Objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> à comparer à cette instance.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>Retourne le tableau managé référencé par ce <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Tableau managé référencé par cette instance.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>Retourne un code de hachage pour ce type valeur.</summary>
      <returns>Code de hachage pour cette instance.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>Retourne l'offset fourni quand <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> a été construit.</summary>
      <returns>Offset pour cette instance.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Détermine si deux objets <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> spécifiés ont la même valeur.</summary>
      <returns>true si la valeur de <paramref name="a" /> est égale à la valeur de <paramref name="b" /> ; sinon false.</returns>
      <param name="a">Objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> à comparer au paramètre <paramref name="b" />. </param>
      <param name="b">Objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> à comparer au paramètre <paramref name="a" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Détermine si deux objets <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> spécifiés n'ont pas la même valeur.</summary>
      <returns>true si la valeur de <paramref name="a" /> n'est pas la même que la valeur de <paramref name="b" /> ; sinon false.</returns>
      <param name="a">Objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> à comparer au paramètre <paramref name="b" />. </param>
      <param name="b">Objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> à comparer au paramètre <paramref name="a" />.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Contrôle si les caractères Unicode sont convertis en caractères ANSI offrant la correspondance la plus proche.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> à laquelle est attribuée la valeur de la propriété <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" />.</summary>
      <param name="BestFitMapping">true pour indiquer que le mappage ajusté est activé ; sinon false.La valeur par défaut est true.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Obtient le comportement de mappage ajusté lors de la conversion de caractères Unicode en caractères ANSI.</summary>
      <returns>true si le mappage ajusté est activé ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>Active ou désactive la levée d'une exception sur un caractère Unicode non mappable converti en caractère ANSI '?'.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>Marshale des données de type VT_BSTR de code managé en code non managé.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> avec l'objet <see cref="T:System.Object" /> spécifié.</summary>
      <param name="value">Objet à inclure dans un wrapper et à marshaler en tant que VT_BSTR.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> avec l'objet <see cref="T:System.String" /> spécifié.</summary>
      <param name="value">Objet à inclure dans un wrapper et à marshaler en tant que VT_BSTR.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>Obtient l'objet <see cref="T:System.String" /> encapsulé à marshaler comme type VT_BSTR.</summary>
      <returns>Objet inclus dans un wrapper par <see cref="T:System.Runtime.InteropServices.BStrWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>Spécifie la convention d'appel nécessaire pour appeler les méthodes implémentées en code non managé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>L'appelant nettoie la pile.Cette procédure active les fonctions d'appel avec varargs qu'il convient d'utiliser pour les méthodes qui acceptent un nombre variable de paramètres tels que Printf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>L'appelé nettoie la pile.Il s'agit de la convention par défaut pour appeler les fonctions non managées avec appel de code non managé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>Le premier paramètre est le pointeur this et est stocké dans le Registre ECX.D'autres paramètres font l'objet d'un push sur la pile.Cette convention d'appel est utilisée pour appeler des méthodes sur des classes exportées à partir d'une DLL non managée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>Ce membre n'est pas réellement une convention d'appel mais il utilise en revanche la convention d'appel de code non managé par défaut.Par exemple, sur Windows, le champ par défaut est <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" /> et sur Windows CE .NET, il s'agit du champ <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>Indique le type d'interface de classe à générer pour une classe exposée à COM, si une interface est générée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> avec la valeur d'énumération <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> spécifiée.</summary>
      <param name="classInterfaceType">Décrit le type d'interface qui est généré pour une classe. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> avec le membre de l'énumération <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> spécifié.</summary>
      <param name="classInterfaceType">Une des valeurs <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> qui décrit le type d'interface qui est généré pour une classe. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>Obtient la valeur <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> qui décrit le type d'interface qui doit être généré pour la classe.</summary>
      <returns>Valeur <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> qui décrit le type d'interface qui doit être généré pour la classe.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>Identifie le type d'interface de classe qui est généré pour une classe.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>Indique que la classe ne prend en charge que la liaison tardive pour des clients COM.Une dispinterface pour la classe est automatiquement exposée aux clients COM à la demande.La bibliothèque de types générée par l'outil Tlbexp.exe (exportateur de bibliothèques de types) ne contient pas d'informations de type pour la dispinterface afin d'empêcher les clients de mettre en cache les DISPID de l'interface.La dispinterface ne présente pas les problèmes de versioning décrits dans <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> car les clients ne peuvent se lier à l'interface que par liaison tardive.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>Indique qu'une interface de classe double est automatiquement générée pour la classe et exposée à COM.Les informations de type sont générées pour l'interface de classe et publiées dans la bibliothèque de types.L'utilisation de AutoDual est fortement déconseillée en raison des limitations de versioning décrites dans <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>Indique qu'aucune interface de classe n'est générée pour la classe.Si aucune interface n'est explicitement implémentée, la classe ne permet que l'accès par liaison tardive à l'aide de l'interface IDispatch.Il s'agit du paramètre recommandé pour <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.L'utilisation de ClassInterfaceType.None est la seule manière d'exposer les fonctionnalités via des interfaces implémentées explicitement par la classe.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>Spécifie l'identificateur de classe d'une coclasse importée à partir d'une bibliothèque de types.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>Initialise une nouvelle instance de <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> avec l'identificateur de classe de la coclasse d'origine.</summary>
      <param name="coClass">
        <see cref="T:System.Type" /> contenant l'identificateur de classe de la coclasse d'origine. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>Obtient l'identificateur de classe de la coclasse d'origine.</summary>
      <returns>
        <see cref="T:System.Type" /> contenant l'identificateur de classe de la coclasse d'origine.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>Autorise l'inscription à liaison tardive d'un gestionnaire d'événements.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> en utilisant le type spécifié et un nom d'événement sur le type.</summary>
      <param name="type">Type d'objet. </param>
      <param name="eventName">Nom d'un événement sur <paramref name="type" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>Attache un gestionnaire d'événements à un objet COM.</summary>
      <param name="target">Objet cible avec lequel le délégué d'événement doit créer une liaison.</param>
      <param name="handler">Délégué d'événement.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>Obtient les attributs de cet événement.</summary>
      <returns>Attributs en lecture seule de cet événement.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>Obtient la classe qui déclare ce membre.</summary>
      <returns>Objet <see cref="T:System.Type" /> de la classe qui déclare ce membre.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>Obtient le nom du membre actuel.</summary>
      <returns>Nom de ce membre.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>Détache un gestionnaire d'événements d'un objet COM.</summary>
      <param name="target">Objet cible avec lequel le délégué d'événement est lié.</param>
      <param name="handler">Délégué d'événement.</param>
      <exception cref="T:System.InvalidOperationException">L'événement n'a pas d'accesseur remove public.</exception>
      <exception cref="T:System.ArgumentException">Le gestionnaire passé ne peut pas être utilisé.</exception>
      <exception cref="T:System.Reflection.TargetException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte <see cref="T:System.Exception" /> sinon.Le paramètre <paramref name="target" /> est null et l'événement n'est pas statique.ou <see cref="T:System.Reflection.EventInfo" /> n'est pas déclaré dans la cible.</exception>
      <exception cref="T:System.MethodAccessException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.MemberAccessException" />, sinon.L'appelant n'est pas autorisé à accéder au membre.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>Spécifie une interface par défaut à exposer à COM.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> avec l'objet <see cref="T:System.Type" /> spécifié comme interface par défaut exposée à COM.</summary>
      <param name="defaultInterface">Valeur <see cref="T:System.Type" /> indiquant l'interface par défaut à exposer à COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>Obtient l'objet <see cref="T:System.Type" /> qui spécifie l'interface par défaut à exposer à COM.</summary>
      <returns>Objet <see cref="T:System.Type" /> qui spécifie l'interface par défaut à exposer à COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>Identifie l'interface source et la classe qui implémente les méthodes de l'interface d'événement qui est générée lorsqu'une coclasse est importée à partir d'une bibliothèque de types COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> avec l'interface source et la classe du fournisseur d'événements.</summary>
      <param name="SourceInterface">
        <see cref="T:System.Type" /> contenant l'interface source d'origine de la bibliothèque de types.COM utilise cette interface pour rappeler la classe managée.</param>
      <param name="EventProvider">
        <see cref="T:System.Type" /> qui contient la classe implémentant les méthodes de l'interface d'événement. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>Obtient la classe qui implémente les méthodes de l'interface d'événement.</summary>
      <returns>
        <see cref="T:System.Type" /> qui contient la classe implémentant les méthodes de l'interface d'événement.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>Obtient l'interface source d'origine de la bibliothèque de types.</summary>
      <returns>
        <see cref="T:System.Type" /> contenant l'interface source.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>Fournit des méthodes qui permettent aux délégués .NET Framework qui gèrent les événements d'être ajoutés et supprimés dans les objets COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Ajoute un délégué à la liste d'appel des événements provenant d'un objet COM.</summary>
      <param name="rcw">Objet COM qui déclenche les événements auxquels l'appelant veut répondre.</param>
      <param name="iid">Identificateur de l'interface source utilisée par l'objet COM pour déclencher des événements. </param>
      <param name="dispid">Identificateur de dispatch de la méthode pour l'interface source.</param>
      <param name="d">Délégué à appeler lorsque l'événement COM est déclenché.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Supprime un délégué dans la liste d'appel des événements provenant d'un objet COM.</summary>
      <returns>Délégué supprimé de la liste d'appel.</returns>
      <param name="rcw">Objet COM auquel le délégué est attaché.</param>
      <param name="iid">Identificateur de l'interface source utilisée par l'objet COM pour déclencher des événements. </param>
      <param name="dispid">Identificateur de dispatch de la méthode pour l'interface source.</param>
      <param name="d">Délégué à supprimer dans la liste d'appel.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>Exception levée quand un HRESULT non reconnu est retourné d'un appel de méthode COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.COMException" /> avec les valeurs par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.COMException" /> avec le message spécifié.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.COMException" /> avec un message d'erreur spécifié et une référence à l'exception interne qui est à l'origine de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.COMException" /> avec un message et un code d'erreur spécifiés.</summary>
      <param name="message">Message indiquant la raison pour laquelle l'exception s'est produite. </param>
      <param name="errorCode">Valeur (HRESULT) du code d'erreur associé à l'exception. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>Indique que le type avec attributs a été défini précédemment dans COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>Initialise une nouvelle instance de l'<see cref="T:System.Runtime.InteropServices.ComImportAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>Identifie comment exposer une interface à COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>Indique que l'interface est exposée à COM en tant qu'interface double, laquelle autorise les liaisons anticipées et tardives.<see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" /> est la valeur par défaut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>Indique que l'interface est exposée à COM en tant qu'interface dispinterface, qui autorise uniquement les liaisons tardives.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>Indique qu'une interface est exposée à COM comme une interface Windows Runtime. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>Indique qu'une interface est exposée à COM en tant qu'interface dérivée de IUnknown, qui autorise uniquement la liaison anticipée.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>Décrit le type d'un membre COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>Le membre est une méthode normale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>Le membre obtient des propriétés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>Le membre définit des propriétés.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>Identifie la liste des interfaces exposées sous la forme de sources d'événements COM pour la classe avec attributs.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> avec le nom de l'interface de source d'événements.</summary>
      <param name="sourceInterfaces">Liste délimitée par une valeur null de noms qualifiés complets d'interfaces de source d'événements. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> avec le type à utiliser comme interface source.</summary>
      <param name="sourceInterface">Type <see cref="T:System.Type" /> de l'interface source. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> avec les types à utiliser comme interfaces sources.</summary>
      <param name="sourceInterface1">Type <see cref="T:System.Type" /> de l'interface source par défaut. </param>
      <param name="sourceInterface2">Type <see cref="T:System.Type" /> d'une interface source. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>Initialise une nouvelle instance de la classe ComSourceInterfacesAttribute avec les types à utiliser comme interfaces sources.</summary>
      <param name="sourceInterface1">Type <see cref="T:System.Type" /> de l'interface source par défaut. </param>
      <param name="sourceInterface2">Type <see cref="T:System.Type" /> d'une interface source. </param>
      <param name="sourceInterface3">Type <see cref="T:System.Type" /> d'une interface source. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> avec les types à utiliser comme interfaces sources.</summary>
      <param name="sourceInterface1">Type <see cref="T:System.Type" /> de l'interface source par défaut. </param>
      <param name="sourceInterface2">Type <see cref="T:System.Type" /> d'une interface source. </param>
      <param name="sourceInterface3">Type <see cref="T:System.Type" /> d'une interface source. </param>
      <param name="sourceInterface4">Type <see cref="T:System.Type" /> d'une interface source. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>Obtient le nom qualifié complet de l'interface de source d'événements.</summary>
      <returns>Nom qualifié complet de l'interface de source d'événements.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>Encapsule des objets que le marshaleur doit marshaler comme VT_CY.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> avec le Decimal à encapsuler et marshaler comme VT_CY de type.</summary>
      <param name="obj">Decimal à encapsuler et marshaler comme VT_CY. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> avec l'objet contenant le Decimal à encapsuler et marshaler comme VT_CY de type.</summary>
      <param name="obj">Objet contenant le Decimal à encapsuler et marshaler comme VT_CY. </param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="obj" /> n'est pas un type <see cref="T:System.Decimal" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>Obtient l'objet encapsulé à marshaler comme VT_CY de type.</summary>
      <returns>Objet encapsulé à marshaler comme VT_CY de type.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>Indique si les appels de IUnknown::QueryInterface de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> peuvent utiliser l'interface <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>Les appels de méthode peuvent utiliser l'interface IUnknown::QueryInterface<see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.Lorsque vous utilisez cette valeur, la surcharge de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> fonctionne comme la surcharge de <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>Les appels de méthode doivent ignorer l'interface IUnknown::QueryInterface<see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>Fournit les valeurs de retour de la méthode <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>L'interface d'un ID d'interface spécifique n'est pas disponible.Dans ce cas, l'interface retournée est null.E_NOINTERFACE est retourné à l'appelant de IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>Le pointeur d'interface retourné par la méthode <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> peut être utilisé comme résultat de IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>Le QueryInterface personnalisé n'a pas été utilisé.À la place, l'implémentation par défaut de IUnknown::QueryInterface doit être utilisée.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>Spécifie la valeur de l'énumération <see cref="T:System.Runtime.InteropServices.CharSet" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> avec la valeur <see cref="T:System.Runtime.InteropServices.CharSet" /> spécifiée.</summary>
      <param name="charSet">Une des valeurs de <see cref="T:System.Runtime.InteropServices.CharSet" />.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>Obtient la valeur par défaut de <see cref="T:System.Runtime.InteropServices.CharSet" /> pour tout appel à <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</summary>
      <returns>Valeur par défaut de <see cref="T:System.Runtime.InteropServices.CharSet" /> pour tout appel à <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>Spécifie les chemins d'accès qui permettent de rechercher les DLL qui fournissent des fonctionnalités pour les appels de code non managé. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" />, en spécifiant les chemins d'accès à utiliser lors de la recherche des cibles des appels de plateforme. </summary>
      <param name="paths">Combinaison d'opérations de bits des valeurs de l'énumération qui spécifient les chemins que la fonction LoadLibraryEx recherche lors des appels de code non managé. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>Obtient une combinaison d'opérations de bits des valeurs d'énumération qui spécifient les chemins que la fonction LoadLibraryEx recherche lors des appels de plateformes. </summary>
      <returns>Combinaison d'opérations de bits des valeurs de l'énumération qui spécifient des chemins de recherche pour les appels de code non managé. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>Définit la valeur par défaut d'un paramètre lorsqu'il est appelé à partir d'un langage qui prend en charge des paramètres par défaut.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> avec la valeur par défaut d'un paramètre.</summary>
      <param name="value">Objet qui représente la valeur par défaut d'un paramètre.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>Obtient la valeur par défaut d'un paramètre.</summary>
      <returns>Objet qui représente la valeur par défaut d'un paramètre.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>Encapsule les objets que le marshaleur doit marshaler en tant que VT_DISPATCH.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> avec l'objet à encapsuler.</summary>
      <param name="obj">Objet à encapsuler et à convertir en <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> n'est ni une classe ni un tableau.ou <paramref name="obj" /> ne prend pas en charge IDispatch. </exception>
      <exception cref="T:System.InvalidOperationException">Le paramètre <paramref name="obj" /> a été marqué avec un attribut <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> à qui une valeur false a été passée.ouLe paramètre <paramref name="obj" /> hérite d'un type marqué avec un attribut <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> à qui une valeur false a été passée.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>Obtient l'objet encapsulé par <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</summary>
      <returns>Objet encapsulé par <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>Spécifie l'identificateur de dispatch COM (DISPID) d'une méthode, d'un champ ou d'une propriété.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe DispIdAttribute avec le DISPID spécifié.</summary>
      <param name="dispId">DISPID du membre. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>Obtient le DISPID du membre.</summary>
      <returns>DISPID du membre.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>Indique que la méthode avec attributs est exposée par une bibliothèque de liens dynamiques (DLL) non managée comme point d'entrée statique.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> avec le nom de la DLL contenant la méthode à importer.</summary>
      <param name="dllName">Nom de la DLL contenant la méthode non managée.Cela peut inclure le nom complet d'un assembly, si la DLL est incluse dans un assembly.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Active ou désactive le comportement de mappage ajusté lors de la conversion de caractères Unicode en caractères ANSI.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>Indique la convention d'appel d'un point d'entrée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>Indique comment marshaler les paramètres de chaîne vers la méthode et contrôle la composition des noms.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>Indique le nom ou le numéro du point d'entrée de DLL à appeler.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>Contrôle si le champ <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> déclenche la recherche de noms, par le Common Language Runtime, de points d'entrée autres que celui spécifié dans la DLL non managée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>Indique si les méthodes non managées qui ont les valeurs de retour HRESULT ou retval sont traduites directement ou si les valeurs de retour HRESULT ou retval sont automatiquement converties en exceptions.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>Indique si l'appelé appelle la fonction API Win32 SetLastError avant de retourner la valeur à partir de la méthode avec attributs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>Active ou désactive la levée d'une exception sur un caractère Unicode non mappable converti en caractère ANSI "?".</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>Obtient le nom du fichier DLL contenant le point d'entrée.</summary>
      <returns>Nom du fichier DLL contenant le point d'entrée.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>Spécifie les chemins d'accès qui permettent de rechercher les DLL qui fournissent des fonctionnalités pour les appels de code non managé. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>Inclure le répertoire d'application dans le chemin de recherche de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>Lors de la recherche de dépendances d'assembly, inclut le répertoire qui contient l'assembly lui-même et commence par rechercher dans ce répertoire.Cette valeur est utilisée par .NET Framework, avant la transmission des chemins d'accès à la fonction Win32 LoadLibraryEx.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>Recherche dans le répertoire de l'application, puis appelle la fonction Win32 LoadLibraryEx avec l'indicateur LOAD_WITH_ALTERED_SEARCH_PATH.Cette valeur est ignorée si une autre valeur est spécifiée.Les systèmes d'exploitation qui ne prennent pas en charge l'attribut <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> utilisent cette valeur et ignorent d'autres valeurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>Inclure le répertoire d'application, le répertoire %WinDir%\System32 et les répertoires utilisateur dans le chemin de recherche de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>Inclure le répertoire %WinDir%\System32 dans le chemin de recherche de DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>Recherche les dépendances d'une DLL dans le dossier où la DLL se trouve avant d'explorer d'autres dossiers. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Inclure tout chemin d'accès qui a été explicitement ajouté au chemin de recherche au niveau du processus à l'aide de la fonction AddDllDirectory Win32. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>Encapsule les objets que le marshaleur doit marshaler en tant que VT_ERROR.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> avec le HRESULT correspondant à l'exception spécifiée.</summary>
      <param name="e">L'exception à convertir en code d'erreur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> avec le HRESULT de l'erreur.</summary>
      <param name="errorCode">HRESULT de l'erreur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> avec un objet contenant le HRESULT de l'erreur.</summary>
      <param name="errorCode">Objet contenant le HRESULT de l'erreur. </param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="errorCode" /> n'est pas un type <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>Obtient le code d'erreur du wrapper.</summary>
      <returns>HRESULT de l'erreur.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>Fournit un moyen d'accéder à un objet managé à partir d'une mémoire non managée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>Récupère l'adresse d'un objet dans un handle <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />.</summary>
      <returns>Adresse de l'objet épinglé en tant que <see cref="T:System.IntPtr" />. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>Alloue un handle <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> pour l'objet spécifié.</summary>
      <returns>Nouveau <see cref="T:System.Runtime.InteropServices.GCHandle" /> qui protège l'objet contre une opération garbage collection.<see cref="T:System.Runtime.InteropServices.GCHandle" /> doit être libéré avec <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> quand il n'est plus nécessaire.</returns>
      <param name="value">Objet qui utilise <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>Alloue un handle du type spécifié pour l'objet spécifié.</summary>
      <returns>Nouveau <see cref="T:System.Runtime.InteropServices.GCHandle" /> du type spécifié.Ce <see cref="T:System.Runtime.InteropServices.GCHandle" /> doit être libéré avec <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> quand il n'est plus nécessaire.</returns>
      <param name="value">Objet qui utilise <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <param name="type">Une des valeurs de <see cref="T:System.Runtime.InteropServices.GCHandleType" />, précisant le type de <see cref="T:System.Runtime.InteropServices.GCHandle" /> à créer. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> spécifié est égal à l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> actuel.</summary>
      <returns>true si l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> spécifié est égal à l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> actuel ; sinon false.</returns>
      <param name="o">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à comparer avec l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> actuel.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>Libère un <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>Retourne un nouvel objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> créé à partir d'un handle d'objet managé.</summary>
      <returns>Nouvel objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> qui correspond au paramètre de valeur.  </returns>
      <param name="value">Handle <see cref="T:System.IntPtr" /> d'objet managé à partir duquel créer un objet <see cref="T:System.Runtime.InteropServices.GCHandle" />.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>Retourne un identificateur pour l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> actuel.</summary>
      <returns>Identificateur de l'objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> actuel.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>Obtient une valeur indiquant si le handle est alloué.</summary>
      <returns>true si le handle est alloué ; sinon false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Retourne une valeur indiquant si deux objets <see cref="T:System.Runtime.InteropServices.GCHandle" /> sont égaux.</summary>
      <returns>true si les paramètres <paramref name="a" /> et <paramref name="b" /> sont égaux ; sinon, false.</returns>
      <param name="a">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à comparer au paramètre <paramref name="b" />. </param>
      <param name="b">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à comparer au paramètre <paramref name="a" />.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> est stocké en utilisant une représentation sous forme d'entier interne.</summary>
      <returns>Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> stocké utilisant une représentation sous forme d'entier interne.</returns>
      <param name="value">
        <see cref="T:System.IntPtr" /> qui indique le handle pour lequel la conversion est requise. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>Un <see cref="T:System.Runtime.InteropServices.GCHandle" /> est stocké en utilisant une représentation sous forme d'entier interne.</summary>
      <returns>Valeur entière.</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> pour lequel l'entier est requis. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Retourne une valeur indiquant si deux objets <see cref="T:System.Runtime.InteropServices.GCHandle" /> ne sont pas égaux.</summary>
      <returns>true si les paramètres <paramref name="a" /> et <paramref name="b" /> ne sont pas égaux ; sinon, false.</returns>
      <param name="a">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à comparer au paramètre <paramref name="b" />. </param>
      <param name="b">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à comparer au paramètre <paramref name="a" />.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>Obtient ou définit l'objet représenté par le handle.</summary>
      <returns>Objet représenté par le handle.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>Retourne la représentation sous forme d'entier interne d'un objet <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Objet <see cref="T:System.IntPtr" /> qui représente un objet <see cref="T:System.Runtime.InteropServices.GCHandle" />. </returns>
      <param name="value">Objet <see cref="T:System.Runtime.InteropServices.GCHandle" /> à partir duquel récupérer une représentation sous forme d'entier interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>Représente les types de handles que la classe <see cref="T:System.Runtime.InteropServices.GCHandle" /> peut allouer.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>Ce type de handle représente un handle opaque, ce qui signifie que vous ne pouvez pas résoudre l'adresse de l'objet épinglé à l'aide du handle.Vous pouvez utiliser ce type pour suivre un objet et empêcher sa collecte par le garbage collector.Ce membre d'énumération est utile lorsqu'un client non managé contient la seule référence, non identifiable par le garbage collector, à un objet managé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>Ce type de handle est similaire à <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />, mais permet d'accepter l'adresse de l'objet épinglé à prendre.Cela évite que le garbage collector transfère l'objet, ce qui diminue l'efficacité du garbage collector.Utilisez la méthode <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> pour libérer le handle alloué le plus rapidement possible.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>Ce type de handle est utilisé pour suivre un objet, mais autorise sa collecte.Lorsqu'un objet est collecté, le contenu du <see cref="T:System.Runtime.InteropServices.GCHandle" /> est mis à zéro.Les références Weak sont remises à zéro avant l'exécution du finaliseur, de sorte que même si le finaliseur réactive l'objet, la référence Weak est toujours remise à zéro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>Ce type de handle est similaire à <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />, mais le handle n'est pas remis à zéro si l'objet est réactivé pendant la finalisation.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>Fournit un <see cref="T:System.Guid" /> explicite lorsqu'un GUID automatique n'est pas recommandé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> avec le GUID spécifié.</summary>
      <param name="guid">
        <see cref="T:System.Guid" /> à assigner. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>Obtient le <see cref="T:System.Guid" /> de la classe.</summary>
      <returns>
        <see cref="T:System.Guid" /> de la classe.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>Effectue le suivi des handles en attente et force une opération de garbage collection lorsque le seuil spécifié est atteint.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.HandleCollector" /> à l'aide d'un nom et d'un seuil auquel commencer la collection de handles. </summary>
      <param name="name">Nom du collecteur.Ce paramètre vous permet de nommer des collecteurs qui assurent le suivi des types de handles séparément.</param>
      <param name="initialThreshold">Valeur qui spécifie le point auquel les collections doivent commencer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="initialThreshold" /> est inférieur à 0.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.HandleCollector" /> à l'aide d'un nom, d'un seuil auquel commencer la collection de handles et d'un seuil auquel la collection de handles doit avoir lieu. </summary>
      <param name="name">Nom du collecteur.  Ce paramètre vous permet de nommer des collecteurs qui assurent le suivi des types de handles séparément.</param>
      <param name="initialThreshold">Valeur qui spécifie le point auquel les collections doivent commencer.</param>
      <param name="maximumThreshold">Valeur qui spécifie le point auquel les collections doivent avoir lieu.Ce paramètre doit avoir pour valeur le nombre maximal de handles disponibles.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="initialThreshold" /> est inférieur à 0.ouLe paramètre <paramref name="maximumThreshold" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="maximumThreshold" /> est inférieur au paramètre <paramref name="initialThreshold" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>Incrémente le compte de handle actuel.</summary>
      <exception cref="T:System.InvalidOperationException">La valeur de la propriété <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> est inférieure à 0.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>Obtient le nombre de handles collectés.</summary>
      <returns>Nombre de handles collectés.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>Obtient une valeur qui spécifie le point auquel les collections doivent commencer.</summary>
      <returns>Valeur qui spécifie le point auquel les collections doivent commencer.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>Obtient une valeur qui spécifie le point auquel les collections doivent avoir lieu.</summary>
      <returns>Valeur qui spécifie le point auquel les collections doivent avoir lieu.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>Obtient le nom d'un objet <see cref="T:System.Runtime.InteropServices.HandleCollector" />.</summary>
      <returns>Cette propriété <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> vous permet de nommer des collecteurs qui assurent le suivi des types de handles séparément.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>Décrémente le compte de handle actuel.</summary>
      <exception cref="T:System.InvalidOperationException">La valeur de la propriété <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> est inférieure à 0.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>Permet aux clients d'accéder à l'objet réel, plutôt qu'à l'objet adaptateur fourni par un marshaleur personnalisé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>Permet d'accéder à l'objet sous-jacent encapsulé par un marshaleur personnalisé.</summary>
      <returns>Objet contenu dans l'objet adaptateur.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>Permet aux développeurs de fournir une implémentation managée, personnalisée, de la méthode IUnknown::QueryInterface(REFIID riid, void **ppvObject).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>Retourne une interface en fonction de l'ID d'interface spécifié.</summary>
      <returns>Une des valeurs d'énumération qui indique si une implémentation personnalisée de IUnknown::QueryInterface a été utilisée.</returns>
      <param name="iid">GUID de l'interface demandée.</param>
      <param name="ppv">Référence à l'interface demandée, lorsque cette méthode est retournée.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>Indique que les données doivent être marshalées de l'appelant vers l'appelé, mais pas à nouveau vers l'appelant.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.InAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>Indique si une interface managée est double, de dispatch uniquement ou IUnknown uniquement lorsqu'elle est exposée à COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> avec le membre de l'énumération <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> spécifié.</summary>
      <param name="interfaceType">Décrit comment l'interface doit être exposée aux clients COM. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> avec le membre de l'énumération <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> spécifié.</summary>
      <param name="interfaceType">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> qui décrit comment l'interface doit être exposée aux clients COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>Obtient la valeur <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> qui décrit comment l'interface doit être exposée à COM.</summary>
      <returns>Valeur <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> qui décrit comment l'interface doit être exposée à COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>Exception levée lorsqu'un objet COM non valide est utilisé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>Initialise une instance de InvalidComObjectException avec les propriétés par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>Initialise une instance de InvalidComObjectException avec un message.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>Exception levée par le marshaleur lorsqu'il rencontre un argument dont le type de variante ne peut pas être marshalé en code managé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>Initialise une nouvelle instance de la classe InvalidOleVariantTypeException avec les valeurs par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe InvalidOleVariantTypeException avec le message spécifié.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>Fournit une collection de méthodes pour l'allocation de mémoire non managée, la copie de blocs de mémoire non managée et la conversion de types managés en types non managés, ainsi que diverses autres méthodes utilisées lors de l'interaction avec du code non managé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>Incrémente le décompte de références sur l'interface spécifiée.</summary>
      <returns>Nouvelle valeur du décompte de références sur le paramètre <paramref name="pUnk" />.</returns>
      <param name="pUnk">Décompte de références d'interface à incrémenter.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>Alloue un bloc de mémoire de la taille spécifiée à partir de l'allocateur de mémoire de tâche COM.</summary>
      <returns>Entier représentant l'adresse du bloc de mémoire alloué.Cette mémoire doit être libérée avec <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="cb">Taille du bloc de mémoire à allouer.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire pour satisfaire la requête.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>Alloue de la mémoire à partir de la mémoire non managée du processus à l'aide du nombre d'octets spécifié.</summary>
      <returns>Pointeur vers la mémoire nouvellement allouée.Cette mémoire doit être libérée en utilisant la méthode <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Nombre d'octets requis en mémoire.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire pour satisfaire la requête.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>Alloue de la mémoire à partir de la mémoire non managée du processus à l'aide du pointeur vers le nombre d'octets spécifié.</summary>
      <returns>Pointeur vers la mémoire nouvellement allouée.Cette mémoire doit être libérée en utilisant la méthode <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Nombre d'octets requis en mémoire.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire pour satisfaire la requête.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>Indique si des wrappers RCW (Runtime Callable Wrapper) d'un contexte quelconque sont disponibles pour le nettoyage.</summary>
      <returns>true si des wrappers RCW sont disponibles pour le nettoyage ; sinon, false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données d'un tableau d'entiers 8 bits non signés managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données d'un tableau de caractères managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> a la valeur null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données d'un tableau de nombres à virgule flottante double précision managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données à partir d'un tableau d'entiers 16 bits signés managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données à partir d'un tableau d'entiers 32 bits signés managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données à partir d'un tableau d'entiers 64 bits signés managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>Copie des données d'un pointeur mémoire non managé dans un tableau d'entiers 8 bits non signés managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>Copie des données d'un pointeur mémoire non managé dans un tableau de caractères managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>Copie des données d'un pointeur mémoire non managé dans un tableau de nombres à virgule flottante double précision managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>Copie des données à partir d'un pointeur mémoire non managé vers un tableau d'entiers 16 bits signés managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>Copie des données à partir d'un pointeur mémoire non managé vers un tableau d'entiers 32 bits signés managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>Copie des données à partir d'un pointeur mémoire non managé vers un tableau d'entiers 64 bits signés managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie.</param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>Copie des données d'un pointeur mémoire non managé dans un tableau <see cref="T:System.IntPtr" /> managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie. </param>
      <param name="destination">Tableau dans lequel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>Copie des données d'un pointeur mémoire non managé dans un tableau de nombres à virgule flottante simple précision managé.</summary>
      <param name="source">Pointeur mémoire à partir duquel effectuer la copie. </param>
      <param name="destination">Tableau dans lequel effectuer la copie. </param>
      <param name="startIndex">Index de base zéro dans le tableau de destination où la copie doit commencer. </param>
      <param name="length">Nombre d'éléments de tableau à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données d'un tableau de <see cref="T:System.IntPtr" /> managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie.</param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer.</param>
      <param name="destination">Pointeur mémoire de destination de la copie.</param>
      <param name="length">Nombre d'éléments de tableau à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> ou <paramref name="length" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copie des données d'un tableau de nombres à virgule flottante simple précision managé et unidimensionnel vers un pointeur mémoire non managé.</summary>
      <param name="source">Tableau unidimensionnel à partir duquel effectuer la copie. </param>
      <param name="startIndex">Index de base zéro dans le tableau source où la copie doit commencer. </param>
      <param name="destination">Pointeur mémoire de destination de la copie. </param>
      <param name="length">Nombre d'éléments de tableau à copier. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> et <paramref name="length" /> ne sont pas valides. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> ou <paramref name="length" /> est null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>Agrège un objet managé avec l'objet COM spécifié.</summary>
      <returns>Pointeur IUnknown interne de l'objet managé.</returns>
      <param name="pOuter">Pointeur IUnknown externe.</param>
      <param name="o">Objet à agréger.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> est un objet Windows Runtime.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Agrège un objet managé du type spécifié avec l'objet COM spécifié. </summary>
      <returns>Pointeur IUnknown interne de l'objet managé. </returns>
      <param name="pOuter">Pointeur IUnknown interne. </param>
      <param name="o">Objet managé à agréger. </param>
      <typeparam name="T">Type de l'objet managé à agréger. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> est un objet Windows Runtime. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>Encapsule l'objet COM spécifié dans un objet du type spécifié.</summary>
      <returns>Objet récemment encapsulé constituant une instance du type requis.</returns>
      <param name="o">Objet à encapsuler. </param>
      <param name="t">Type de wrapper à créer. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> doit être dérivé de __ComObject. ou<paramref name="t" /> est un type Windows Runtime.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="t" /> est null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> ne peut pas être converti dans le type de destination puisqu'il ne prend pas en charge toutes les interfaces requises. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Encapsule l'objet COM spécifié dans un objet du type spécifié.</summary>
      <returns>Objet récemment encapsulé. </returns>
      <param name="o">Objet à encapsuler. </param>
      <typeparam name="T">Type d'objet à encapsuler. </typeparam>
      <typeparam name="TWrapper">Type d'objet à retourner. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> doit être dérivé de __ComObject. ou<paramref name="T" /> est un type Windows Runtime.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> ne peut pas être converti dans <paramref name="TWrapper" /> puisqu'il ne prend pas en charge toutes les interfaces requises. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Libère toutes les sous-structures d'un type spécifié vers lesquelles pointe le bloc de mémoire non managé spécifié. </summary>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée. </param>
      <typeparam name="T">Type de la structure mise en forme.Ceci fournit les informations relatives à la disposition nécessaires pour supprimer la mémoire tampon dans le paramètre <paramref name="ptr" />.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> possède une disposition automatique.Optez plutôt pour séquentiel ou explicite.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>Libère toutes les sous-structures vers lesquelles pointe le bloc de mémoire non managée spécifié.</summary>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée. </param>
      <param name="structuretype">Type d'une classe mise en forme.Ceci fournit les informations relatives à la disposition nécessaires pour supprimer la mémoire tampon dans le paramètre <paramref name="ptr" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> possède une disposition automatique.Optez plutôt pour séquentiel ou explicite.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>Libère toutes les références à un wrapper RCW (Wrapper pouvant être appelé par le runtime) en affectant la valeur 0 à son décompte de références.</summary>
      <returns>Nouvelle valeur du décompte de références du wrapper RCW associé au paramètre <paramref name="o" />qui est 0 (zéro) si la libération réussit.</returns>
      <param name="o">Wrapper RCW à libérer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> n'est pas un objet COM valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> a la valeur null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>Libère un BSTR à l'aide de la fonction COM SysFreeString.</summary>
      <param name="ptr">Adresse du BSTR à libérer. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>Libère un bloc de mémoire alloué par l'allocateur de mémoire de tâche COM non managé.</summary>
      <param name="ptr">Adresse de la mémoire à libérer. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>Libère la mémoire précédemment allouée de la mémoire non managée du processus.</summary>
      <param name="hglobal">Handle retourné par l'appel correspondant d'origine à <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>Retourne un pointeur vers une interface IUnknown qui représente l'interface spécifiée de l'objet spécifié.L'accès à l'interface de requête personnalisée est activé par défaut.</summary>
      <returns>Pointeur d'interface qui représente l'interface spécifiée de l'objet.</returns>
      <param name="o">Objet qui fournit l'interface. </param>
      <param name="T">Type d'interface demandé. </param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="T" /> n'est pas une interface.ou Le type n'est pas visible par COM. ouLe paramètre <paramref name="T" /> est un type générique.</exception>
      <exception cref="T:System.InvalidCastException">Le paramètre <paramref name="o" /> ne prend pas en charge l'interface demandée. </exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="o" /> est null.ou Le paramètre <paramref name="T" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>Retourne un pointeur vers une interface IUnknown qui représente l'interface spécifiée de l'objet spécifié.L'accès à l'interface de requête personnalisée est contrôlé par le mode de personnalisation spécifié.</summary>
      <returns>Pointeur d'interface qui représente l'interface de l'objet.</returns>
      <param name="o">Objet qui fournit l'interface.</param>
      <param name="T">Type d'interface demandé.</param>
      <param name="mode">Une des valeurs d'énumération qui indique s'il faut appliquer une personnalisation IUnknown::QueryInterface fournie par un <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="T" /> n'est pas une interface.ou Le type n'est pas visible par COM.ouLe paramètre <paramref name="T" /> est un type générique.</exception>
      <exception cref="T:System.InvalidCastException">L'objet <paramref name="o" /> ne prend pas en charge l'interface demandée.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="o" /> est null.ou Le paramètre <paramref name="T" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Retourne un pointeur vers une interface IUnknown qui représente l'interface spécifiée d'un objet du type spécifié.L'accès à l'interface de requête personnalisée est activé par défaut.</summary>
      <returns>Pointeur d'interface qui représente l'interface <paramref name="TInterface" />.</returns>
      <param name="o">Objet qui fournit l'interface. </param>
      <typeparam name="T">Type de <paramref name="o" />. </typeparam>
      <typeparam name="TInterface">Type d'interface à retourner. </typeparam>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="TInterface" /> n'est pas une interface.ou Le type n'est pas visible par COM. ouLe paramètre <paramref name="T" /> est un type générique ouvert.</exception>
      <exception cref="T:System.InvalidCastException">Le paramètre <paramref name="o" /> ne prend pas en charge l'interface <paramref name="TInterface" />. </exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="o" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Convertit un pointeur fonction non managé en un délégué d'un type spécifié. </summary>
      <returns>Instance du type délégué spécifié.</returns>
      <param name="ptr">Pointeur vers la fonction non managée à convertir. </param>
      <typeparam name="TDelegate">Type de délégué à retourner. </typeparam>
      <exception cref="T:System.ArgumentException">Le paramètre générique <paramref name="TDelegate" /> n'est pas un délégué ou il s'agit d'un type générique ouvert.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="ptr" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>Convertit un pointeur fonction non managé en un délégué.</summary>
      <returns>Instance de délégué qui peut être castée en type délégué approprié.</returns>
      <param name="ptr">Pointeur vers la fonction non managée à convertir.</param>
      <param name="t">Type du délégué à retourner.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="t" /> n'est pas un délégué ou est générique.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="ptr" /> est null.ouLe paramètre <paramref name="t" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>Récupère un code qui identifie le type de l'exception qui s'est produite.</summary>
      <returns>Type de l'exception.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>Convertit le code d'erreur HRESULT spécifié en objet <see cref="T:System.Exception" /> correspondant.</summary>
      <returns>Objet qui représente le HRESULT converti.</returns>
      <param name="errorCode">HRESULT à convertir.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Convertit le code d'erreur HRESULT spécifié en objet <see cref="T:System.Exception" /> correspondant, avec des informations supplémentaires sur l'erreur passées dans une interface IErrorInfo pour l'objet exception.</summary>
      <returns>Objet qui représente le HRESULT converti et les informations obtenues à partir de <paramref name="errorInfo" />.</returns>
      <param name="errorCode">HRESULT à convertir.</param>
      <param name="errorInfo">Pointeur vers l'interface IErrorInfo qui fournit plus d'informations sur l'erreur.Spécifiez IntPtr(0) pour utiliser l'interface IErrorInfo actuelle ou IntPtr(-1) pour ignorer l'interface IErrorInfo actuelle et construire l'exception uniquement à partir du code d'erreur.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>Convertit un délégué en pointeur fonction pouvant être appelé à partir du code non managé.</summary>
      <returns>Valeur qui peut être passée à du code non managé, qui peut à son tour l'utiliser pour appeler le délégué managé sous-jacent. </returns>
      <param name="d">Délégué à passer au code non managé.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="d" /> est un type générique.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="d" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Convertit un délégué d'un type spécifié en pointeur fonction pouvant être appelé à partir du code non managé. </summary>
      <returns>Valeur qui peut être passée à du code non managé, qui peut à son tour l'utiliser pour appeler le délégué managé sous-jacent. </returns>
      <param name="d">Délégué à passer au code non managé. </param>
      <typeparam name="TDelegate">Type de délégué à convertir. </typeparam>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="d" /> est null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>Convertit l'exception spécifiée en HRESULT.</summary>
      <returns>HRESULT mappé en l'exception fournie.</returns>
      <param name="e">Exception à convertir en HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>Retourne le HRESULT correspondant à la dernière erreur provoquée par du code Win32 exécuté en utilisant <see cref="T:System.Runtime.InteropServices.Marshal" />.</summary>
      <returns>HRESULT correspondant au dernier code d'erreur Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>Retourne une interface IUnknown à partir d'un objet managé.</summary>
      <returns>Pointeur IUnknown pour le paramètre <paramref name="o" />.</returns>
      <param name="o">Objet dont l'interface IUnknown est demandée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>Retourne le code d'erreur retourné par la dernière fonction non managée appelée en utilisant l'appel de code non managé dont l'indicateur <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> est activé.</summary>
      <returns>Dernier code d'erreur défini par un appel à la fonction Win32 SetLastError.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>Convertit un objet en COM VARIANT.</summary>
      <param name="obj">Objet pour lequel obtenir un VARIANT COM.</param>
      <param name="pDstNativeVariant">Pointeur devant recevoir le VARIANT et qui correspond au paramètre <paramref name="obj" />.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="obj" /> est un type générique.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Convertit un objet de type spécifié en COM VARIANT. </summary>
      <param name="obj">Objet pour lequel obtenir un VARIANT COM. </param>
      <param name="pDstNativeVariant">Pointeur devant recevoir le VARIANT et qui correspond au paramètre <paramref name="obj" />. </param>
      <typeparam name="T">Type de l'objet à convertir. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>Retourne une instance d'un type qui représente un objet COM par un pointeur vers son interface IUnknown.</summary>
      <returns>Objet qui représente l'objet COM non managé spécifié.</returns>
      <param name="pUnk">Pointeur vers l'interface IUnknown. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>Convertit COM VARIANT en un objet.</summary>
      <returns>Objet qui correspond au paramètre <paramref name="pSrcNativeVariant" />.</returns>
      <param name="pSrcNativeVariant">Pointeur vers un VARIANT COM.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> n'est pas un type VARIANT valide.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> a un type non pris en charge.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Convertit COM VARIANT en un objet de type spécifié. </summary>
      <returns>Objet du type spécifié qui correspond au paramètre <paramref name="pSrcNativeVariant" />. </returns>
      <param name="pSrcNativeVariant">Pointeur vers un VARIANT COM. </param>
      <typeparam name="T">Type dans lequel convertir COM VARIANT. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> n'est pas un type VARIANT valide. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> a un type non pris en charge. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>Convertit un tableau de VARIANT COM en tableau d'objets. </summary>
      <returns>Tableau d'objets qui correspond à <paramref name="aSrcNativeVariant" />.</returns>
      <param name="aSrcNativeVariant">Pointeur vers le premier élément d'un tableau de VARIANT COM.</param>
      <param name="cVars">Nombre de VARIANT COM dans <paramref name="aSrcNativeVariant" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> est un nombre négatif.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Convertit un tableau de COM VARIANT en un tableau d'un type spécifié. </summary>
      <returns>Tableau d'objets <paramref name="T" /> qui correspond à <paramref name="aSrcNativeVariant" />. </returns>
      <param name="aSrcNativeVariant">Pointeur vers le premier élément d'un tableau de VARIANT COM. </param>
      <param name="cVars">Nombre de VARIANT COM dans <paramref name="aSrcNativeVariant" />. </param>
      <typeparam name="T">Type de tableau à retourner. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> est un nombre négatif. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>Obtient le premier emplacement dans la table de fonctions virtuelles (v-table ou VTBL) contenant des méthodes définies par l'utilisateur.</summary>
      <returns>Premier emplacement VTBL qui contient des méthodes définies par l'utilisateur.Le premier emplacement est 3 si l'interface est basée sur IUnknown et 7 si l'interface est basée sur IDispatch.</returns>
      <param name="t">Type qui représente une interface.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> n'est pas visible par COM.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>Retourne le type associé à l'identificateur de classe (CLSID) spécifié. </summary>
      <returns>System.__ComObject que le CLSID soit valide ou non. </returns>
      <param name="clsid">CLSID du type à retourner. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>Récupère le nom du type représenté par un objet ITypeInfo.</summary>
      <returns>Nom du type vers lequel pointe le paramètre <paramref name="typeInfo" />.</returns>
      <param name="typeInfo">Objet qui représente un pointeur ITypeInfo.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="typeInfo" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>Crée un objet de wrapper RCW (Wrapper pouvant être appelé par le runtime) unique pour une interface IUnknown donnée.</summary>
      <returns>Wrapper RCW (Runtime Callable Wrapper) unique pour l'interface IUnknown spécifiée.</returns>
      <param name="unknown">Pointeur managé vers une interface IUnknown.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>Indique si un objet spécifié représente un objet COM.</summary>
      <returns>true si le paramètre <paramref name="o" /> est un type COM ; sinon, false.</returns>
      <param name="o">Objet à vérifier.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Retourne l'offset de champ de la forme non managée d'une classe managée spécifiée.</summary>
      <returns>Offset, en octets, du paramètre <paramref name="fieldName" /> au sein de la classe spécifiée déclarée par l'appel de code non managé. </returns>
      <param name="fieldName">Nom du champ dans le type <paramref name="T" />. </param>
      <typeparam name="T">Type valeur managé ou type référence mis en forme.Vous devez appliquer l'attribut <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> à la classe.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>Retourne l'offset de champ de la forme non managée de la classe managée.</summary>
      <returns>Offset, en octets, du paramètre <paramref name="fieldName" /> au sein de la classe spécifiée déclarée par l'appel de code non managé.</returns>
      <param name="t">Type valeur ou type référence mis en forme qui spécifie la classe managée.Vous devez appliquer l'attribut <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> à la classe.</param>
      <param name="fieldName">Champ dans le paramètre <paramref name="t" />.</param>
      <exception cref="T:System.ArgumentException">La classe ne peut pas être exportée sous forme de structure ou le champ n'est pas public.À compter de .NET Framework version 2.0, le champ peut être privé.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="t" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>Copie tous les caractères jusqu'au premier caractère null d'une chaîne ANSI non managée vers un <see cref="T:System.String" /> managé, puis convertit chaque caractère ANSI en Unicode.</summary>
      <returns>Chaîne managée qui contient une copie de la chaîne ANSI non managée.Si <paramref name="ptr" /> est null, la méthode retourne une chaîne vide.</returns>
      <param name="ptr">Adresse du premier caractère de la chaîne non managée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>Alloue un objet <see cref="T:System.String" /> managé, copie un nombre spécifié de caractères d'une chaîne ANSI non managée dans celui-ci et élargit chaque caractère ANSI au format Unicode.</summary>
      <returns>Chaîne managée qui contient une copie de la chaîne ANSI native si la valeur du paramètre <paramref name="ptr" /> n'est pas null ; sinon, cette méthode retourne null.</returns>
      <param name="ptr">Adresse du premier caractère de la chaîne non managée.</param>
      <param name="len">Nombre d'octets de la chaîne d'entrée à copier.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> est inférieur à zéro.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>Alloue un <see cref="T:System.String" /> managé afin d'y copier une chaîne BSTR stockée dans la mémoire non managée.</summary>
      <returns>Chaîne managée qui contient une copie de la chaîne non managée si la valeur du paramètre <paramref name="ptr" /> n'est pas null ; sinon, cette méthode retourne null.</returns>
      <param name="ptr">Adresse du premier caractère de la chaîne non managée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>Alloue un objet <see cref="T:System.String" /> managé et copie tous les caractères - jusqu'au premier caractère null - d'une chaîne Unicode non managée dans cet objet.</summary>
      <returns>Chaîne managée qui contient une copie de la chaîne non managée si la valeur du paramètre <paramref name="ptr" /> n'est pas null ; sinon, cette méthode retourne null.</returns>
      <param name="ptr">Adresse du premier caractère de la chaîne non managée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>Alloue un objet <see cref="T:System.String" /> managé et copie un nombre spécifié de caractères d'une chaîne Unicode non managée dans cet objet.</summary>
      <returns>Chaîne managée qui contient une copie de la chaîne non managée si la valeur du paramètre <paramref name="ptr" /> n'est pas null ; sinon, cette méthode retourne null.</returns>
      <param name="ptr">Adresse du premier caractère de la chaîne non managée.</param>
      <param name="len">Nombre de caractères Unicode à copier.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Marshale, dans un nouvel objet managé alloué du type spécifié par un paramètre de type générique, les données d'un bloc de mémoire non managée. </summary>
      <returns>Objet managé contenant les données vers lesquelles pointe le paramètre <paramref name="ptr" />. </returns>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée. </param>
      <typeparam name="T">Type de l'objet dans lequel les données doivent être copiées.Il doit s'agir d'une classe ou d'une structure mise en forme.</typeparam>
      <exception cref="T:System.ArgumentException">La disposition de <paramref name="T" /> n'est ni séquentielle ni explicite.</exception>
      <exception cref="T:System.MissingMethodException">La classe spécifiée par <paramref name="T" /> n'a pas de constructeur par défaut accessible. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>Marshale les données d'un bloc de mémoire non managée dans un objet managé.</summary>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée.</param>
      <param name="structure">Objet dans lequel les données doivent être copiées.Il doit s'agir d'une instance d'une classe mise en forme.</param>
      <exception cref="T:System.ArgumentException">La disposition de structure n'est ni séquentielle ni explicite.ou La structure est un type valeur boxed.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>Marshale, dans un nouvel objet managé alloué du type spécifié, les données d'un bloc de mémoire non managée.</summary>
      <returns>Objet managé contenant les données vers lesquelles pointe le paramètre <paramref name="ptr" />.</returns>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée.</param>
      <param name="structureType">Type d'objet à créer.Cet objet doit représenter une classe mise en forme ou une structure.</param>
      <exception cref="T:System.ArgumentException">La disposition du paramètre <paramref name="structureType" /> n'est ni séquentielle ni explicite.ouLe paramètre <paramref name="structureType" /> est un type générique.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> a la valeur null.</exception>
      <exception cref="T:System.MissingMethodException">La classe spécifiée par <paramref name="structureType" /> n'a pas de constructeur par défaut accessible. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Marshale les données d'un bloc de mémoire non managée dans un objet managé d'un type spécifié. </summary>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée. </param>
      <param name="structure">Objet dans lequel les données doivent être copiées. </param>
      <typeparam name="T">Type de <paramref name="structure" />.Il doit s'agir d'une classe formatée.</typeparam>
      <exception cref="T:System.ArgumentException">La disposition de structure n'est ni séquentielle ni explicite. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>Demande un pointeur vers une interface spécifiée à partir d'un objet COM.</summary>
      <returns>HRESULT indiquant la réussite ou l'échec de l'appel.</returns>
      <param name="pUnk">Interface à interroger.</param>
      <param name="iid">Identificateur d'interface (IID) de l'interface demandée.</param>
      <param name="ppv">Lorsque cette méthode est retournée, contient une référence à l'interface retournée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>Lit un octet unique à partir de la mémoire non managée.</summary>
      <returns>Octet lu dans la mémoire non managée.</returns>
      <param name="ptr">Adresse de début de lecture dans la mémoire non managée.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null. ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>Lit un seul octet à un offset (ou index) donné dans la mémoire managée.</summary>
      <returns>Octet lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée où commencer la lecture.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>Lit un seul octet à un offset (ou index) donné dans la mémoire managée. </summary>
      <returns>Octet lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet source.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>Lit un entier 16 bits signé à partir de la mémoire non managée.</summary>
      <returns>Entier signé 16 bits lu dans la mémoire non managée.</returns>
      <param name="ptr">Adresse de début de lecture dans la mémoire non managée.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>Lit un entier signé 16 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 16 bits lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée où commencer la lecture.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>Lit un entier signé 16 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 16 bits lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet source.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>Lit un entier 32 bits signé à partir de la mémoire non managée.</summary>
      <returns>Entier signé 32 bits lu dans la mémoire non managée.</returns>
      <param name="ptr">Adresse de début de lecture dans la mémoire non managée.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>Lit un entier signé 32 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 32 bits lu dans la mémoire non managée.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée où commencer la lecture.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>Lit un entier signé 32 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 32 bits lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet source.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>Lit un entier 64 bits signé à partir de la mémoire non managée.</summary>
      <returns>Entier signé 64 bits lu dans la mémoire non managée.</returns>
      <param name="ptr">Adresse de début de lecture dans la mémoire non managée.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>Lit un entier signé 64 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 64 bits lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée où commencer la lecture.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>Lit un entier signé 64 bits à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier signé 64 bits lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet source.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>Lit un entier natif dimensionné par processeur dans la mémoire non managée.</summary>
      <returns>Entier lu dans la mémoire non managée.Un entier 32 bits est retourné sur les ordinateurs 32 bits et un entier 64 bits est retourné sur les ordinateurs 64 bits.</returns>
      <param name="ptr">Adresse de début de lecture dans la mémoire non managée.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null. ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>Lit un entier natif dimensionné par processeur à un offset donné dans la mémoire non managée.</summary>
      <returns>Entier lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée où commencer la lecture.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>Lit un entier natif dimensionné par processeur à partir de la mémoire non managée.</summary>
      <returns>Entier lu dans la mémoire non managée à l'offset donné.</returns>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet source.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant la lecture.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>Redimensionne un bloc de mémoire précédemment alloué avec <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</summary>
      <returns>Entier représentant l'adresse du bloc de mémoire réalloué.Cette mémoire doit être libérée avec <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="pv">Pointeur vers la mémoire allouée avec <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</param>
      <param name="cb">Nouvelle taille du bloc alloué.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire pour satisfaire la requête.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>Redimensionne un bloc de mémoire précédemment alloué avec <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</summary>
      <returns>Pointeur vers la mémoire réallouée.Cette mémoire doit être libérée en utilisant <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="pv">Pointeur vers la mémoire allouée avec <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</param>
      <param name="cb">Nouvelle taille du bloc alloué.Ce n'est pas un pointeur ; c'est le nombre d'octets que vous demandez, casté en type <see cref="T:System.IntPtr" />.Si vous passez un pointeur, il est traité comme une taille.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire pour satisfaire la requête.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>Décrémente le décompte de références sur l'interface spécifiée.</summary>
      <returns>Nouvelle valeur du décompte de références sur l'interface spécifiée par le paramètre <paramref name="pUnk" />.</returns>
      <param name="pUnk">Interface à libérer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>Décrémente le décompte de références du wrapper RCW (Wrapper pouvant être appelé par le runtime) spécifié associé à l'objet COM indiqué.</summary>
      <returns>Nouvelle valeur du décompte de références du wrapper RCW (Runtime Callable Wrapper) associé à <paramref name="o" />.Cette valeur est généralement zéro puisque le wrapper RCW (Runtime Callable Wrapper) ne conserve qu'une seule référence à l'objet COM inclus dans un wrapper indépendamment du nombre de clients managés qui l'appellent.</returns>
      <param name="o">Objet COM à libérer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> n'est pas un objet COM valide.</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> a la valeur null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Retourne la taille d'un type non managé en octets. </summary>
      <returns>Taille, en octets, du type spécifié par le paramètre de type générique <paramref name="T" />. </returns>
      <typeparam name="T">Type dont la taille doit être retournée. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>Retourne la taille non managée d'un objet en octets.</summary>
      <returns>Taille de l'objet spécifié dans le code non managé.</returns>
      <param name="structure">Objet dont la taille doit être retournée.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="structure" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>Retourne la taille d'un type non managé en octets.</summary>
      <returns>Taille du type spécifié dans le code non managé.</returns>
      <param name="t">Type dont la taille doit être retournée.</param>
      <exception cref="T:System.ArgumentException">Le paramètre <paramref name="t" /> est un type générique.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="t" /> est null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Retourne la taille non managée en octets d'un objet d'un type spécifié. </summary>
      <returns>Taille, en octets, de l'objet spécifié dans le code non managé. </returns>
      <param name="structure">Objet dont la taille doit être retournée. </param>
      <typeparam name="T">Type du paramètre <paramref name="structure" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="structure" /> est null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>Alloue un BSTR afin d'y copier le contenu d'un <see cref="T:System.String" /> managé.</summary>
      <returns>Pointeur non managé vers le BSTR, ou 0 si <paramref name="s" /> est null.</returns>
      <param name="s">Chaîne managée à copier.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire disponible.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La longueur de <paramref name="s" /> est hors limites.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>Copie le contenu d'un <see cref="T:System.String" /> managé dans un bloc de mémoire alloué à partir de l'allocateur de tâche COM non managé.</summary>
      <returns>Entier représentant un pointeur vers le bloc de mémoire alloué pour la chaîne, ou 0 si <paramref name="s" /> est null.</returns>
      <param name="s">Chaîne managée à copier.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire disponible.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="s" /> dépasse la longueur maximale autorisée par le système d'exploitation.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>Copie le contenu d'un <see cref="T:System.String" /> managé dans un bloc de mémoire alloué à partir de l'allocateur de tâche COM non managé.</summary>
      <returns>Entier représentant un pointeur vers le bloc de mémoire alloué pour la chaîne, ou 0 si s est null.</returns>
      <param name="s">Chaîne managée à copier.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="s" /> dépasse la longueur maximale autorisée par le système d'exploitation.</exception>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire disponible.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>Copie le contenu d'un objet <see cref="T:System.String" /> managé dans la mémoire non managée, avec conversion au format ANSI pendant la copie.</summary>
      <returns>Adresse, dans la mémoire non managée, où <paramref name="s" /> a été copié, ou 0 si <paramref name="s" /> est null.</returns>
      <param name="s">Chaîne managée à copier.</param>
      <exception cref="T:System.OutOfMemoryException">Il n'y a pas suffisamment de mémoire disponible.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="s" /> dépasse la longueur maximale autorisée par le système d'exploitation.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>Copie le contenu d'un objet <see cref="T:System.String" /> managé dans la mémoire non managée.</summary>
      <returns>Adresse, dans la mémoire non managée, où <paramref name="s" /> a été copié, ou 0 si <paramref name="s" /> est null.</returns>
      <param name="s">Chaîne managée à copier.</param>
      <exception cref="T:System.OutOfMemoryException">La méthode n'a pas pu allouer suffisamment de mémoire de tas natif.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="s" /> dépasse la longueur maximale autorisée par le système d'exploitation.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>Marshale les données d'un objet managé dans un bloc de mémoire non managée.</summary>
      <param name="structure">Objet managé contenant les données à marshaler.Cet objet doit être une structure ou une instance d'une classe mise en forme.</param>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée qui doit être alloué avant l'appel de cette méthode.</param>
      <param name="fDeleteOld">true pour appeler la méthode <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> sur le paramètre <paramref name="ptr" /> avant que cette méthode copie les données.Le bloc doit contenir des données valides.Notez que passer false lorsque le bloc de mémoire contient déjà des données peut entraîner une fuite de mémoire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> est un type référence qui n'est pas une classe mise en forme. ou<paramref name="structure" /> est un type générique. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Marshale les données d'un objet managé d'un type spécifié dans un bloc de mémoire non managée. </summary>
      <param name="structure">Objet managé contenant les données à marshaler.L'objet doit être une structure ou une instance d'une classe mise en forme.</param>
      <param name="ptr">Pointeur vers un bloc de mémoire non managée qui doit être alloué avant l'appel de cette méthode. </param>
      <param name="fDeleteOld">true pour appeler la méthode <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> sur le paramètre <paramref name="ptr" /> avant que cette méthode copie les données.Le bloc doit contenir des données valides.Notez que passer false lorsque le bloc de mémoire contient déjà des données peut entraîner une fuite de mémoire.</param>
      <typeparam name="T">Type de l'objet managé. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> est un type référence qui n'est pas une classe mise en forme. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>Représente la taille de caractère par défaut dans le système ; il s'agit de la valeur 2 pour les systèmes Unicode et de la valeur 1 pour les systèmes ANSI.Ce champ est en lecture seule.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>Représente la taille maximale, en octets, d'un jeu de caractères à deux octets (DBSC) pour le système d'exploitation actuel.Ce champ est en lecture seule.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>Lève une exception avec une valeur HRESULT d'échec spécifique.</summary>
      <param name="errorCode">HRESULT correspondant à l'exception souhaitée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Lève une exception avec un HRESULT d'échec spécifique, selon l'interface IErrorInfo spécifiée.</summary>
      <param name="errorCode">HRESULT correspondant à l'exception souhaitée.</param>
      <param name="errorInfo">Pointeur vers l'interface IErrorInfo qui fournit plus d'informations sur l'erreur.Spécifiez IntPtr(0) pour utiliser l'interface IErrorInfo actuelle ou IntPtr(-1) pour ignorer l'interface IErrorInfo actuelle et construire l'exception uniquement à partir du code d'erreur.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>Obtient l'adresse de l'élément à l'index spécifié dans le tableau spécifié.</summary>
      <returns>Adresse de <paramref name="index" /> dans <paramref name="arr" />.</returns>
      <param name="arr">Tableau qui contient l'élément souhaité.</param>
      <param name="index">Index dans le paramètre <paramref name="arr" /> de l'élément souhaité.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Obtient l'adresse de l'élément à l'index spécifié dans un tableau du type spécifié. </summary>
      <returns>Adresse de <paramref name="index" /> dans <paramref name="arr" />. </returns>
      <param name="arr">Tableau qui contient l'élément souhaité. </param>
      <param name="index">Index de l'élément souhaité dans le tableau <paramref name="arr" />. </param>
      <typeparam name="T">Type de tableau. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>Écrit une valeur d'octet unique dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>Écrit une valeur d'octet unique dans la mémoire non managée à l'offset spécifié.</summary>
      <param name="ptr">Adresse de base où écrire dans la mémoire non managée.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>Écrit une valeur d'octet unique dans la mémoire non managée à l'offset spécifié.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>Écrit un caractère en tant que valeur entière 16 bits dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>Écrit une valeur entière 16 bits dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>Écrit une valeur entière signée 16 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base où écrire dans le tas natif.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>Écrit une valeur entière signée 16 bits dans la mémoire non managée à l'offset spécifié.</summary>
      <param name="ptr">Adresse de base où écrire dans la mémoire non managée.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>Écrit une valeur entière signée 16 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>Écrit une valeur entière signée 16 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture. </param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>Écrit une valeur d'entier 32 bits signé dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null. ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>Écrit une valeur entière signée 32 bits dans la mémoire non managée à l'offset spécifié.</summary>
      <param name="ptr">Adresse de base où écrire dans la mémoire non managée.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>Écrit une valeur entière signée 32 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>Écrit une valeur entière signée 64 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base de la mémoire non managée à écrire.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>Écrit une valeur d'entier 64 bits signé dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>Écrit une valeur entière signée 64 bits dans la mémoire non managée à un offset spécifié.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>Écrit une valeur entière native dimensionnée par processeur dans la mémoire non managée à l'offset spécifié.</summary>
      <param name="ptr">Adresse de base où écrire dans la mémoire non managée.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>Écrit une valeur entière native dimensionnée par processeur dans la mémoire non managée.</summary>
      <param name="ptr">Adresse où écrire dans la mémoire non managée.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> n'est pas un format reconnu.ou<paramref name="ptr" /> a la valeur null.ou<paramref name="ptr" /> n'est pas valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>Écrit une valeur entière native dimensionnée par processeur dans la mémoire non managée.</summary>
      <param name="ptr">Adresse de base dans la mémoire non managée de l'objet cible.</param>
      <param name="ofs">Offset d'octet supplémentaire, qui est ajouté au paramètre <paramref name="ptr" /> avant l'écriture.</param>
      <param name="val">Valeur à écrire.</param>
      <exception cref="T:System.AccessViolationException">L'adresse de base (<paramref name="ptr" />) plus l'octet d'offset (<paramref name="ofs" />) produisent une adresse null ou non valide.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> est un objet <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Cette méthode n'accepte pas les paramètres <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>Libère un pointeur BSTR qui a été alloué à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" />.</summary>
      <param name="s">Adresse du pointeur BSTR à libérer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>Libère un pointeur de chaîne non managé qui a été alloué à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Adresse de la chaîne non managée à libérer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>Libère un pointeur de chaîne non managé qui a été alloué à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Adresse de la chaîne non managée à libérer.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>Libère un pointeur de chaîne non managé qui a été alloué à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Adresse de la chaîne non managée à libérer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>Libère un pointeur de chaîne non managé qui a été alloué à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Adresse de la chaîne non managée à libérer.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>Indique comment marshaler les données entre du code managé et non managé.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> avec la valeur <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> spécifiée.</summary>
      <param name="unmanagedType">Valeur sous laquelle les données doivent être marshalées. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> avec le membre de l'énumération <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> spécifié.</summary>
      <param name="unmanagedType">Valeur sous laquelle les données doivent être marshalées. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>Spécifie le type d'élément des champs <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> ou <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> managés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>Spécifie l'index de paramètre de l'attribut iid_is non managé utilisé par COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>Fournit des informations supplémentaires à un marshaleur personnalisé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>Spécifie le nom qualifié complet d'un marshaleur personnalisé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>Implémente <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> en tant que type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>Indique le type d'élément de <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>Indique le type d'élément défini par l'utilisateur de <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>Indique le nombre d'éléments dans le tableau de longueur fixe ou le nombre de caractères (non d'octets) dans une chaîne à importer.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>Indique le paramètre de base zéro qui contient le nombre d'éléments de tableau, semblable à size_is dans COM.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>Obtient la valeur <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> sous laquelle les données doivent être marshalées.</summary>
      <returns>Valeur <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> sous laquelle les données doivent être marshalées.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>Exception qui est levée par le marshaleur quand il rencontre un <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> qu'il ne prend pas en charge.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>Initialise une nouvelle instance de la classe MarshalDirectiveException avec des propriétés par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe MarshalDirectiveException avec un message d'erreur spécifié.</summary>
      <param name="message">Message d'erreur qui spécifie la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> avec un message d'erreur spécifié et une référence à l'exception interne qui est à l'origine de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>Indique qu'un paramètre est facultatif.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe OptionalAttribute avec les valeurs par défaut.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>Indique que la transformation de signature HRESULT ou retval qui a lieu durant les appels COM Interop doit être supprimée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>Exception levée lorsque le rang d'un SAFEARRAY entrant ne correspond pas au rang spécifié dans la signature managée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>Initialise une nouvelle instance de la classe SafeArrayTypeMismatchException avec les valeurs par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe SafeArrayRankMismatchException avec le message spécifié.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>Exception levée lorsque le type du SAFEARRAY entrant ne correspond pas au type spécifié dans la signature managée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>Initialise une nouvelle instance de la classe SafeArrayTypeMismatchException avec les valeurs par défaut.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe SafeArrayTypeMismatchException avec le message spécifié.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>Fournit un tampon de mémoire contrôlé qui peut être utilisé pour la lecture et l'écriture.Toute tentative d'accès à la mémoire en dehors du tampon contrôlé (sous-utilisation et dépassement) entraîne la levée d'exceptions.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> et spécifie si le handle de tampon doit être libéré de manière fiable. </summary>
      <param name="ownsHandle">true pour libérer de manière fiable le handle pendant la phase de finalisation ; false pour empêcher la libération fiable (déconseillé).</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>Obtient un pointeur à partir d'un objet <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> pour un bloc de mémoire.</summary>
      <param name="pointer">Pointeur d'octet, passé par référence, pour recevoir le pointeur à partir de l'objet <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.Vous devez affecter la valeur null à ce pointeur avant d'appeler cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>Obtient la taille du tampon, en octets.</summary>
      <returns>Nombre d'octets dans le tampon de mémoire.</returns>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>Définit la taille d'allocation de la région de mémoire en spécifiant le nombre de types valeur.Vous devez appeler cette méthode avant d'utiliser l'instance <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Nombre d'éléments du type valeur pour lequel il faut allouer de la mémoire.</param>
      <typeparam name="T">Type valeur pour lequel il faut allouer de la mémoire.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> est inférieur à zéro.ou<paramref name="numElements" /> multiplié par la taille de chaque élément est supérieur à l'espace d'adressage disponible.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>Spécifie la taille d'allocation du tampon de mémoire à l'aide du nombre spécifié d'éléments et de la taille des éléments.Vous devez appeler cette méthode avant d'utiliser l'instance <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Nombre d'éléments contenus dans la mémoire tampon.</param>
      <param name="sizeOfEachElement">Taille de chaque élément dans le tampon.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> est inférieur à zéro. ou<paramref name="sizeOfEachElement" /> est inférieur à zéro.ou<paramref name="numElements" /> multiplié par <paramref name="sizeOfEachElement" /> est supérieur à l'espace d'adressage disponible.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>Définit la taille d'allocation de la région de mémoire en octets.Vous devez appeler cette méthode avant d'utiliser l'instance <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numBytes">Nombre d'octets dans le tampon.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> est inférieur à zéro.ou<paramref name="numBytes" /> est supérieur à l'espace d'adressage disponible.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>Lit un type valeur dans la mémoire à l'offset spécifié.</summary>
      <returns>Type valeur lu dans la mémoire.</returns>
      <param name="byteOffset">Emplacement à partir duquel le type valeur doit être lu.Vous devrez peut-être tenir compte des problèmes d'alignement.</param>
      <typeparam name="T">Type valeur à lire.</typeparam>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Lit le nombre spécifié de types valeur dans la mémoire à partir de l'offset indiqué, puis les écrit dans un tableau à partir de l'index indiqué. </summary>
      <param name="byteOffset">Emplacement de démarrage de la lecture.</param>
      <param name="array">Tableau de sortie où écrire.</param>
      <param name="index">Emplacement de démarrage de l'écriture dans le tableau de sortie.</param>
      <param name="count">Nombre de types valeur à lire dans le tableau d'entrée et à écrire dans le tableau de sortie.</param>
      <typeparam name="T">Type valeur à lire.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.ou<paramref name="count" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">La longueur du tableau moins l'index est inférieure à <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>Libère un pointeur obtenu par la méthode <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" />.</summary>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>Écrit un type valeur dans la mémoire à l'emplacement donné.</summary>
      <param name="byteOffset">Emplacement où démarrer l'écriture.Vous devrez peut-être tenir compte des problèmes d'alignement.</param>
      <param name="value">Valeur à écrire.</param>
      <typeparam name="T">Type valeur à écrire.</typeparam>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Écrit le nombre spécifié de types valeur dans un emplacement de mémoire en lisant les octets qui commencent à partir de l'emplacement spécifié dans le tableau d'entrée.</summary>
      <param name="byteOffset">Emplacement où écrire dans la mémoire.</param>
      <param name="array">Tableau d'entrée.</param>
      <param name="index">Offset de démarrage de la lecture dans le tableau.</param>
      <param name="count">Nombre de types valeur à écrire.</param>
      <typeparam name="T">Type valeur à écrire.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ou <paramref name="count" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">La longueur du tableau d'entrée moins <paramref name="index" /> est inférieure à <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> n'a pas été appelée.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>Représente des erreurs de gestion structurée des exceptions (SEH). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.SEHException" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.SEHException" /> avec le message spécifié.</summary>
      <param name="message">Message qui indique la raison de l'exception. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.SEHException" /> avec un message d'erreur spécifié et une référence à l'exception interne qui est à l'origine de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>Indique si l'exception est récupérable et si l'exécution du code peut se poursuivre à partir de l'emplacement où l'exception a été levée.</summary>
      <returns>Toujours false, car les exceptions susceptibles d'être récupérées ne sont pas implémentées.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>Prend en charge l'équivalence de type.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> avec la portée et l'identificateur spécifiés. </summary>
      <param name="scope">Première chaîne d'équivalence de type.</param>
      <param name="identifier">Seconde chaîne d'équivalence de type.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>Obtient la valeur du paramètre <paramref name="identifier" /> passé au constructeur <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valeur du paramètre <paramref name="identifier" /> du constructeur.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>Obtient la valeur du paramètre <paramref name="scope" /> passé au constructeur <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valeur du paramètre <paramref name="scope" /> du constructeur.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>Encapsule des objets que le marshaleur doit marshaler comme VT_UNKNOWN.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> avec l'objet à encapsuler.</summary>
      <param name="obj">Objet à encapsuler. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>Obtient l'objet contenu dans ce wrapper.</summary>
      <returns>Objet encapsulé.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>Contrôle le comportement de marshaling d'une signature de délégué passée comme pointeur fonction non managé vers ou à partir de code non managé.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> avec la convention d'appel spécifiée. </summary>
      <param name="callingConvention">Convention d'appel spécifiée.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Active ou désactive le comportement de mappage ajusté lors de la conversion de caractères Unicode en caractères ANSI.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>Obtient la valeur de la convention d'appel.</summary>
      <returns>Valeur de la convention d'appel spécifiée par le constructeur <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" />.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>Indique comment marshaler les paramètres de chaîne vers la méthode et contrôle la composition des noms.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>Indique si l'appelé appelle la fonction API Win32 SetLastError avant de retourner la valeur à partir de la méthode avec attributs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>Active ou désactive la levée d'une exception sur un caractère Unicode non mappable converti en caractère ANSI "?".</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>Indique comment marshaler les paramètres ou les champs en code non managé. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>Chaîne de caractères ANSI sur un octet, préfixée par sa longueur.Vous pouvez utiliser ce membre sur le type de données <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>Type dynamique qui détermine le type d'un objet au moment de l'exécution et marshale l'objet comme ce type.Ce membre est valide pour les méthodes d'appel de plateforme uniquement.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>Valeur booléenne sur 4 octets (true != 0, false = 0).Il s'agit du type BOOL Win32.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>Chaîne de caractères Unicode sur deux octets, préfixée par sa longueur.Vous pouvez utiliser ce membre, qui est la chaîne par défaut dans COM, sur le type de données <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>Quand la propriété <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> a la valeur ByValArray, le champ <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> doit être défini pour indiquer le nombre d'éléments dans le tableau.Le champ <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> peut éventuellement contenir le <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> des éléments du tableau, quand les types de chaîne doivent être différenciés.Vous pouvez utiliser ce <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> uniquement sur un tableau dont les éléments apparaissent sous la forme de champs dans une structure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>Utilisé pour des tableaux de caractères de longueur fixe inline affichés dans une structure.Le type de caractère utilisé avec <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> est déterminé par l'argument <see cref="T:System.Runtime.InteropServices.CharSet" /> de l'attribut <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> appliqué à la structure conteneur.Utilisez toujours le champ <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> pour indiquer la taille du tableau.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>Type de devise.Utilisé sur un <see cref="T:System.Decimal" /> pour marshaler la valeur décimale en tant que type de devise COM, au lieu d'un Decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>Type natif associé à <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> ou à <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> et qui entraîne l'exportation du paramètre en tant que HRESULT dans la bibliothèque de types exportée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>Entier pouvant être utilisé comme pointeur fonction de type C.Vous pouvez utiliser ce membre sur un type de données <see cref="T:System.Delegate" /> ou sur un type qui hérite d'un <see cref="T:System.Delegate" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Chaîne Windows Runtime.Vous pouvez utiliser ce membre sur le type de données <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>Entier signé sur 1 octet.Vous pouvez utiliser ce membre pour transformer une valeur booléenne en valeur bool sur 1 octet de type C (true = 1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>Entier signé sur 2 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>Entier signé sur 4 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>Entier signé sur 8 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>Pointeur IDispatch COM (Object dans Microsoft Visual Basic 6.0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Pointeur d'interface Windows Runtime.Vous pouvez utiliser ce membre sur le type de données <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>Pointeur d'interface COM.Le <see cref="T:System.Guid" /> de l'interface est obtenu à partir des métadonnées de classe.Utilisez ce membre pour spécifier le type exact d'interface ou le type d'interface par défaut si vous l'appliquez à une classe.Ce membre produit le même comportement que <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> quand vous l'appliquez au type de données <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>Pointeur IUnknown COM.Vous pouvez utiliser ce membre sur le type de données <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>Pointeur vers le premier élément d'un tableau de style C.Lors du marshaling du code managé au code non managé, la longueur du tableau est déterminée par la longueur du tableau managé.Lors du marshaling du code non managé au code managé, la longueur du tableau est déterminée par les champs <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> et <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" />, suivis éventuellement par le type non managé des éléments du tableau quand les types de chaîne doivent être différenciés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>Chaîne de caractères ANSI sur un octet.Vous pouvez utiliser ce membre sur les types de données <see cref="T:System.String" /> et <see cref="T:System.Text.StringBuilder" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>Pointeur vers une structure de type C que vous utilisez pour marshaler les classes mises en forme managées.Ce membre est valide pour les méthodes d'appel de plateforme uniquement.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>Chaîne de caractères dépendante de la plateforme, à savoir ANSI sous Windows 98 et Unicode sous Windows NT et Windows XP.Cette valeur est uniquement prise en charge pour un appel de code non managé uniquement, elle ne l'est pas pour COM Interop, l'exportation d'une chaîne de type LPTStr n'étant pas prise en charge.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>Chaîne de caractères Unicode se terminant par null sur 2 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>Nombre à virgule flottante sur 4 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>Nombre à virgule flottante sur 8 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>Un SafeArray est un tableau autodescriptif qui inclut le type, le rang et les limites des données de tableau associées.Vous pouvez utiliser ce membre avec le champ <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> pour substituer le type d'élément par défaut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>VARIANT utilisé pour marshaler des classes et des types de valeur mis en forme managés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>Entier signé qui dépend de la plateforme sur : 4 octets sous Windows 32 bits et 8 octets sous Windows 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>Entier non signé qui dépend de la plateforme sur : 4 octets sous Windows 32 bits et 8 octets sous Windows 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>Chaîne char préfixée par sa longueur et dépendante de la plateforme : ANSI sous Windows 98, Unicode sous Windows NT.Ce membre similaire à BSTR est rarement utilisé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>Entier non signé sur 1 octet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>Entier non signé sur 2 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>Entier non signé sur 4 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>Entier non signé sur 8 octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>Type VARIANT_BOOL sur 2 octets défini par OLE (true = -1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Valeur qui permet à Visual Basic de changer une chaîne en code non managé et de répercuter les résultats dans du code managé.Cette valeur est uniquement prise en charge pour l'appel de code non managé.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>Indique comment marshaler les éléments de tableau lorsqu'un tableau est marshalé de code managé en code non managé sous la forme de <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>Indique un pointeur SAFEARRAY.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>Indique des octets préfixés par leur longueur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>Indique qu'un blob contient un objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>Indique une valeur Boolean.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>Indique une chaîne BSTR.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>Indique qu'une valeur est une référence.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>Indique un tableau de style C.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>Indique le format du Presse-papiers.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>Indique un ID de classe.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>Indique une valeur monétaire.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>Indique une valeur DATE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>Indique une valeur decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>Indique un pointeur IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>Indique qu'une valeur n'a pas été spécifiée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>Indique un SCODE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>Indique une valeur FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>Indique un HRESULT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>Indique une valeur char.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>Indique un entier short.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>Indique un entier long.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>Indique un entier 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>Indique une valeur entière.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>Indique une chaîne terminée par le caractère NULL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>Indique une chaîne large terminée par null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>Indique une valeur null, similaire à une valeur null dans SQL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>Indique un type de pointeur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>Indique une valeur float.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>Indique une valeur double.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>Indique un type défini par l'utilisateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>Indique un SAFEARRAY.Non valide dans un VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>Indique que le nom d'un stockage suit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>Indique qu'un stockage contient un objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>Indique que le nom d'un flux suit</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>Indique qu'un flux contient un objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>Indique byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>Indique un unsignedshort.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>Indique un unsignedlong.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>Indique un entier non signé 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>Indique une valeur entière unsigned.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>Indique un pointeur IUnknown.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>Indique un type défini par l'utilisateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>Indique un pointeur VARIANT far.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>Indique un tableau à nombre d'éléments simple.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>Indique un void de style C.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>Marshale des données de type VT_VARIANT | VT_BYREF de code managé en code non managé.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> pour le paramètre <see cref="T:System.Object" /> spécifié.</summary>
      <param name="obj">Objet à marshaler. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>Obtient l'objet encapsulé par l'objet <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</summary>
      <returns>Objet encapsulé par l'objet <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>Spécifie le comportement demandé lors de la configuration d'un récepteur de notifications ou d'une connexion de mise en cache avec un objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>Pour les connexions de notifications de données, vérifiez l'accessibilité des données. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>Pour les connexions de notifications de données (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> ou <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />), cet indicateur demande à l'objet de données de ne pas envoyer de données lorsqu'il appelle <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>Demande que l'objet effectue une seule notification de modifications ou mise à jour de cache avant de supprimer la connexion.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>Demande que l'objet n'attende pas la modification des données ou de l'affichage pour lancer un appel initial à <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> (pour les connexions de notifications de données ou d'affichage) ou pour mettre à mettre à jour le cache (pour les connexions de cache).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>Cette valeur est utilisée par les applications d'objet DLL et les gestionnaires d'objets qui dessinent leurs objets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>Synonyme de <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />, qui est utilisé le plus fréquemment.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>Pour les connexions de cache, cet indicateur ne met à jour la représentation mise en cache que si l'objet qui contient le cache est enregistré.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>Stocke les paramètres utilisés lors d'une opération de liaison de moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>Spécifie la taille de la structure BIND_OPTS en octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>Indique la période (heure de l'horloge en millisecondes, telle que retournée par la fonction GetTickCount) afin que l'appelant spécifié termine l'opération de liaison.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>Contrôle les aspects des opérations de liaison de moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>Représente les indicateurs devant être utilisés lors de l'ouverture du fichier qui contient l'objet identifié par le moniker.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>Contient un pointeur vers une structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> liée, une structure <see cref="T:System.Runtime.InteropServices.VARDESC" /> ou une interface ITypeComp.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>Représente un pointeur vers une structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>Représente un pointeur vers une interface <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>Représente un pointeur vers une structure <see cref="T:System.Runtime.InteropServices.VARDESC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>Identifie la convention d'appel utilisée par une méthode décrite dans une structure METHODDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>Indique que la convention d'appel C declaration (CDECL) est utilisée pour une méthode. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>Indique que la convention d'appel Macintosh Pascal (MACPASCAL) est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>Indique la fin de l'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>Indique que la convention d'appel CDECL Macintosh Programmers' Workbench (MPW) est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>Indique que la convention d'appel PASCAL Macintosh Programmers' Workbench (MPW) est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>Indique que la convention d'appel MSC Pascal (MSCPASCAL) est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>Indique que la convention d'appel Pascal est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>Cette valeur est réservée à une utilisation ultérieure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>Indique que la convention d'appel standard (STDCALL) est utilisée pour une méthode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>Indique que la convention d'appel standard SYSCALL est utilisée pour une méthode.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>Décrit une connexion qui existe à un point de connexion donné.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>Représente un jeton de connexion retourné à partir d'un appel à <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>Représente un pointeur vers l'interface IUnknown sur un récepteur de notifications connecté.L'appelant doit appeler IUnknown::Release sur ce pointeur lorsque la structure CONNECTDATA n'est plus nécessaire.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>Spécifie la direction du flux de données dans le paramètre <paramref name="dwDirection" /> de la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />.Cela détermine les formats que l'énumérateur résultant peut énumérer.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>Demande que <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> fournisse un énumérateur pour les formats pouvant être spécifiés dans <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>Demande que <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> fournisse un énumérateur pour les formats pouvant être spécifiés dans <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>Identifie la description de type liée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>Indique qu'une structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> a été retournée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>Indique que IMPLICITAPPOBJ a été retourné.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>Indique un marqueur de fin d'énumération.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>Indique qu'aucune correspondance n'a été trouvée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>Indique que TYPECOMP a été retourné.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>Indique que VARDESC a été retourné.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>Contient les arguments passés à une méthode ou une propriété par IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>Représente le nombre d'arguments.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>Représente le nombre d'arguments nommés. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>Représente les ID de dispatch des arguments nommés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>Représente une référence à un tableau d'arguments.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>Spécifie l'aspect des données ou de l'affichage voulu de l'objet lors du dessin ou de l'obtention de données.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>Représentation d'un objet qui permet son affichage en tant qu'objet incorporé dans un conteneur.Cette valeur est généralement spécifiée pour des objets de document composé.La présentation peut être fournie pour l'écran ou l'imprimante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>Représentation d'un objet à l'écran comme s'il était reproduit sur une imprimante à l'aide de la commande Imprimer du menu Fichier.Les données décrites peuvent représenter une séquence de pages.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>Représentation iconique d'un objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>Représentation miniature d'un objet qui permet son affichage dans un outil de navigation.La miniature est une bitmap d'environ 120 x 120 pixels, 16 couleurs (recommandé), indépendant du périphérique éventuellement encapsulé dans un métafichier.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>Contient la description de type et les informations de transfert de processus pour une variable, une fonction ou un paramètre de fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>Contient des informations sur un élément.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>Identifie le type de l'élément.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>Contient des informations sur un élément. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>Contient des informations de communication à distance avec l'élément.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>Contient des informations sur le paramètre.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>Décrit les exceptions qui se produisent pendant IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>Décrit l'erreur destinée au client.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>Contient le lecteur, le chemin d'accès et le nom de fichier complets d'un fichier d'aide contenant davantage d'informations sur l'erreur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>Indique le nom de la source de l'exception.Généralement, il s'agit d'un nom d'application.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>Indique l'ID de contexte d'aide de la rubrique dans le fichier d'aide.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>Représente un pointeur vers une fonction qui accepte une structure <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> comme argument et retourne une valeur HRESULT.Si un remplissage différé n'est pas souhaité, ce champ a la valeur null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>Ce champ est réservé ; il doit avoir la valeur null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>Valeur de retour décrivant l'erreur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>Représente un code d'erreur identifiant l'erreur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>Ce champ est réservé ; il doit avoir la valeur 0.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>Représente le nombre d'intervalles de 100 nanosecondes depuis le 1er janvier 1601.Cette structure est une valeur 64 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>Spécifie 32 bits de poids fort de FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>Spécifie 32 bits de poids faible de FILETIME.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>Représente un format Presse-papiers généralisé. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>Spécifie le format Presse-papiers pertinent.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>Spécifie une des constantes d'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> indiquant la quantité de détails qui doit être contenue dans le rendu.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>Spécifie une partie de l'aspect lorsqu'un saut de page doit être inséré à l'intérieur des données. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>Spécifie un pointeur vers une structure DVTARGETDEVICE contenant des informations relatives au périphérique cible pour lequel les données sont composées. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>Spécifie l'une des constantes d'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> qui indiquent le type de support de stockage utilisé pour transférer les données de l'objet. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>Définit une description de fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>Retourne la convention d'appel d'une fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>Compte le nombre total de paramètres.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>Compte les paramètres facultatifs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>Compte les valeurs de retour permises.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>Contient le type de retour de la fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>Spécifie si la fonction est virtuelle, statique ou de répartition uniquement.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>Spécifie le type d'une fonction de propriété.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>Indique la taille de <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>Stocke le nombre d'erreurs qu'une fonction peut retourner sur un système 16 bits.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>Identifie l'ID d'une fonction membre.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>Spécifie l'offset dans la VTBL pour <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>Indique le <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> d'une fonction.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>Identifie les constantes qui définissent les propriétés d'une fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>Fonction qui prend en charge la liaison de données.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>Fonction qui représente le mieux l'objet.Seule une fonction de type peut posséder cet attribut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>Permet une optimisation dans laquelle le compilateur recherche un membre nommé « xyz » sur le type « abc ».Si ce membre est trouvé et s'il est marqué comme fonction d'accesseur pour un élément de la collection par défaut, un appel à cette fonction membre est généré.Autorisé sur les membres dans des dispinterfaces et des interfaces ; non autorisé sur des modules.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>Fonction qui est affichée à l'utilisateur comme pouvant être liée.Vous devez également définir <see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>La fonction existe et elle peut être liée mais elle ne doit pas être visible à l'utilisateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>Mappé comme des propriétés individuelles pouvant être liées.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>La propriété est affichée dans un explorateur d'objets, mais pas dans un explorateur de propriétés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>Marque l'interface comme ayant des comportements par défaut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>Lorsque défini, tout appel à une méthode définissant la propriété résulte d'abord en un appel à IPropertyNotifySink::OnRequestEdit.L'implémentation de OnRequestEdit détermine si l'appel a l'autorisation de définir la propriété.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>La fonction ne doit pas être accessible à partir de langages de macro.Cet indicateur est destiné à des fonctions de niveau système ou à des fonctions qui ne doivent pas être affichées dans les explorateurs de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>Cette fonction retourne un objet qui est une source d'événements.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>Le membre d'informations de type est le membre par défaut à afficher dans l'interface utilisateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>La fonction prend en charge GetLastError.Si une erreur se produit pendant la fonction, l'appelant peut appeler GetLastError pour récupérer le code d'erreur.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>Définit comment accéder à une fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>La fonction est accessible uniquement par l'intermédiaire de IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>La fonction est accessible par une adresse static et accepte un pointeur this implicite.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>La fonction est accessible par l'intermédiaire de la table de fonctions virtuelles (VTBL, virtual function table) et accepte un pointeur this implicite.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>La fonction est accessible par une adresse static et n'accepte pas de pointeur this implicite.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>La fonction est accessible de la même manière que <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" /> sauf qu'elle possède une implémentation.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>Fournit une définition managée de l'interface IAdviseSink.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>Avertit tous les récepteurs de notifications enregistrés que l'objet n'est plus exécuté, mais simplement chargé.  Cette méthode est appelée par un serveur.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>Avertit tous les récepteurs de notifications actuellement enregistré des objets de données que des données contenues dans l'objet ont été modifiées.</summary>
      <param name="format">
        <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, passé par référence, qui décrit les informations sur le format, le périphérique cible, le rendu et le stockage de l'objet de données appelant.</param>
      <param name="stgmedium">
        <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />, passé par référence, qui définit le support de stockage (mémoire globale, fichier sur disque, objet de stockage, objet de flux, l'interface graphique GDI (Graphics Device Interface) ou indéfini) ainsi que la propriété de ce support pour l'objet de données appelant.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Avertit tous les récepteurs de notifications enregistrés que l'objet a été renommé.Cette méthode est appelée par un serveur.</summary>
      <param name="moniker">Pointeur vers l'interface IMoniker sur le nouveau moniker complet de l'objet.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>Avertit tous les récepteurs de notifications enregistrés que l'objet a été enregistré.Cette méthode est appelée par un serveur.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>Avertit les récepteurs de notifications enregistrés d'un objet que son affichage a été modifié.Cette méthode est appelée par un serveur.</summary>
      <param name="aspect">Aspect, ou affichage, de l'objet.Contient une valeur extraite de l'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />.</param>
      <param name="index">Partie de l'affichage qui a été modifiée.Actuellement, seule la valeur -1 est valide.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>Fournit la définition managée de l'interface IBindCtx.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Énumère les chaînes correspondant aux clés de la table de paramètres d'objets contextuels mise à jour en interne.</summary>
      <param name="ppenum">Lorsque cette méthode retourne une valeur, contient une référence à l'énumérateur du paramètre de l'objet.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Retourne les options de liaison en cours stockées dans le contexte de liaison actuel.</summary>
      <param name="pbindopts">Pointeur vers la structure qui doit recevoir les options de liaison. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>Recherche la clé donnée dans la table de paramètres d'objets contextuels mise à jour en interne et retourne l'objet correspondant, s'il existe.</summary>
      <param name="pszKey">Nom de l'objet à rechercher. </param>
      <param name="ppunk">Lorsque cette méthode retourne une valeur, contient le pointeur d'interface de l'objet.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>Retourne l'accès à la table ROT (Running Object Table) appropriée pour ce processus de liaison.</summary>
      <param name="pprot">Lorsque cette méthode retourne une valeur, contient une référence à la table ROT (Running Object Table).Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>Inscrit l'objet passé comme l'un des objets liés durant une opération de moniker et devant être libéré à la fin de l'opération.</summary>
      <param name="punk">Objet à inscrire comme devant être libéré. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>Inscrit le pointeur d'objet spécifié sous le nom donné dans la table de pointeurs d'objets mise à jour en interne.</summary>
      <param name="pszKey">Nom sous lequel inscrire <paramref name="punk" />. </param>
      <param name="punk">Objet à inscrire. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>Libère tous les objets actuellement inscrits dans le contexte de liaison par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>Supprime l'objet du jeu d'objets inscrits à libérer.</summary>
      <param name="punk">Objet dont l'inscription doit être annulée pour sa libération. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>Révoque l'inscription de l'objet figurant actuellement sous la clé spécifiée dans la table de paramètres d'objets contextuels mise à jour en interne, si cette clé est inscrite actuellement.</summary>
      <returns>Valeur S_OKHRESULT si la clé spécifiée a été supprimée de la table ; sinon, valeur S_FALSEHRESULT.</returns>
      <param name="pszKey">Clé dont l'inscription doit être annulée. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Stocke un bloc de paramètres dans le contexte de liaison.Ces paramètres s'appliqueront aux opérations UCOMIMoniker ultérieures utilisant ce contexte de liaison.</summary>
      <param name="pbindopts">Structure contenant les options de liaison à définir. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>Fournit la définition managée de l'interface IConnectionPoint.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>Établit une connexion de notifications entre le point de connexion et l'objet récepteur de l'appelant.</summary>
      <param name="pUnkSink">Référence au récepteur qui doit recevoir les appels pour l'interface sortante gérée par ce point de connexion. </param>
      <param name="pdwCookie">Lorsque cette méthode est retournée, contient le cookie de connexion.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crée un objet énumérateur pour l'itération par le biais des connexions qui existent avec ce point de connexion.</summary>
      <param name="ppEnum">Lorsque cette méthode retourne une valeur, contient le nouvel énumérateur créé.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>Retourne l'IID pour l'interface sortante managée par ce point de connexion.</summary>
      <param name="pIID">Lorsque ce paramètre retourne une valeur, contient l'IID de l'interface sortante gérée par ce point de connexion.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>Récupère le pointeur d'interface IConnectionPointContainer vers l'objet connectable qui est le propriétaire conceptuel de ce point de connexion.</summary>
      <param name="ppCPC">Lorsque ce paramètre retourne une valeur, contient l'interface IConnectionPointContainer de l'objet connectable.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>Arrête une connexion de notifications précédemment établie par le biais de la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
      <param name="dwCookie">Cookie de connexion précédemment retourné par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>Fournit la définition managée de l'interface IConnectionPointContainer.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crée un énumérateur de tous les points de connexion pris en charge dans l'objet connectable, avec un point de connexion par IID.</summary>
      <param name="ppEnum">Lorsque cette méthode retourne une valeur, contient le pointeur d'interface de l'énumérateur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>Vérifie si l'objet connectable possède un point de connexion pour un IID spécifique et, si tel est le cas, retourne l'interface de pointeur IConnectionPoint pour ce point de connexion.</summary>
      <param name="riid">Référence à l'IID d'interface sortante dont le point de connexion est demandé. </param>
      <param name="ppCP">Lorsque cette méthode retourne une valeur, contient le point de connexion qui gère l'interface sortante <paramref name="riid" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>Contient les informations nécessaires pour transférer un élément de structure, un paramètre ou une valeur de retour de fonction entre des processus.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>Réservé ; a la valeur null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>Indique une valeur <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> décrivant le type.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>Décrit comment transférer un élément de structure, un paramètre ou une valeur de retour de fonction entre des processus.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>Le paramètre passe des informations de l'appelant à l'appelé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>Le paramètre est l'identificateur local d'une application cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>Le paramètre retourne des informations de l'appelé à l'appelant.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>Le paramètre est la valeur de retour du membre.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>Ne spécifie pas si le paramètre passe ou reçoit des informations.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>Gère la définition de l'interface IEnumConnectionPoints.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <param name="ppenum">Cette méthode retourne une référence à l'énumérateur créé récemment.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre de références IConnectionPoint à retourner dans <paramref name="rgelt" />. </param>
      <param name="rgelt">Lorsque cette méthode retourne une valeur, contient une référence aux connexions énumérées.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Lorsque cette méthode retourne une valeur, contient une référence au nombre réel de connexions énumérées dans <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>Gère la définition de l'interface IEnumConnections.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <param name="ppenum">Cette méthode retourne une référence à l'énumérateur créé récemment.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre de structures <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> à retourner dans <paramref name="rgelt" />. </param>
      <param name="rgelt">Lorsque cette méthode retourne une valeur, contient une référence aux connexions énumérées.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Lorsque cette méthode retourne une valeur, contient une référence au nombre réel de connexions énumérées dans <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>Fournit la définition managée de l'interface IEnumFORMATETC.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <param name="newEnum">Cette méthode retourne une référence à l'énumérateur créé récemment.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre de références <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> à retourner dans <paramref name="rgelt" />.</param>
      <param name="rgelt">Cette méthode retourne une référence aux références <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> énumérés.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Cette méthode retourne une référence au nombre réel de références énumérées dans <paramref name="rgelt" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
      <returns>HRESULT avec la valeur S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>Gère la définition de l'interface IEnumMoniker.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <param name="ppenum">Cette méthode retourne une référence à l'énumérateur créé récemment.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre de monikers à retourner dans <paramref name="rgelt" />. </param>
      <param name="rgelt">Lorsque cette méthode retourne une valeur, contient une référence aux monikers énumérés.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Lorsque cette méthode retourne une valeur, contient une référence au nombre réel de monikers énumérés dans <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>Gère la définition de l'interface IEnumString.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <param name="ppenum">Cette méthode retourne une référence à l'énumérateur créé récemment.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre de chaînes à retourner dans <paramref name="rgelt" />. </param>
      <param name="rgelt">Lorsque cette méthode retourne une valeur, contient une référence aux chaînes énumérées.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Lorsque cette méthode retourne une valeur, contient une référence au nombre réel de chaînes énumérées dans <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>Gère la définition de l'interface IEnumVARIANT.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>Crée un autre énumérateur qui contient le même état d'énumération que l'énumérateur en cours.</summary>
      <returns>Référence <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> au nouvel énumérateur créé.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>Récupère un nombre spécifié d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le paramètre <paramref name="pceltFetched" /> équivaut au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à retourner dans <paramref name="rgelt" />. </param>
      <param name="rgVar">Lorsque cette méthode retourne une valeur, contient une référence aux éléments énumérés.Ce paramètre est passé sans être initialisé.</param>
      <param name="pceltFetched">Lorsque cette méthode retourne une valeur, contient une référence au nombre réel d'éléments énumérés dans <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>Remet au début la séquence d'énumération.</summary>
      <returns>HRESULT avec la valeur S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>Ignore un nombre défini d'éléments dans la séquence d'énumération.</summary>
      <returns>S_OK si le nombre d'éléments ignorés est égal au paramètre <paramref name="celt" /> ; sinon, S_FALSE.</returns>
      <param name="celt">Nombre d'éléments à ignorer dans l'énumération. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>Fournit la définition managée de l'interface IMoniker, avec une fonctionnalité COM provenant de IPersist et IPersistStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Utilise le moniker pour établir une liaison à l'objet qu'il identifie.</summary>
      <param name="pbc">Référence à l'interface IBindCtx sur l'objet de contexte de liaison utilisé dans cette opération de liaison. </param>
      <param name="pmkToLeft">Référence au moniker à gauche du moniker en cours, si le moniker fait partie d'un moniker composite. </param>
      <param name="riidResult">IID (ID d'interface) de l'interface que le client a l'intention d'utiliser pour communiquer avec l'objet que le moniker identifie. </param>
      <param name="ppvResult">Lorsque cette méthode retourne une valeur, contient une référence à l'interface demandée par <paramref name="riidResult" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Récupère un pointeur d'interface vers le stockage qui contient l'objet identifié par le moniker.</summary>
      <param name="pbc">Référence à l'interface IBindCtx sur l'objet de contexte de liaison utilisé dans cette opération de liaison. </param>
      <param name="pmkToLeft">Référence au moniker à gauche du moniker en cours, si le moniker fait partie d'un moniker composite. </param>
      <param name="riid">ID d'interface (IID, interface identifier) de l'interface de stockage demandée. </param>
      <param name="ppvObj">Lorsque cette méthode retourne une valeur, contient une référence à l'interface demandée par <paramref name="riid" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Crée un nouveau moniker basé sur le préfixe commun que ce moniker partage avec un autre moniker.</summary>
      <param name="pmkOther">Référence à l'interface IMoniker sur un autre moniker à comparer avec le moniker en cours pour identifier un préfixe commun. </param>
      <param name="ppmkPrefix">Lorsque cette méthode retourne une valeur, contient le moniker qui est le préfixe commun du moniker actuel et de <paramref name="pmkOther" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Combine le moniker en cours à un autre moniker, créant un nouveau moniker composite.</summary>
      <param name="pmkRight">Référence à l'interface IMoniker sur un moniker à ajouter à la fin du moniker actuel. </param>
      <param name="fOnlyIfNotGeneric">true pour indiquer que l'appelant exige une composition non générique.L'opération ne continue que si <paramref name="pmkRight" /> est une classe de moniker avec laquelle le moniker actuel peut se combiner d'une certaine manière, autrement qu'en constituant un composite générique.false pour indiquer que la méthode peut créer un composite générique si nécessaire.</param>
      <param name="ppmkComposite">Lorsque cette méthode retourne une valeur, contient une référence au moniker composite résultant.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Fournit un pointeur à un énumérateur pouvant énumérer les composants d'un moniker composite.</summary>
      <param name="fForward">true pour énumérer les monikers de gauche à droite.false pour énumérer de droite à gauche.</param>
      <param name="ppenumMoniker">Lorsque cette méthode retourne une valeur, contient une référence à l'objet énumérateur du moniker.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>Récupère le CLSID (identificateur de classe).</summary>
      <param name="pClassID">Lorsque cette méthode est retournée, contient le CLSID.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>Obtient le nom complet, qui est une représentation lisible par l'utilisateur du moniker actuel.</summary>
      <param name="pbc">Référence au contexte de liaison à utiliser dans cette opération. </param>
      <param name="pmkToLeft">Référence au moniker à gauche du moniker en cours, si le moniker fait partie d'un moniker composite. </param>
      <param name="ppszDisplayName">Lorsque cette méthode retourne une valeur, contient la chaîne du nom complet.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>Retourne la taille en octets du flux requis pour enregistrer l'objet.</summary>
      <param name="pcbSize">Lorsque cette méthode retourne une valeur, contient une valeur long indiquant la taille en octets du flux requis pour enregistrer cet objet.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Fournit un nombre représentant l'heure de la dernière modification de l'objet identifié par le moniker actuel.</summary>
      <param name="pbc">Référence au contexte de liaison à utiliser dans l'opération de liaison. </param>
      <param name="pmkToLeft">Référence au moniker à gauche du moniker en cours, si le moniker fait partie d'un moniker composite. </param>
      <param name="pFileTime">Lorsque cette méthode retourne une valeur, contient l'heure de la dernière modification.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>Calcule un entier 32 bits utilisant l'état interne du moniker.</summary>
      <param name="pdwHash">Lorsque cette méthode retourne une valeur, contient la valeur de hachage de ce moniker.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Fournit un moniker qui, lorsqu'il est composé à la droite du moniker actuel ou d'un moniker de structure similaire, produit une composition nulle.</summary>
      <param name="ppmk">Lorsque cette méthode retourne une valeur, contient un moniker qui est l'inverse du moniker actuel.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>Vérifie si l'objet a subi des modifications depuis son dernier enregistrement.</summary>
      <returns>Valeur S_OKHRESULT si l'objet a été modifié ; sinon, valeur S_FALSEHRESULT.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Compare le moniker actuel à un moniker spécifié et indique s'ils sont identiques.</summary>
      <returns>Valeur S_OKHRESULT si les monikers sont identiques ; sinon, valeur S_FALSEHRESULT.  </returns>
      <param name="pmkOtherMoniker">Référence au moniker à utiliser pour comparaison. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Détermine si l'objet qui est identifié par le moniker en cours est actuellement chargé et en service.</summary>
      <returns>Valeur S_OKHRESULT si le moniker est en service ; valeur S_FALSEHRESULT si le moniker n'est pas en service ; sinon, valeur E_UNEXPECTEDHRESULT.</returns>
      <param name="pbc">Référence au contexte de liaison à utiliser dans l'opération de liaison. </param>
      <param name="pmkToLeft">Référence au moniker à gauche du moniker en cours, si ce dernier fait partie d'un composite. </param>
      <param name="pmkNewlyRunning">Référence au dernier moniker ajouté à la table ROT (Running Object Table). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>Indique si ce moniker correspond à l'une des classes de moniker fournies par le système.</summary>
      <returns>Valeur S_OKHRESULT si le moniker est un moniker système ; sinon, valeur S_FALSEHRESULT.</returns>
      <param name="pdwMksys">Lorsque cette méthode retourne une valeur, contient un pointeur vers un entier faisant partie des valeurs de l'énumération MKSYS et faisant référence à l'une des classes de moniker COM.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>Initialise un objet à partir du flux ayant été précédemment enregistré.</summary>
      <param name="pStm">Flux à partir duquel l'objet est chargé. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Lit autant de caractères du nom complet spécifié que <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> peut interpréter et construit un moniker correspondant à la partie lue.</summary>
      <param name="pbc">Référence au contexte de liaison à utiliser dans l'opération de liaison. </param>
      <param name="pmkToLeft">Référence au moniker qui a été construit à partir du nom complet jusqu'à ce point. </param>
      <param name="pszDisplayName">Référence à la chaîne contenant le reste du nom complet à analyser. </param>
      <param name="pchEaten">Lorsque cette méthode retourne une valeur, contient le nombre de caractères utilisés pendant l'analyse de <paramref name="pszDisplayName" />.Ce paramètre est passé sans être initialisé.</param>
      <param name="ppmkOut">Lorsque cette méthode retourne une valeur, contient une référence au moniker généré à partir de <paramref name="pszDisplayName" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Retourne un moniker réduit correspondant à un autre moniker qui se réfère au même objet que le moniker actuel, mais peut être lié avec une efficacité équivalente ou supérieure.</summary>
      <param name="pbc">Référence à l'interface IBindCtx sur le contexte de liaison à utiliser dans cette opération de liaison. </param>
      <param name="dwReduceHowFar">Valeur spécifiant l'importance de la réduction du moniker actuel. </param>
      <param name="ppmkToLeft">Référence au moniker à gauche du moniker actuel. </param>
      <param name="ppmkReduced">Lorsque cette méthode retourne une valeur, contient une référence à la forme réduite de ce moniker, pouvant être null si une erreur se produit ou si le moniker actuel est réduit à une taille nulle.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Fournit un moniker qui, lorsqu'il est ajouté au moniker actuel (ou à un moniker de structure similaire), produit le moniker spécifié.</summary>
      <param name="pmkOther">Référence au moniker vers lequel un chemin relatif doit être pris. </param>
      <param name="ppmkRelPath">Lorsque cette méthode retourne une valeur, contient une référence au moniker relatif.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>Enregistre un objet dans le flux spécifié.</summary>
      <param name="pStm">Flux dans lequel l'objet est enregistré. </param>
      <param name="fClearDirty">true pour désactiver l'indicateur modifié à la fin de l'enregistrement ; sinon, false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>Définit les attributs d'une interface implémentée ou héritée d'un type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>L'interface ou la dispinterface représente la valeur par défaut de la source ou du récepteur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>Les récepteurs reçoivent des événements par l'intermédiaire de la table de fonctions virtuelles (VTBL, virtual function table).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>Le membre ne doit pas pouvoir être affiché ou programmé par des utilisateurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Ce membre d'une coclasse est appelé et non implémenté.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>Spécifie la façon d'appeler une fonction par IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>Le membre est appelé en utilisant une syntaxe d'appel de fonction normale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>La fonction est appelée en utilisant une syntaxe de propriété d'accès normale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>La fonction est appelée en utilisant une syntaxe d'assignation de valeur de propriété.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>La fonction est appelée en utilisant une syntaxe d'assignation de référence de propriété.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>Fournit la définition managée de l'interface IPersistFile, avec une fonctionnalité provenant de IPersist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>Récupère le CLSID (identificateur de classe).</summary>
      <param name="pClassID">Lorsque cette méthode retourne une valeur, contient une référence au CLSID.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>Récupère le chemin d'accès absolu au fichier de travail actuel de l'objet, ou s'il n'y a actuellement pas de fichiers de travail, l'invite du nom de fichier par défaut de l'objet.</summary>
      <param name="ppszFileName">Lorsque cette méthode retourne une valeur, contient l'adresse d'un pointeur vers une chaîne se terminant par zéro contenant le chemin d'accès au fichier en cours, ou l'invite du nom de fichier par défaut (par exemple *.txt).Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>Vérifie si l'objet a subi des modifications depuis son dernier enregistrement dans son fichier en cours.</summary>
      <returns>S_OK si le fichier a changé depuis son dernier enregistrement ; S_FALSE dans le cas contraire.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>Ouvre le fichier spécifié et initialise un objet à partir du contenu du fichier.</summary>
      <param name="pszFileName">Chaîne se terminant par zéro contenant le chemin d'accès absolu au fichier à ouvrir. </param>
      <param name="dwMode">Combinaison de valeurs de l'énumération STGM servant à indiquer le mode d'accès pour l'ouverture de <paramref name="pszFileName" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>Enregistre une copie de l'objet dans le fichier spécifié.</summary>
      <param name="pszFileName">Chaîne se terminant par zéro contenant le chemin d'accès absolu au fichier dans lequel l'objet est enregistré. </param>
      <param name="fRemember">true pour utiliser le paramètre <paramref name="pszFileName" /> comme fichier de travail actuel ; sinon, false. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>Indique à l'objet qu'il peut écrire dans son fichier.</summary>
      <param name="pszFileName">Le chemin d'accès absolu au fichier dans lequel l'objet a été précédemment enregistré. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>Fournit la définition managée de l'interface IRunningObjectTable.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Énumère les objets actuellement inscrits comme en cours d'exécution.</summary>
      <param name="ppenumMoniker">Lorsque cette méthode retourne une valeur, contient le nouvel énumérateur de la table ROT (Running Object Table).Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>Retourne l'objet inscrit si le nom d'objet fourni est inscrit comme en cours d'exécution.</summary>
      <returns>Valeur HRESULT indiquant le succès ou l'échec de l'opération. </returns>
      <param name="pmkObjectName">Référence au moniker à rechercher dans la table ROT (Running Object Table). </param>
      <param name="ppunkObject">Lorsque cette méthode retourne une valeur, contient l'objet en cours d'exécution demandé.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Recherche ce moniker dans la table ROT (Running Object Table) et indique l'heure de modification enregistrée, le cas échéant.</summary>
      <returns>Valeur HRESULT indiquant le succès ou l'échec de l'opération.</returns>
      <param name="pmkObjectName">Référence au moniker à rechercher dans la table ROT (Running Object Table). </param>
      <param name="pfiletime">Lorsque cet objet retourne une valeur, contient l'heure de la dernière modification de l'objet.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Détermine si le moniker spécifié est inscrit dans la table ROT (Running Object Table) actuellement.</summary>
      <returns>Valeur HRESULT indiquant le succès ou l'échec de l'opération.</returns>
      <param name="pmkObjectName">Référence au moniker à rechercher dans la table ROT (Running Object Table). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Consigne l'heure à laquelle un objet spécifique a été modifié pour que IMoniker::GetTimeOfLastChange indique l'heure de modification appropriée.</summary>
      <param name="dwRegister">Entrée de la table ROT (Running Object Table) correspondant à l'objet modifié. </param>
      <param name="pfiletime">Référence à l'heure de la dernière modification de l'objet. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Inscrit que l'objet fourni est en état d'exécution.</summary>
      <returns>Valeur qui peut être utilisée pour identifier cette entrée de la table ROT (Running Object Table) dans des appels ultérieurs à  <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> ou à <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" />.</returns>
      <param name="grfFlags">Spécifie si la référence de la table ROT (Running Object Table) à <paramref name="punkObject" /> est faible ou forte et contrôle l'accès à l'objet via l'entrée correspondante dans la table ROT (Running Object Table). </param>
      <param name="punkObject">Référence à l'objet inscrit comme étant en cours d'exécution. </param>
      <param name="pmkObjectName">Référence au moniker qui identifie <paramref name="punkObject" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>Annule l'inscription de l'objet spécifié dans la table ROT (Running Object Table).</summary>
      <param name="dwRegister">Entrée de la table ROT (Running Object Table) à révoquer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>Fournit la définition managée de l'interface IStream, avec une fonctionnalité ISequentialStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>Crée un nouvel objet de flux avec son propre pointeur de recherche référençant les mêmes octets que le flux d'origine.</summary>
      <param name="ppstm">Lorsque cette méthode retourne une valeur, contient le nouvel objet de flux.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>S'assure que toute modification effectuée sur un objet de flux ouvert en mode traité est réfléchie dans le stockage parent.</summary>
      <param name="grfCommitFlags">Valeur contrôlant la manière dont les modifications apportées à un objet de flux sont validées. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>Copie un nombre d'octets spécifié du pointeur de recherche actif dans le flux à destination du pointeur de recherche actif dans un autre flux.</summary>
      <param name="pstm">Référence au flux de destination. </param>
      <param name="cb">Nombre d'octets à copier à partir du flux source. </param>
      <param name="pcbRead">Après appel réussi de la méthode, contient le nombre réel d'octets lus à partir de la source. </param>
      <param name="pcbWritten">Après appel réussi de la méthode, contient le nombre réel d'octets écrits dans la destination. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Restreint l'accès à une plage d'octets spécifiée dans le flux.</summary>
      <param name="libOffset">Offset d'octet au début de la plage. </param>
      <param name="cb">Longueur (en octets) de la plage à limiter. </param>
      <param name="dwLockType">Restrictions demandées pour l'accès à la plage. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Lit un nombre d'octets spécifié dans l'objet de flux en mémoire en commençant au pointeur de recherche actif.</summary>
      <param name="pv">Lorsque cette méthode retourne une valeur, contient les données lues à partir du flux.Ce paramètre est passé sans être initialisé.</param>
      <param name="cb">Nombre d'octets à lire à partir de l'objet de flux. </param>
      <param name="pcbRead">Pointeur vers une variable ULONG qui reçoit le nombre réel d'octets lus à partir de l'objet de flux. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>Abandonne toutes les modifications effectuées sur un flux traité depuis le dernier appel <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>Déplace le pointeur de recherche vers un nouvel emplacement relatif au début, à la fin du flux, ou au pointeur de recherche actif.</summary>
      <param name="dlibMove">Déplacement à ajouter à <paramref name="dwOrigin" />. </param>
      <param name="dwOrigin">Origine de la recherche.Il peut s'agir du début du fichier, de la position du pointeur de recherche actif ou de la fin du fichier.</param>
      <param name="plibNewPosition">Après appel réussi de la méthode, contient l'offset du pointeur à partir du début du flux. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>Modifie la taille de l'objet de flux.</summary>
      <param name="libNewSize">Nouvelle taille du flux sous la forme d'un nombre d'octets. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>Récupère la structure <see cref="T:System.Runtime.InteropServices.STATSTG" /> pour ce flux.</summary>
      <param name="pstatstg">Lorsque cette méthode retourne une valeur, contient une structure STATSTG qui décrit cet objet de flux.Ce paramètre est passé sans être initialisé.</param>
      <param name="grfStatFlag">Membres de la structure STATSTG que cette méthode ne retourne pas, ce qui évite quelques opérations d'allocation de mémoire. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Supprime les restrictions d'accès à une plage d'octets dont l'accès a été limité au préalable à l'aide de la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" />.</summary>
      <param name="libOffset">Offset d'octet au début de la plage. </param>
      <param name="cb">Longueur (en octets) de la plage à limiter. </param>
      <param name="dwLockType">Restrictions d'accès placées au préalable sur la plage. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Écrit un nombre d'octets spécifié dans l'objet de flux en commençant au pointeur de recherche actif.</summary>
      <param name="pv">Mémoire tampon dans laquelle écrire ce flux. </param>
      <param name="cb">Nombre d'octets à écrire dans le flux. </param>
      <param name="pcbWritten">Après appel réussi de la méthode, contient le nombre réel d'octets écrits dans l'objet de flux.Si l'appelant affecte la valeur <see cref="F:System.IntPtr.Zero" /> à ce pointeur, cette méthode ne fournit pas le nombre réel d'octets écrits.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>Fournit la définition managée de l'interface ITypeComp.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>Mappe un nom à un membre d'un type, ou lie des variables et des fonctions globales contenues dans une bibliothèque de types.</summary>
      <param name="szName">Nom à lier. </param>
      <param name="lHashVal">Valeur de hachage pour <paramref name="szName" /> calculée par LHashValOfNameSys. </param>
      <param name="wFlags">Mot indicateur contenant un ou plusieurs indicateurs d'appel définis dans l'énumération INVOKEKIND. </param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient une référence à la description de type comprenant l'élément auquel elle est liée, si FUNCDESC ou VARDESC a été retourné.Ce paramètre est passé sans être initialisé.</param>
      <param name="pDescKind">Lorsque cette méthode retourne une valeur, contient une référence à un énumérateur DESCKIND qui indique si le nom lié est VARDESC, FUNCDESC ou TYPECOMP.Ce paramètre est passé sans être initialisé.</param>
      <param name="pBindPtr">Lorsque cette méthode retourne une valeur, contient une référence à l'interface VARDESC, FUNCDESC ou ITypeComp liée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Lie les descriptions de type contenues dans une bibliothèque de types.</summary>
      <param name="szName">Nom à lier. </param>
      <param name="lHashVal">Valeur de hachage pour <paramref name="szName" /> déterminée par LHashValOfNameSys. </param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient une référence à un ITypeInfo du type auquel <paramref name="szName" /> était lié.Ce paramètre est passé sans être initialisé.</param>
      <param name="ppTComp">Lorsque cette méthode retourne une valeur, contient une référence à une variable ITypeComp.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>Fournit la définition managée de l'interface ITypeInfo d'automation de composant.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Récupère les adresses de fonctions statiques ou de variables, telles que celles définies dans une DLL.</summary>
      <param name="memid">ID de membre de l'adresse de membre static à récupérer. </param>
      <param name="invKind">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> qui indique si le membre est une propriété et dans ce cas, de quel type est cette propriété. </param>
      <param name="ppv">Lorsque cette méthode retourne une valeur, contient une référence au membre static.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crée une nouvelle instance d'un type décrivant une classe Component (coclasse).</summary>
      <param name="pUnkOuter">Objet qui joue le rôle du IUnknown contrôleur. </param>
      <param name="riid">IID de l'interface utilisée par l'appelant pour communiquer avec l'objet obtenu. </param>
      <param name="ppvObj">Lorsque cette méthode retourne une valeur, contient une référence à l'objet créé.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Récupère la bibliothèque de types qui contient cette description de type ainsi que l'index correspondant dans cette bibliothèque.</summary>
      <param name="ppTLB">Lorsque cette méthode retourne une valeur, contient une référence à la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
      <param name="pIndex">Lorsque cette méthode retourne une valeur, contient une référence à l'index de la description de type dans la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Récupère une description ou une spécification d'un point d'entrée pour une fonction dans une DLL.</summary>
      <param name="memid">ID de la fonction membre dont la description d'entrée de DLL doit être retournée. </param>
      <param name="invKind">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> qui spécifie le type de membre identifié par <paramref name="memid" />. </param>
      <param name="pBstrDllName">Si la valeur n'est pas null, la fonction définit <paramref name="pBstrDllName" /> avec un BSTR qui contient le nom de la DLL. </param>
      <param name="pBstrName">Si la valeur n'est pas null, la fonction définit <paramref name="lpbstrName" /> avec un BSTR qui contient le nom du point d'entrée. </param>
      <param name="pwOrdinal">Si la valeur n'est pas null et que la fonction est définie par un ordinal, <paramref name="lpwOrdinal" /> est défini de manière à pointer vers l'ordinal. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation, le nom et le chemin d'accès complets du fichier d'aide et l'ID de contexte de la rubrique d'aide se rapportant à une description de type spécifiée.</summary>
      <param name="index">ID du membre dont la documentation doit être retournée. </param>
      <param name="strName">Lorsque cette méthode retourne une valeur, contient le nom de la méthode de l'élément.Ce paramètre est passé sans être initialisé.</param>
      <param name="strDocString">Cette méthode retourne la chaîne de documentation pour l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="dwHelpContext">Lorsque cette méthode retourne une valeur, contient une référence au contexte d'aide associé à l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strHelpFile">Lorsque cette méthode retourne une valeur, contient le nom qualifié complet du fichier d'aide.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Récupère la structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> qui contient des informations sur une fonction spécifiée.</summary>
      <param name="index">Index de la description de fonction à retourner. </param>
      <param name="ppFuncDesc">Lorsque cette méthode retourne une valeur, contient une référence à une structure FUNCDESC qui décrit la fonction spécifiée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Effectue un mappage entre les noms et les ID de membres, ainsi qu'entre les noms et les ID de paramètres.</summary>
      <param name="rgszNames">Tableau de noms à mapper. </param>
      <param name="cNames">Nombre de noms à mapper. </param>
      <param name="pMemId">Lorsque cette méthode retourne une valeur, contient une référence à un tableau dans lequel les mappages des noms sont insérés.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Récupère la valeur <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> pour une interface implémentée ou une interface de base dans une description de type.</summary>
      <param name="index">Index de l'interface implémentée ou de l'interface de base. </param>
      <param name="pImplTypeFlags">Lorsque cette méthode retourne une valeur, contient une référence à l'énumération IMPLTYPEFLAGS.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>Récupère des informations de marshaling.</summary>
      <param name="memid">ID de membre qui indique les informations de marshaling nécessaires. </param>
      <param name="pBstrMops">Lorsque cette méthode retourne une valeur, contient une référence à la chaîne opcode utilisée pour marshaler les champs de la structure décrite par la description de type référencée, ou retourne null s'il n'y a aucune information à retourner.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Récupère la variable avec l'ID de membre spécifié (ou le nom de la propriété ou de la méthode et ses paramètres) correspondant à l'ID de fonction spécifié.</summary>
      <param name="memid">ID du membre dont le nom (ou les noms) doit être retourné. </param>
      <param name="rgBstrNames">Lorsque cette méthode retourne une valeur, contient le ou les noms associés au membre.Ce paramètre est passé sans être initialisé.</param>
      <param name="cMaxNames">Longueur du tableau <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">Lorsque cette méthode retourne une valeur, contient le nombre de noms contenus dans le tableau <paramref name="rgBstrNames" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère les descriptions de type référencées, si une description de type fait référence à d'autres descriptions de type.</summary>
      <param name="hRef">Handle de la description de type référencée à retourner. </param>
      <param name="ppTI">Lorsque cette méthode retourne une valeur, contient la description de type référencée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Récupère la description des types d'interfaces implémentés, si une description de type décrit une classe COM.</summary>
      <param name="index">Index du type implémenté dont le handle est retourné. </param>
      <param name="href">Lorsque cette méthode retourne une valeur, contient une référence à un handle pour l'interface implémentée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>Récupère une structure <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> qui contient les attributs de la description de type.</summary>
      <param name="ppTypeAttr">Lorsque cette méthode retourne une valeur, contient une référence à la structure qui contient les attributs de cette description de type.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Récupère l'interface ITypeComp pour la description de type qui permet à un compilateur client d'effectuer une liaison avec les membres de la description de type.</summary>
      <param name="ppTComp">Lorsque cette méthode retourne une valeur, contient une référence à l'interface ITypeComp de la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Récupère une structure VARDESC qui décrit la variable spécifiée.</summary>
      <param name="index">Index de la description de variable à retourner. </param>
      <param name="ppVarDesc">Lorsque cette méthode retourne une valeur, contient une référence à la structure VARDESC qui décrit la variable spécifiée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Appelle une méthode ou accède à une propriété d'un objet qui implémente l'interface décrite par la description de type.</summary>
      <param name="pvInstance">Référence à l'interface décrite par cette description de type. </param>
      <param name="memid">Valeur identifiant le membre d'interface. </param>
      <param name="wFlags">Indicateurs décrivant le contexte de l'appel Invoke. </param>
      <param name="pDispParams">Référence à une structure qui contient un tableau d'arguments, un tableau de DISPID pour des arguments nommés et le nombre d'éléments de chaque tableau. </param>
      <param name="pVarResult">Référence à l'emplacement où le résultat doit être stocké.Si <paramref name="wFlags" /> spécifie DISPATCH_PROPERTYPUT ou DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> est ignoré.Affectez null si aucun résultat n'est requis.</param>
      <param name="pExcepInfo">Pointeur vers une structure d'informations d'exception qui est remplie uniquement lorsque DISP_E_EXCEPTION est retourné. </param>
      <param name="puArgErr">Si Invoke retourne DISP_E_TYPEMISMATCH, <paramref name="puArgErr" /> indique l'index dans le paramètre <paramref name="rgvarg" /> de l'argument de type incorrect.Si plusieurs arguments retournent une erreur, <paramref name="puArgErr" /> indique uniquement le premier argument contenant une erreur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libère une structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Référence à la structure FUNCDESC à libérer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libère une structure <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Référence à la structure TYPEATTR à libérer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>Libère une structure VARDESC précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Référence à la structure VARDESC à libérer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>Fournit la définition managée de l'interface ITypeInfo2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Récupère les adresses de fonctions statiques ou de variables, telles que celles définies dans une DLL.</summary>
      <param name="memid">ID de membre de l'adresse de membre static à récupérer. </param>
      <param name="invKind">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> qui indique si le membre est une propriété et dans ce cas, de quel type est cette propriété. </param>
      <param name="ppv">Lorsque cette méthode retourne une valeur, contient une référence au membre static.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crée une nouvelle instance d'un type décrivant une classe Component (coclasse).</summary>
      <param name="pUnkOuter">Objet qui joue le rôle du IUnknown contrôleur. </param>
      <param name="riid">IID de l'interface utilisée par l'appelant pour communiquer avec l'objet obtenu. </param>
      <param name="ppvObj">Lorsque cette méthode retourne une valeur, contient une référence à l'objet créé.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>Obtient tous les éléments de données personnalisés pour la bibliothèque.</summary>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>Obtient toutes les données personnalisées de la fonction spécifiée.</summary>
      <param name="index">Index de la fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>Obtient toutes les données personnalisées pour le type d'implémentation spécifié.</summary>
      <param name="index">Index du type d'implémentation pour les données personnalisées. </param>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>Obtient toutes les données personnalisées pour le paramètre de fonction spécifié.</summary>
      <param name="indexFunc">Index de la fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="indexParam">Index du paramètre de cette fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>Obtient la variable pour les données personnalisées.</summary>
      <param name="index">Index de la variable pour laquelle obtenir les données personnalisées. </param>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Récupère la bibliothèque de types qui contient cette description de type ainsi que l'index correspondant dans cette bibliothèque.</summary>
      <param name="ppTLB">Lorsque cette méthode retourne une valeur, contient une référence à la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
      <param name="pIndex">Lorsque cette méthode retourne une valeur, contient une référence à l'index de la description de type dans la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>Obtient les données personnalisées.</summary>
      <param name="guid">GUID utilisé pour identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un Object qui spécifie l'emplacement où mettre les données récupérées.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Récupère une description ou une spécification d'un point d'entrée pour une fonction dans une DLL.</summary>
      <param name="memid">ID de la fonction membre dont la description d'entrée de DLL doit être retournée. </param>
      <param name="invKind">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> qui spécifie le type de membre identifié par <paramref name="memid" />. </param>
      <param name="pBstrDllName">Si la valeur n'est pas null, la fonction définit <paramref name="pBstrDllName" /> avec un BSTR qui contient le nom de la DLL. </param>
      <param name="pBstrName">Si la valeur n'est pas null, la fonction définit <paramref name="lpbstrName" /> avec un BSTR qui contient le nom du point d'entrée. </param>
      <param name="pwOrdinal">Si la valeur n'est pas null et que la fonction est définie par un ordinal, <paramref name="lpwOrdinal" /> est défini de manière à pointer vers l'ordinal. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation, le nom et le chemin d'accès complets du fichier d'aide et l'ID de contexte de la rubrique d'aide se rapportant à une description de type spécifiée.</summary>
      <param name="index">ID du membre dont la documentation doit être retournée. </param>
      <param name="strName">Lorsque cette méthode retourne une valeur, contient le nom de la méthode de l'élément.Ce paramètre est passé sans être initialisé.</param>
      <param name="strDocString">Cette méthode retourne la chaîne de documentation pour l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="dwHelpContext">Lorsque cette méthode retourne une valeur, contient une référence au contexte d'aide associé à l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strHelpFile">Lorsque cette méthode retourne une valeur, contient le nom qualifié complet du fichier d'aide.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation, le nom et le chemin d'accès complets du fichier d'aide, le contexte de localisation à utiliser et l'ID de contexte de la rubrique d'aide de la bibliothèque dans le fichier d'aide.</summary>
      <param name="memid">Identificateur de membre pour la description de type. </param>
      <param name="pbstrHelpString">Cette méthode retourne un BSTR comprenant le nom de l'élément spécifié.Si l'appelant n'a pas besoin du nom de l'élément, <paramref name="pbstrHelpString" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
      <param name="pdwHelpStringContext">Cette méthode retourne le contexte de localisation de l'aide.Si l'appelant n'a pas besoin du contexte d'aide, <paramref name="pdwHelpStringContext" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
      <param name="pbstrHelpStringDll">Cette méthode retourne un BSTR comprenant le nom qualifié complet du fichier dans lequel se situe la DLL utilisée pour le fichier d'aide.Si l'appelant n'a pas besoin du nom de fichier, <paramref name="pbstrHelpStringDll" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtient les données personnalisées de la fonction spécifiée.</summary>
      <param name="index">Index de la fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="guid">GUID utilisé pour identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un Object spécifiant l'emplacement où mettre les données.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Récupère la structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> qui contient des informations sur une fonction spécifiée.</summary>
      <param name="index">Index de la description de fonction à retourner. </param>
      <param name="ppFuncDesc">Lorsque cette méthode retourne une valeur, contient une référence à une structure FUNCDESC qui décrit la fonction spécifiée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>Crée une liaison avec un membre spécifique en se basant sur un DISPID connu, et sans connaître le nom du membre (par exemple, lors de la liaison à un membre par défaut).</summary>
      <param name="memid">Identificateur du membre. </param>
      <param name="invKind">Une des valeurs <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> qui spécifie le type de membre identifié par memid.</param>
      <param name="pFuncIndex">Cette méthode retourne un index dans la fonction.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Effectue un mappage entre les noms et les ID de membres, ainsi qu'entre les noms et les ID de paramètres.</summary>
      <param name="rgszNames">Tableau de noms à mapper. </param>
      <param name="cNames">Nombre de noms à mapper. </param>
      <param name="pMemId">Lorsque cette méthode retourne une valeur, contient une référence à un tableau dans lequel les mappages des noms sont insérés.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtient le type d'implémentation des données personnalisées.</summary>
      <param name="index">Index du type d'implémentation pour les données personnalisées. </param>
      <param name="guid">GUID utilisé pour identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un Object qui spécifie l'emplacement où mettre les données récupérées.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Récupère la valeur <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> pour une interface implémentée ou une interface de base dans une description de type.</summary>
      <param name="index">Index de l'interface implémentée ou de l'interface de base. </param>
      <param name="pImplTypeFlags">Lorsque cette méthode retourne une valeur, contient une référence à l'énumération IMPLTYPEFLAGS.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>Récupère des informations de marshaling.</summary>
      <param name="memid">ID de membre qui indique les informations de marshaling nécessaires. </param>
      <param name="pBstrMops">Lorsque cette méthode retourne une valeur, contient une référence à la chaîne opcode utilisée pour marshaler les champs de la structure décrite par la description de type référencée, ou retourne null s'il n'y a aucune information à retourner.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Récupère la variable avec l'ID de membre spécifié (ou le nom de la propriété ou de la méthode et ses paramètres) correspondant à l'ID de fonction spécifié.</summary>
      <param name="memid">ID du membre dont le nom (ou les noms) doit être retourné. </param>
      <param name="rgBstrNames">Lorsque cette méthode retourne une valeur, contient le ou les noms associés au membre.Ce paramètre est passé sans être initialisé.</param>
      <param name="cMaxNames">Longueur du tableau <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">Lorsque cette méthode retourne une valeur, contient le nombre de noms contenus dans le tableau <paramref name="rgBstrNames" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>Obtient le paramètre de données personnalisées spécifié.</summary>
      <param name="indexFunc">Index de la fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="indexParam">Index du paramètre de cette fonction pour laquelle obtenir les données personnalisées. </param>
      <param name="guid">GUID utilisé pour identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un Object qui spécifie l'emplacement où mettre les données récupérées.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère les descriptions de type référencées, si une description de type fait référence à d'autres descriptions de type.</summary>
      <param name="hRef">Handle de la description de type référencée à retourner. </param>
      <param name="ppTI">Lorsque cette méthode retourne une valeur, contient la description de type référencée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Récupère la description des types d'interfaces implémentés, si une description de type décrit une classe COM.</summary>
      <param name="index">Index du type implémenté dont le handle est retourné. </param>
      <param name="href">Lorsque cette méthode retourne une valeur, contient une référence à un handle pour l'interface implémentée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>Récupère une structure <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> qui contient les attributs de la description de type.</summary>
      <param name="ppTypeAttr">Lorsque cette méthode retourne une valeur, contient une référence à la structure qui contient les attributs de cette description de type.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Récupère l'interface ITypeComp pour la description de type qui permet à un compilateur client d'effectuer une liaison avec les membres de la description de type.</summary>
      <param name="ppTComp">Cette méthode retourne une référence au ITypeComp de la bibliothèque de types conteneur.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>Retourne les indicateurs de type sans allocation.Cette méthode retourne un indicateur de type DWORD qui développe les indicateurs de type sans augmenter TYPEATTR (attribut de type).</summary>
      <param name="pTypeFlags">Cette méthode retourne une référence DWORD à TYPEFLAG.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Retourne rapidement l'énumération TYPEKIND, sans effectuer d'allocation.</summary>
      <param name="pTypeKind">Cette méthode retourne une référence à une énumération TYPEKIND.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Obtient la variable pour les données personnalisées.</summary>
      <param name="index">Index de la variable pour laquelle obtenir les données personnalisées. </param>
      <param name="guid">GUID utilisé pour identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un Object qui spécifie l'emplacement où mettre les données récupérées.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Récupère une structure VARDESC qui décrit la variable spécifiée.</summary>
      <param name="index">Index de la description de variable à retourner. </param>
      <param name="ppVarDesc">Lorsque cette méthode retourne une valeur, contient une référence à la structure VARDESC qui décrit la variable spécifiée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>Crée une liaison avec un membre spécifique en se basant sur un DISPID connu, et sans connaître le nom du membre (par exemple, lors de la liaison à un membre par défaut).</summary>
      <param name="memid">Identificateur du membre. </param>
      <param name="pVarIndex">Cette méthode retourne un index de <paramref name="memid" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Appelle une méthode ou accède à une propriété d'un objet qui implémente l'interface décrite par la description de type.</summary>
      <param name="pvInstance">Référence à l'interface décrite par cette description de type. </param>
      <param name="memid">Identificateur du membre d'interface. </param>
      <param name="wFlags">Indicateurs décrivant le contexte de l'appel Invoke. </param>
      <param name="pDispParams">Référence à une structure qui contient un tableau d'arguments, un tableau de DISPID pour des arguments nommés et le nombre d'éléments de chaque tableau. </param>
      <param name="pVarResult">Référence à l'emplacement où le résultat doit être stocké.Si <paramref name="wFlags" /> spécifie DISPATCH_PROPERTYPUT ou DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> est ignoré.Affectez null si aucun résultat n'est requis.</param>
      <param name="pExcepInfo">Pointeur vers une structure d'informations d'exception qui est remplie uniquement lorsque DISP_E_EXCEPTION est retourné. </param>
      <param name="puArgErr">Si Invoke retourne DISP_E_TYPEMISMATCH, <paramref name="puArgErr" /> indique l'index de l'argument de type incorrect.Si plusieurs arguments retournent une erreur, <paramref name="puArgErr" /> indique uniquement le premier argument contenant une erreur.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libère une structure <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Référence à la structure FUNCDESC à libérer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libère une structure <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Référence à la structure TYPEATTR à libérer. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>Libère une structure VARDESC précédemment retournée par la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Référence à la structure VARDESC à libérer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>Fournit la définition managée de l'interface ITypeLib.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Recherche les occurrences d'une description de type dans une bibliothèque de types.</summary>
      <param name="szNameBuf">Nom à rechercher.Il s'agit d'un paramètre entrée/sortie.</param>
      <param name="lHashVal">Valeur de hachage servant à accélérer la recherche, calculée par la fonction LHashValOfNameSys.Si <paramref name="lHashVal" /> est égal à 0, une valeur est calculée.</param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient un tableau de pointeurs vers les descriptions de types contenant le nom spécifié dans <paramref name="szNameBuf" />.Ce paramètre est passé sans être initialisé.</param>
      <param name="rgMemId">Tableau de MEMBERID des éléments trouvés ; <paramref name="rgMemId" />[i] est le MEMBERID qui indexe dans la description de type spécifiée par <paramref name="ppTInfo" />[i].La valeur ne peut pas être null.</param>
      <param name="pcFound">À l'entrée, indique combien d'instances sont à rechercher.Par exemple, <paramref name="pcFound" /> = 1 peut être appelé pour rechercher la première occurrence.La recherche s'interrompt lorsqu'une instance est trouvée.En quittant, indique le nombre d'instances trouvées.Si les valeurs in et out de <paramref name="pcFound" /> sont identiques, cela signifie que d'autres descriptions de types peuvent contenir le nom.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation de la bibliothèque, le nom et le chemin d'accès complets du fichier d'aide et l'ID de contexte de la rubrique d'aide de la bibliothèque dans le fichier d'aide.</summary>
      <param name="index">Index de la description de type dont la documentation doit être retournée. </param>
      <param name="strName">Lorsque cette méthode retourne une valeur, contient une chaîne représentant le nom de l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strDocString">Lorsque cette méthode retourne une valeur, contient une chaîne qui représente la chaîne de documentation pour l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="dwHelpContext">Lorsque cette méthode retourne une valeur, contient l'identificateur du contexte d'aide associé à l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strHelpFile">Lorsque cette méthode retourne une valeur, contient une chaîne représentant le nom qualifié complet du fichier d'aide.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Récupère la structure qui contient les attributs de la bibliothèque.</summary>
      <param name="ppTLibAttr">Lorsque cette méthode retourne une valeur, contient une structure comprenant les attributs de la bibliothèque.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Permet à un compilateur client d'effectuer une liaison aux types, variables, constantes et fonctions globales d'une bibliothèque.</summary>
      <param name="ppTComp">Lorsque cette méthode retourne une valeur, contient une instance d'une instance de ITypeComp pour ce ITypeLib.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère la description de type spécifiée dans la bibliothèque.</summary>
      <param name="index">Index de l'interface ITypeInfo à retourner. </param>
      <param name="ppTI">Lorsque cette méthode retourne une valeur, contient un ITypeInfo décrivant le type référencé par <paramref name="index" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>Retourne le nombre de descriptions de types de la bibliothèque de types.</summary>
      <returns>Nombre de descriptions de types de la bibliothèque de types.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère la description de type qui correspond au GUID spécifié.</summary>
      <param name="guid">IID de l'interface ou CLSID de la classe dont les informations de type sont demandées. </param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient l'interface ITypeInfo demandée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Récupère le type d'une description de type.</summary>
      <param name="index">Index de la description de type dans la bibliothèque de types. </param>
      <param name="pTKind">Lorsque cette méthode retourne une valeur, contient une référence à l'énumération TYPEKIND pour la description de type.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>Indique si une chaîne passée contient le nom d'un type ou d'un membre décrit dans la bibliothèque.</summary>
      <returns>true si <paramref name="szNameBuf" /> a été trouvé dans la bibliothèque de types ; sinon, false.</returns>
      <param name="szNameBuf">Chaîne à tester.Il s'agit d'un paramètre entrée/sortie.</param>
      <param name="lHashVal">Valeur de hachage de <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libère la structure <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> obtenue à l'origine à partir de la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Structure TLIBATTR à libérer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>Fournit une définition managée de l'interface ITypeLib2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Recherche les occurrences d'une description de type dans une bibliothèque de types.</summary>
      <param name="szNameBuf">Nom à rechercher. </param>
      <param name="lHashVal">Valeur de hachage servant à accélérer la recherche, calculée par la fonction LHashValOfNameSys.Si <paramref name="lHashVal" /> est égal à 0, une valeur est calculée.</param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient un tableau de pointeurs vers les descriptions de types contenant le nom spécifié dans <paramref name="szNameBuf" />.Ce paramètre est passé sans être initialisé.</param>
      <param name="rgMemId">Cette méthode retourne un tableau de MEMBERIDdes éléments trouvés ; <paramref name="rgMemId" /> [i] est le MEMBERID qui indexe dans la description de type spécifiée par <paramref name="ppTInfo" /> [i].Ce paramètre ne peut pas être null.Ce paramètre est passé sans être initialisé.</param>
      <param name="pcFound">À l'entrée, valeur passée par référence qui indique le nombre d'instances à rechercher.Par exemple, <paramref name="pcFound" /> = 1 peut être appelé pour rechercher la première occurrence.La recherche s'interrompt lorsqu'une instance est trouvée.En quittant, indique le nombre d'instances trouvées.Si les valeurs in et out de <paramref name="pcFound" /> sont identiques, cela signifie que d'autres descriptions de types peuvent contenir le nom.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>Obtient tous les éléments de données personnalisés pour la bibliothèque.</summary>
      <param name="pCustData">Pointeur vers CUSTDATA, qui détient tous les éléments de données personnalisés. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>Obtient les données personnalisées.</summary>
      <param name="guid">
        <see cref="T:System.Guid" /> passé par référence, permettant d'identifier les données. </param>
      <param name="pVarVal">Cette méthode retourne un objet qui spécifie l'emplacement où mettre les données récupérées.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation de la bibliothèque, le nom et le chemin d'accès complets du fichier d'aide et l'ID de contexte de la rubrique d'aide de la bibliothèque dans le fichier d'aide.</summary>
      <param name="index">Index de la description de type dont la documentation doit être retournée. </param>
      <param name="strName">Cette méthode retourne une chaîne qui indique le nom de l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strDocString">Cette méthode retourne la chaîne de documentation pour l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="dwHelpContext">Lorsque cette méthode retourne une valeur, contient l'identificateur du contexte d'aide associé à l'élément spécifié.Ce paramètre est passé sans être initialisé.</param>
      <param name="strHelpFile">Cette méthode retourne une chaîne qui indique le nom qualifié complet du fichier d'aide.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Récupère la chaîne de documentation de la bibliothèque, le nom et le chemin d'accès complets du fichier d'aide, le contexte de localisation à utiliser et l'ID de contexte de la rubrique d'aide de la bibliothèque dans le fichier d'aide.</summary>
      <param name="index">Index de la description de type dont la documentation doit être retournée ; si <paramref name="index" /> a la valeur -1, la documentation de la bibliothèque est retournée. </param>
      <param name="pbstrHelpString">Cette méthode retourne un BSTR qui indique le nom de l'élément spécifié.Si l'appelant n'a pas besoin du nom de l'élément, <paramref name="pbstrHelpString" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
      <param name="pdwHelpStringContext">Cette méthode retourne le contexte de localisation de l'aide.Si l'appelant n'a pas besoin du contexte d'aide, <paramref name="pdwHelpStringContext" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
      <param name="pbstrHelpStringDll">Cette méthode retourne un BSTR qui indique le nom qualifié complet du fichier dans lequel se situe la DLL utilisée pour le fichier d'aide.Si l'appelant n'a pas besoin du nom de fichier, <paramref name="pbstrHelpStringDll" /> peut être null.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>Récupère la structure qui contient les attributs de la bibliothèque.</summary>
      <param name="ppTLibAttr">Lorsque cette méthode retourne une valeur, contient une structure comprenant les attributs de la bibliothèque.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>Retourne des statistiques relatives à une bibliothèque de types qui sont requises pour un dimensionnement efficace des tables de hachage.</summary>
      <param name="pcUniqueNames">Pointeur vers un nombre de noms uniques.Si l'appelant n'a pas besoin de cette information, affectez-lui la valeur null.</param>
      <param name="pcchUniqueNames">Cette méthode retourne un pointeur vers une modification du nombre de noms uniques.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Permet à un compilateur client d'effectuer une liaison aux types, variables, constantes et fonctions globales d'une bibliothèque.</summary>
      <param name="ppTComp">Cette méthode retourne une instance de ITypeComp pour ITypeLib.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère la description de type spécifiée dans la bibliothèque.</summary>
      <param name="index">Index de l'interface ITypeInfo à retourner. </param>
      <param name="ppTI">Lorsque cette méthode retourne une valeur, contient un ITypeInfo décrivant le type référencé par <paramref name="index" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>Retourne le nombre de descriptions de types de la bibliothèque de types.</summary>
      <returns>Nombre de descriptions de types de la bibliothèque de types.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Récupère la description de type qui correspond au GUID spécifié.</summary>
      <param name="guid">
        <see cref="T:System.Guid" /> passé par référence, qui représente l'IID de l'interface CLSID de la classe dont les informations de type sont demandées. </param>
      <param name="ppTInfo">Lorsque cette méthode retourne une valeur, contient l'interface ITypeInfo demandée.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Récupère le type d'une description de type.</summary>
      <param name="index">Index de la description de type dans la bibliothèque de types. </param>
      <param name="pTKind">Lorsque cette méthode retourne une valeur, contient une référence à l'énumération TYPEKIND pour la description de type.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>Indique si une chaîne passée contient le nom d'un type ou d'un membre décrit dans la bibliothèque.</summary>
      <returns>true si <paramref name="szNameBuf" /> a été trouvé dans la bibliothèque de types ; sinon, false.</returns>
      <param name="szNameBuf">Chaîne à tester. </param>
      <param name="lHashVal">Valeur de hachage de <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libère la structure <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> obtenue à l'origine à partir de la méthode <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Structure TLIBATTR à libérer. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>Définit les indicateurs applicables aux bibliothèques de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>La bibliothèque de types décrit des contrôles ; elle ne doit pas être affichée dans des explorateurs de types conçus pour des objets non visuels.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>La bibliothèque de types existe sur le disque sous une forme persistante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>La bibliothèque de types ne doit pas être visible aux utilisateurs, même lorsque son utilisation n'est pas restreinte.Elle doit être utilisée par des contrôles.Les hôtes doivent créer une nouvelle bibliothèque de types qui encapsule le contrôle avec des propriétés étendues.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>La bibliothèque de types est restreinte et elle ne doit pas être affichée aux utilisateurs.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>Contient des informations sur la procédure de transfert d'un élément de structure, d'un paramètre ou d'une valeur de retour de fonction entre des processus.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>Représente un pointeur vers une valeur passée entre des processus.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>Représente des valeurs de masque de bits qui décrivent l'élément de structure, le paramètre ou la valeur de retour.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>Décrit comment transférer un élément de structure, un paramètre ou une valeur de retour de fonction entre des processus.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>Le paramètre a des données personnalisées.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>Le paramètre a des comportements par défaut définis.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>Le paramètre passe des informations de l'appelant à l'appelé.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>Le paramètre est l'identificateur local d'une application cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>Le paramètre est facultatif.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>Le paramètre retourne des informations de l'appelé à l'appelant.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>Le paramètre est la valeur de retour du membre.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>Ne spécifie pas si le paramètre passe ou reçoit des informations.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>Fournit la définition managée de la structure STATDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>Représente la valeur d'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> qui détermine le moment auquel le récepteur de notifications est averti des modifications apportées aux données.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>Représente l'interface <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> qui recevra les notifications de modifications.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>Représente le jeton qui identifie de manière unique la connexion de notifications.Ce jeton est retourné par la méthode qui configure la connexion de notifications.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>Représente la structure <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> des données dignes d'intérêt pour le récepteur de notifications.Le récepteur de notifications est averti des modifications apportées aux données spécifiées par cette structure <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>Contient des informations statistiques relatives à un objet de stockage, de flux ou de tableau d'octets ouvert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>Spécifie l'heure du dernier accès à ce stockage, flux ou tableau d'octets. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>Spécifie la taille en octets du flux ou du tableau d'octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>Indique l'identificateur de classe de l'objet de stockage.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>Indique l'heure de création de ce stockage, flux ou tableau d'octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>Indique les types de verrouillage de région pris en charge par le flux ou le tableau d'octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>Indique le mode d'accès spécifié à l'ouverture de l'objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>Indique les bits d'état actuels de l'objet de stockage (la dernière valeur définie par la méthode IStorage::SetStateBits).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>Indique l'heure de la dernière modification de ce stockage, flux ou tableau d'octets.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>Représente un pointeur vers une chaîne se terminant par un caractère null qui contient le nom de l'objet décrit par cette structure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>Indique le type d'objet de stockage qui est une des valeurs de l'énumération STGTY.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>Fournit la définition managée de la structure STGMEDIUM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>Représente un pointeur vers une instance d'interface permettant au processus émetteur de contrôler la manière dont le stockage est libéré lorsque le processus récepteur appelle la fonction ReleaseStgMedium.Si <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, ReleaseStgMedium utilise des procédures par défaut pour libérer le stockage ; sinon, ReleaseStgMedium utilise l'interface IUnknown spécifiée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>Spécifie le type de support de stockage.Les routines de marshaling et d'unmarshaling utilisent cette valeur pour déterminer le membre de l'union qui a été utilisé.Cette valeur doit être l'un des éléments de l'énumération <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>Représente un handle, une chaîne ou un pointeur d'interface que le processus récepteur peut utiliser pour accéder aux données transférées.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>Identifie la plateforme du système d'exploitation cible.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>Le système d'exploitation cible de la bibliothèque de types est Apple Macintosh.Par défaut, tous les champs de données sont alignés sur des limites d'octet pair.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>Le système d'exploitation cible de la bibliothèque de types est un système Windows 16 bits.Par défaut, les champs de données sont compactés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>Le système d'exploitation cible de la bibliothèque de types est un système Windows 32 bits.Par défaut, les champs de données sont alignés naturellement (par exemple, un entier sur 2 octets est aligné sur une limite d'octet pair, un entier sur 4 octets est aligné sur une limite de mot quadruple, et ainsi de suite).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>Le système d'exploitation cible de la bibliothèque de types est un système Windows 64 bits.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>Fournit la définition managée de la structure TYMED.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>Le support de stockage est un métafichier amélioré.Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, le processus de destination doit utiliser DeleteEnhMetaFile pour supprimer la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>Le support de stockage est un fichier sur disque identifié par un chemin d'accès.Si le membre STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, le processus de destination doit utiliser OpenFile pour supprimer le fichier.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>Le support de stockage est un composant GDI (Graphics Device Interface) (HBITMAP).Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, le processus de destination doit utiliser DeleteObject pour supprimer la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>Le support de stockage est un handle de mémoire globale (HGLOBAL).Allouez le handle global possédant l'indicateur GMEM_SHARE.Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, le processus de destination doit utiliser GlobalFree pour libérer la mémoire.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>Le support de stockage est un composant de stockage identifié par un pointeur IStorage.Les données se trouvent dans les flux et les stockages contenus dans cette instance de IStorage.Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> n'est pas null, le processus de destination doit utiliser IStorage::Release pour libérer le composant de stockage.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>Le support de stockage est un objet de flux identifié par un pointeur IStream.Utilisez ISequentialStream::Read pour lire les données.Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> n'est pas null, le processus de destination doit utiliser IStream::Release pour libérer le composant de flux.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>Le support de stockage est un métafichier (HMETAFILE).Utilisez les fonctions Windows ou WIN32 pour accéder aux données du métafichier.Si le membre <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> est null, le processus de destination doit utiliser DeleteMetaFile pour supprimer la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>Aucune donnée n'est passée.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>Contient des attributs d'un UCOMITypeInfo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>Spécifie l'alignement des octets pour une instance de ce type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>Taille d'une instance de ce type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>Taille de la table de méthodes virtuelles (VTBL) de ce type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>Indique le nombre de fonctions sur l'interface décrite par cette structure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>Indique le nombre d'interfaces implémentées sur l'interface décrite par cette structure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>Indique le nombre de variables et de champs de données sur l'interface décrite par cette structure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>GUID des informations de type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>Attributs IDL du type décrit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>Paramètres régionaux des noms de membres et des chaînes de documentation.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>Constante utilisée avec les champs <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> et <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>ID du constructeur ou <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> en l'absence d'un identificateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>ID du destructeur ou <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> en l'absence d'un identificateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>Si <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />, spécifie le type pour lequel ce type est un alias.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>Valeur <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> décrivant le type décrit par ces informations.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>Numéro de version principale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>Numéro de version secondaire.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>Valeur <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> décrivant ces informations.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>Décrit le type d'une variable, le type de retour d'une fonction ou le type d'un paramètre de fonction.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>Si la variable est VT_SAFEARRAY ou VT_PTR, le champ lpValue contient un pointeur vers un TYPEDESC spécifiant le type d'élément.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>Indique le type de variante de l'élément décrit par ce TYPEDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>Définit les propriétés et les attributs d'une description de type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>La classe prend en charge l'agrégation.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Description de type qui décrit un objet Application.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>Les instances du type peuvent être créées à l'aide de ITypeInfo::CreateInstance.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>Le type est un contrôle à partir duquel les autres types sont dérivés ; il ne doit pas être affiché aux utilisateurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>Indique que l'interface dérive directement ou indirectement de IDispatch.Cet indicateur est calculé ; il n'existe pas de langage de description d'objet pour celui-ci.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>L'interface fournit à la fois une liaison IDispatch et VTBL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>Le type ne doit pas être affiché aux navigateurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>Le type est sous licence.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>L'interface ne peut pas ajouter de membres au moment de l'exécution.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>Les types utilisés dans l'interface sont totalement compatibles avec Automation, y compris la prise en charge de la liaison VTBL.Si une interface est de type dual, cet indicateur est défini, ainsi que <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />.Cet indicateur n'est pas autorisé sur les dispinterfaces.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>Le type est prédéfini.L'application cliente doit automatiquement créer une instance unique de l'objet doté de cet attribut.Le nom de la variable pointant vers l'objet est identique au nom de classe de l'objet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>Indique que l'interface va utiliser une bibliothèque de liens dynamiques proxy/stub.Cet indicateur spécifie que l'inscription du proxy de la bibliothèque de types ne doit pas être annulée lors de l'annulation de l'inscription de la bibliothèque de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>L'objet prend en charge IConnectionPointWithDefault et il a des comportements par défaut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>Ne doit pas être accessible à partir de langages de macro.Cet indicateur est destiné à des types de niveau système ou à des types que les explorateurs de types ne doivent pas afficher.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>Indique que la résolution de noms doit être vérifiée avant les enfants dans les interfaces de base, ce qui est l'inverse du comportement par défaut.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>Spécifie divers types de données et de fonctions.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>Type qui est un alias d'un autre type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>Un ensemble d'interfaces de composants implémentés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>Ensemble de méthodes et de propriétés accessibles par l'intermédiaire de IDispatch::Invoke.Par défaut, les interfaces doubles retournent TKIND_DISPATCH.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>Ensemble d'énumérateurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>Type comportant des fonctions virtuelles, toutes pures.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>Marqueur de fin d'énumération.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>Module pouvant uniquement comporter des fonctions et des données statiques (par exemple, une DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>Structure sans méthodes.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>Union de tous les membres ayant un offset de zéro.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>Identifie une bibliothèque de types particulière et fournit une prise en charge de localisation des noms de membres.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>Représente un identificateur global unique de bibliothèque d'une bibliothèque de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>Représente l'ID des paramètres régionaux d'une bibliothèque de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>Représente la plateforme matérielle cible d'une bibliothèque de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>Représente des indicateurs de bibliothèque.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>Représente le numéro de version principale d'une bibliothèque de types.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>Représente le numéro de version secondaire d'une bibliothèque de types.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>Décrit une variable, une constante ou des données membres.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>Contient des informations sur une variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>Contient le type de variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>Ce champ est réservé à une utilisation ultérieure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>Indique l'ID de membre d'une variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>Définit comment marshaler une variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>Définit les propriétés d'une variable.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>Contient des informations sur une variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>Décrit une constante symbolique.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>Indique l'offset de cette variable dans l'instance.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>Identifie les constantes qui définissent les propriétés d'une variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>Variable qui prend en charge la liaison de données.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>La variable est la propriété unique qui représente le mieux l'objet.Seule une variable dans les informations de type peut avoir cet attribut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>Permet une optimisation dans laquelle le compilateur recherche un membre nommé « xyz » sur le type « abc ».Si ce membre est trouvé et s'il est marqué comme fonction d'accesseur pour un élément de la collection par défaut, un appel à cette fonction membre est généré.Autorisé sur les membres dans des dispinterfaces et des interfaces ; non autorisé sur des modules.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>La variable est affichée à l'utilisateur comme pouvant être liée.Vous devez également définir <see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>Même si elle existe et peut être liée, la variable ne doit pas être visible pour l'utilisateur dans un explorateur.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>La variable est mappée comme des propriétés individuelles pouvant être liées.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>La variable s'affiche dans un explorateur d'objets, mais pas dans un explorateur de propriétés.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>L'assignation à la variable ne doit pas être autorisée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>Marque l'interface comme ayant des comportements par défaut.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>Lorsque défini, toute tentative de modification directe de la propriété entraîne un appel à IPropertyNotifySink::OnRequestEdit.L'implémentation de OnRequestEdit détermine si la modification est acceptée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>La variable ne doit pas être accessible à partir de langages de macro.Cet indicateur est destiné à des variables de niveau système ou à des variables dont vous ne souhaitez pas permettre l'affichage dans des explorateurs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>La variable retourne un objet qui est une source d'événements.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>La variable est l'affichage par défaut dans l'interface utilisateur.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>Définit le type de variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>La structure VARDESC décrit une constante symbolique.Aucune mémoire ne lui est associée.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>La variable est accessible uniquement par l'intermédiaire de IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>La variable est un champ ou un membre du type.Elle existe à un offset fixe au sein de chaque instance du type.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>Il n'existe qu'une instance de la variable.</summary>
    </member>
  </members>
</doc>