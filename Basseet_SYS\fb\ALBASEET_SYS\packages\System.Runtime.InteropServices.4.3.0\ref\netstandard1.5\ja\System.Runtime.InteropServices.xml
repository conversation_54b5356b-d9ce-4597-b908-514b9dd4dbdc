﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>データの単位が、データ サイズの倍数ではないアドレスから読み込まれたり、アドレスに書き込まれたりしたときにスローされる例外。このクラスは継承できません。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>
        <see cref="T:System.DataMisalignedException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、<see cref="T:System.DataMisalignedException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーを記述する <see cref="T:System.String" /> オブジェクト。<paramref name="message" /> の内容は、ユーザーが理解できる内容にします。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと基になる例外を使用して、<see cref="T:System.DataMisalignedException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーを記述する <see cref="T:System.String" /> オブジェクト。<paramref name="message" /> の内容は、ユーザーが理解できる内容にします。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
      <param name="innerException">現在の <see cref="T:System.DataMisalignedException" /> の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>DLL インポートで指定した DLL が見つからない場合にスローされる例外。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>
        <see cref="T:System.DllNotFoundException" /> クラスの新しいインスタンスを既定のプロパティを使用して初期化します。</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、<see cref="T:System.DllNotFoundException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.DllNotFoundException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>不足している <see cref="T:System.Object" /> を表します。このクラスは継承できません。</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>
        <see cref="T:System.Reflection.Missing" /> クラスの唯一のインスタンスを表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>配列とオフセットを、指定した配列内にカプセル化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 構造体の新しいインスタンスを初期化します。</summary>
      <param name="array">マネージ配列。</param>
      <param name="offset">プラットフォーム呼び出しによって渡される要素のオフセット (バイト単位)。</param>
      <exception cref="T:System.ArgumentException">配列は 2 GB を超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>指定したオブジェクトが現在の <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトに一致するかどうかを示します。</summary>
      <returns>オブジェクトが現在の <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> に一致する場合は true。それ以外の場合は false。</returns>
      <param name="obj">現在のインスタンスと比較する対象のオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトが現在のインスタンスに一致するかどうかを示します。</summary>
      <returns>指定された <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトが現在のインスタンスと一致する場合は true。それ以外の場合は false。</returns>
      <param name="obj">このインスタンスと比較する <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> が参照するマネージ配列を返します。</summary>
      <returns>対象のインスタンスが参照するマネージ配列。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>値型に対するハッシュ コードを返します。</summary>
      <returns>対象のインスタンスのハッシュ コード。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> の構築時に指定されたオフセットを返します。</summary>
      <returns>対象のインスタンスのオフセット。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>指定した 2 つの <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトの値が同一かどうかを判断します。</summary>
      <returns>
        <paramref name="a" /> の値が <paramref name="b" /> の値と同じ場合は true。それ以外の場合は false。</returns>
      <param name="a">
        <paramref name="b" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクト。</param>
      <param name="b">
        <paramref name="a" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>指定した 2 つの <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトの値が異なるかどうかを判断します。</summary>
      <returns>
        <paramref name="a" /> の値が <paramref name="b" /> の値と異なる場合は true。それ以外の場合は false。</returns>
      <param name="a">
        <paramref name="b" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクト。</param>
      <param name="b">
        <paramref name="a" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクト。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Unicode 文字を最も近い ANSI 文字に変換するかどうかを制御します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>
        <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" /> プロパティの値を設定して、<see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="BestFitMapping">最適マッピングが有効なことを示す場合は true。それ以外の場合は false。既定値は、true です。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Unicode 文字を ANSI 文字に変換するときの、最適マッピングの動作を取得します。</summary>
      <returns>最適マッピングが有効な場合は true。それ以外の場合は false。既定値は、true です。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>マップできない Unicode 文字 (ANSI の "?" に変換される文字) が見つかったときに、例外をスローするかどうかを指定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>VT_BSTR 型のデータをマネージ コードからアンマネージ コードにマーシャリングします。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>
        <see cref="T:System.Object" /> オブジェクトを指定して、<see cref="T:System.Runtime.InteropServices.BStrWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">ラップされ、VT_BSTR としてマーシャリングされるオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>
        <see cref="T:System.String" /> オブジェクトを指定して、<see cref="T:System.Runtime.InteropServices.BStrWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">ラップされ、VT_BSTR としてマーシャリングされるオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>VT_BSTR 型としてマーシャリングされる、ラップされた <see cref="T:System.String" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> によりラップされるオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>アンマネージ コードで実装されたメソッドを呼び出すために必要な呼び出し規約を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>呼び出し元がスタックを消去します。これを使用すると、varargs で関数を呼び出すことができます。Printf など、受け取るパラメーターの数が可変のメソッドで使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>呼び出し先がスタックを消去します。これは、プラットフォーム呼び出しでアンマネージ関数を呼び出すための既定の規約です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>最初のパラメーターは this ポインターで、レジスタ ECX に格納されます。その他のパラメーターは、スタックにプッシュされます。この呼び出し規約は、アンマネージ DLL からエクスポートしたクラスのメソッドを呼び出すために使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>このメンバーは実際には呼び出し規約ではありません。代わりに、既定のプラットフォーム呼び出し規約を使用します。たとえば、Windows では <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />、Windows CE .NET では <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" /> が既定値になります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>インターフェイスが生成される場合に、COM に公開されるクラスに対して生成されるクラス インターフェイスの型を示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 列挙値を指定して、<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="classInterfaceType">クラスに対して生成するインターフェイスの型について説明します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 列挙体メンバーを使用して、<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="classInterfaceType">クラスに対して生成するインターフェイスの型について説明する <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 値の 1 つ。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>クラスに対して生成するインターフェイスの型について説明する <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 値を取得します。</summary>
      <returns>クラスに対して生成するインターフェイスの型について説明する <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 値。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>クラスに対して生成するクラス インターフェイスの型を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>クラスが COM クライアントに対して遅延バインディングだけをサポートすることを示します。クラスの dispinterface は、要求に応じて自動的に COM クライアントに公開されます。Tlbexp.exe (タイプ ライブラリ エクスポーター) によって生成されるタイプ ライブラリには、dispinterface の型情報は含まれません。これは、クライアントがこのインターフェースの DISPID をキャッシュしないようにするためです。 クライアントがこの dispinterface に対して実行できるのは遅延バインディングだけであるため、<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> に示されているバージョン管理の問題は、このインターフェイスでは発生しません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>クラスに対してデュアル クラス インターフェイスを自動的に生成し、COM に公開することを示します。型情報は、クラス インターフェイスに対して作成され、タイプ ライブラリで公開されます。<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> で説明したようなバージョン管理の制約があるため、AutoDual は使用しないことを強くお勧めします。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>クラスに対してクラス インターフェイスを生成しないことを示します。インターフェイスを明示的に実装していないクラスには、IDispatch インターフェイスを通じた遅延バインディングによってしかアクセスできません。これは、<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> の推奨値です。このクラスが明示的に実装しているインターフェイスを通じて機能を公開するには、ClassInterfaceType.None の使用が唯一の方法です。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>タイプ ライブラリからインポートされるコクラスのクラス ID を指定します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>元のコクラスのクラス ID を使用して、<see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> の新しいインスタンスを初期化します。</summary>
      <param name="coClass">元のコクラスのクラス ID を格納している <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>元のコクラスのクラス ID を取得します。</summary>
      <returns>元のコクラスのクラス ID を格納している <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>イベント ハンドラーの遅延バインディング登録を許可します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>指定された型およびその型のイベント名を使用して、<see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="type">オブジェクトの型。</param>
      <param name="eventName">
        <paramref name="type" /> のイベント名。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>イベント ハンドラーを COM オブジェクトにアタッチします。</summary>
      <param name="target">イベント デリゲートをバインドするターゲット オブジェクト。</param>
      <param name="handler">イベント デリゲート。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>このイベントの属性を取得します。</summary>
      <returns>このイベントの読み取り専用属性。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>このメンバーを宣言するクラスを取得します。</summary>
      <returns>このメンバーを宣言するクラスの <see cref="T:System.Type" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>現在のメンバーの名前を取得します。</summary>
      <returns>このメンバーの名前。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>イベント ハンドラーを COM オブジェクトからデタッチします。</summary>
      <param name="target">イベント デリゲートをバインドするターゲット オブジェクト。</param>
      <param name="handler">イベント デリゲート。</param>
      <exception cref="T:System.InvalidOperationException">このイベントには、パブリックな remove アクセサーがありません。</exception>
      <exception cref="T:System.ArgumentException">渡されたハンドラーを使用できません。</exception>
      <exception cref="T:System.Reflection.TargetException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、<see cref="T:System.Exception" /> を代わりにキャッチします。<paramref name="target" /> パラメーターが null で、イベントが静的ではありません。または<see cref="T:System.Reflection.EventInfo" /> が対象で宣言されていません。</exception>
      <exception cref="T:System.MethodAccessException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.MemberAccessException" /> を代わりにキャッチします。呼び出し元に、メンバーへのアクセス許可がありません。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>COM に公開する既定のインターフェイスを指定します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>COM に公開する既定のインターフェイスとして <see cref="T:System.Type" /> オブジェクトを指定して、<see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="defaultInterface">COM に公開する既定のインターフェイスを示す <see cref="T:System.Type" /> 値。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>COM に公開する既定のインターフェイスを指定する <see cref="T:System.Type" /> オブジェクトを取得します。</summary>
      <returns>COM に公開する既定のインターフェイスを指定する <see cref="T:System.Type" /> オブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>ソース インターフェイスと、コクラスが COM タイプ ライブラリからインポートされるときに生成されるイベント インターフェイスのメソッドを実装するクラスとを識別します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>ソース インターフェイスとイベント プロバイダー クラスを指定して、<see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="SourceInterface">タイプ ライブラリからの元のソース インターフェイスを格納する <see cref="T:System.Type" />。COM はこのインターフェイスを使用してマネージ クラスにコールバックします。</param>
      <param name="EventProvider">イベント インターフェイスのメソッドを実装するクラスを格納する <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>イベント インターフェイスのメソッドを実装するクラスを取得します。</summary>
      <returns>イベント インターフェイスのメソッドを実装するクラスを格納する <see cref="T:System.Type" />。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>タイプ ライブラリから元のソース インターフェイスを取得します。</summary>
      <returns>ソース インターフェイスを格納している <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>COM オブジェクトとの間でイベントを処理する .NET Framework デリゲートの追加および削除を行うためのメソッドを提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>COM オブジェクトから生成されるイベントの呼び出しリストにデリゲートを追加します。</summary>
      <param name="rcw">呼び出し元が応答するイベントの発生元の COM オブジェクト。</param>
      <param name="iid">イベントを発生させるために COM オブジェクトによって使用されるソース インターフェイスの識別子。</param>
      <param name="dispid">ソース インターフェイスのメソッドのディスパッチ識別子。</param>
      <param name="d">COM イベントの発生時に呼び出すデリゲート。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>COM オブジェクトから生成されるイベントの呼び出しリストからデリゲートを削除します。</summary>
      <returns>呼び出しリストから削除されたデリゲート。</returns>
      <param name="rcw">デリゲートがアタッチされている COM オブジェクト。</param>
      <param name="iid">イベントを発生させるために COM オブジェクトによって使用されるソース インターフェイスの識別子。</param>
      <param name="dispid">ソース インターフェイスのメソッドのディスパッチ識別子。</param>
      <param name="d">呼び出しリストから削除するデリゲート。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>COM メソッドの呼び出しによって、認識されない HRESULT が返された場合にスローされる例外です。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.COMException" /> クラスの新しいインスタンスを既定値で初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>メッセージを指定して、<see cref="T:System.Runtime.InteropServices.COMException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.COMException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>指定したメッセージとエラー コードを使用して、<see cref="T:System.Runtime.InteropServices.COMException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外が発生した理由を示すメッセージ。</param>
      <param name="errorCode">この例外に関連付けられているエラー コード (HRESULT) 値。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>属性の型が以前に COM で定義されたことを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />の新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>インターフェイスを COM に公開する方法を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>インターフェイスがデュアル インターフェイスとして COM に公開されていることを示します。このインターフェイスでは事前バインディングと遅延バインディングの両方が有効できます。既定値は <see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" /> です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>インターフェイスが dispinterface として COM に公開されていることを示します。このインターフェイスでは遅延バインディングのみが有効です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>インターフェイスが Windows ランタイム インターフェイスとして COM に公開されていることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>インターフェイスが IUnknown から派生するインターフェイスとして COM に公開されていることを示します。このインターフェイスでは事前バインディングのみが有効です。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>COM メンバーの型について説明します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>メンバーは通常のメソッドです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>メンバーはプロパティを取得します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>メンバーはプロパティを設定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>属性付きクラスの COM イベント ソースとして公開されるインターフェイスのリストを識別します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>イベント ソース インターフェイスの名前を指定して、<see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sourceInterfaces">イベント ソース インターフェイスの完全修飾名の null 区切りリスト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>ソース インターフェイスとして使用する型を指定して、<see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sourceInterface">ソース インターフェイスの <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>ソース インターフェイスとして使用する型を指定して、<see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sourceInterface1">既定のソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">ソース インターフェイスの <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>ソース インターフェイスとして使用する型を指定して、ComSourceInterfacesAttribute クラスの新しいインスタンスを初期化します。</summary>
      <param name="sourceInterface1">既定のソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">ソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">ソース インターフェイスの <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>ソース インターフェイスとして使用する型を指定して、<see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sourceInterface1">既定のソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">ソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">ソース インターフェイスの <see cref="T:System.Type" />。</param>
      <param name="sourceInterface4">ソース インターフェイスの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>イベント ソース インターフェイスの完全限定名を取得します。</summary>
      <returns>イベント ソース インターフェイスの完全限定名。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>マーシャラーが VT_CY としてマーシャリングする必要があるオブジェクトをラップします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>ラップされ、型 VT_CY としてマーシャリングされる Decimal を指定して、<see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="obj">ラップされ、VT_CY としてマーシャリングされる Decimal。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>ラップされ、型 VT_CY としてマーシャリングされる Decimal を格納しているオブジェクトを指定して、<see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="obj">ラップされ、VT_CY としてマーシャリングされる Decimal を格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> パラメーターが <see cref="T:System.Decimal" /> 型ではありません。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>型 VT_CY としてマーシャリングされる、ラップされたオブジェクトを取得します。</summary>
      <returns>型 VT_CY としてマーシャリングされる、ラップされたオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> メソッドの IUnknown::QueryInterface 呼び出しで <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> インターフェイスを使用できるかどうかを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>IUnknown::QueryInterface メソッドの呼び出しは <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> インターフェイスを使用できます。この値を使用する場合、<see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> メソッド オーバーロードは、<see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" /> オーバーロードと同じように動作します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>IUnknown::QueryInterface メソッドの呼び出しは <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> インターフェイスを無視する必要があります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> メソッドの戻り値を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>特定のインターフェイス ID のインターフェイスを使用できません。この場合、返されるインターフェイスは null です。IUnknown::QueryInterface の呼び出し元に E_NOINTERFACE が返されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> メソッドから返されるインターフェイス ポインターは、IUnknown::QueryInterface の結果として使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>カスタムの QueryInterface は使用されていません。代わりに、IUnknown::QueryInterface の既定の実装を使用する必要があります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>
        <see cref="T:System.Runtime.InteropServices.CharSet" /> 列挙体の値を指定します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.CharSet" /> 値を使用して、<see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="charSet">
        <see cref="T:System.Runtime.InteropServices.CharSet" /> 値の 1 つ。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>
        <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> の任意の呼び出しに対する <see cref="T:System.Runtime.InteropServices.CharSet" /> の既定値を取得します。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> の任意の呼び出しに対する <see cref="T:System.Runtime.InteropServices.CharSet" /> の既定値。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>プラットフォーム呼び出しに関数を提供する DLL の検索に使用するパスを指定します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>プラットフォームの対象の検索を呼び出すときに使用するパスを指定して、<see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="paths">LoadLibraryEx 関数がプラットフォーム呼び出しの間に検索するパスを指定する列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>LoadLibraryEx 関数がプラットフォーム呼び出しの間に検索するパスを指定する列挙値のビットごとの組み合わせを取得します。</summary>
      <returns>プラットフォーム呼び出しの検索パスを指定する列挙値のビットごとの組み合わせ。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>既定のパラメーターをサポートする言語から呼び出された場合に、パラメーターの既定値を設定します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>パラメーターの既定値を使用して、<see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">パラメーターの既定値を表すオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>パラメーターの既定値を取得します。</summary>
      <returns>パラメーターの既定値を表すオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>マーシャラーが VT_DISPATCH としてマーシャリングする必要があるオブジェクトをラップします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> クラスの新しいインスタンスを、ラップするオブジェクトを使用して初期化します。</summary>
      <param name="obj">ラップして <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" /> に変換するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> がクラスまたは配列ではありません。または<paramref name="obj" /> では、IDispatch がサポートされません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="obj" /> パラメーターは、値 false を渡された <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 属性でマークされました。または<paramref name="obj" /> パラメーターは、値 false を渡された <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 属性でマークされた型から継承します。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>
        <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> がラップするオブジェクトを取得します。</summary>
      <returns>オブジェクトは <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> によってラップされます。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>メソッド、フィールド、またはプロパティの COM ディスパッチ ID (DISPID) を指定します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>指定した DISPID を使用して、DispIdAttribute クラスの新しいインスタンスを初期化します。</summary>
      <param name="dispId">メンバーの DISPID。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>メンバーの DISPID を取得します。</summary>
      <returns>メンバーの DISPID。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>属性付きメソッドがアンマネージ DLL (Dynamic-Link Library) によって静的エントリ ポイントとして公開されることを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> クラスの新しいインスタンスを、インポートするメソッドを格納した DLL の名前を使用して初期化します。</summary>
      <param name="dllName">アンマネージ メソッドを格納する DLL の名前。DLL がアセンブリに含まれている場合、これにはアセンブリの表示名を含めることができます。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Unicode 文字を ANSI 文字に変換するときの、最適マッピング動作のオン/オフを切り替えます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>エントリ ポイントの呼び出し規約を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>文字列パラメーターをメソッドにマーシャリングし、名前マングルを制御する方法を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>呼び出す DLL エントリ ポイントの名前または序数を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>
        <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> フィールドで、指定された名前以外のエントリ ポイント名をアンマネージ DLL から共通言語ランタイムに検索させるかどうかを制御します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>戻り値が HRESULT または retval であるアンマネージ メソッドを直接変換するか、戻り値 HRESULT または retval を自動的に例外に変換するかを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>属性付きメソッドから戻る前に、呼び出し先が SetLastError Win32 API 関数を呼び出すかどうかを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>マップできない Unicode 文字 (ANSI の "?" に変換される文字) が見つかったときに、例外をスローするかどうかを指定します。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>エントリ ポイントを含む DLL ファイルの名前を取得します。</summary>
      <returns>エントリ ポイントを含む DLL ファイルの名前。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>プラットフォーム呼び出しに関数を提供する DLL の検索に使用するパスを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>DLL 検索パスのアプリケーション ディレクトリを含めます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>アセンブリの依存関係を検索する場合、アセンブリ自体を含むディレクトリを含め、そのディレクトリを最初に検索します。この値は .NET Framework で、パスが Win32 LoadLibraryEx 関数に渡される前に使用されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>アプリケーション ディレクトリを検索し、次に LOAD_WITH_ALTERED_SEARCH_PATH フラグを持つ Win32 LoadLibraryEx 関数を呼び出します。この値は、他の値が指定されていない場合は無視されます。<see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 属性をサポートしていないオペレーティング システムではこの値を使用し、他の値は無視されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>アプリケーション ディレクトリ、%WinDir%\System32 ディレクトリ、および DLL 検索パスのユーザー ディレクトリを含めます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>DLL 検索パスの %WinDir%\System32 ディレクトリを含めます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>DLL が別のフォルダーを検索する前に位置するフォルダーの DLL 依存関係を検索します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Win32 AddDllDirectory 関数を使用してプロセス全体の検索パスに明示的に追加された任意のパスを含めます。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>マーシャラーが VT_ERROR としてマーシャリングする必要があるオブジェクトをラップします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> クラスの新しいインスタンスを、提供される例外に対応する HRESULT を使用して初期化します。</summary>
      <param name="e">エラー コードに変換する例外。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> クラスの新しいインスタンスを、エラーの HRESULT を使用して初期化します。</summary>
      <param name="errorCode">エラーの HRESULT。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> クラスの新しいインスタンスを、エラーの HRESULT を格納しているオブジェクトを使用して初期化します。</summary>
      <param name="errorCode">エラーの HRESULT を格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="errorCode" /> パラメーターが <see cref="T:System.Int32" /> 型ではありません。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>ラッパーのエラー コードを取得します。</summary>
      <returns>エラーの HRESULT。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>アンマネージ メモリからマネージ オブジェクトにアクセスする手段を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>
        <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" /> ハンドル内のオブジェクトのアドレスを取得します。</summary>
      <returns>
        <see cref="T:System.IntPtr" /> としての固定オブジェクトのアドレス。</returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>指定したオブジェクトに <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> ハンドルを割り当てます。</summary>
      <returns>オブジェクトをガベージ コレクションから保護する新しい <see cref="T:System.Runtime.InteropServices.GCHandle" />。<see cref="T:System.Runtime.InteropServices.GCHandle" /> は、不要になったときに <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> で解放する必要があります。</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> を使用するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>指定したオブジェクトに指定した型のハンドルを割り当てます。</summary>
      <returns>指定した型の新しい <see cref="T:System.Runtime.InteropServices.GCHandle" />。この <see cref="T:System.Runtime.InteropServices.GCHandle" /> は、不要になったときに <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> で解放する必要があります。</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> を使用するオブジェクト。</param>
      <param name="type">作成する <see cref="T:System.Runtime.InteropServices.GCHandle" /> の型を示す、<see cref="T:System.Runtime.InteropServices.GCHandleType" /> 値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>指定の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトが現在の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>指定した <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトが現在の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトと等しい場合は true。それ以外の場合は false。</returns>
      <param name="o">現在の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトと比較する <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> を解放します。</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>マネージ オブジェクトを識別するハンドルから作成された新しい<see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトを返します。</summary>
      <returns>値パラメーターに対応する新しい <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトの作成元のマネージ オブジェクトを識別する <see cref="T:System.IntPtr" /> ハンドル。</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>現在の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトの識別子を返します。</summary>
      <returns>現在の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトの識別子。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>ハンドルが割り当てられているかどうかを示す値を取得します。</summary>
      <returns>ハンドルが割り当てられている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> の 2 つのオブジェクトが等しいかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="a" /> パラメーターと <paramref name="b" /> パラメーターが等しい場合は true。それ以外の場合は false。</returns>
      <param name="a">
        <paramref name="b" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。</param>
      <param name="b">
        <paramref name="a" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> は、内部整数表現を使用して格納されます。</summary>
      <returns>内部整数表現を使用して格納された <see cref="T:System.Runtime.InteropServices.GCHandle" />。</returns>
      <param name="value">変換を要求する対象のハンドルを示す <see cref="T:System.IntPtr" />。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> は、内部整数表現を使用して格納されます。</summary>
      <returns>整数値。</returns>
      <param name="value">整数を要求する対象の <see cref="T:System.Runtime.InteropServices.GCHandle" />。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> の 2 つのオブジェクトが等しくないかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="a" /> パラメーターと <paramref name="b" /> パラメーターが等しくない場合は true。それ以外の場合は false。</returns>
      <param name="a">
        <paramref name="b" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。</param>
      <param name="b">
        <paramref name="a" /> パラメーターと比較する <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>ハンドルが表すオブジェクトを取得または設定します。</summary>
      <returns>ハンドルが表すオブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトの内部整数表現を返します。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクトを表す <see cref="T:System.IntPtr" /> オブジェクト。 </returns>
      <param name="value">内部整数表現の取得元の <see cref="T:System.Runtime.InteropServices.GCHandle" /> オブジェクト。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> クラスが割り当てることのできるハンドルの種類を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>この種類のハンドルは、非透過ハンドルを表します。つまり、このハンドルを使用して、その中に格納されている固定化されたオブジェクトのアドレスを解決することはできません。この種類のハンドルは、オブジェクトを追跡し、ガベージ コレクターによって収集されないようにするために使用できます。マネージ オブジェクトへの、ガベージ コレクターが検出できない唯一の参照をアンマネージ クライアントが保持している場合、この列挙体メンバーを使用すると便利です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>この種類ハンドルは <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> に似ていますが、このハンドルを使用した場合は、固定オブジェクトのアドレスを取得できます。これにより、ガベージ コレクターがそのオブジェクトを移動できなくなるため、ガベージ コレクターの効率は低下することになります。割り当てられたハンドルをできる限り早く解放するには、<see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> メソッドを使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>この種類のハンドルはオブジェクトを追跡するために使用しますが、そのオブジェクトを収集できるようにもします。オブジェクトが収集された場合、<see cref="T:System.Runtime.InteropServices.GCHandle" /> インスタンスの内容は 0 に設定されます。Weak 参照はファイナライザーの実行前に 0 に設定されるため、ファイナライザーがそのオブジェクトを復活させた場合でも、Weak 参照は 0 のままになります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>この種類のハンドルは <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" /> に似ていますが、ファイナライズ中にオブジェクトが復活された場合でも、このハンドルは 0 には設定されません。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>自動 GUID が望ましくない場合に、明示的な <see cref="T:System.Guid" /> を指定します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>指定した GUID を使用して、<see cref="T:System.Runtime.InteropServices.GuidAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="guid">割り当てる <see cref="T:System.Guid" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>クラスの <see cref="T:System.Guid" /> を取得します。</summary>
      <returns>クラスの <see cref="T:System.Guid" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>保留状態のハンドルを追跡し、指定したしきい値に達したときに、強制的にガベージ コレクションを実行します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>名前およびハンドル コレクションを開始するしきい値を使用して、<see cref="T:System.Runtime.InteropServices.HandleCollector" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">コレクターの名前。このパラメーターにより、ハンドルの種類を個別に追跡するコレクターに名前を付けることができます。</param>
      <param name="initialThreshold">コレクションを開始するポイントを指定する値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> パラメーターが 0 未満です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>名前、ハンドル コレクションを開始するしきい値、およびハンドル コレクションを強制的に実行するしきい値を使用して、<see cref="T:System.Runtime.InteropServices.HandleCollector" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">コレクターの名前。このパラメーターにより、ハンドルの種類を個別に追跡するコレクターに名前を付けることができます。</param>
      <param name="initialThreshold">コレクションを開始するポイントを指定する値。</param>
      <param name="maximumThreshold">コレクションを強制的に実行するポイントを指定する値。この値は、使用できるハンドルの最大数に設定する必要があります。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> パラメーターが 0 未満です。または<paramref name="maximumThreshold" /> パラメーターが 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="maximumThreshold" /> パラメーターが <paramref name="initialThreshold" /> パラメーター未満です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>現在のハンドル カウントをインクリメントします。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> プロパティが 0 未満です。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>収集したハンドルの数を取得します。</summary>
      <returns>収集したハンドルの数。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>コレクションを開始するポイントを指定する値を取得します。</summary>
      <returns>コレクションを開始するポイントを指定する値。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>コレクションを強制的に実行するポイントを指定する値を取得します。</summary>
      <returns>コレクションを強制的に実行するポイントを指定する値。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>
        <see cref="T:System.Runtime.InteropServices.HandleCollector" /> オブジェクトの名前を取得します。</summary>
      <returns>この <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> プロパティにより、ハンドルの種類を個別に追跡するコレクターに名前を付けることができます。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>現在のハンドル カウントをデクリメントします。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> プロパティが 0 未満です。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>クライアントが、カスタム マーシャラーによって渡されるアダプター オブジェクトではなく、実際のオブジェクトにアクセスできるようにします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>カスタム マーシャラーによってラップされたオブジェクトにアクセスできるようにします。</summary>
      <returns>アダプター オブジェクトに格納されているオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>IUnknown::QueryInterface(REFIID riid, void **ppvObject) メソッドのカスタムのマネージ実装を開発者が提供できるようにします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>指定したインターフェイス ID に従ってインターフェイスを返します。</summary>
      <returns>IUnknown::QueryInterface のカスタム実装が使用されたかどうかを示す列挙値の 1 つ。</returns>
      <param name="iid">要求されたインターフェイスの GUID。</param>
      <param name="ppv">要求されたインターフェイスへの参照 (このメソッドから制御が戻るとき)。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>呼び出し元から呼び出し先へデータをマーシャリングするが、逆方向にはマーシャリングしないことを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.InAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>COM への公開時に、マネージ インターフェイスがデュアル、ディスパッチ専用、または IUnknown 専用のいずれであるかを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 列挙体メンバーを使用して、<see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="interfaceType">インターフェイスを COM クライアントに公開する方法を記述します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 列挙体メンバーを使用して、<see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="interfaceType">インターフェイスを COM クライアントに公開する方法を記述する <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 値の 1 つ。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>インターフェイスを COM に公開する方法を記述する <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 値を取得します。</summary>
      <returns>インターフェイスを COM に公開する方法を記述する <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 値。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>無効な COM オブジェクトを使用したときにスローされる例外。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>InvalidComObjectException のインスタンスを既定のプロパティを使用して初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>メッセージを使用して InvalidComObjectException のインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>マネージ コードにマーシャリングできないバリアント型の引数が見つかった場合に、マーシャラーによってスローされる例外。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>InvalidOleVariantTypeException クラスの新しいインスタンスを既定値で初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>指定したメッセージを使用して、InvalidOleVariantTypeException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>アンマネージ コードを扱うときに使用できるさまざまなメソッドを提供します。これらのメソッドを使用すると、アンマネージ メモリの割り当て、アンマネージ メモリ ブロックのコピー、マネージ型からアンマネージ型への変換などができます。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>指定したインターフェイスの参照カウントをインクリメントします。</summary>
      <returns>
        <paramref name="pUnk" /> パラメーターの参照カウントの新しい値。</returns>
      <param name="pUnk">インクリメントするインターフェイスの参照カウント。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>COM タスク メモリ アロケーターから、指定したサイズのメモリ ブロックを割り当てます。</summary>
      <returns>割り当てられたメモリ ブロックのアドレスを表す整数。このメモリは、<see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> を使用して解放する必要があります。</returns>
      <param name="cb">割り当てるメモリ ブロックのサイズ。</param>
      <exception cref="T:System.OutOfMemoryException">要求を満たすにはメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>指定したバイト数を使用して、プロセスのアンマネージ メモリからメモリを割り当てます。</summary>
      <returns>新しく割り当てられたメモリへのポインター。このメモリは、<see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> メソッドを使用して解放する必要があります。</returns>
      <param name="cb">メモリ内で必要なバイト数。</param>
      <exception cref="T:System.OutOfMemoryException">要求を満たすにはメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>指定したバイト数へのポインターを使用して、プロセスのアンマネージ メモリからメモリを割り当てます。</summary>
      <returns>新しく割り当てられたメモリへのポインター。このメモリは、<see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> メソッドを使用して解放する必要があります。</returns>
      <param name="cb">メモリ内で必要なバイト数。</param>
      <exception cref="T:System.OutOfMemoryException">要求を満たすにはメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>任意のコンテキストからのランタイム呼び出し可能ラッパー (RCW: Runtime Callable Wrapper) をクリーンアップに使用できるかどうかを示します。</summary>
      <returns>任意の RCW をクリーンアップに使用できる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の 8 ビット符号なし整数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の文字列のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の倍精度浮動小数点数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の 16 ビット符号付き整数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の 32 ビット符号付き整数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の 64 ビット符号付き整数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを 8 ビット符号なし整数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを文字列のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを倍精度浮動小数点数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを 16 ビット符号付き整数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを 32 ビット符号付き整数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを 64 ビット符号付き整数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータをマネージ <see cref="T:System.IntPtr" /> 配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>アンマネージ メモリ ポインターのデータを単精度浮動小数点数のマネージ配列にコピーします。</summary>
      <param name="source">コピー元のメモリ ポインター。</param>
      <param name="destination">コピー先の配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー先の配列内の 0 から始まるインデックス。</param>
      <param name="length">コピーする配列要素の数。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元のマネージ <see cref="T:System.IntPtr" /> 配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" />、または <paramref name="length" /> が null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>1 次元の単精度浮動小数点数のマネージ配列のデータをアンマネージ メモリ ポインターにコピーします。</summary>
      <param name="source">コピー元の 1 次元配列。</param>
      <param name="startIndex">コピーの開始位置となる、コピー元の配列内の 0 から始まるインデックス。</param>
      <param name="destination">コピー先のメモリ ポインター。</param>
      <param name="length">コピーする配列要素の数。 </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> および <paramref name="length" /> は有効ではありません。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" />、または <paramref name="length" /> が null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>指定した COM オブジェクトを使用してマネージ オブジェクトを集約します。</summary>
      <returns>マネージ オブジェクトの内部 IUnknown ポインター。</returns>
      <param name="pOuter">外部 IUnknown ポインター。</param>
      <param name="o">集約するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> は Windows ランタイム オブジェクトです。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した COM オブジェクトを使用して、指定した型のマネージ オブジェクトを集約します。</summary>
      <returns>マネージ オブジェクトの内部 IUnknown ポインターです。 </returns>
      <param name="pOuter">外側の IUnknown ポインター。</param>
      <param name="o">集約するマネージ オブジェクト。</param>
      <typeparam name="T">集約するマネージ オブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> は Windows ランタイム オブジェクトです。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>指定した型のオブジェクト内の、指定した COM オブジェクトをラップします。</summary>
      <returns>指定した型のインスタンスとなる新しくラップされたオブジェクト。</returns>
      <param name="o">ラップされるオブジェクト。</param>
      <param name="t">作成するラッパーの型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> は __ComObject から派生する必要があります。または<paramref name="t" /> が Windows ランタイム 型です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> パラメーターが null です。</exception>
      <exception cref="T:System.InvalidCastException">必要なすべてのインターフェイスをサポートしていないため、<paramref name="o" /> は指定された型に変換できません。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した型のオブジェクト内の、指定した COM オブジェクトをラップします。</summary>
      <returns>新しくラップされたオブジェクト。 </returns>
      <param name="o">ラップされるオブジェクト。</param>
      <typeparam name="T">ラップするオブジェクトの型。</typeparam>
      <typeparam name="TWrapper">返すオブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> は __ComObject から派生する必要があります。または<paramref name="T" /> が Windows ランタイム 型です。</exception>
      <exception cref="T:System.InvalidCastException">必要なすべてのインターフェイスをサポートしていないため、<paramref name="o" /> は <paramref name="TWrapper" /> に変換できません。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定したアンマネージ メモリ ブロックが指す、指定した型のすべてのサブ構造体を解放します。</summary>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。 </param>
      <typeparam name="T">形式構造体の型です。これは、<paramref name="ptr" /> パラメーター内のバッファーを削除するために必要なレイアウト情報を提供します。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> のレイアウトが自動レイアウトです。代わりに、sequential または explicit を使用します。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>指定したアンマネージ メモリ ブロックが指す、すべてのサブ構造体を解放します。</summary>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。</param>
      <param name="structuretype">書式指定クラスの型。これは、<paramref name="ptr" /> パラメーター内のバッファーを削除するために必要なレイアウト情報を提供します。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> のレイアウトが自動レイアウトです。代わりに、sequential または explicit を使用します。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>ランタイム呼び出し可能ラッパー (RCW: Runtime Callable Wrapper) の参照カウントを 0 に設定することによって、RCW へのすべての参照を解放します。</summary>
      <returns>
        <paramref name="o" />パラメーターに関連付けられた RCW の参照カウントの新しい値。正常に解放された場合、この値は 0 です。</returns>
      <param name="o">解放する RCW。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> が有効な COM オブジェクトではありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> は null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>COM SysFreeString 関数を使用して、BSTR を解放します。</summary>
      <param name="ptr">解放する BSTR のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>アンマネージ COM タスク メモリ アロケーターによって割り当てられたメモリ ブロックを解放します。</summary>
      <param name="ptr">解放するメモリのアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>以前にプロセスのアンマネージ メモリから割り当てられたメモリを解放します。</summary>
      <param name="hglobal">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> への元の一致する呼び出しによって返されたハンドル。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>指定したオブジェクトの指定したインターフェイスを表す IUnknown インターフェイスへのポインターを返します。カスタム クエリ インターフェイス アクセスは既定で有効になっています。</summary>
      <returns>オブジェクトの指定したインターフェイスを表すインターフェイス ポインター。</returns>
      <param name="o">インターフェイスを提供するオブジェクト。</param>
      <param name="T">要求されるインターフェイスの型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> パラメーターがインターフェイスではありません。または型が COM から参照できません。または<paramref name="T" /> パラメーターがジェネリック型です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> パラメーターが要求されたインターフェイスをサポートしていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> パラメーターが null です。または<paramref name="T" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>指定したオブジェクトの指定したインターフェイスを表す IUnknown インターフェイスへのポインターを返します。カスタム クエリ インターフェイス アクセスは、指定したカスタマイズ モードで制御されます。</summary>
      <returns>オブジェクトのインターフェイスを表すインターフェイス ポインター。</returns>
      <param name="o">インターフェイスを提供するオブジェクト。</param>
      <param name="T">要求されるインターフェイスの型。</param>
      <param name="mode">
        <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> によって提供される IUnknown::QueryInterface カスタマイズを適用するかどうかを示す列挙値のいずれか。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> パラメーターがインターフェイスではありません。または型が COM から参照できません。または<paramref name="T" /> パラメーターがジェネリック型です。</exception>
      <exception cref="T:System.InvalidCastException">オブジェクト <paramref name="o" /> が要求されたインターフェイスをサポートしていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> パラメーターが null です。または<paramref name="T" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した種類のオブジェクトの指定したインターフェイスを表す IUnknown インターフェイスへのポインターを返します。カスタム クエリ インターフェイス アクセスは既定で有効になっています。</summary>
      <returns>
        <paramref name="TInterface" /> インターフェイスを表すインターフェイス ポインター。</returns>
      <param name="o">インターフェイスを提供するオブジェクト。</param>
      <typeparam name="T">
        <paramref name="o" /> の型。</typeparam>
      <typeparam name="TInterface">返すインターフェイスの型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TInterface" /> パラメーターがインターフェイスではありません。または型が COM から参照できません。または<paramref name="T" /> パラメーターがオープン ジェネリック型です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> パラメーターが <paramref name="TInterface" /> インターフェイスをサポートしていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 以上でサポート]アンマネージ関数ポインターを指定した型のデリゲートに変換します。</summary>
      <returns>指定したデリゲート型のインスタンス。</returns>
      <param name="ptr">変換するアンマネージ関数ポインター。</param>
      <typeparam name="TDelegate">返すデリゲートの型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> ジェネリック パラメーターはデリゲートでない、またはオープン ジェネリック型です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>アンマネージ関数ポインターをデリゲートに変換します。</summary>
      <returns>適切なデリゲート型にキャストできるデリゲート インスタンス。</returns>
      <param name="ptr">変換するアンマネージ関数ポインター。</param>
      <param name="t">返されるデリゲートの型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> パラメーターは、デリゲートでもジェネリックでもありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> パラメーターが null です。または<paramref name="t" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>発生した例外の型を識別するコードを取得します。</summary>
      <returns>例外の型。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>指定した HRESULT エラー コードを対応する <see cref="T:System.Exception" /> オブジェクトに変換します。</summary>
      <returns>変換後の HRESULT を表すオブジェクト。</returns>
      <param name="errorCode">変換される HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>例外オブジェクトの IErrorInfo インターフェイスに渡された追加エラー情報を使用して、指定した HRESULT エラー コードを対応する <see cref="T:System.Exception" /> オブジェクトに変換します。</summary>
      <returns>変換された HRESULT と <paramref name="errorInfo" /> から取得した情報を表すオブジェクト。</returns>
      <param name="errorCode">変換される HRESULT。</param>
      <param name="errorInfo">エラーの詳細情報を提供する IErrorInfo インターフェイスへのポインター。IntPtr(0) を指定すると、現在の IErrorInfo インターフェイスを使用できます。または IntPtr(-1) を指定すると、現在の IErrorInfo インターフェイスを無視し、エラー コードだけを使用して例外を構築できます。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>アンマネージ コードから呼び出すことができる関数ポインターにデリゲートを変換します。</summary>
      <returns>アンマネージ コードに渡すことができる値。アンマネージ コードはこの値を使用して、基になるマネージ デリゲートを呼び出すことができます。</returns>
      <param name="d">アンマネージ コードに渡されるデリゲート。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="d" /> パラメーターがジェネリック型です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した型のデリゲートをアンマネージ コードから呼び出すことができる関数ポインターに変換します。</summary>
      <returns>アンマネージ コードに渡すことができる値。アンマネージ コードはこの値を使用して、基になるマネージ デリゲートを呼び出すことができます。</returns>
      <param name="d">アンマネージ コードに渡されるデリゲート。</param>
      <typeparam name="TDelegate">変換するデリゲートの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>指定した例外を HRESULT に変換します。</summary>
      <returns>提供された例外に割り当てられる HRESULT。</returns>
      <param name="e">HRESULT に変換する例外。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>
        <see cref="T:System.Runtime.InteropServices.Marshal" /> を使用して実行した Win32 コードが原因の最終エラーに対応する HRESULT を返します。</summary>
      <returns>最終 Win32 エラー コードに対応する HRESULT。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>マネージ オブジェクトから IUnknown インターフェイスを返します。</summary>
      <returns>
        <paramref name="o" /> パラメーターの IUnknown ポインター。</returns>
      <param name="o">要求される IUnknown インターフェイスを持つオブジェクト。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>
        <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> フラグが設定され、プラットフォーム呼び出しを使用して呼び出された、最終アンマネージ関数によって返されるエラー コードを返します。</summary>
      <returns>Win32 SetLastError 関数への呼び出しで最後に設定されたエラー コード。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>オブジェクトを COM VARIANT に変換します。</summary>
      <param name="obj">COM VARIANT の取得対象となるオブジェクト。</param>
      <param name="pDstNativeVariant">
        <paramref name="obj" /> パラメーターに対応する VARIANT を受け取るポインター。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> パラメーターがジェネリック型です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した型のオブジェクトを COM VARIANT に変換します。</summary>
      <param name="obj">COM VARIANT の取得対象となるオブジェクト。</param>
      <param name="pDstNativeVariant">
        <paramref name="obj" /> パラメーターに対応する VARIANT を受け取るポインター。</param>
      <typeparam name="T">変換するオブジェクトの型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>IUnknown インターフェイスへのポインターで COM オブジェクトを表す型のインスタンスを返します。</summary>
      <returns>指定したアンマネージ COM オブジェクトを表すオブジェクト。</returns>
      <param name="pUnk">IUnknown インターフェイスへのポインター。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>COM VARIANT をオブジェクトに変換します。</summary>
      <returns>
        <paramref name="pSrcNativeVariant" /> パラメーターに対応するオブジェクト。</returns>
      <param name="pSrcNativeVariant">COM VARIANT へのポインター。</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> が有効な VARIANT 型ではありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> に、サポートされていない型があります。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 以上でサポート]COM VARIANT を、指定した型のオブジェクトに変換します。</summary>
      <returns>
        <paramref name="pSrcNativeVariant" /> パラメーターに対応する、指定した型のオブジェクト。 </returns>
      <param name="pSrcNativeVariant">COM VARIANT へのポインター。</param>
      <typeparam name="T">COM VARIANT の変換先の型。</typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> が有効な VARIANT 型ではありません。 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> に、サポートされていない型があります。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>COM VARIANT の配列をオブジェクトの配列に変換します。</summary>
      <returns>
        <paramref name="aSrcNativeVariant" /> に対応するオブジェクト配列。</returns>
      <param name="aSrcNativeVariant">COM VARIANT の配列の最初の要素へのポインター。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 内の COM VARIANT の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> は負の値です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[.NET Framework 4.5.1 以上でサポート]COM VARIANT の配列を指定した型の配列に変換します。</summary>
      <returns>
        <paramref name="aSrcNativeVariant" /> に対応する <paramref name="T" /> オブジェクトの配列。 </returns>
      <param name="aSrcNativeVariant">COM VARIANT の配列の最初の要素へのポインター。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 内の COM VARIANT の数。</param>
      <typeparam name="T">返す配列の型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> は負の値です。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>ユーザー定義のメソッドを格納している、仮想関数テーブル (V テーブルまたは VTBL) の最初のスロットを取得します。</summary>
      <returns>ユーザー定義のメソッドを格納している最初の VTBL スロット。IUnknownに基づくインターフェイスの場合、最初のスロットは 3 です。IDispatch に基づくインターフェイスの場合、最初のスロットは 7 です。</returns>
      <param name="t">インターフェイスを表す型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> は COM から参照できません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>指定したクラス ID (CLSID) に関連付けられている型を返します。</summary>
      <returns>CLSID が有効かどうかに関係なく System.__ComObject。</returns>
      <param name="clsid">取得する型の CLSID。 </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>ITypeInfo オブジェクトで表される型の名前を取得します。</summary>
      <returns>
        <paramref name="typeInfo" /> パラメーターが指す型の名前。</returns>
      <param name="typeInfo">ITypeInfo ポインターを表すオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeInfo" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>指定した IUnknown インターフェイスの一意のランタイム呼び出し可能ラッパー (RCW: Runtime Callable Wrapper) オブジェクトを作成します。</summary>
      <returns>指定した IUnknown インターフェイスの一意の RCW。</returns>
      <param name="unknown">IUnknown インターフェイスへのマネージ ポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>指定したオブジェクトが COM オブジェクトを表すかどうかを示します。</summary>
      <returns>
        <paramref name="o" /> パラメーターが COM 型である場合は true。それ以外の場合は false。</returns>
      <param name="o">確認する対象のオブジェクト。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定されたマネージ クラスのアンマネージ フォームのフィールド オフセットを返します。</summary>
      <returns>プラットフォーム呼び出しによって宣言される指定したクラス内の <paramref name="fieldName" /> パラメーターのバイト単位のオフセット。 </returns>
      <param name="fieldName">
        <paramref name="T" /> 型内のフィールドの名前。</param>
      <typeparam name="T">マネージ値型または書式指定参照型。クラスには <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 属性を適用する必要があります。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>マネージ クラスのアンマネージ フォームのフィールド オフセットを返します。</summary>
      <returns>プラットフォーム呼び出しによって宣言される指定したクラス内の <paramref name="fieldName" /> パラメーターのバイト単位のオフセット。</returns>
      <param name="t">マネージ クラスを指定する値型または書式指定参照型。<see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> をクラスに適用する必要があります。</param>
      <param name="fieldName">
        <paramref name="t" /> パラメーター内のフィールド。</param>
      <exception cref="T:System.ArgumentException">クラスを構造体としてエクスポートできません。またはフィールドが非パブリックです。.NET Framework Version 2.0 から開始すると、フィールドはプライベートになる場合があります。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>アンマネージ ANSI 文字列から最初の null 文字に遭遇するまでのすべての文字をマネージ <see cref="T:System.String" /> にコピーし、各 ANSI 文字を Unicode に拡張します。</summary>
      <returns>アンマネージ ANSI 文字列のコピーを保持するマネージ文字列。<paramref name="ptr" /> が null の場合、このメソッドは null 文字列を返します。</returns>
      <param name="ptr">アンマネージ文字列の最初の文字のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>マネージ <see cref="T:System.String" /> を割り当て、アンマネージ ANSI 文字列から指定された数の文字をコピーし、各 ANSI 文字を Unicode に変換します。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターの値が null でない場合は、ネイティブ ANSI 文字列のコピーを保持するマネージ文字列。それ以外の場合は、null。</returns>
      <param name="ptr">アンマネージ文字列の最初の文字のアドレス。</param>
      <param name="len">コピーする入力文字列のバイト数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> が 0 未満です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>マネージ <see cref="T:System.String" /> を割り当て、アンマネージ メモリに格納されている BSTR 文字列をそれにコピーします。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターの値が null でない場合は、アンマネージ文字列のコピーを保持するマネージ文字列。それ以外の場合、このメソッドは null を返します。</returns>
      <param name="ptr">アンマネージ文字列の最初の文字のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>マネージ <see cref="T:System.String" /> を割り当て、アンマネージ Unicode 文字列から最初の null 文字に遭遇するまでの文字をすべてコピーします。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターの値が null でない場合は、アンマネージ文字列のコピーを保持するマネージ文字列。それ以外の場合、このメソッドは null を返します。</returns>
      <param name="ptr">アンマネージ文字列の最初の文字のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>マネージ <see cref="T:System.String" /> を割り当て、アンマネージ Unicode 文字列から指定された数の文字をコピーします。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターの値が null でない場合は、アンマネージ文字列のコピーを保持するマネージ文字列。それ以外の場合、このメソッドは null を返します。</returns>
      <param name="ptr">アンマネージ文字列の最初の文字のアドレス。</param>
      <param name="len">コピーする Unicode 文字の数。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 以上でサポート]アンマネージ メモリ ブロックから、ジェネリック型パラメーターによって指定された型の、新しく割り当てられたマネージ オブジェクトにデータをマーシャリングします。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターが指すデータを格納しているマネージ オブジェクト。</returns>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。</param>
      <typeparam name="T">データをコピーする先のオブジェクトの型。これは、書式指定クラスまたは構造体である必要があります。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> のレイアウトがシーケンシャルまたは明示的ではありません。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="T" /> で指定したクラスがアクセスできる既定のコンストラクターがありません。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>アンマネージ メモリ ブロックからマネージ オブジェクトにデータをマーシャリングします。</summary>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。</param>
      <param name="structure">データのコピー先のオブジェクト。これは、書式指定クラスのインスタンスである必要があります。</param>
      <exception cref="T:System.ArgumentException">構造体のレイアウトが sequential または explicit ではありません。または構造体がボックス化された値型です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>アンマネージ メモリ ブロックから、指定した型の、新しく割り当てられたマネージ オブジェクトにデータをマーシャリングします。</summary>
      <returns>
        <paramref name="ptr" /> パラメーターが指すデータを格納しているマネージ オブジェクト。</returns>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。</param>
      <param name="structureType">作成するオブジェクトの型。このオブジェクトは、書式指定クラスまたは構造体を表す必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> パラメーターのレイアウトが sequential または explicit ではありません。または<paramref name="structureType" /> パラメーターがジェネリック型です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> は null です。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="structureType" /> で指定したクラスがアクセスできる既定のコンストラクターがありません。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]アンマネージ メモリ ブロックから、指定した型のマネージ オブジェクトにデータをマーシャリングします。</summary>
      <param name="ptr">アンマネージ メモリ ブロックへのポインター。</param>
      <param name="structure">データのコピー先のオブジェクト。</param>
      <typeparam name="T">
        <paramref name="structure" /> の型。書式が設定されたクラスである必要があります。</typeparam>
      <exception cref="T:System.ArgumentException">構造体のレイアウトが sequential または explicit ではありません。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>COM オブジェクトから指定したインターフェイスへのポインターを要求します。</summary>
      <returns>呼び出しの成功または失敗を示す HRESULT。</returns>
      <param name="pUnk">照会するインターフェイス。</param>
      <param name="iid">要求したインターフェイスのインターフェイス ID (IID: Interface Identifier)。</param>
      <param name="ppv">このメソッドが返されるときに、返されたインターフェイスへの参照を格納します。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>アンマネージ メモリから 1 バイトを読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られたバイト。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のアドレス。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリから、指定したオフセット (またはインデックス) の 1 バイトを読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られたバイト。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>アンマネージ メモリから、指定したオフセット (またはインデックス) の 1 バイトを読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られたバイト。</returns>
      <param name="ptr">ソース オブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>アンマネージ メモリから 16 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られた 16 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のアドレス。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 16 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた 16 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 16 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた 16 ビット符号付き整数。</returns>
      <param name="ptr">ソース オブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>アンマネージ メモリから 32 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られた 32 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のアドレス。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 32 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られた 32 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 32 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた 32 ビット符号付き整数。</returns>
      <param name="ptr">ソース オブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>アンマネージ メモリから 64 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られた 64 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のアドレス。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 64 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた 64 ビット符号付き整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットから 64 ビット符号付き整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた 64 ビット符号付き整数。</returns>
      <param name="ptr">ソース オブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>アンマネージ メモリからプロセッサ ネイティブ サイズの整数を読み取ります。</summary>
      <returns>アンマネージ メモリから読み取られた整数。32 ビット コンピューター上では 32 ビット整数が返され、64 ビット コンピューター上では 64 ビット整数が返されます。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のアドレス。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリの指定されたオフセットからプロセッサ ネイティブ サイズの整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた整数。</returns>
      <param name="ptr">読み込み元となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>アンマネージ メモリからプロセッサ ネイティブ サイズの整数を読み取ります。</summary>
      <returns>アンマネージ メモリの指定されたオフセットから読み取られた整数。</returns>
      <param name="ptr">ソース オブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">読み取りの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> を使用して割り当てられたメモリ ブロックのサイズを変更します。</summary>
      <returns>再割り当てされたメモリ ブロックのアドレスを表す整数。このメモリは、<see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> を使用して解放する必要があります。</returns>
      <param name="pv">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> を使用して割り当てられたメモリへのポインター。</param>
      <param name="cb">割り当てられたブロックの新しいサイズ。</param>
      <exception cref="T:System.OutOfMemoryException">要求を満たすにはメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> を使用して割り当てられたメモリ ブロックのサイズを変更します。</summary>
      <returns>再割り当てされたメモリへのポインター。このメモリは、<see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> を使用して解放する必要があります。</returns>
      <param name="pv">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> を使用して割り当てられたメモリへのポインター。</param>
      <param name="cb">割り当てられたブロックの新しいサイズ。これはポインターではありません。<see cref="T:System.IntPtr" /> 型にキャストされた、要求しているバイト数です。ポインターを渡すと、サイズとして扱われます。</param>
      <exception cref="T:System.OutOfMemoryException">要求を満たすにはメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>指定したインターフェイスの参照カウントをデクリメントします。</summary>
      <returns>
        <paramref name="pUnk" /> パラメーターで指定したインターフェイスの参照カウントの新しい値。</returns>
      <param name="pUnk">解放するインターフェイス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>指定した COM オブジェクトに関連付けられている指定したランタイム呼び出し可能ラッパー (RCW: Runtime Callable Wrapper) の参照カウントをデクリメントします。</summary>
      <returns>
        <paramref name="o" /> に関連付けられている RCW の参照カウントの新しい値。この値は通常 0 です。RCW は、ラップされた COM オブジェクトへの参照を、それを呼び出しているマネージ クライアントの数に関係なく、1 つしか保持しないためです。</returns>
      <param name="o">解放する COM オブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> が有効な COM オブジェクトではありません。</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> は null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[.NET Framework 4.5.1 以上でサポート]アンマネージ型のサイズを返します (バイト単位)。</summary>
      <returns>サイズ、<paramref name="T" /> ジェネリック型パラメーターで指定される型の、バイト単位のサイズです。</returns>
      <typeparam name="T">サイズが返される型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>オブジェクトのアンマネージ サイズをバイト単位で返します。</summary>
      <returns>アンマネージ コードの指定したオブジェクトのサイズ。</returns>
      <param name="structure">サイズが返されるオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>アンマネージ型のサイズを返します (バイト単位)。</summary>
      <returns>アンマネージ コードの指定した型のサイズ。</returns>
      <param name="t">サイズが返される型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> パラメーターがジェネリック型です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> パラメーターが null です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定された型のオブジェクトのアンマネージのサイズをバイト数で返します。</summary>
      <returns>アンマネージ コードの指定したオブジェクトのサイズ (バイト単位)。</returns>
      <param name="structure">サイズが返されるオブジェクト。</param>
      <typeparam name="T">
        <paramref name="structure" /> パラメーターの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>BSTR を割り当て、それにマネージ <see cref="T:System.String" /> の内容をコピーします。</summary>
      <returns>BSTR へのアンマネージ ポインター。<paramref name="s" /> が null の場合は 0。</returns>
      <param name="s">コピーするマネージ文字列。</param>
      <exception cref="T:System.OutOfMemoryException">使用できるメモリが不足しています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> の長さが範囲を超えています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>アンマネージ COM タスク アロケーターから割り当てられたメモリ ブロックに、マネージ <see cref="T:System.String" /> の内容をコピーします。</summary>
      <returns>文字列に割り当てられたメモリ ブロックへのポインターを表す整数。<paramref name="s" /> が null の場合は 0。</returns>
      <param name="s">コピーされるマネージ文字列。</param>
      <exception cref="T:System.OutOfMemoryException">使用できるメモリが不足しています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> パラメーターがオペレーティング システムで許可されている最大長を超えています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>アンマネージ COM タスク アロケーターから割り当てられたメモリ ブロックに、マネージ <see cref="T:System.String" /> の内容をコピーします。</summary>
      <returns>文字列に割り当てられたメモリ ブロックへのポインターを表す整数。s が null の場合は 0。</returns>
      <param name="s">コピーされるマネージ文字列。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> パラメーターがオペレーティング システムで許可されている最大長を超えています。</exception>
      <exception cref="T:System.OutOfMemoryException">使用できるメモリが不足しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>マネージ <see cref="T:System.String" /> の内容をアンマネージ メモリにコピーし、コピー時に ANSI 形式に変換します。</summary>
      <returns>
        <paramref name="s" /> のコピー先となったアンマネージ メモリ内のアドレス。<paramref name="s" /> が null の場合は 0。</returns>
      <param name="s">コピーされるマネージ文字列。</param>
      <exception cref="T:System.OutOfMemoryException">使用できるメモリが不足しています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> パラメーターがオペレーティング システムで許可されている最大長を超えています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>マネージ <see cref="T:System.String" /> の内容をアンマネージ メモリにコピーします。</summary>
      <returns>
        <paramref name="s" /> のコピー先となったアンマネージ メモリ内のアドレス。<paramref name="s" /> が null の場合は 0。</returns>
      <param name="s">コピーされるマネージ文字列。</param>
      <exception cref="T:System.OutOfMemoryException">メソッドが十分なネイティブ ヒープ メモリを割り当てることができませんでした。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> パラメーターがオペレーティング システムで許可されている最大長を超えています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>マネージ オブジェクトからアンマネージ メモリ ブロックにデータをマーシャリングします。</summary>
      <param name="structure">マーシャリングするデータを保持すマネージ オブジェクト。このオブジェクトは、書式指定クラスの構造体またはインスタンスである必要があります。</param>
      <param name="ptr">このメソッドを呼び出す前に割り当てる必要があるアンマネージ メモリ ブロックへのポインター。</param>
      <param name="fDeleteOld">このメソッドがデータをコピーする前に、<paramref name="ptr" /> パラメーターに対して <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> メソッドを呼び出す場合は true。ブロックには有効なデータを含める必要があります。メモリ ブロックに既にデータが格納されているときに false を渡すと、メモリ リークが発生する可能性があることに注意してください。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> は書式指定クラスではない参照型です。または<paramref name="structure" /> がジェネリック型です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定した型のマネージ オブジェクトから、アンマネージ メモリ ブロックにデータをマーシャリングします。</summary>
      <param name="structure">マーシャリングするデータを保持すマネージ オブジェクト。オブジェクトは、書式指定クラスの構造体またはインスタンスである必要があります。</param>
      <param name="ptr">このメソッドを呼び出す前に割り当てる必要があるアンマネージ メモリ ブロックへのポインター。 </param>
      <param name="fDeleteOld">このメソッドがデータをコピーする前に、<paramref name="ptr" /> パラメーターに対して <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> メソッドを呼び出す場合は true。ブロックには有効なデータを含める必要があります。メモリ ブロックに既にデータが格納されているときに false を渡すと、メモリ リークが発生する可能性があることに注意してください。</param>
      <typeparam name="T">マネージ オブジェクトの型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> は書式指定クラスではない参照型です。</exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>システムの既定の文字サイズを表します。Unicode システムの既定の文字サイズは 2 で、ANSI システムの場合は 1 です。このフィールドは読み取り専用です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>現在のオペレーティング システムでの 2 バイト文字セット (DBCS) サイズの上限をバイト単位で表します。このフィールドは読み取り専用です。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>特定の失敗 HRESULT 値を使用して例外をスローします。</summary>
      <param name="errorCode">必要な例外に対応する HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>指定した IErrorInfo インターフェイスに基づいて、特定のエラー HRESULT を使用して例外をスローします。</summary>
      <param name="errorCode">必要な例外に対応する HRESULT。</param>
      <param name="errorInfo">エラーの詳細情報を提供する IErrorInfo インターフェイスへのポインター。IntPtr(0) を指定すると、現在の IErrorInfo インターフェイスを使用できます。または IntPtr(-1) を指定すると、現在の IErrorInfo インターフェイスを無視し、エラー コードだけを使用して例外を構築できます。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>指定した配列内の、指定したインデックス位置にある要素のアドレスを取得します。</summary>
      <returns>
        <paramref name="arr" /> 内の <paramref name="index" /> のアドレス。</returns>
      <param name="arr">目的の要素を格納している配列。</param>
      <param name="index">必要な要素の、<paramref name="arr" /> パラメーター内のインデックス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[.NET Framework 4.5.1 以上でサポート]指定された型の配列内の、指定したインデックス位置にある要素のアドレスを取得します。</summary>
      <returns>
        <paramref name="arr" /> 内の <paramref name="index" /> のアドレス。</returns>
      <param name="arr">目的の要素を格納している配列。</param>
      <param name="index">
        <paramref name="arr" /> 配列内の目的の要素のインデックス。</param>
      <typeparam name="T">配列の型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>1 バイト値をアンマネージ メモリに書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>1 バイトをアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>1 バイトをアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>文字を 16 ビット整数としてアンマネージ メモリに書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>アンマネージ メモリに 16 ビット整数を書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>16 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">書き込み先となるネイティブ ヒープ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>16 ビット符号付き整数をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>16 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>16 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>アンマネージ メモリに 32 ビット符号付き整数を書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>32 ビット符号付き整数をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>32 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>64 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>アンマネージ メモリに 64 ビット符号付き整数を書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>64 ビット符号付き整数値をアンマネージ メモリの指定されたオフセット位置に書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>アンマネージ メモリの指定されたオフセット位置にプロセッサ ネイティブ サイズの整数値を書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>アンマネージ メモリにプロセッサ ネイティブ サイズの整数を書き込みます。</summary>
      <param name="ptr">書き込み先となるアンマネージ メモリ内のアドレス。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> が認識された形式ではありません。または<paramref name="ptr" /> は null です。または<paramref name="ptr" /> が無効です。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>アンマネージ メモリにプロセッサ ネイティブ サイズの整数を書き込みます。</summary>
      <param name="ptr">対象のオブジェクトのアンマネージ メモリ内のベース アドレス。</param>
      <param name="ofs">書き込みの前に <paramref name="ptr" /> パラメーターに追加される追加のバイト オフセット。</param>
      <param name="val">書き込む値。</param>
      <exception cref="T:System.AccessViolationException">ベース アドレス (<paramref name="ptr" />) にオフセット バイト (<paramref name="ofs" />) を足すと、null または無効なアドレスが生成されます。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> が <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> オブジェクトです。このメソッドは、<see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> パラメーターを受け入れません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" /> メソッドを使用して割り当てられた BSTR ポインターを解放します。</summary>
      <param name="s">解放する BSTR のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" /> メソッドを使用して割り当てられたアンマネージ文字列ポインターを解放します。</summary>
      <param name="s">解放するアンマネージ文字列のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" /> メソッドを使用して割り当てられたアンマネージ文字列ポインターを解放します。</summary>
      <param name="s">解放するアンマネージ文字列のアドレス。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" /> メソッドを使用して割り当てられたアンマネージ文字列ポインターを解放します。</summary>
      <param name="s">解放するアンマネージ文字列のアドレス。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" /> メソッドを使用して割り当てられたアンマネージ文字列ポインターを解放します。</summary>
      <param name="s">解放するアンマネージ文字列のアドレス。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>マネージ コードとアンマネージ コード間でデータをマーシャリングする方法を示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 値を使用して、<see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="unmanagedType">マーシャリング後のデータの値。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>指定した <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 列挙体メンバーを使用して、<see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="unmanagedType">マーシャリング後のデータの値。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>アンマネージの <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> または <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> の要素の型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>COM で使用されるアンマネージ iid_is 属性のパラメーター インデックスを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>カスタム マーシャラーに追加の情報を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>カスタム マーシャラーの完全修飾名を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>
        <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> を型として実装します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> の要素の型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> のユーザー定義要素の型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>固定長配列の要素の数、またはインポートする文字列の文字数 (バイト数ではない) を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>COM の size_is のように、0 から始まる配列要素のカウントを格納しているパラメーターを示します。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>マーシャリング後データの <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 値を取得します。</summary>
      <returns>マーシャリング後のデータの <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 値。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>マーシャラーが、サポートしていない <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> を検出した場合にスローする例外。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>MarshalDirectiveException クラスの新しいインスタンスを既定のプロパティを使用して初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、MarshalDirectiveException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を指定するエラー メッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>パラメーターが省略可能であることを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>OptionalAttribute クラスの新しいインスタンスを既定値で初期化します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>COM 相互運用呼び出し中に発生する HRESULT または retval シグネチャ変換を中止することを示します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>着信 SAFEARRAY のランクが、マネージ シグネチャで指定したランクと一致しない場合にスローされる例外。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>SafeArrayTypeMismatchException クラスの新しいインスタンスを既定値で初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>指定したメッセージを使用して、SafeArrayRankMismatchException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>着信 SAFEARRAY の型が、マネージ シグネチャで指定した型と一致しない場合にスローされる例外。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>SafeArrayTypeMismatchException クラスの新しいインスタンスを既定値で初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>指定したメッセージを使用して、SafeArrayTypeMismatchException クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>読み取り用および書き込み用に使用できる管理されたメモリ バッファーを提供します。管理されたバッファーの外部のメモリにアクセスしようとした場合 (アンダーランおよびオーバーラン)、例外が発生します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> クラスの新しいインスタンスを作成し、バッファー ハンドルを安全に解放するかどうかを指定します。</summary>
      <param name="ownsHandle">終了処理中にハンドルを安全に解放する場合は true。安全な解放を行わない場合は false (お勧めしません)。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> オブジェクトからメモリ ブロックのポインターを取得します。</summary>
      <param name="pointer">
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> オブジェクト内からポインターを受け取るための参照渡しされるバイト ポインター。このポインターは、このメソッドを呼び出す前に null に設定する必要があります。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>バッファーのサイズを取得します (バイト単位)。</summary>
      <returns>メモリ バッファー内のバイト数。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>値型の数を指定して、メモリ領域の割り当てサイズを定義します。このメソッドは、<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> インスタンスを使用する前に呼び出す必要があります。</summary>
      <param name="numElements">メモリを割り当てる対象の値型の要素の数。</param>
      <typeparam name="T">メモリを割り当てる対象の値型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> が 0 未満です。または<paramref name="numElements" /> と各要素のサイズを乗算した値が、利用できるアドレス空間を超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>指定された要素数および要素サイズを使用して、メモリ バッファーの割り当てサイズを指定します。このメソッドは、<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> インスタンスを使用する前に呼び出す必要があります。</summary>
      <param name="numElements">バッファー内の要素の数。</param>
      <param name="sizeOfEachElement">バッファー内の各要素のサイズ。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> が 0 未満です。または<paramref name="sizeOfEachElement" /> が 0 未満です。または<paramref name="numElements" /> に <paramref name="sizeOfEachElement" /> を乗算した値が、利用できるアドレス空間を超えています。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>メモリ領域の割り当てサイズを定義します (バイト単位)。このメソッドは、<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> インスタンスを使用する前に呼び出す必要があります。</summary>
      <param name="numBytes">バッファーのバイト数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> が 0 未満です。または<paramref name="numBytes" /> が、利用できるアドレス空間を超えています。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>メモリの指定されたオフセット位置の値型を読み取ります。</summary>
      <returns>メモリから読み取られた値型。</returns>
      <param name="byteOffset">値型を読み取る位置。アライメントの問題を考慮する必要があります。</param>
      <typeparam name="T">読み取る値型。</typeparam>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>メモリの指定したオフセットから指定した数の値型を読み取り、指定したインデックスから始まる配列にそれらを書き込みます。</summary>
      <param name="byteOffset">読み取りを開始する位置。</param>
      <param name="array">データを書き込む先の出力配列。</param>
      <param name="index">書き込みが開始される出力配列内の位置。</param>
      <param name="count">入力配列から読み取って出力配列に書き込む値型の数。</param>
      <typeparam name="T">読み取る値型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">配列の長さからインデックスを減算した値が <paramref name="count" /> 未満です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>
        <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" /> メソッドによって取得されたポインターを解放します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>値型をメモリの指定した位置に書き込みます。</summary>
      <param name="byteOffset">書き込みを開始する位置。アライメントの問題を考慮する必要があります。</param>
      <param name="value">書き込む値。</param>
      <typeparam name="T">書き込む値型。</typeparam>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>入力配列内の指定した位置からバイトを読み取ることによって、指定した数の値型をメモリ位置に書き込みます。</summary>
      <param name="byteOffset">書き込み先のメモリ内の位置。</param>
      <param name="array">入力配列。</param>
      <param name="index">読み取りを開始する配列内のオフセット。</param>
      <param name="count">書き込む値型の数。</param>
      <typeparam name="T">書き込む値型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> または <paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">入力配列の長さから <paramref name="index" /> を減算した値が <paramref name="count" /> 未満です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> メソッドが呼び出されていません。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>構造化例外処理 (SEH: Structured Exception Handling) エラーを表します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SEHException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>指定したメッセージを使用して、<see cref="T:System.Runtime.InteropServices.SEHException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の理由を示すメッセージ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Runtime.InteropServices.SEHException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> パラメーターが null ではない場合、現在の例外は内部例外を処理する catch ブロックで発生します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>例外から回復できるかどうか、および例外がスローされたポイントからコードを続行できるかどうかを示します。</summary>
      <returns>再開可能な例外は実装されていないため、常に false が返されます。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>型の同値のサポートを提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>指定されたスコープと識別子を使用して、<see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scope">1 番目の型の同値文字列。</param>
      <param name="identifier">2 番目の型の同値文字列。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>
        <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> コンストラクターに渡された <paramref name="identifier" /> パラメーターの値を取得します。</summary>
      <returns>コンストラクターの <paramref name="identifier" /> パラメーターの値。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>
        <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> コンストラクターに渡された <paramref name="scope" /> パラメーターの値を取得します。</summary>
      <returns>コンストラクターの <paramref name="scope" /> パラメーターの値。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>マーシャラーが VT_UNKNOWN としてマーシャリングする必要があるオブジェクトをラップします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>ラップするオブジェクトを使用して、<see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="obj">ラップするオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>ラッパーに格納されているオブジェクトを取得します。</summary>
      <returns>ラップされたオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>アンマネージ コードとの間のアンマネージ関数ポインターとして渡されたデリゲート シグネチャのマーシャリング動作を制御します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>呼び出し規約を指定して、<see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="callingConvention">指定した呼び出し規約。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Unicode 文字を ANSI 文字に変換するときの、最適マッピング動作のオン/オフを切り替えます。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>呼び出し規約の値を取得します。</summary>
      <returns>
        <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" /> コンストラクターによって指定された呼び出し規約の値。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>文字列パラメーターをメソッドにマーシャリングする方法を示し、名前マングルを制御します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>属性付きメソッドから戻る前に、呼び出し先が SetLastError Win32 API 関数を呼び出すかどうかを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>マップできない Unicode 文字 (ANSI の "?" に変換される文字) が見つかったときに、例外をスローするかどうかを指定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>パラメーターまたはフィールドをアンマネージ コードにマーシャリングする方法を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>長さを示すプレフィックスを付けた 1 バイトの ANSI 文字列。このメンバーは <see cref="T:System.String" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>実行時にオブジェクトの型を確認し、その型としてオブジェクトをマーシャリングする動的な型。このメンバーは、プラットフォーム呼び出しメソッドにのみ有効です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>4 バイトの Boolean 値 (true != 0、false = 0)。これは Win32 BOOL 型です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>長さを示すプリフィックスを付けた 2 バイトの Unicode 文字列。このメンバー (COM の既定の文字列) は、<see cref="T:System.String" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>
        <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> プロパティを ByValArray に設定した場合、<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> フィールドは、配列の要素数を示すように設定する必要があります。<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> フィールドには、文字列型を区別する必要がある場合に、オプションとして配列要素の <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> を格納できます。この <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> は、要素が構造体にフィールドとして定義されている配列でのみ使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>構造体に定義されているインライン固定長文字配列で使用します。<see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> で使用する文字型は、格納される構造体に適用する <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 属性の引数 <see cref="T:System.Runtime.InteropServices.CharSet" /> によって決定されます。配列のサイズを示す場合は、常に <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> フィールドを使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>通貨型。10 進値を Decimal ではなく、COM 通貨型としてマーシャリングするために、<see cref="T:System.Decimal" /> で使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> または <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> に関連付けられたネイティブな型。この型により、パラメーターはエクスポート先のタイプ ライブラリに HRESULT としてエクスポートされます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>C スタイルの関数ポインターとして使用できる整数値。このメンバーは、<see cref="T:System.Delegate" /> データ型または <see cref="T:System.Delegate" /> から継承した型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Windows ランタイム 文字列。このメンバーは <see cref="T:System.String" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>1 バイト符号付き整数。このメンバーを使用すると、Boolean 値を 1 バイトの C スタイル bool (true = 1、false = 0) に変換できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>2 バイト符号付き整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>4 バイト符号付き整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>8 バイト符号付き整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>COM の IDispatch ポインター (Microsoft Visual Basic 6.0 では Object)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Windows ランタイム インターフェイス ポインター。このメンバーは <see cref="T:System.Object" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>COM インターフェイス ポインター。インターフェイスの <see cref="T:System.Guid" /> は、クラス メタデータから取得します。インターフェイス型を直接指定する場合、またはクラスに適用する場合は既定のインターフェイス型を指定する場合に、このメンバーを使用します。このメンバーは、<see cref="T:System.Object" /> データ型に適用すると、<see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> と同じ動作を生成します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>COM IUnknown ポインター。このメンバーは <see cref="T:System.Object" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>C スタイル配列の最初の要素へのポインター。マネージ コードからアンマネージ コードにマーシャリングする場合、配列長はマネージ配列長によって決定されます。アンマネージ コードからマネージ コードにマーシャリングする場合、配列の長さは <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> フィールドと <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" /> フィールドによって決まります。文字列の型を区別する必要がある場合は、配列内の要素のアンマネージ型も考慮されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>終端が null の 1 バイトの ANSI 文字列。このメンバーは、<see cref="T:System.String" /> データ型および <see cref="T:System.Text.StringBuilder" /> データ型で使用できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>マネージ書式指定クラスをマーシャリングするときに使用する C スタイル構造体へのポインター。このメンバーは、プラットフォーム呼び出しメソッドにのみ有効です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>プラットフォームに依存する文字列。Windows 98 では ANSI、Windows NT と Windows XP では Unicode。型 LPTStr の文字列のエクスポートがサポートされていないため、この値は COM 相互運用ではサポートされず、プラットフォーム呼び出しのみでサポートされます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>終端が null の 2 バイトの Unicode 文字列。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>4 バイトの浮動小数点数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>8 バイトの浮動小数点数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>SafeArray は、関連付けられた配列データの型、ランク、および境界を格納する自己記述型の配列です。<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> フィールドと併せてこのメンバーを使用することによって、既定の要素の型をオーバーライドできます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>マネージ書式指定クラスと値型をマーシャリングするために使用する VARIANT。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>プラットフォーム依存、符号付き整数: 32 ビット Windows では 4 バイト、64 ビット Windows では 8 バイト。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>プラットフォーム依存、符号なし整数: 32 ビット Windows では 4 バイト、64 ビット Windows では 8 バイト。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>長さを示すプレフィックスを付けた、プラットフォームに依存する char 文字列。Windows 98 では ANSI、Windows NT では Unicode。この BSTR に似たメンバーを使用することはほとんどありません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>1 バイト符号なし整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>2 バイト符号なし整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>4 バイト符号なし整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>8 バイト符号なし整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>2 バイトの OLE 定義 VARIANT_BOOL 型 (true = -1、false = 0)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Visual Basic で、アンマネージ コードの文字列を変更し、結果をマネージ コードに反映できるようにする値。この値は、プラットフォーム呼び出しでだけサポートされます。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>配列が <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> としてマネージ コードからアンマネージ コードにマーシャリングされる場合の、配列要素のマーシャリング方法を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>SAFEARRAY ポインターを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>長さのプリフィックスが付いたバイトを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>BLOB がオブジェクトを格納していることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>ブール値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>BSTR 文字列を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>値が参照であることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>C スタイル配列を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>クリップボード形式を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>クラス ID を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>通貨の値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>DATE 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>decimal 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>IDispatch ポインターを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>値が指定されなかったことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>SCODE を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>FILETIME 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>HRESULT を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>char 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>short 整数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>long 整数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>64 ビット整数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>整数値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>null で終わる文字列を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>null で終わるワイド文字列を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>null 値を示します。これは、SQL における null 値に相当します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>ポインター型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>float 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>double 値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>ユーザー定義型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>SAFEARRAY を示します。VARIANT では有効ではありません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>ストレージの名前が続くことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>ストレージがオブジェクトを格納していることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>ストリームの名前が続くことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>ストリームがオブジェクトを格納していることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>byte を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>unsignedshort を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>unsignedlong を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>64 ビット符号なし整数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>unsigned 整数値を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>IUnknown ポインターを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>ユーザー定義型を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>VARIANT far ポインターを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>カウントされた単純な配列を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>C スタイル void を示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>VT_VARIANT | VT_BYREF 型のデータをマネージ コードからアンマネージ コードにマーシャリングします。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> パラメーターの <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="obj">マーシャリングするオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>
        <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> オブジェクトによってラップされたオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> オブジェクトによってラップされたオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>オブジェクトを使用してアドバイズ シンクまたはキャッシュ コネクションを設定する場合に要求される動作を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>データのアドバイザリ コネクションの場合に、データへのアクセシビリティを確保します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>データのアドバイザリ コネクション (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> または <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />) の場合、このフラグは、データ オブジェクトが <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> の呼び出し時にデータを送信しないよう要求します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>コネクションを削除する前に、オブジェクトが 1 回だけ変更通知またはキャッシュの更新を行うよう要求します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>オブジェクトがデータまたは表示の変更を待機せずに <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> の初期呼び出し (データまたは表示のアドバイザリ コネクションの場合) またはキャッシュの更新 (キャッシュ コネクションの場合) を行うよう要求します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>この値は、オブジェクトの描画を実行する DLL オブジェクト アプリケーションおよびオブジェクト ハンドラーによって使用されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>頻繁に使用される <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" /> のシノニム。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>キャッシュ コネクションの場合、このフラグは、キャッシュを格納しているオブジェクトの保存時にだけ、キャッシュされている表示を更新します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>モニカー バインディング操作中に使用するパラメーターを格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>BIND_OPTS 構造体のサイズをバイト単位で指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>バインディング操作を完了するために呼び出し元が指定した時間 (GetTickCount 関数が返すミリ秒単位の時間) を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>モニカー バインディング操作のあらゆる局面を制御します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>モニカーが識別するオブジェクトを格納しているファイルを開くときに使用するフラグを表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>バインド先の <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体、<see cref="T:System.Runtime.InteropServices.VARDESC" /> 構造体、または ITypeComp インターフェイスへのポインターを格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>
        <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体へのポインターを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" /> インターフェイスへのポインターを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>
        <see cref="T:System.Runtime.InteropServices.VARDESC" /> 構造体へのポインターを表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>METHODDATA 構造体で記述されたメソッドが使用する呼び出し規約を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>メソッドに対して CDECL (C declaration) 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>メソッドに対して MACPASCAL (Macintosh Pascal) 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" /> 列挙体の末尾を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>メソッドに対して MPW (Macintosh Programmers' Workbench) CDECL 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>メソッドに対して MPW (Macintosh Programmers' Workbench) PASCAL 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>メソッドに対して MSCPASCAL (MSC Pascal) 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>メソッドに対して Pascal 呼び出し規約が使用されることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>この値は、今後使用するために予約されています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>メソッドに対して STDCALL (標準呼び出し規約) を使用することを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>メソッドに対して標準 SYSCALL 呼び出し規約を使用することを示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>指定したコネクション ポイントに存在する接続を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> への呼び出しによって返された接続トークンを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>接続されたアドバイズ シンク上の IUnknown インターフェイスへのポインターを表します。CONNECTDATA 構造体が必要なくなったときに、呼び出し元からこのポインターの指す IUnknown::Release を呼び出す必要があります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> メソッドの <paramref name="dwDirection" /> パラメーターのデータ フローの方向を指定します。これにより、結果の列挙子が列挙できる形式が決定されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> が、<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> で指定できる形式の列挙子を提供することを要求します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> が <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" /> で指定できる形式の列挙子を提供することを要求します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>バインドされている型の説明を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>
        <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体が返されたことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>IMPLICITAPPOBJ が返されたことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>列挙体末尾のマーカーを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>一致が見つからなかったことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>TYPECOMP が返されたことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>VARDESC が返されたことを示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>IDispatch::Invoke によってメソッドまたはプロパティに渡された引数を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>引数の数を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>名前つき引数の数を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>名前つき引数のディスパッチ ID を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>引数の配列への参照を表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>データの描画時または取得時にオブジェクトのデータやビューに必要な特徴を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>オブジェクトをコンテナー内の埋め込みオブジェクトとして表示するオブジェクトの表現。この値は通常、複合ドキュメント オブジェクトに指定されます。この表示形式は、画面またはプリンターに対して指定できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>[ファイル] メニューの [印刷] を使用してプリンターに印刷したような画面上のオブジェクトの表現。記述されるデータでページのシーケンスを表現できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>オブジェクトのアイコン表示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>参照ツールでオブジェクトが表示されるようにする、オブジェクトのサムネイル表示。サムネイルは、約 120 × 120 ピクセル、16 色 (推奨) のデバイスに依存しないビットマップで、メタファイルにラップされる場合があります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>変数、関数、または関数パラメーターの、型の説明およびプロセス転送情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>要素に関する情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>要素の型を識別します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>要素に関する情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>要素をリモート処理するための情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>パラメーターに関する情報を格納します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>IDispatch::Invoke 中に発生する例外を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>ユーザーに対して表示するエラーを記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>エラーに関する詳細な情報が含まれているヘルプ ファイルの、ドライブ名、パス名、およびファイル名から成る完全修飾名を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>例外の理由を示すメッセージです。通常は、これはアプリケーション名です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>ヘルプ ファイル内のトピックのヘルプ コンテキスト ID を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>引数として <see cref="T:System.Runtime.InteropServices.EXCEPINFO" />構造体をとり、HRESULT 値を返す関数を指すポインターを表します。遅延読み込みを行わない場合は、このフィールドは null に設定されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>このフィールドは予約されています。null に設定する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>エラーについて記述する戻り値です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>エラーを識別するエラー コードを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>このフィールドは予約されています。0 に設定する必要があります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>1601 年 1 月 1 日以降の 100 ナノ秒間隔の数を表します。この構造体は 64 ビット値です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>FILETIME の上位の 32 ビット値を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>FILETIME の下位の 32 ビット値を指定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>汎用のクリップボード形式を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>関係する特定のクリップボード形式を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>表示に含まれる必要がある詳細情報を示す <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 列挙定数の 1 つを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>データがページ境界を越えて分割される必要があるときの特性の部分を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>構成されているデータの対象デバイスに関する情報を格納している DVTARGETDEVICE 構造体へのポインターを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>オブジェクトのデータを転送するために使用するストレージ メディアの種類を示す <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 列挙定数の 1 つを指定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>関数の記述を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>関数の呼び出し規約を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>パラメーターの合計数をカウントします。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>オプションのパラメーターをカウントします。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>許可されている戻り値をカウントします。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>関数の戻り値の型を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>関数が仮想、静的、またはディスパッチ専用かどうかを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>プロパティ関数の型を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>
        <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" /> のサイズを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>関数が 16 ビットのシステム上で返すことができるエラーの数を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>関数メンバーの ID を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>
        <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" /> の仮想関数テーブル (VTBL: Virtual Function Table) 内のオフセットを指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>関数の <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> を示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>関数のプロパティを定義する定数を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>データ連結をサポートしている関数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>オブジェクトを最もよく表す関数です。型の 1 つの関数だけが、この属性を持つことができます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>コンパイラが "abc" の型で "xyz" という名前のメンバーを検索する場合に最適化を許可します。このようなメンバーが検出され、既定のコレクション要素のアクセサー関数としてフラグが付けられている場合は、そのメンバー関数への呼び出しが生成されます。dispinterface およびインターフェイスのメンバーでは許可されますが、モジュールでは許可されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>ユーザーに対して表示されるバインド可能な関数です。<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" /> も設定する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>関数は存在し、バインド可能ですが、ユーザーに対して表示しないでください。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>個別のバインド可能なプロパティとして割り当てられます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>プロパティはオブジェクト ブラウザーには表示されますが、プロパティ ブラウザーには表示されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>インターフェイスが既定の動作を備えていることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>設定した場合は、そのプロパティを設定したメソッドを呼び出すと、初めに IPropertyNotifySink::OnRequestEdit が呼び出されます。OnRequestEdit を実装すると、呼び出しがこのプロパティを設定できるかどうかを確認します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>関数は、マクロ言語からはアクセスできません。このフラグは、システムレベル関数または型ブラウザーに表示しない関数で使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>関数は、イベントの発生元のオブジェクトを返します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>型情報メンバーは、ユーザー インターフェイスに表示する既定のメンバーです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>関数は GetLastError をサポートしています。関数の実行中にエラーが発生した場合、呼び出し元は GetLastError を呼び出してエラー コードを取得できます。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>関数にアクセスする方法を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>関数には、IDispatch を使用したときにだけアクセスできます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>関数は、static アドレスによってアクセスし、暗黙の this ポインターを取得します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>関数は、仮想関数テーブル (VTBL: virtual function table) を使用してアクセスし、暗黙の this ポインターを取得します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>関数は、static アドレスを使用してアクセスし、暗黙の this ポインターを取得しません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>関数は、実装されている場合を除き、<see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" /> と同様にアクセスします。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>IAdviseSink インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>登録されているすべてのアドバイズ シンクに、オブジェクトが実行状態から読み込み済み状態に変更されたことを通知します。このメソッドは、サーバーから呼び出されます。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>現在アドバイズ シンクが登録されているすべてのデータ オブジェクトに、オブジェクトのデータが変更されたことを通知します。</summary>
      <param name="format">呼び出し元データ オブジェクトの形式、対象デバイス、レンダリング、およびストレージ情報が記述されている <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> (参照渡し)。</param>
      <param name="stgmedium">呼び出し元データ オブジェクトのストレージ メディア (グローバル メモリ、ディスク ファイル、ストレージ オブジェクト、ストリーム オブジェクト、グラフィック デバイス インターフェイス (GDI: Graphics Device Interface) オブジェクト、または未定義) およびそのメディアの所有権を定義する <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> (参照渡し)。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>登録されているすべてのアドバイズ シンクに、オブジェクトの名前が変更されたことを通知します。このメソッドは、サーバーから呼び出されます。</summary>
      <param name="moniker">オブジェクトの新しく完全なモニカーの IMoniker インターフェイスへのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>登録されているすべてのアドバイズ シンクに、オブジェクトの名前が保存されたことを通知します。このメソッドは、サーバーから呼び出されます。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>オブジェクトの、登録されているアドバイズ シンクに、ビューが変更されたことを通知します。このメソッドは、サーバーから呼び出されます。</summary>
      <param name="aspect">オブジェクトの特性またはビュー。<see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 列挙体から取得された値を格納します。</param>
      <param name="index">ビューの変更部分。現在、-1 だけが有効です。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>IBindCtx インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>コンテキスト オブジェクト パラメーターの内部で維持されるテーブルのキーである文字列を列挙します。</summary>
      <param name="ppenum">このメソッドが返されるときに、オブジェクト パラメーター列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>現在のバインド コンテキストに格納されている現在のバインディング オプションを返します。</summary>
      <param name="pbindopts">バインディング オプションを受け取る構造体へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>コンテキスト オブジェクト パラメーターの内部で維持されるテーブルで特定のキーを検索し、存在する場合は対応するオブジェクトを返します。</summary>
      <param name="pszKey">検索する対象となるオブジェクトの名前。</param>
      <param name="ppunk">このメソッドが返されるときに、オブジェクトのインターフェイス ポインターを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>このバインディング プロセスに関連するランニング オブジェクト テーブル (ROT: Running Object Table) へのアクセスを返します。</summary>
      <param name="pprot">このメソッドが返されるときに、ランニング オブジェクト テーブル (ROT) への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>モニカー操作中にバインドされ、操作完了時に解放する必要があるオブジェクトの 1 つとして、渡されたオブジェクトを登録します。</summary>
      <param name="punk">解放のために登録するオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>オブジェクト ポインターの内部で維持されるテーブルで、指定した名前を使用して指定したオブジェクト ポインターを登録します。</summary>
      <param name="pszKey">
        <paramref name="punk" /> の登録に使用する名前。</param>
      <param name="punk">登録するオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" /> メソッドによってバインド コンテキストに現在登録されているすべてのオブジェクトを解放します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>解放する必要がある登録済みオブジェクトのセットからオブジェクトを削除します。</summary>
      <param name="punk">解放のために登録を解除するオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>コンテキスト オブジェクト パラメーターの内部で維持されるテーブルで、キーが現在登録されている場合、そのキーを使用して現在検出されているオブジェクトの登録を取り消します。</summary>
      <returns>指定したキーがテーブルから正常に削除された場合は S_OKHRESULT 値。それ以外の場合は S_FALSEHRESULT 値。</returns>
      <param name="pszKey">登録を解除するキー。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>一連のパラメーターをバインド コンテキストに格納します。これらのパラメーターは、以降に実行される UCOMIMoniker 操作のうち、このバインド コンテキストを使用する操作に適用されます。</summary>
      <param name="pbindopts">設定するバインディング オプションを格納している構造体。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>IConnectionPoint インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>コネクション ポイントと呼び出し元のシンク オブジェクト間にアドバイザリ コネクションを確立します。</summary>
      <param name="pUnkSink">このコネクション ポイントが管理するアウトゴーイング インターフェイスへの呼び出しを受信するためのシンクへの参照。</param>
      <param name="pdwCookie">このメソッドが返されるときに、接続クッキーを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>コネクション ポイントに存在する接続を反復処理するための列挙子オブジェクトを作成します。</summary>
      <param name="ppEnum">このメソッドが返されるときに、新しく作成された列挙子を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>コネクション ポイントが管理するアウトゴーイング インターフェイスの IID を返します。</summary>
      <param name="pIID">このパラメーターが返されるときに、このコネクション ポイントが管理するアウトゴーイング インターフェイスの IID を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>概念上このコネクション ポイントを所有している接続可能オブジェクトへの IConnectionPointContainer インターフェイス ポインターを取得します。</summary>
      <param name="ppCPC">このパラメーターが返されるときに、接続可能オブジェクトの IConnectionPointContainer インターフェイスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> メソッドが以前に確立したアドバイザリ コネクションを終了します。</summary>
      <param name="dwCookie">
        <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> メソッドが以前に返した接続クッキー。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>IConnectionPointContainer インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>IID あたり 1 つのコネクション ポイントとして、接続可能オブジェクトでサポートされているすべてのコネクション ポイントの列挙子を作成します。</summary>
      <param name="ppEnum">このメソッドが返されるときに、列挙子のインターフェイス ポインターを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>特定の IID のコネクション ポイントが存在するかどうかを接続可能オブジェクトに確認し、存在する場合はそのコネクション ポイントへの IConnectionPoint インターフェイス ポインターを返します。</summary>
      <param name="riid">コネクション ポイントを要求しているアウトゴーイング インターフェイス IID への参照。</param>
      <param name="ppCP">このメソッドが返されるときに、アウトゴーイング インターフェイス <paramref name="riid" /> を管理するコネクション ポイントを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>構造体の要素、パラメーター、または関数の戻り値をプロセス間で転送するために必要な情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>予約済み。null に設定されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>型を記述する <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> 値を示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>構造体要素、パラメーター、または関数の戻り値をプロセス間で転送する方法を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>パラメーターは、呼び出し元から呼び出し先に情報を渡します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>パラメーターは、クライアント アプリケーションのローカル ID です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>パラメーターは、呼び出し先から呼び出し元に情報を返します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>パラメーターは、メンバーの戻り値です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>パラメーターが情報を渡すか、情報を受け取るかを指定しません。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>IEnumConnectionPoints インターフェイスの定義を管理します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>現在の列挙状態と同じ列挙状態を含む新しい列挙子を作成します。</summary>
      <param name="ppenum">このメソッドから制御が戻るときに、新しく作成された列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返される IConnectionPoint の参照の数。</param>
      <param name="rgelt">このメソッドが返されるときに、列挙された接続への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドが返されるときに、<paramref name="rgelt" /> に列挙された実際の接続数への参照を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>IEnumConnections インターフェイスの定義を管理します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>現在の列挙状態と同じ列挙状態を含む新しい列挙子を作成します。</summary>
      <param name="ppenum">このメソッドから制御が戻るときに、新しく作成された列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返される <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> 構造体の数。</param>
      <param name="rgelt">このメソッドが返されるときに、列挙された接続への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドが返されるときに、<paramref name="rgelt" /> に列挙された実際の接続数への参照を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>IEnumFORMATETC インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>現在の列挙状態と同じ列挙状態を格納する新しい列挙子を作成します。</summary>
      <param name="newEnum">このメソッドから制御が戻るときに、新しく作成された列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返される <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> の参照の数。</param>
      <param name="rgelt">このメソッドから制御が戻るときに、列挙された <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 参照への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドから制御が戻るときに、<paramref name="rgelt" /> に列挙された参照の実数への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
      <returns>S_OK 値を保持している HRESULT。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>IEnumMoniker インターフェイスの定義を管理します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>現在の列挙状態と同じ列挙状態を含む新しい列挙子を作成します。</summary>
      <param name="ppenum">このメソッドから制御が戻るときに、新しく作成された列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返されるモニカーの数。</param>
      <param name="rgelt">このメソッドが返されるときに、列挙されたモニカーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドが返されるときに、<paramref name="rgelt" /> に列挙されたモニカーの実数への参照を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>IEnumString インターフェイスの定義を管理します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>現在の列挙状態と同じ列挙状態を含む新しい列挙子を作成します。</summary>
      <param name="ppenum">このメソッドから制御が戻るときに、新しく作成された列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返す文字列の数。</param>
      <param name="rgelt">このメソッドが返されるときに、列挙された文字列への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドが返されるときに、<paramref name="rgelt" /> に列挙された文字列の実数への参照を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>IEnumVARIANT インターフェイスの定義を管理します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>現在の列挙状態と同じ列挙状態を含む新しい列挙子を作成します。</summary>
      <returns>新しく作成された列挙子への <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> 参照。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>列挙体シーケンス内の指定した数の項目を取得します。</summary>
      <returns>
        <paramref name="pceltFetched" /> パラメーターが <paramref name="celt" /> パラメーターに等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">
        <paramref name="rgelt" /> に返される要素の数。</param>
      <param name="rgVar">このメソッドが返されるときに、列挙された要素への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pceltFetched">このメソッドが返されるときに、<paramref name="rgelt" /> に列挙された実際の要素数への参照を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>列挙体シーケンスを先頭にリセットします。</summary>
      <returns>S_OK 値を保持している HRESULT。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>列挙体シーケンス内の指定した数の項目をスキップします。</summary>
      <returns>スキップした要素の数が <paramref name="celt" /> に等しい場合は S_OK。それ以外の場合は S_FALSE。</returns>
      <param name="celt">列挙体内でスキップする要素の数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>IPersist および IPersistStream の COM 機能を備えた IMoniker インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>モニカーを使用して、そのモニカーが識別するオブジェクトにバインドします。</summary>
      <param name="pbc">このバインド操作で使用するバインド コンテキスト オブジェクトの IBindCtx インターフェイスへの参照。</param>
      <param name="pmkToLeft">モニカーが複合モニカーの一部である場合、現在のモニカーの左側にあるモニカーへの参照。</param>
      <param name="riidResult">モニカーが識別するオブジェクトと通信するために、クライアントが使用するインターフェイスのインターフェイス ID (IID: Interface Identifier)。</param>
      <param name="ppvResult">このメソッドが返されるときに、<paramref name="riidResult" /> によって要求されたインターフェイスへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>モニカーで識別されるオブジェクトが格納されているストレージへのインターフェイス ポインターを取得します。</summary>
      <param name="pbc">このバインド操作で使用するバインド コンテキスト オブジェクトの IBindCtx インターフェイスへの参照。</param>
      <param name="pmkToLeft">モニカーが複合モニカーの一部である場合、現在のモニカーの左側にあるモニカーへの参照。</param>
      <param name="riid">要求したストレージ インターフェイスのインターフェイス ID (IID: Interface Identifier)。</param>
      <param name="ppvObj">このメソッドが返されるときに、<paramref name="riid" /> によって要求されたインターフェイスへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>このモニカーが別のモニカーと共有する、共通のプリフィックスに基づいて、新しいモニカーを作成します。</summary>
      <param name="pmkOther">共通のプリフィックス作成のために現在のモニカーと比較する別のモニカーの IMoniker インターフェイスへの参照。</param>
      <param name="ppmkPrefix">このメソッドが返されるときに、現在のモニカーと <paramref name="pmkOther" /> の共通のプリフィックスであるモニカーを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>現在のモニカーと別のモニカーを結合し、新しい複合モニカーを作成します。</summary>
      <param name="pmkRight">現在のモニカーの末尾に追加するモニカーの IMoniker インターフェイスへの参照。</param>
      <param name="fOnlyIfNotGeneric">呼び出し元が汎用的でない複合を必要としている場合は true とします。この場合、<paramref name="pmkRight" /> が汎用的な複合モニカーを形成しないような方法で現在のモニカーを複合できるモニカー クラスである場合に限り、操作が続行されます。 メソッドが必要に応じて汎用的な複合モニカーを作成できる場合は false とします。</param>
      <param name="ppmkComposite">このメソッドが返されるときに、作成された複合モニカーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>複合モニカーのコンポーネントを列挙できる列挙子へのポインターを提供します。</summary>
      <param name="fForward">モニカーを左から右へ列挙する場合は true。右から左へ列挙する場合は false。</param>
      <param name="ppenumMoniker">このメソッドが返されるときに、モニカーの列挙子オブジェクトへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>オブジェクトのクラス ID (CLSID: Class Identifier) を取得します。</summary>
      <param name="pClassID">このメソッドが返されるときに、CLSID を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>現在のモニカーについてユーザーが解釈できる形式の表示名を取得します。</summary>
      <param name="pbc">この操作に使用するバインド コンテキストへの参照。</param>
      <param name="pmkToLeft">モニカーが複合モニカーの一部である場合、現在のモニカーの左側にあるモニカーへの参照。</param>
      <param name="ppszDisplayName">このメソッドが返されるときに、表示名文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>オブジェクトを保存するために必要なストリームのサイズをバイト単位で返します。</summary>
      <param name="pcbSize">このメソッドが返されるときに、このオブジェクトを保存するために必要なストリームのサイズをバイト単位で示す long 値を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>このモニカーで識別されるオブジェクトが、最後に変更された時刻を表す数値を示します。</summary>
      <param name="pbc">このバインド操作に使用するバインド コンテキストへの参照。</param>
      <param name="pmkToLeft">モニカーが複合モニカーの一部である場合、現在のモニカーの左側にあるモニカーへの参照。</param>
      <param name="pFileTime">このメソッドが返されるときに、最後の変更時刻を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>モニカーの内部状態を使用して、32 ビット整数を計算します。</summary>
      <param name="pdwHash">このメソッドが返されるときに、このモニカーのハッシュ値を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>現在のモニカーまたは類似の構造を持つモニカーの右側に複合された場合に、他の何にも複合されないモニカーを示します。</summary>
      <param name="ppmk">このメソッドが返されるときに、現在のモニカーと逆のモニカーを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>最後に保存した時点からオブジェクトが変更されたかどうかを確認します。</summary>
      <returns>オブジェクトが変更されている場合は S_OKHRESULT 値。それ以外の場合は S_FALSEHRESULT 値。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>指定したモニカーと現在のモニカーを比較し、同一かどうかを示します。</summary>
      <returns>モニカーが同一の場合は S_OKHRESULT 値。それ以外の場合は S_FALSEHRESULT 値。</returns>
      <param name="pmkOtherMoniker">比較に使用するモニカーへの参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>現在のモニカーで識別されるオブジェクトが現在読み込まれ、実行中かどうかを判断します。</summary>
      <returns>モニカーが実行中の場合は S_OKHRESULT 値。モニカーが実行されていない場合は S_FALSEHRESULT 値。それ以外の場合は E_UNEXPECTEDHRESULT 値。</returns>
      <param name="pbc">このバインド操作に使用するバインド コンテキストへの参照。</param>
      <param name="pmkToLeft">モニカーが複合モニカーの一部である場合、現在のモニカーの左側にあるモニカーへの参照。</param>
      <param name="pmkNewlyRunning">ランニング オブジェクト テーブル (ROT: Running Object Table) に最後に追加されたモニカーへの参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>このモニカーがシステム指定のモニカー クラスの 1 つかどうかを示します。</summary>
      <returns>モニカーがシステム モニカーの場合は S_OKHRESULT 値。それ以外の場合は S_FALSEHRESULT 値。</returns>
      <param name="pdwMksys">このメソッドが返されるときに、MKSYS 列挙体からの値の 1 つであり、COM モニカー クラスの 1 つを参照する整数へのポインターを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>オブジェクトが以前格納されたストリームから、そのオブジェクトを初期化します。</summary>
      <param name="pStm">読み込まれるオブジェクトが格納されているストリーム。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>指定した表示名の文字を読み取るときに、<see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> が読み取った部分に対応するモニカーを理解および構築するために必要な文字数だけ読み取ります。</summary>
      <param name="pbc">このバインド操作に使用するバインド コンテキストへの参照。</param>
      <param name="pmkToLeft">このポイントまでの表示名から構築されたモニカーへの参照。</param>
      <param name="pszDisplayName">解析する対象の、残りの表示名を格納している文字列への参照。</param>
      <param name="pchEaten">このメソッドが返されるときに、<paramref name="pszDisplayName" /> の解析に使用した文字数を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="ppmkOut">このメソッドが返されるときに、<paramref name="pszDisplayName" /> から構築されたモニカーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>現在のモニカーと同じオブジェクトを参照するが、それ以上に効率的にバインドできる縮小された別のモニカーを返します。</summary>
      <param name="pbc">このバインド操作で使用するバインド コンテキストの IBindCtx インターフェイスへの参照。</param>
      <param name="dwReduceHowFar">現在のモニカーを縮小する程度を指定する値。</param>
      <param name="ppmkToLeft">現在のモニカーの左側のモニカーへの参照。</param>
      <param name="ppmkReduced">このメソッドが返されるときに、現在のモニカーの縮小形式への参照を格納します。エラーが発生したか、現在のモニカーが縮小されなかった場合は、null を格納することができます。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>現在のモニカー (または、同様の構造体を持つモニカー) に追加された場合に、指定したモニカーを生成するモニカーを示します。</summary>
      <param name="pmkOther">相対パスを取得する必要があるモニカーへの参照。</param>
      <param name="ppmkRelPath">このメソッドが返されるときに、関連モニカーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>指定したストリームにオブジェクトを保存します。</summary>
      <param name="pStm">オブジェクトの保存先ストリーム。</param>
      <param name="fClearDirty">true (変更したフラグを保存完了後にクリアする場合)。それ以外の場合は false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>実装された型のインターフェイスまたは継承された型のインターフェイスの属性を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>インターフェイスまたは dispinterface は、ソースまたはシンクの既定値を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>シンクは、仮想関数テーブル (VTBL: virtual function table) を経由してイベントを受け取ります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>メンバーは、ユーザーが表示したり、プログラミングしたりできません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>コクラスのこのメンバーは、実装されるのではなく呼び出されます。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>IDispatch::Invoke で関数を呼び出す方法を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>メンバーは、通常の関数呼び出しの構文を使用して呼び出されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>関数は、通常のプロパティ アクセスの構文を使用して呼び出されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>関数は、プロパティ値割り当ての構文を使用して呼び出されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>関数は、プロパティ参照割り当ての構文を使用して呼び出されます。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>IPersist 機能を備えた IPersistFile インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>オブジェクトのクラス ID (CLSID: Class Identifier) を取得します。</summary>
      <param name="pClassID">このメソッドが返されるときに、CLSID への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>オブジェクトの現在の作業ファイルへの絶対パスを取得します。現在の作業ファイルがない場合は、オブジェクトの既定のファイル名プロンプトを取得します。</summary>
      <param name="ppszFileName">このメソッドが返されるときに、現在のファイルのパスを含む 0 で終わる文字列へのポインターのアドレス、または既定のファイル名プロンプト (*.txt など) を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>現在のファイルを最後に保存した時点からオブジェクトが変更されたかどうかを確認します。</summary>
      <returns>最後に保存した時点からファイルが変更された場合は S_OK。最後に保存した時点からファイルが変更されていない場合は S_FALSE。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>指定したファイルを開き、ファイルに含まれているオブジェクトを初期化します。</summary>
      <param name="pszFileName">開くファイルの絶対パスを含む 0 で終わる文字列。</param>
      <param name="dwMode">
        <paramref name="pszFileName" /> を開くためのアクセス モードを示す、STGM 列挙体からの値の組み合わせ。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>指定したファイルにオブジェクトのコピーを保存します。</summary>
      <param name="pszFileName">オブジェクトの保存先ファイルの絶対パスを含む、0 で終わる文字列。</param>
      <param name="fRemember">現在の作業ファイルとして <paramref name="pszFileName" /> パラメーターを使用する場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>ファイルに書き込むことができることをオブジェクトに通知します。</summary>
      <param name="pszFileName">オブジェクトが前回保存されたファイルの絶対パス。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>IRunningObjectTable インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>現在実行中として登録されているオブジェクトを列挙します。</summary>
      <param name="ppenumMoniker">このメソッドが返されるときに、ランニング オブジェクト テーブル (ROT: Running Object Table) の新しい列挙子を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>指定したオブジェクト名が実行中として登録されている場合は、登録済みのオブジェクトを返します。</summary>
      <returns>操作が成功したかどうかを示す HRESULT 値。</returns>
      <param name="pmkObjectName">ROT で検索するモニカーへの参照。</param>
      <param name="ppunkObject">このメソッドが返されるときに、要求された実行中オブジェクトを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>ROT でモニカーを検索し、変更時刻が記録されていれば報告します。</summary>
      <returns>操作が成功したかどうかを示す HRESULT 値。</returns>
      <param name="pmkObjectName">ROT で検索するモニカーへの参照。</param>
      <param name="pfiletime">このオブジェクトが返されるときに、オブジェクトの最後の変更時刻を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>指定したモニカーが現在 ROT に登録されているかどうかを確認します。</summary>
      <returns>操作が成功したかどうかを示す HRESULT 値。</returns>
      <param name="pmkObjectName">ROT で検索するモニカーへの参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>IMoniker::GetTimeOfLastChange が適切な変更時刻を報告できるように、特定のオブジェクトが変更された時刻を記録します。</summary>
      <param name="dwRegister">変更されたオブジェクトの ROT エントリ。</param>
      <param name="pfiletime">オブジェクトの最後の変更時刻への参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>指定したオブジェクトが実行状態になったことを登録します。</summary>
      <returns>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> または <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" /> への後続の呼び出しで、この ROT エントリを識別するために使用できる値。</returns>
      <param name="grfFlags">
        <paramref name="punkObject" /> への ROT 参照が、ウィーク参照かストロング参照かを指定し、ROT のエントリを使用してオブジェクトへのアクセスを制御します。</param>
      <param name="punkObject">実行中として登録されているオブジェクトへの参照。</param>
      <param name="pmkObjectName">
        <paramref name="punkObject" /> を識別するモニカーへの参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>ROT から指定したオブジェクトの登録を解除します。</summary>
      <param name="dwRegister">登録解除する ROT エントリ。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>ISequentialStream 機能を備えた IStream インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>元のストリームと同じバイトを参照する独自のシーク ポインターで、新しいストリーム オブジェクトを作成します。</summary>
      <param name="ppstm">このメソッドが返されるときに、新しいストリーム オブジェクトを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>トランザクション モードで開かれたストリーム オブジェクトに加えた変更が、親ストレージに反映されるようにします。</summary>
      <param name="grfCommitFlags">ストリーム オブジェクトへの変更をコミットする方法を制御する値。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>ストリームの現在のシーク ポインターから別のストリームの現在のシーク ポインターに指定したバイト数をコピーします。</summary>
      <param name="pstm">コピー先ストリームへの参照。</param>
      <param name="cb">コピー元ストリームからコピーするバイト数。</param>
      <param name="pcbRead">正常に返された場合は、コピー元から読み取られた実際のバイト数を格納します。</param>
      <param name="pcbWritten">正常に返された場合は、コピー先に書き込まれた実際のバイト数を格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>ストリームの指定したバイト範囲へのアクセスを制限します。</summary>
      <param name="libOffset">範囲の先頭のバイト オフセット。</param>
      <param name="cb">制限する範囲のバイト単位の長さ。</param>
      <param name="dwLockType">範囲へのアクセスに対して要求する制限。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>現在のシーク ポインターを開始点として、ストリーム オブジェクトからメモリに指定したバイト数を読み取ります。</summary>
      <param name="pv">このメソッドが返されるときに、ストリームから読み取られたデータを格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="cb">ストリーム オブジェクトから読み取るバイト数。</param>
      <param name="pcbRead">ストリーム オブジェクトから読み取られた実際のバイト数を受け取る ULONG 変数へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>最後の <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" /> 呼び出し以降に処理されたストリームに加えられたすべての変更を破棄します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>ストリームの先頭、ストリームの末尾、または現在のシーク ポインターに対して相対的な新しい場所にシーク ポインターを移動します。</summary>
      <param name="dlibMove">
        <paramref name="dwOrigin" /> に追加する変位。</param>
      <param name="dwOrigin">シークの開始点。開始点は、ファイルの先頭、現在のシーク ポインター、またはファイルの末尾にできます。</param>
      <param name="plibNewPosition">正常に返された場合は、ストリームの先頭からのシーク ポインターのオフセットを格納します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>ストリーム オブジェクトのサイズを変更します。</summary>
      <param name="libNewSize">ストリームの新しいサイズ (バイト数)。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>ストリームの <see cref="T:System.Runtime.InteropServices.STATSTG" /> 構造体を取得します。</summary>
      <param name="pstatstg">このメソッドが返されるときに、このストリーム オブジェクトについて記述する STATSTG 構造体を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="grfStatFlag">STATSTG 構造体のメンバーのうち、このメソッドが返さないメンバー (不要なメモリ割り当てが行われないようにするため)。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" /> メソッドで制限したバイト範囲へのアクセス制限を解除します。</summary>
      <param name="libOffset">範囲の先頭のバイト オフセット。</param>
      <param name="cb">制限する範囲のバイト単位の長さ。</param>
      <param name="dwLockType">以前にその範囲に与えたアクセス制限。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>現在のシーク ポインターを開始点として、ストリーム オブジェクトに指定したバイト数だけ書き込みます。</summary>
      <param name="pv">ストリームを書き込むバッファー。</param>
      <param name="cb">ストリームに書き込むバイト数。</param>
      <param name="pcbWritten">正常に返された場合は、ストリーム オブジェクトに書き込まれた実際のバイト数を格納します。呼び出し元がこのポインターを <see cref="F:System.IntPtr.Zero" /> に設定した場合、このメソッドは書き込まれた実際のバイト数を示しません。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>ITypeComp インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>型のメンバーに名前を割り当てたり、タイプ ライブラリに格納されているグローバル変数、および関数をバインドしたりします。</summary>
      <param name="szName">バインドする名前。</param>
      <param name="lHashVal">LHashValOfNameSys によって計算された <paramref name="szName" /> のハッシュ値。</param>
      <param name="wFlags">INVOKEKIND 列挙体に定義されている 1 つ以上の呼び出しフラグを含んでいる単語。</param>
      <param name="ppTInfo">FUNCDESC または VARDESC が返された場合は、このメソッドが返されるときに、バインド先の項目を格納している型の説明への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pDescKind">このメソッドが返されるときに、バインド先の名前が VARDESC、FUNCDESC、TYPECOMP のいずれであるかを示す DESCKIND 列挙子への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pBindPtr">このメソッドが返されるときに、バインド先の VARDESC、FUNCDESC、または ITypeComp インターフェイスへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>タイプ ライブラリ内に格納されている型の説明にバインドします。</summary>
      <param name="szName">バインドする名前。</param>
      <param name="lHashVal">LHashValOfNameSys によって決定された <paramref name="szName" /> のハッシュ値。</param>
      <param name="ppTInfo">このメソッドが返されるときに、<paramref name="szName" /> がバインドされた型の ITypeInfo への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="ppTComp">このメソッドが返されるときに、ITypeComp 変数への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>コンポーネント オートメーションの ITypeInfo インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>DLL 内で定義されている静的な関数または変数のアドレスを取得します。</summary>
      <param name="memid">取得する static メンバーのアドレスのメンバー ID。</param>
      <param name="invKind">メンバーがプロパティかどうかを指定し、プロパティである場合はその種類を示す <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 値の 1 つ。</param>
      <param name="ppv">このメソッドが返されるときに、static メンバーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>コンポーネント クラス (coclass) を記述する型の新しいインスタンスを作成します。</summary>
      <param name="pUnkOuter">制御側の IUnknown として機能するオブジェクト。</param>
      <param name="riid">作成されたオブジェクトと通信するために、呼び出し元が使用するインターフェイスの IID。</param>
      <param name="ppvObj">このメソッドが返されるときに、作成されたオブジェクトへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>型の説明が格納されているタイプ ライブラリと、そのタイプ ライブラリ内におけるその型の説明のインデックスを取得します。</summary>
      <param name="ppTLB">このメソッドが返されるときに、この型の説明が格納されているタイプ ライブラリへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pIndex">このメソッドが返されるときに、型の説明が格納されているタイプ ライブラリ内におけるその説明のインデックスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>DLL 内の関数へのエントリ ポイントの記述または仕様を取得します。</summary>
      <param name="memid">DLL エントリの記述を取得するメンバー関数の ID。</param>
      <param name="invKind">
        <paramref name="memid" /> で識別されるメンバーの種類を指定する <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 値の 1 つ。</param>
      <param name="pBstrDllName">null ではない場合、関数は <paramref name="pBstrDllName" /> を DLL の名前が格納されている BSTR に設定します。</param>
      <param name="pBstrName">null ではない場合、関数は <paramref name="lpbstrName" /> をエントリ ポイントの名前が格納されている BSTR に設定します。</param>
      <param name="pwOrdinal">null ではない場合、関数は序数によって定義されます。<paramref name="lpwOrdinal" /> はその序数を指すように設定されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>指定した型の説明に対するヘルプ トピックのドキュメント文字列、ヘルプ ファイルの完全限定名とパス、およびコンテキスト ID を取得します。</summary>
      <param name="index">ドキュメントを取得するメンバーの ID。</param>
      <param name="strName">このメソッドが返されるときに、項目メソッドの名前を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strDocString">このメソッドから制御が戻るときに、指定した項目のドキュメント文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="dwHelpContext">このメソッドが返されるときに、指定した項目に関連付けられたヘルプ コンテキストへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strHelpFile">このメソッドが返されるときに、ヘルプ ファイルの完全修飾名を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>指定した関数に関する情報を格納している <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体を取得します。</summary>
      <param name="index">取得する関数の説明のインデックス。</param>
      <param name="ppFuncDesc">このメソッドが返されるときに、指定した関数について記述する FUNCDESC 構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>メンバー名をメンバー ID に、パラメーター名をパラメーター ID に割り当てます。</summary>
      <param name="rgszNames">割り当てる名前の配列。</param>
      <param name="cNames">割り当てる名前の数。</param>
      <param name="pMemId">このメソッドが返されるときに、名前の割り当てを格納する配列への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>型の説明で実装されたインターフェイスまたは基本インターフェイスに対する <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 値を取得します。</summary>
      <param name="index">実装されたインターフェイスまたは基本インターフェイスのインデックス。</param>
      <param name="pImplTypeFlags">このメソッドが返されるときに、IMPLTYPEFLAGS 列挙体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>マーシャリング情報を取得します。</summary>
      <param name="memid">必要なマーシャリング情報を示すメンバー ID。</param>
      <param name="pBstrMops">このメソッドが返されるときに、参照先の型の説明で記述されている、構造体のフィールドのマーシャリングに使用する opcode 文字列への参照を格納します。返す情報がない場合は、null を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>指定した関数 ID に対応する指定したメンバー ID の変数 (またはプロパティやメソッドの名前とそのパラメーター) を取得します。</summary>
      <param name="memid">1 つ以上の名前が返されるメンバーの ID。</param>
      <param name="rgBstrNames">このメソッドが返されるときに、メンバーと関連付けられた 1 つ以上の名前を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 配列の長さ。</param>
      <param name="pcNames">このメソッドが返されるときに、<paramref name="rgBstrNames" /> 配列内の名前の数を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>型の説明が別の型の説明を参照している場合に、参照先の型の説明を取得します。</summary>
      <param name="hRef">取得する参照先の型の説明へのハンドル。</param>
      <param name="ppTI">このメソッドが返されるときに、参照先の型の説明を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>型の説明が COM クラスの記述である場合に、実装されたインターフェイス型に関する型の説明を取得します。</summary>
      <param name="index">ハンドルを取得する実装された型のインデックス。</param>
      <param name="href">このメソッドが返されるときに、実装されたインターフェイスのハンドルへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>型の説明の属性を格納している <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 構造体を取得します。</summary>
      <param name="ppTypeAttr">このメソッドが返されるときに、この型の説明の属性を格納する構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>型の説明の ITypeComp インターフェイスを取得します。これを使用すると、クライアント コンパイラを型の説明のメンバーにバインドできます。</summary>
      <param name="ppTComp">このメソッドが返されるときに、型の説明が格納されているタイプ ライブラリの ITypeComp インターフェイスへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>指定した変数を記述している VARDESC 構造体を取得します。</summary>
      <param name="index">取得する変数の説明のインデックス。</param>
      <param name="ppVarDesc">このメソッドが返されるときに、指定した変数について記述する VARDESC 構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>型の説明により説明されるインターフェイスを実装しているオブジェクトについて、メソッドを呼び出したりプロパティにアクセスしたりします。</summary>
      <param name="pvInstance">型の説明で記述されているインターフェイスへの参照。</param>
      <param name="memid">インターフェイス メンバーを識別する値。</param>
      <param name="wFlags">呼び出しのコンテキストを記述するフラグ。</param>
      <param name="pDispParams">引数の配列、名前付き引数の DISPID の配列、各配列内の要素数のカウントを格納している構造体への参照。</param>
      <param name="pVarResult">結果が格納される場所への参照。<paramref name="wFlags" /> が DISPATCH_PROPERTYPUT または DISPATCH_PROPERTYPUTREF を指定している場合、<paramref name="pVarResult" /> は無視されます。結果が必要ない場合は、null に設定します。</param>
      <param name="pExcepInfo">DISP_E_EXCEPTION が返される場合にだけ情報が格納される例外情報構造体へのポインター。</param>
      <param name="puArgErr">Invoke が DISP_E_TYPEMISMATCH を返す場合、<paramref name="puArgErr" /> は、型が正しくない引数について <paramref name="rgvarg" /> 内のインデックスを示します。複数の引数がエラーを返す場合、<paramref name="puArgErr" /> はエラーのある最初の引数だけを示します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> メソッドによって返された <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体を解放します。</summary>
      <param name="pFuncDesc">解放する FUNCDESC 構造体への参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> メソッドによって返された <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 構造体を解放します。</summary>
      <param name="pTypeAttr">解放する TYPEATTR 構造体への参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> メソッドによって返された VARDESC 構造体を解放します。</summary>
      <param name="pVarDesc">解放する VARDESC 構造体への参照。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>ITypeInfo2 インターフェイスのマネージ定義を行います。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>DLL 内で定義されている静的な関数または変数のアドレスを取得します。</summary>
      <param name="memid">取得する static メンバーのアドレスのメンバー ID。</param>
      <param name="invKind">メンバーがプロパティかどうかを指定し、プロパティである場合はその種類を示す <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 値の 1 つ。</param>
      <param name="ppv">このメソッドが返されるときに、static メンバーへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>コンポーネント クラス (coclass) を記述する型の新しいインスタンスを作成します。</summary>
      <param name="pUnkOuter">制御側の IUnknown として機能するオブジェクト。</param>
      <param name="riid">作成されたオブジェクトと通信するために、呼び出し元が使用するインターフェイスの IID。</param>
      <param name="ppvObj">このメソッドが返されるときに、作成されたオブジェクトへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>ライブラリのすべてのカスタム データ項目を取得します。</summary>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>指定した関数からすべてのカスタム データを取得します。</summary>
      <param name="index">カスタム データを取得する関数のインデックス。</param>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>指定した実装の種類のすべてのカスタム データを取得します。</summary>
      <param name="index">カスタム データの実装の種類のインデックス。</param>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>指定した関数パラメーターのすべてのカスタム データを取得します。</summary>
      <param name="indexFunc">カスタム データを取得する関数のインデックス。</param>
      <param name="indexParam">カスタム データを取得する関数のパラメーターのインデックス。</param>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>カスタム データの変数を取得します。</summary>
      <param name="index">カスタム データを取得する変数のインデックス。</param>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>型の説明が格納されているタイプ ライブラリと、そのタイプ ライブラリ内におけるその型の説明のインデックスを取得します。</summary>
      <param name="ppTLB">このメソッドが返されるときに、この型の説明が格納されているタイプ ライブラリへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="pIndex">このメソッドが返されるときに、型の説明が格納されているタイプ ライブラリ内におけるその説明のインデックスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>カスタム データを取得します。</summary>
      <param name="guid">データの識別に使用する GUID。</param>
      <param name="pVarVal">このメソッドが返されるときに、取得したデータを格納する場所を指定する Object を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>DLL 内の関数へのエントリ ポイントの記述または仕様を取得します。</summary>
      <param name="memid">DLL エントリの記述を取得するメンバー関数の ID。</param>
      <param name="invKind">
        <paramref name="memid" /> で識別されるメンバーの種類を指定する <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 値の 1 つ。</param>
      <param name="pBstrDllName">null ではない場合、関数は <paramref name="pBstrDllName" /> を DLL の名前が格納されている BSTR に設定します。</param>
      <param name="pBstrName">null ではない場合、関数は <paramref name="lpbstrName" /> をエントリ ポイントの名前が格納されている BSTR に設定します。</param>
      <param name="pwOrdinal">null ではない場合、関数は序数によって定義されます。<paramref name="lpwOrdinal" /> はその序数を指すように設定されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>指定した型の説明に対するヘルプ トピックのドキュメント文字列、ヘルプ ファイルの完全限定名とパス、およびコンテキスト ID を取得します。</summary>
      <param name="index">ドキュメントを取得するメンバーの ID。</param>
      <param name="strName">このメソッドが返されるときに、項目メソッドの名前を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strDocString">このメソッドから制御が戻るときに、指定した項目のドキュメント文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="dwHelpContext">このメソッドが返されるときに、指定した項目に関連付けられたヘルプ コンテキストへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strHelpFile">このメソッドが返されるときに、ヘルプ ファイルの完全修飾名を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>ヘルプ ファイルのライブラリ ヘルプ トピックのドキュメント文字列、完全なヘルプ ファイル名とパス、使用するローカリゼーション コンテキスト、およびコンテキスト ID を取得します。</summary>
      <param name="memid">型の説明のメンバー識別子。</param>
      <param name="pbstrHelpString">このメソッドが返されるときに、指定した項目の名前を格納する BSTR を格納します。呼び出し元が項目名を必要としない場合、<paramref name="pbstrHelpString" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
      <param name="pdwHelpStringContext">このメソッドから制御が戻るときに、ヘルプのローカリゼーション コンテキストを格納します。呼び出し元がヘルプ コンテキストを必要としない場合、<paramref name="pdwHelpStringContext" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
      <param name="pbstrHelpStringDll">このメソッドが返されるときに、ヘルプ ファイルに使用される DLL を含むファイルの完全修飾名が入った BSTR を格納します。呼び出し元がファイル名を必要としない場合、<paramref name="pbstrHelpStringDll" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>指定した関数からカスタム データを取得します。</summary>
      <param name="index">カスタム データを取得する関数のインデックス。</param>
      <param name="guid">データの識別に使用する GUID。</param>
      <param name="pVarVal">このメソッドが返されるときに、データを格納する場所を指定する Object を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>指定した関数に関する情報を格納している <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体を取得します。</summary>
      <param name="index">取得する関数の説明のインデックス。</param>
      <param name="ppFuncDesc">このメソッドが返されるときに、指定した関数について記述する FUNCDESC 構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>メンバー名が未知の場合に、既知の DISPID に基づいて特定のメンバーにバインドします (既定のメンバーへのバインドなど)。</summary>
      <param name="memid">メンバー識別子。</param>
      <param name="invKind">memid で識別されるメンバーの種類を指定する <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 値の 1 つ。</param>
      <param name="pFuncIndex">このメソッドが返されるときに、関数のインデックスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>メンバー名をメンバー ID に、パラメーター名をパラメーター ID に割り当てます。</summary>
      <param name="rgszNames">割り当てる名前の配列。</param>
      <param name="cNames">割り当てる名前の数。</param>
      <param name="pMemId">このメソッドが返されるときに、名前の割り当てを格納する配列への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>カスタム データの実装の種類を取得します。</summary>
      <param name="index">カスタム データの実装の種類のインデックス。</param>
      <param name="guid">データの識別に使用する GUID。</param>
      <param name="pVarVal">このメソッドが返されるときに、取得したデータを格納する場所を指定する Object を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>型の説明で実装されたインターフェイスまたは基本インターフェイスに対する <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 値を取得します。</summary>
      <param name="index">実装されたインターフェイスまたは基本インターフェイスのインデックス。</param>
      <param name="pImplTypeFlags">このメソッドが返されるときに、IMPLTYPEFLAGS 列挙体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>マーシャリング情報を取得します。</summary>
      <param name="memid">必要なマーシャリング情報を示すメンバー ID。</param>
      <param name="pBstrMops">このメソッドが返されるときに、参照先の型の説明で記述されている、構造体のフィールドのマーシャリングに使用する opcode 文字列への参照を格納します。返す情報がない場合は、null を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>指定した関数 ID に対応する指定したメンバー ID の変数 (またはプロパティやメソッドの名前とそのパラメーター) を取得します。</summary>
      <param name="memid">1 つ以上の名前が返されるメンバーの ID。</param>
      <param name="rgBstrNames">このメソッドが返されるときに、メンバーと関連付けられた 1 つ以上の名前を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 配列の長さ。</param>
      <param name="pcNames">このメソッドが返されるときに、<paramref name="rgBstrNames" /> 配列内の名前の数を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>指定したカスタム データ パラメーターを取得します。</summary>
      <param name="indexFunc">カスタム データを取得する関数のインデックス。</param>
      <param name="indexParam">カスタム データを取得する関数のパラメーターのインデックス。</param>
      <param name="guid">データの識別に使用する GUID。</param>
      <param name="pVarVal">このメソッドが返されるときに、取得したデータを格納する場所を指定する Object を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>型の説明が別の型の説明を参照している場合に、参照先の型の説明を取得します。</summary>
      <param name="hRef">取得する参照先の型の説明へのハンドル。</param>
      <param name="ppTI">このメソッドが返されるときに、参照先の型の説明を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>型の説明が COM クラスの記述である場合に、実装されたインターフェイス型に関する型の説明を取得します。</summary>
      <param name="index">ハンドルを取得する実装された型のインデックス。</param>
      <param name="href">このメソッドが返されるときに、実装されたインターフェイスのハンドルへの参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>型の説明の属性を格納している <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 構造体を取得します。</summary>
      <param name="ppTypeAttr">このメソッドが返されるときに、この型の説明の属性を格納する構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>型の説明の ITypeComp インターフェイスを取得します。これを使用すると、クライアント コンパイラを型の説明のメンバーにバインドできます。</summary>
      <param name="ppTComp">このメソッドが返されるときに、型の説明が格納されているタイプ ライブラリの ITypeComp への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>割り当てを行わずに型フラグを返します。このメソッドは、DWORD 型のフラグを返します。このフラグは、TYPEATTR (型の属性) を増大させずに型フラグを展開します。</summary>
      <param name="pTypeFlags">このメソッドが返されるときに、TYPEFLAG への DWORD 型の参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>割り当てを行わずに、直ちに TYPEKIND 列挙体を返します。</summary>
      <param name="pTypeKind">このメソッドが返されるときに、TYPEKIND 列挙体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>カスタム データの変数を取得します。</summary>
      <param name="index">カスタム データを取得する変数のインデックス。</param>
      <param name="guid">データの識別に使用する GUID。</param>
      <param name="pVarVal">このメソッドが返されるときに、取得したデータを格納する場所を指定する Object を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>指定した変数を記述している VARDESC 構造体を取得します。</summary>
      <param name="index">取得する変数の説明のインデックス。</param>
      <param name="ppVarDesc">このメソッドが返されるときに、指定した変数について記述する VARDESC 構造体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>メンバー名が未知の場合に、既知の DISPID に基づいて特定のメンバーにバインドします (既定のメンバーへのバインドなど)。</summary>
      <param name="memid">メンバー識別子。</param>
      <param name="pVarIndex">このメソッドが返されるときに、<paramref name="memid" /> のインデックスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>型の説明により説明されるインターフェイスを実装しているオブジェクトについて、メソッドを呼び出したりプロパティにアクセスしたりします。</summary>
      <param name="pvInstance">型の説明で記述されているインターフェイスへの参照。</param>
      <param name="memid">インターフェイス メンバーの識別子。</param>
      <param name="wFlags">呼び出しのコンテキストを記述するフラグ。</param>
      <param name="pDispParams">引数の配列、名前付き引数の DISPID の配列、各配列内の要素数のカウントを格納している構造体への参照。</param>
      <param name="pVarResult">結果が格納される場所への参照。<paramref name="wFlags" /> が DISPATCH_PROPERTYPUT または DISPATCH_PROPERTYPUTREF を指定している場合、<paramref name="pVarResult" /> は無視されます。結果が必要ない場合は、null に設定します。</param>
      <param name="pExcepInfo">DISP_E_EXCEPTION が返される場合にだけ情報が格納される例外情報構造体へのポインター。</param>
      <param name="puArgErr">Invoke が DISP_E_TYPEMISMATCH を返す場合、<paramref name="puArgErr" /> は、型が正しくない引数のインデックスを示します。複数の引数がエラーを返す場合、<paramref name="puArgErr" /> はエラーのある最初の引数だけを示します。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> メソッドによって返された <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 構造体を解放します。</summary>
      <param name="pFuncDesc">解放する FUNCDESC 構造体への参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> メソッドによって返された <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 構造体を解放します。</summary>
      <param name="pTypeAttr">解放する TYPEATTR 構造体への参照。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>以前に <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> メソッドによって返された VARDESC 構造体を解放します。</summary>
      <param name="pVarDesc">解放する VARDESC 構造体への参照。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>ITypeLib インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>タイプ ライブラリで型の説明を検索します。</summary>
      <param name="szNameBuf">検索する名前。これは in/out パラメーターです。</param>
      <param name="lHashVal">LHashValOfNameSys 関数によって計算され、検索を高速化するハッシュ値。<paramref name="lHashVal" /> が 0 の場合、値が計算されます。</param>
      <param name="ppTInfo">このメソッドが返されるときに、<paramref name="szNameBuf" /> で指定した名前を含む型の説明へのポインターの配列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="rgMemId">検出された項目の MEMBERID の配列。<paramref name="rgMemId" />[i] は、<paramref name="ppTInfo" />[i] によって指定される型の説明にインデックスを作成する MEMBERID です。null にすることはできません。</param>
      <param name="pcFound">開始時に、検索するインスタンス数を示します。たとえば、最初の項目を検索するには、<paramref name="pcFound" /> = 1 を呼び出します。検索は 1 つのインスタンスが見つかったときに停止します。終了時には、検索されたインスタンス数を示します。<paramref name="pcFound" /> の in 値と out 値が同じ場合は、その名前を含む型の説明がもっと多く存在している可能性があります。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>ヘルプ ファイルのライブラリ ヘルプ トピックのライブラリ ドキュメント文字列、完全なヘルプ ファイル名とパス、およびコンテキスト識別子を取得します。</summary>
      <param name="index">ドキュメントを取得する型の説明のインデックス。</param>
      <param name="strName">このメソッドが返されるときに、指定した項目の名前を表す文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strDocString">このメソッドが返されるときに、指定した項目のドキュメント文字列を表す文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="dwHelpContext">このメソッドが返されるときに、指定した項目に関連付けられたヘルプ コンテキストの識別子を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strHelpFile">このメソッドが返されるときに、ヘルプ ファイルの完全修飾名を表す文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>ライブラリの属性を格納している構造体を取得します。</summary>
      <param name="ppTLibAttr">このメソッドが返されるときに、ライブラリの属性を格納している構造体を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>クライアント コンパイラがライブラリの型、変数、定数、およびグローバル関数をバインドできるようにします。</summary>
      <param name="ppTComp">このメソッドが返されるときに、この ITypeLib の ITypeComp インスタンスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>ライブラリ内の指定した型の説明を取得します。</summary>
      <param name="index">取得する ITypeInfo インターフェイスのインデックス。</param>
      <param name="ppTI">このメソッドが返されるときに、<paramref name="index" /> が参照する型について記述する ITypeInfo を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>タイプ ライブラリにある型の説明の数を返します。</summary>
      <returns>タイプ ライブラリにある型の説明の数。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>指定された GUID に対応するタイプ記述を取得します。</summary>
      <param name="guid">インターフェイスの IID または型情報が要求されたクラスの CLSID。</param>
      <param name="ppTInfo">このメソッドが返されるときに、要求された ITypeInfo インターフェイスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>型の説明の型を取得します。</summary>
      <param name="index">タイプ ライブラリ内の型の説明のインデックス。</param>
      <param name="pTKind">このメソッドが返されるときに、型の説明の TYPEKIND 列挙体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>渡された文字列がライブラリで記述される型またはメンバーの名前を格納しているかどうかを示します。</summary>
      <returns>
        <paramref name="szNameBuf" /> がタイプ ライブラリに存在する場合は true。それ以外の場合は false。</returns>
      <param name="szNameBuf">テストする文字列。これは in/out パラメーターです。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> のハッシュ値。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> メソッドから初めに取得した <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 構造体を解放します。</summary>
      <param name="pTLibAttr">解放する TLIBATTR 構造体。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>ITypeLib2 インターフェイスのマネージ定義を提供します。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>タイプ ライブラリで型の説明を検索します。</summary>
      <param name="szNameBuf">検索する名前。</param>
      <param name="lHashVal">LHashValOfNameSys 関数によって計算され、検索を高速化するハッシュ値。<paramref name="lHashVal" /> が 0 の場合、値が計算されます。</param>
      <param name="ppTInfo">このメソッドが返されるときに、<paramref name="szNameBuf" /> で指定した名前を含む型の説明へのポインターの配列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="rgMemId">このメソッドから制御が戻るときに、検出された項目の MEMBERID の配列を格納します。<paramref name="rgMemId" />[i] は、<paramref name="ppTInfo" />[i] によって指定される型の説明にインデックスを作成する MEMBERID です。このパラメーターには、null は指定できません。このパラメーターは初期化せずに渡されます。</param>
      <param name="pcFound">検索するインスタンス数を示している、開始時の値 (参照渡し)。たとえば、最初の項目を検索するには、<paramref name="pcFound" /> = 1 を呼び出します。検索は 1 つのインスタンスが見つかったときに停止します。終了時には、検索されたインスタンス数を示します。<paramref name="pcFound" /> の in 値と out 値が同じ場合は、その名前を含む型の説明がもっと多く存在している可能性があります。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>ライブラリのすべてのカスタム データ項目を取得します。</summary>
      <param name="pCustData">すべてのカスタム データ項目を格納する CUSTDATA へのポインター。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>カスタム データを取得します。</summary>
      <param name="guid">データの識別に使用される <see cref="T:System.Guid" /> (参照渡し)。</param>
      <param name="pVarVal">このメソッドから制御が戻るときに、取得したデータを格納する場所を指定するオブジェクトを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>ヘルプ ファイルのライブラリ ヘルプ トピックのライブラリ ドキュメント文字列、完全なヘルプ ファイル名とパス、およびコンテキスト識別子を取得します。</summary>
      <param name="index">ドキュメントを取得する型の説明のインデックス。</param>
      <param name="strName">このメソッドから制御が戻るときに、指定した項目の名前を指定する文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strDocString">このメソッドから制御が戻るときに、指定した項目のドキュメント文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="dwHelpContext">このメソッドが返されるときに、指定した項目に関連付けられたヘルプ コンテキストの識別子を格納します。このパラメーターは初期化せずに渡されます。</param>
      <param name="strHelpFile">このメソッドから制御が戻るときに、ヘルプ ファイルの完全修飾名を指定する文字列を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>ヘルプ ファイルのライブラリ ヘルプ トピックのライブラリのドキュメント文字列、完全なヘルプ ファイル名とパス、使用するローカリゼーション コンテキスト、およびコンテキスト ID を取得します。</summary>
      <param name="index">ドキュメントを取得する型の説明のインデックス。<paramref name="index" /> が -1 の場合は、ライブラリのドキュメントが返されます。</param>
      <param name="pbstrHelpString">このメソッドから制御が戻るときに、指定した項目の名前を指定する BSTR を格納します。呼び出し元が項目名を必要としない場合、<paramref name="pbstrHelpString" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
      <param name="pdwHelpStringContext">このメソッドから制御が戻るときに、ヘルプのローカリゼーション コンテキストを格納します。呼び出し元がヘルプ コンテキストを必要としない場合、<paramref name="pdwHelpStringContext" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
      <param name="pbstrHelpStringDll">このメソッドから制御が戻るときに、ヘルプ ファイルに使用される DLL を含むファイルの完全修飾名を指定する BSTR を格納します。呼び出し元がファイル名を必要としない場合、<paramref name="pbstrHelpStringDll" /> は null にできます。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>ライブラリの属性を格納している構造体を取得します。</summary>
      <param name="ppTLibAttr">このメソッドが返されるときに、ライブラリの属性を格納している構造体を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>ハッシュ テーブルの効果的なサイズ変更に必要なタイプ ライブラリに関する統計情報を返します。</summary>
      <param name="pcUniqueNames">一意名の数へのポインター。呼び出し元がこの情報を必要としない場合は、null に設定します。</param>
      <param name="pcchUniqueNames">このメソッドから制御が戻るときに、一意名の数の変更へのポインターを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>クライアント コンパイラがライブラリの型、変数、定数、およびグローバル関数をバインドできるようにします。</summary>
      <param name="ppTComp">このメソッドから制御が戻るときに、この ITypeLib の ITypeComp インスタンスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>ライブラリ内の指定した型の説明を取得します。</summary>
      <param name="index">取得する ITypeInfo インターフェイスのインデックス。</param>
      <param name="ppTI">このメソッドが返されるときに、<paramref name="index" /> が参照する型について記述する ITypeInfo を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>タイプ ライブラリにある型の説明の数を返します。</summary>
      <returns>タイプ ライブラリにある型の説明の数。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>指定された GUID に対応するタイプ記述を取得します。</summary>
      <param name="guid">型情報を要求されたクラスの CLSID インターフェイスの IID を表す <see cref="T:System.Guid" /> (参照渡し)。</param>
      <param name="ppTInfo">このメソッドが返されるときに、要求された ITypeInfo インターフェイスを格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>型の説明の型を取得します。</summary>
      <param name="index">タイプ ライブラリ内の型の説明のインデックス。</param>
      <param name="pTKind">このメソッドが返されるときに、型の説明の TYPEKIND 列挙体への参照を格納します。このパラメーターは初期化せずに渡されます。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>渡された文字列がライブラリで記述される型またはメンバーの名前を格納しているかどうかを示します。</summary>
      <returns>
        <paramref name="szNameBuf" /> がタイプ ライブラリに存在する場合は true。それ以外の場合は false。</returns>
      <param name="szNameBuf">テストする文字列。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> のハッシュ値。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> メソッドから初めに取得した <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 構造体を解放します。</summary>
      <param name="pTLibAttr">解放する TLIBATTR 構造体。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>タイプ ライブラリに適用するフラグを定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>タイプ ライブラリはコントロールについて記述します。表示できる形式を持たないオブジェクト用の型ブラウザーには表示しないでください。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>タイプ ライブラリは、ディスク上の永続化されたフォームに存在しています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>タイプ ライブラリの使用は制限されませんが、ユーザーに対しては表示しないでください。タイプ ライブラリは、コントロールによって使用される必要があります。ホストでは、エクステンダー プロパティと共にコントロールをラップする新しいタイプ ライブラリを作成することをお勧めします。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>タイプ ライブラリは制限されています。ユーザーに対して表示しないでください。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>構造体の要素、パラメーター、または関数の戻り値をプロセス間で転送する方法に関する情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>プロセス間で渡されている値へのポインターを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>構造体要素、パラメーター、または戻り値を記述するビットマスク値を表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>構造体要素、パラメーター、または関数の戻り値をプロセス間で転送する方法を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>パラメーターは、カスタム データを保持しています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>パラメーターは、定義された既定の動作を保持しています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>パラメーターは、呼び出し元から呼び出し先に情報を渡します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>パラメーターは、クライアント アプリケーションのローカル ID です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>パラメーターはオプションです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>パラメーターは、呼び出し先から呼び出し元に情報を返します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>パラメーターは、メンバーの戻り値です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>パラメーターが情報を渡すか、情報を受け取るかを指定しません。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>STATDATA 構造体のマネージ定義を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>アドバイズ シンクがデータの変更を通知される時点を決定する <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> 列挙値を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>変更通知を受け取る <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> インターフェイスを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>アドバイザリ コネクションを一意に識別するトークンを表します。このトークンは、アドバイザリ コネクションを設定するメソッドによって返されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>アドバイズ シンクが必要とするデータの <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 構造体を表します。アドバイズ シンクは、この <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 構造体で指定されたデータの変更の通知を受け取ります。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>開いているストレージ、ストリーム、またはバイト配列オブジェクトに関する統計情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>ストレージ、ストリーム、またはバイト配列に対する最後のアクセス時刻を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>ストリームまたはバイト配列のサイズをバイト単位で指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>ストレージ オブジェクトのクラス識別子を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>ストレージ、ストリーム、またはバイト配列の作成時刻を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>ストリームまたはバイト配列がサポートしている領域ロックの種類を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>オブジェクトを開いたときに指定されたアクセス モードを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>ストレージ オブジェクトの現在の状態ビット (最後に IStorage::SetStateBits メソッドによって設定された値) を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>ストレージ、ストリーム、またはバイト配列に対する最後の変更時刻を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>構造体が記述するオブジェクトの名前を格納している、終端が null の文字列へのポインターを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>将来使用するために予約されています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>STGTY 列挙体の値の 1 つで、ストレージ オブジェクトの種類を示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>STGMEDIUM 構造体のマネージ定義を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>受信プロセスが ReleaseStgMedium 関数を呼び出したときにストレージが解放される方法を送信プロセスが制御できるようにするインターフェイス インスタンスへのポインターを表します。<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> が null の場合、ReleaseStgMedium は既定プロシージャを使用してストレージを解放します。それ以外の場合、ReleaseStgMedium は指定された IUnknown インターフェイスを使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>ストレージ メディアの種類を指定します。マーシャリング ルーチンとマーシャリング解除ルーチンは、この値を使用して、どの共用体メンバーが使用されたかを確認します。この値は、<see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 列挙体の要素のいずれかである必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>受信プロセスが、転送されるデータにアクセスするために使用できるハンドル、文字列、またはインターフェイス ポインターを表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>対象オペレーティング システム プラットフォームを識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>タイプ ライブラリの対象オペレーティング システムは、Apple Macintosh です。既定では、すべてのデータ フィールドは偶数バイト境界に配置されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>タイプ ライブラリの対象オペレーティング システムは、16 ビット Windows システムです。既定では、データ フィールドはパックされます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>タイプ ライブラリの対象オペレーティング システムは、32 ビット Windows システムです。既定では、データ メンバーは自然に配置されます。たとえば、2 バイト整数は偶数バイト境界に配置され、4 バイト整数はクワドワード境界に配置されます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>タイプ ライブラリの対象オペレーティング システムは、64 ビット Windows システムです。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>TYMED 構造体のマネージ定義を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>ストレージ メディアは、拡張メタファイルです。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null の場合、目的のプロセスは DeleteEnhMetaFile を使用してビットマップを削除する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>ストレージ メディアは、パスで識別されるディスク ファイルです。STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null の場合、目的のプロセスは OpenFile を使用してファイルを削除する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>ストレージ メディアは、グラフィック デバイス インターフェイス (GDI: Graphics Device Interface) コンポーネント (HBITMAP) です。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null の場合、目的のプロセスは DeleteObject を使用してビットマップを削除する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>ストレージ メディアは、グローバル メモリ ハンドル (HGLOBAL) です。GMEM_SHARE フラグを使用して、グローバル メモリ ハンドルを割り当てます。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null の場合、目的のプロセスは GlobalFree を使用してメモリを解放する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>ストレージ メディアは、IStorage ポインターで識別されるストレージ コンポーネントです。データはストリーム内にあり、ストレージはこの IStorage インスタンスにより格納されます。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null でない場合、目的のプロセスは IStorage::Release を使用してストレージ コンポーネントを解放する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>ストレージ メディアは、IStream ポインターで識別されるストリーム オブジェクトです。ISequentialStream::Read を使用してデータを読み取ります。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null でない場合、目的のプロセスは IStream::Release を使用してストリーム コンポーネントを解放する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>ストレージ メディアは、メタファイル (HMETAFILE) です。Windows 関数または Win32 関数を使用して、メタフェイルのデータにアクセスします。<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> メンバーが null の場合、目的のプロセスは DeleteMetaFile を使用してビットマップを削除する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>データが渡されていません。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>UCOMITypeInfo の属性を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>この型のインスタンスのバイト配置を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>この型のインスタンスのサイズ。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>この型の仮想メソッド テーブル (VTBL) のサイズ。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>構造体が記述するインターフェイス上の関数の数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>構造体が記述するインターフェイス上で実装されたインターフェイスの数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>構造体が記述するインターフェイス上の変数とデータ フィールドの数を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>将来使用するために予約されています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>型情報の GUID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>記述された型の IDL 属性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>メンバー名とドキュメント文字列のロケール。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>将来使用するために予約されています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>
        <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> フィールドと <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" /> フィールドで使用する定数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>コンストラクターの ID。または存在しない場合は <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>デストラクターの ID。または存在しない場合は <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>
        <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" /> の場合は、この型をエイリアスとする元の型を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>この情報が記述している型を記述する <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> 値。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>メジャー バージョン番号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>マイナー バージョン番号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>この情報を記述する <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> 値。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>変数の型、関数の戻り値の型、関数パラメーターの型を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>変数が VT_SAFEARRAY または VT_PTR の場合、lpValue フィールドは要素の型を指定する TYPEDESC へのポインターを格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>TYPEDESC が記述する項目のバリアントの型を示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>型の説明のプロパティと属性を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>クラスは、集約をサポートしています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Application オブジェクトを記述する型の説明。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>型のインスタンスは、ITypeInfo::CreateInstance で作成できます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>型は他の型の派生元になるコントロールであり、ユーザーには表示されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>インターフェイスが IDispatch から直接または間接に派生したものであることを示します。このフラグは計算されます。このフラグのオブジェクト記述言語はありません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>インターフェイスは、IDispatch と VTBL バインディングの両方を提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>型はブラウザーに表示しないでください。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>型はライセンス付きです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>インターフェイスは、実行時にメンバーを追加できません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>インターフェイスで使用される型は、VTBL バインディングのサポートを含むオートメーションに完全に準拠しています。インターフェイスでデュアルを設定すると、このフラグと <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" /> が設定されます。このフラグはディスパッチ インターフェイスには設定できません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>型は定義済みです。クライアント アプリケーションは、この属性を持つオブジェクトの単一のインスタンスを自動的に作成します。オブジェクトを指す変数の名前は、そのオブジェクトのクラス名と同じです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>インターフェイスはプロキシ/スタブ ダイナミック リンク ライブラリを使用することを示します。このフラグは、タイプ ライブラリの登録が解除されている場合に、タイプ ライブラリ プロキシの登録を解除してはいけないことを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>オブジェクトは、IConnectionPointWithDefault をサポートし、既定の動作を備えています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>マクロ言語からはアクセスできません。このフラグは、システム レベルの型または型ブラウザーに表示しない型で使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>基本インターフェイスが、名前解決を確認してから子を確認することを示します。これは既定の反対の動作です。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>データおよび関数のさまざまな型を指定します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>別の型のエイリアスである型です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>実装されたコンポーネントのインターフェイス セットです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>IDispatch::Invoke を使用してアクセスできるメソッド、およびプロパティのセット。既定では、デュアル インターフェイスは TKIND_DISPATCH を返します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>列挙子のセットです。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>すべて純粋仮想関数である仮想関数を持つ型です。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>列挙体末尾のマーカー。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>静的関数および静的データ (DLL など) だけを保持できるモジュール。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>メソッドを持たない構造体。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>ゼロのオフセットを持つ、すべてのメンバーの和集合。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>特定のタイプ ライブラリを識別し、メンバー名に対するローカリゼーション サポートを提供します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>タイプ ライブラリのグローバル一意ライブラリ識別子を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>タイプ ライブラリのロケール ID を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>タイプ ライブラリの対象のハードウェア プラットフォームを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>ライブラリ フラグを表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>タイプ ライブラリのメジャー バージョン番号を表します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>タイプ ライブラリのマイナー バージョン番号を表します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>変数、定数、またはデータの各メンバーを記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>変数に関する情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>変数を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>このフィールドは将来使用するために予約されています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>変数のメンバー ID を示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>変数をマーシャリングする方法を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>変数のプロパティを指定します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>変数に関する情報を格納します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>記号定数を記述します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>インスタンス内でこの変数のオフセットを示します。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>変数のプロパティを定義する定数を識別します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>変数はデータ連結をサポートしています。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>変数は、オブジェクトを最もよく表す単一のプロパティです。型情報の 1 つの変数だけが、この属性を持つことができます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>コンパイラが "abc" の型で "xyz" という名前のメンバーを検索する場合に最適化を許可します。そのようなメンバーが見つかり、既定のコレクション要素のアクセサー関数であることを示すフラグが付いている場合、そのメンバー関数の呼び出しが生成されます。dispinterface およびインターフェイスのメンバーでは許可されますが、モジュールでは許可されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>変数は、連結できる状態でユーザーに表示されます。<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" /> も設定する必要があります。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>連結できる変数が存在しますが、ユーザーのブラウザーには表示されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>変数は、個別のバインド可能なプロパティとして割り当てられます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>変数はオブジェクト ブラウザーに表示されますが、プロパティ ブラウザーには表示されません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>変数への割り当ては許可しないでください。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>インターフェイスが既定の動作を備えていることを示します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>設定した場合は、そのプロパティを直接変更しようとすると IPropertyNotifySink::OnRequestEdit が呼び出されます。OnRequestEdit の実装は、変更を受け入れるかどうかを判断します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>変数は、マクロ言語からはアクセスできません。このフラグは、システム レベル変数または型ブラウザーに表示しない変数で使用します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>変数は、イベントの発生元のオブジェクトを返します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>変数は、ユーザー インターフェイスの既定の表示です。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>変数の種類を定義します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>VARDESC 構造体は、記号定数を記述します。記号定数に関連付けられたメモリはありません。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>変数には、IDispatch::Invoke を使用したときにだけアクセスできます。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>変数は、フィールドまたは型のメンバーです。それぞれの型のインスタンス内の固定オフセットに存在します。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>変数のインスタンスは 1 つしか存在しません。</summary>
    </member>
  </members>
</doc>