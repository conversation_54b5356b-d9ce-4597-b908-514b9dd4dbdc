<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Text.Encodings.Web</name>
    </assembly>
    <members>
        <member name="M:System.IO.TextWriterExtensions.WritePartialString(System.IO.TextWriter,System.String,System.Int32,System.Int32)">
            <summary>
            Writes a partial string (given offset and count) to the underlying TextWriter.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.AsciiByteMap">
            <summary>
            A lookup map that maps individual ASCII chars to a single byte.
            Storing a 0 byte indicates that no mapping exists for this input.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AsciiByteMap.TryLookup(System.Text.Rune,System.Byte@)">
            <summary>
            Returns false if <paramref name="key"/> is non-ASCII or if it
            maps to a zero value.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap">
            <summary>
            A bitmap which represents all 64k codepoints in the
            Basic Multilingual Plane.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.AllowChar(System.Char)">
            <summary>
            Adds the given <see cref="T:System.Char"/> to the bitmap's allow list.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.ForbidChar(System.Char)">
            <summary>
            Removes the given <see cref="T:System.Char"/> from the bitmap's allow list.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.ForbidHtmlCharacters">
            <summary>
            Removes all HTML-sensitive characters from the bitmap's allow list.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.ForbidUndefinedCharacters">
            <summary>
            Removes from the bitmap's allow list all code points which aren't mapped to defined characters
            or which are otherwise always disallowed.
            </summary>
            <remarks>
            Always-disallowed categories include Cc, Cs, Co, Cn, Zs [except U+0020 SPACE], Zl, and Zp.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.IsCharAllowed(System.Char)">
            <summary>
            Queries the bitmap to see if the given <see cref="T:System.Char"/> is in the allow list.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.AllowedBmpCodePointsBitmap.IsCodePointAllowed(System.UInt32)">
            <summary>
            Queries the bitmap to see if the given code point is in the allow list.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.OptimizedInboxTextEncoder">
            <summary>
            Allows efficient escaping for the library's built-in types (HTML, URL, JS).
            Assumes the following:
              (a) All C0 and C1 code points are disallowed.
              (b) Escaping 1 ASCII input character results in no more than 6 output characters.
              (c) All Unicode scalar values may be represented in escaped form.
              (d) The escaped form of any Unicode scalar value consists of only ASCII characters.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.OptimizedInboxTextEncoder.AllowedAsciiCodePoints">
            <summary>
            A bitmap which represents allowed ASCII code points.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.OptimizedInboxTextEncoder.AsciiPreescapedData">
             <summary>
             A bitmap which represents the 64-bit pre-escaped form of the ASCII code points.
             A pre-escaped code point has the form [ WW 00 FF EE DD CC BB AA ],
             where AA - FF are the six-ASCII-byte escaped representation of the
             code point, zero-padded at the end. The high byte of the pre-escaped form
             is the number of non-zero bytes which make up the pre-escaped data.
            
             Example: If the escaped form of "@" is "%40", the pre-escaped form will be:
             0x30_00_00_00_00_30_34_25. Iterate over the least significant bytes one-by-one
             to reconstruct the escaped representation, stopping when you hit a null byte.
             </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.OptimizedInboxTextEncoder.IsScalarValueAllowed(System.Text.Rune)">
            <summary>
            Given a scalar value, returns a value stating whether that value is present
            in this encoder's allow list.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.SpanUtility">
            <summary>
            Contains helpers for manipulating spans so that we can keep unsafe code out of the common path.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteBytes(System.Span{System.Byte},System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Tries writing four bytes to the span. If success, returns true. If the span is not large
            enough to hold four bytes, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteBytes(System.Span{System.Byte},System.Byte,System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Tries writing five bytes to the span. If success, returns true. If the span is not large
            enough to hold five bytes, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteBytes(System.Span{System.Byte},System.Byte,System.Byte,System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Tries writing six bytes to the span. If success, returns true. If the span is not large
            enough to hold six bytes, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteChars(System.Span{System.Char},System.Char,System.Char,System.Char,System.Char)">
            <summary>
            Tries writing four chars to the span. If success, returns true. If the span is not large
            enough to hold four chars, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteChars(System.Span{System.Char},System.Char,System.Char,System.Char,System.Char,System.Char)">
            <summary>
            Tries writing five chars to the span. If success, returns true. If the span is not large
            enough to hold five chars, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteChars(System.Span{System.Char},System.Char,System.Char,System.Char,System.Char,System.Char,System.Char)">
            <summary>
            Tries writing six chars to the span. If success, returns true. If the span is not large
            enough to hold six chars, leaves the span unchanged and returns false.
            </summary>
            <remarks>
            Parameters are intended to be constant values.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.SpanUtility.TryWriteUInt64LittleEndian(System.Span{System.Byte},System.Int32,System.UInt64)">
            <summary>
            Tries writing a 64-bit value as little endian to the span. If success, returns true. If
            the span is not large enough to hold 8 bytes, leaves the span unchanged and returns false.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.ScalarEscaperBase">
            <summary>
            A class that can escape a scalar value and write either UTF-16 or UTF-8 format.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.HtmlEncoder">
            <summary>
            Represents a type used to do HTML encoding.
            </summary>
        </member>
        <member name="P:System.Text.Encodings.Web.HtmlEncoder.Default">
            <summary>
            Returns a default built-in instance of <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/>.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.HtmlEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
            <summary>
            Creates a new instance of HtmlEncoder with provided settings.
            </summary>
            <param name="settings">Settings used to control how the created <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/> encodes, primarily which characters to encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/>.</returns>
        </member>
        <member name="M:System.Text.Encodings.Web.HtmlEncoder.Create(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Creates a new instance of HtmlEncoder specifying character to be encoded.
            </summary>
            <param name="allowedRanges">Set of characters that the encoder is allowed to not encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/></returns>
            <remarks>Some characters in <paramref name="allowedRanges"/> might still get encoded, i.e. this parameter is just telling the encoder what ranges it is allowed to not encode, not what characters it must not encode.</remarks>
        </member>
        <member name="T:System.Text.Encodings.Web.JavaScriptEncoder">
            <summary>
            Represents a type used to do JavaScript encoding/escaping.
            </summary>
        </member>
        <member name="P:System.Text.Encodings.Web.JavaScriptEncoder.Default">
            <summary>
            Returns a default built-in instance of <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/>.
            </summary>
        </member>
        <member name="P:System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping">
            <summary>
            Returns a built-in instance of <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/> that is less strict about what gets encoded.
            </summary>
            <remarks>
            <para>
            Unlike the <see cref="P:System.Text.Encodings.Web.JavaScriptEncoder.Default"/>, this encoder instance does not escape HTML-sensitive characters like &lt;, &gt;, &amp;, etc. and hence must be used cautiously
            (for example, if the output data is within a response whose content-type is known with a charset set to UTF-8).
            </para>
            <para>
            Unlike the <see cref="P:System.Text.Encodings.Web.JavaScriptEncoder.Default"/>, the quotation mark is encoded as \" rather than \u0022.
            </para>
            <para>
            Unlike the <see cref="P:System.Text.Encodings.Web.JavaScriptEncoder.Default"/> (which only allows <see cref="P:System.Text.Unicode.UnicodeRanges.BasicLatin"/>), using this encoder instance allows <see cref="P:System.Text.Unicode.UnicodeRanges.All"/> to go through unescaped.
            </para>
            <para>
            Unlike the <see cref="P:System.Text.Encodings.Web.JavaScriptEncoder.Default"/>, this encoder instance allows some other characters to go through unescaped (for example, '+'), and hence must be used cautiously.
            </para>
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
            <summary>
            Creates a new instance of JavaScriptEncoder with provided settings.
            </summary>
            <param name="settings">Settings used to control how the created <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/> encodes, primarily which characters to encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/>.</returns>
        </member>
        <member name="M:System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Creates a new instance of JavaScriptEncoder specifying character to be encoded.
            </summary>
            <param name="allowedRanges">Set of characters that the encoder is allowed to not encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/>.</returns>
            <remarks>Some characters in <paramref name="allowedRanges"/> might still get encoded, i.e. this parameter is just telling the encoder what ranges it is allowed to not encode, not what characters it must not encode.</remarks>
        </member>
        <member name="T:System.Text.Encodings.Web.TextEncoder">
            <summary>
            An abstraction representing various text encoders.
            </summary>
            <remarks>
            TextEncoder subclasses can be used to do HTML encoding, URI encoding, and JavaScript encoding.
            Instances of such subclasses can be accessed using <see cref="P:System.Text.Encodings.Web.HtmlEncoder.Default"/>, <see cref="P:System.Text.Encodings.Web.UrlEncoder.Default"/>, and <see cref="P:System.Text.Encodings.Web.JavaScriptEncoder.Default"/>.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.TryEncodeUnicodeScalar(System.Int32,System.Char*,System.Int32,System.Int32@)">
            <summary>
            Encodes a Unicode scalar into a buffer.
            </summary>
            <param name="unicodeScalar">Unicode scalar.</param>
            <param name="buffer">The destination of the encoded text.</param>
            <param name="bufferLength">Length of the destination <paramref name="buffer"/> in chars.</param>
            <param name="numberOfCharactersWritten">Number of characters written to the <paramref name="buffer"/>.</param>
            <returns>Returns false if <paramref name="bufferLength"/> is too small to fit the encoded text, otherwise returns true.</returns>
            <remarks>This method is seldom called directly. One of the TextEncoder.Encode overloads should be used instead.
            Implementations of <see cref="T:System.Text.Encodings.Web.TextEncoder"/> need to be thread safe and stateless.
            </remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.FindFirstCharacterToEncode(System.Char*,System.Int32)">
            <summary>
            Finds index of the first character that needs to be encoded.
            </summary>
            <param name="text">The text buffer to search.</param>
            <param name="textLength">The number of characters in the <paramref name="text"/>.</param>
            <returns></returns>
            <remarks>This method is seldom called directly. It's used by higher level helper APIs.</remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.WillEncode(System.Int32)">
            <summary>
            Determines if a given Unicode scalar will be encoded.
            </summary>
            <param name="unicodeScalar">Unicode scalar.</param>
            <returns>Returns true if the <paramref name="unicodeScalar"/> will be encoded by this encoder, otherwise returns false.</returns>
        </member>
        <member name="P:System.Text.Encodings.Web.TextEncoder.MaxOutputCharactersPerInputCharacter">
            <summary>
            Maximum number of characters that this encoder can generate for each input character.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.String)">
            <summary>
            Encodes the supplied string and returns the encoded text as a new string.
            </summary>
            <param name="value">String to encode.</param>
            <returns>Encoded string.</returns>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.String)">
            <summary>
            Encodes the supplied string into a <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="output">Encoded text is written to this output.</param>
            <param name="value">String to be encoded.</param>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.String,System.Int32,System.Int32)">
            <summary>
             Encodes a substring into a <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="output">Encoded text is written to this output.</param>
            <param name="value">String whose substring is to be encoded.</param>
            <param name="startIndex">The index where the substring starts.</param>
            <param name="characterCount">Number of characters in the substring.</param>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.Char[],System.Int32,System.Int32)">
            <summary>
             Encodes characters from an array into a <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="output">Encoded text is written to the output.</param>
            <param name="value">Array of characters to be encoded.</param>
            <param name="startIndex">The index where the substring starts.</param>
            <param name="characterCount">Number of characters in the substring.</param>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.EncodeUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Encodes the supplied UTF-8 text.
            </summary>
            <param name="utf8Source">A source buffer containing the UTF-8 text to encode.</param>
            <param name="utf8Destination">The destination buffer to which the encoded form of <paramref name="utf8Source"/>
            will be written.</param>
            <param name="bytesConsumed">The number of bytes consumed from the <paramref name="utf8Source"/> buffer.</param>
            <param name="bytesWritten">The number of bytes written to the <paramref name="utf8Destination"/> buffer.</param>
            <param name="isFinalBlock"><see langword="true"/> if there is further source data that needs to be encoded;
            <see langword="false"/> if there is no further source data that needs to be encoded.</param>
            <returns>An <see cref="T:System.Buffers.OperationStatus"/> describing the result of the encoding operation.</returns>
            <remarks>The buffers <paramref name="utf8Source"/> and <paramref name="utf8Destination"/> must not overlap.</remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Encodes the supplied characters.
            </summary>
            <param name="source">A source buffer containing the characters to encode.</param>
            <param name="destination">The destination buffer to which the encoded form of <paramref name="source"/>
            will be written.</param>
            <param name="charsConsumed">The number of characters consumed from the <paramref name="source"/> buffer.</param>
            <param name="charsWritten">The number of characters written to the <paramref name="destination"/> buffer.</param>
            <param name="isFinalBlock"><see langword="true"/> if there is further source data that needs to be encoded;
            <see langword="false"/> if there is no further source data that needs to be encoded.</param>
            <returns>An <see cref="T:System.Buffers.OperationStatus"/> describing the result of the encoding operation.</returns>
            <remarks>The buffers <paramref name="source"/> and <paramref name="destination"/> must not overlap.</remarks>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoder.FindFirstCharacterToEncodeUtf8(System.ReadOnlySpan{System.Byte})">
            <summary>
            Given a UTF-8 text input buffer, finds the first element in the input buffer which would be
            escaped by the current encoder instance.
            </summary>
            <param name="utf8Text">The UTF-8 text input buffer to search.</param>
            <returns>
            The index of the first element in <paramref name="utf8Text"/> which would be escaped by the
            current encoder instance, or -1 if no data in <paramref name="utf8Text"/> requires escaping.
            </returns>
        </member>
        <member name="T:System.Text.Encodings.Web.TextEncoderSettings">
            <summary>
            Represents a filter which allows only certain Unicode code points through.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor">
            <summary>
            Instantiates an empty filter (allows no code points through by default).
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor(System.Text.Encodings.Web.TextEncoderSettings)">
            <summary>
            Instantiates the filter by cloning the allow list of another <see cref="T:System.Text.Encodings.Web.TextEncoderSettings"/>.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Instantiates the filter where only the character ranges specified by <paramref name="allowedRanges"/>
            are allowed by the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCharacter(System.Char)">
            <summary>
            Allows the character specified by <paramref name="character"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCharacters(System.Char[])">
            <summary>
            Allows all characters specified by <paramref name="characters"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCodePoints(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Allows all code points specified by <paramref name="codePoints"/>.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowRange(System.Text.Unicode.UnicodeRange)">
            <summary>
            Allows all characters specified by <paramref name="range"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowRanges(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Allows all characters specified by <paramref name="ranges"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.Clear">
            <summary>
            Resets this settings object by disallowing all characters.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidCharacter(System.Char)">
            <summary>
            Disallows the character <paramref name="character"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidCharacters(System.Char[])">
            <summary>
            Disallows all characters specified by <paramref name="characters"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidRange(System.Text.Unicode.UnicodeRange)">
            <summary>
            Disallows all characters specified by <paramref name="range"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidRanges(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Disallows all characters specified by <paramref name="ranges"/> through the filter.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.GetAllowedCodePoints">
            <summary>
            Gets an enumeration of all allowed code points.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.TextEncoderSettings.GetAllowedCodePointsBitmap">
            <summary>
            Retrieves the bitmap of allowed characters from this settings object.
            The data is returned readonly byref.
            </summary>
        </member>
        <member name="T:System.Text.Encodings.Web.UrlEncoder">
            <summary>
            Represents a type used to do URL encoding.
            </summary>
        </member>
        <member name="P:System.Text.Encodings.Web.UrlEncoder.Default">
            <summary>
            Returns a default built-in instance of <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.
            </summary>
        </member>
        <member name="M:System.Text.Encodings.Web.UrlEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
            <summary>
            Creates a new instance of UrlEncoder with provided settings.
            </summary>
            <param name="settings">Settings used to control how the created <see cref="T:System.Text.Encodings.Web.UrlEncoder"/> encodes, primarily which characters to encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.</returns>
        </member>
        <member name="M:System.Text.Encodings.Web.UrlEncoder.Create(System.Text.Unicode.UnicodeRange[])">
            <summary>
            Creates a new instance of UrlEncoder specifying character to be encoded.
            </summary>
            <param name="allowedRanges">Set of characters that the encoder is allowed to not encode.</param>
            <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.</returns>
            <remarks>Some characters in <paramref name="allowedRanges"/> might still get encoded, i.e. this parameter is just telling the encoder what ranges it is allowed to not encode, not what characters it must not encode.</remarks>
        </member>
        <member name="T:System.Text.Unicode.UnicodeHelpers">
            <summary>
            Contains helpers for dealing with Unicode code points.
            </summary>
        </member>
        <member name="F:System.Text.Unicode.UnicodeHelpers.UNICODE_LAST_CODEPOINT">
            <summary>
            The last code point defined by the Unicode specification.
            </summary>
        </member>
        <member name="M:System.Text.Unicode.UnicodeHelpers.GetDefinedBmpCodePointsBitmapLittleEndian">
            <summary>
            Returns a bitmap of all BMP code points as a series of little-endian 32-bit values.
            On other-endian architectures, caller must convert each 32-bit integer to native endianness
            before using the data.
            </summary>
        </member>
        <member name="M:System.Text.Unicode.UnicodeHelpers.GetUtf8RepresentationForScalarValue(System.UInt32)">
            <summary>
            Given a Unicode scalar value, returns the UTF-8 representation of the value.
            The return value's bytes should be popped from the LSB.
            </summary>
        </member>
        <member name="M:System.Text.Unicode.UnicodeHelpers.IsSupplementaryCodePoint(System.Int32)">
            <summary>
            Determines whether the given scalar value is in the supplementary plane and thus
            requires 2 characters to be represented in UTF-16 (as a surrogate pair).
            </summary>
        </member>
        <member name="T:System.Text.Unicode.UnicodeRange">
            <summary>
            Represents a contiguous range of Unicode code points.
            </summary>
            <remarks>
            Currently only the Basic Multilingual Plane is supported.
            </remarks>
        </member>
        <member name="M:System.Text.Unicode.UnicodeRange.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a new <see cref="T:System.Text.Unicode.UnicodeRange"/>.
            </summary>
            <param name="firstCodePoint">The first code point in the range.</param>
            <param name="length">The number of code points in the range.</param>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRange.FirstCodePoint">
            <summary>
            The first code point in this range.
            </summary>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRange.Length">
            <summary>
            The number of code points in this range.
            </summary>
        </member>
        <member name="M:System.Text.Unicode.UnicodeRange.Create(System.Char,System.Char)">
            <summary>
            Creates a new <see cref="T:System.Text.Unicode.UnicodeRange"/> from a span of characters.
            </summary>
            <param name="firstCharacter">The first character in the range.</param>
            <param name="lastCharacter">The last character in the range.</param>
            <returns>The <see cref="T:System.Text.Unicode.UnicodeRange"/> representing this span.</returns>
        </member>
        <member name="T:System.Text.Unicode.UnicodeRanges">
            <summary>
            Contains predefined <see cref="T:System.Text.Unicode.UnicodeRange"/> instances which correspond to blocks
            from the Unicode 7.0 specification.
            </summary>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.None">
            <summary>
            An empty <see cref="T:System.Text.Unicode.UnicodeRange"/>. This range contains no code points.
            </summary>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.All">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> which contains all characters in the Unicode Basic
            Multilingual Plane (U+0000..U+FFFF).
            </summary>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.BasicLatin">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Basic Latin' Unicode block (U+0000..U+007F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0000.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Latin1Supplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin-1 Supplement' Unicode block (U+0080..U+00FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0080.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended-A' Unicode block (U+0100..U+017F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0100.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended-B' Unicode block (U+0180..U+024F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0180.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.IpaExtensions">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'IPA Extensions' Unicode block (U+0250..U+02AF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0250.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SpacingModifierLetters">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Spacing Modifier Letters' Unicode block (U+02B0..U+02FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U02B0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarks">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Combining Diacritical Marks' Unicode block (U+0300..U+036F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0300.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GreekandCoptic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Greek and Coptic' Unicode block (U+0370..U+03FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0370.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Cyrillic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cyrillic' Unicode block (U+0400..U+04FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0400.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cyrillic Supplement' Unicode block (U+0500..U+052F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0500.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Armenian">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Armenian' Unicode block (U+0530..U+058F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0530.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Hebrew">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hebrew' Unicode block (U+0590..U+05FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0590.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Arabic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic' Unicode block (U+0600..U+06FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0600.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Syriac">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Syriac' Unicode block (U+0700..U+074F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0700.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ArabicSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic Supplement' Unicode block (U+0750..U+077F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0750.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Thaana">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Thaana' Unicode block (U+0780..U+07BF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0780.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.NKo">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'NKo' Unicode block (U+07C0..U+07FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U07C0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Samaritan">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Samaritan' Unicode block (U+0800..U+083F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0800.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Mandaic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Mandaic' Unicode block (U+0840..U+085F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0840.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SyriacSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Syriac Supplement' Unicode block (U+0860..U+086F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0860.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ArabicExtendedB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic Extended-B' Unicode block (U+0870..U+089F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0870.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ArabicExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic Extended-A' Unicode block (U+08A0..U+08FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U08A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Devanagari">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Devanagari' Unicode block (U+0900..U+097F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0900.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Bengali">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Bengali' Unicode block (U+0980..U+09FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0980.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Gurmukhi">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Gurmukhi' Unicode block (U+0A00..U+0A7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0A00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Gujarati">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Gujarati' Unicode block (U+0A80..U+0AFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0A80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Oriya">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Oriya' Unicode block (U+0B00..U+0B7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0B00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Tamil">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tamil' Unicode block (U+0B80..U+0BFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0B80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Telugu">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Telugu' Unicode block (U+0C00..U+0C7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0C00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Kannada">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Kannada' Unicode block (U+0C80..U+0CFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0C80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Malayalam">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Malayalam' Unicode block (U+0D00..U+0D7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0D00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Sinhala">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Sinhala' Unicode block (U+0D80..U+0DFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0D80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Thai">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Thai' Unicode block (U+0E00..U+0E7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0E00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Lao">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Lao' Unicode block (U+0E80..U+0EFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0E80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Tibetan">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tibetan' Unicode block (U+0F00..U+0FFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U0F00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Myanmar">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Myanmar' Unicode block (U+1000..U+109F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1000.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Georgian">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Georgian' Unicode block (U+10A0..U+10FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U10A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamo">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hangul Jamo' Unicode block (U+1100..U+11FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1100.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Ethiopic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ethiopic' Unicode block (U+1200..U+137F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1200.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ethiopic Supplement' Unicode block (U+1380..U+139F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1380.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Cherokee">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cherokee' Unicode block (U+13A0..U+13FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U13A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.UnifiedCanadianAboriginalSyllabics">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Unified Canadian Aboriginal Syllabics' Unicode block (U+1400..U+167F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1400.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Ogham">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ogham' Unicode block (U+1680..U+169F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1680.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Runic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Runic' Unicode block (U+16A0..U+16FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U16A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Tagalog">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tagalog' Unicode block (U+1700..U+171F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1700.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Hanunoo">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hanunoo' Unicode block (U+1720..U+173F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1720.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Buhid">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Buhid' Unicode block (U+1740..U+175F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1740.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Tagbanwa">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tagbanwa' Unicode block (U+1760..U+177F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1760.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Khmer">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Khmer' Unicode block (U+1780..U+17FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1780.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Mongolian">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Mongolian' Unicode block (U+1800..U+18AF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1800.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.UnifiedCanadianAboriginalSyllabicsExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Unified Canadian Aboriginal Syllabics Extended' Unicode block (U+18B0..U+18FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U18B0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Limbu">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Limbu' Unicode block (U+1900..U+194F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1900.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.TaiLe">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tai Le' Unicode block (U+1950..U+197F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1950.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.NewTaiLue">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'New Tai Lue' Unicode block (U+1980..U+19DF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1980.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.KhmerSymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Khmer Symbols' Unicode block (U+19E0..U+19FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U19E0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Buginese">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Buginese' Unicode block (U+1A00..U+1A1F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1A00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.TaiTham">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tai Tham' Unicode block (U+1A20..U+1AAF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1A20.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Combining Diacritical Marks Extended' Unicode block (U+1AB0..U+1AFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1AB0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Balinese">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Balinese' Unicode block (U+1B00..U+1B7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1B00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Sundanese">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Sundanese' Unicode block (U+1B80..U+1BBF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1B80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Batak">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Batak' Unicode block (U+1BC0..U+1BFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1BC0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Lepcha">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Lepcha' Unicode block (U+1C00..U+1C4F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1C00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.OlChiki">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ol Chiki' Unicode block (U+1C50..U+1C7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1C50.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedC">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cyrillic Extended-C' Unicode block (U+1C80..U+1C8F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1C80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GeorgianExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Georgian Extended' Unicode block (U+1C90..U+1CBF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1C90.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SundaneseSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Sundanese Supplement' Unicode block (U+1CC0..U+1CCF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1CC0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.VedicExtensions">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Vedic Extensions' Unicode block (U+1CD0..U+1CFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1CD0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.PhoneticExtensions">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Phonetic Extensions' Unicode block (U+1D00..U+1D7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1D00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.PhoneticExtensionsSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Phonetic Extensions Supplement' Unicode block (U+1D80..U+1DBF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1D80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Combining Diacritical Marks Supplement' Unicode block (U+1DC0..U+1DFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1DC0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedAdditional">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended Additional' Unicode block (U+1E00..U+1EFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1E00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GreekExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Greek Extended' Unicode block (U+1F00..U+1FFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U1F00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GeneralPunctuation">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'General Punctuation' Unicode block (U+2000..U+206F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2000.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SuperscriptsandSubscripts">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Superscripts and Subscripts' Unicode block (U+2070..U+209F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2070.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CurrencySymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Currency Symbols' Unicode block (U+20A0..U+20CF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U20A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksforSymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Combining Diacritical Marks for Symbols' Unicode block (U+20D0..U+20FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U20D0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LetterlikeSymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Letterlike Symbols' Unicode block (U+2100..U+214F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2100.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.NumberForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Number Forms' Unicode block (U+2150..U+218F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2150.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Arrows">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arrows' Unicode block (U+2190..U+21FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2190.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MathematicalOperators">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Mathematical Operators' Unicode block (U+2200..U+22FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2200.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousTechnical">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Miscellaneous Technical' Unicode block (U+2300..U+23FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2300.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ControlPictures">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Control Pictures' Unicode block (U+2400..U+243F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2400.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.OpticalCharacterRecognition">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Optical Character Recognition' Unicode block (U+2440..U+245F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2440.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.EnclosedAlphanumerics">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Enclosed Alphanumerics' Unicode block (U+2460..U+24FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2460.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.BoxDrawing">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Box Drawing' Unicode block (U+2500..U+257F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2500.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.BlockElements">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Block Elements' Unicode block (U+2580..U+259F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2580.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GeometricShapes">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Geometric Shapes' Unicode block (U+25A0..U+25FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U25A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousSymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Miscellaneous Symbols' Unicode block (U+2600..U+26FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2600.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Dingbats">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Dingbats' Unicode block (U+2700..U+27BF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2700.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousMathematicalSymbolsA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Miscellaneous Mathematical Symbols-A' Unicode block (U+27C0..U+27EF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U27C0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalArrowsA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Supplemental Arrows-A' Unicode block (U+27F0..U+27FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U27F0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.BraillePatterns">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Braille Patterns' Unicode block (U+2800..U+28FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2800.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalArrowsB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Supplemental Arrows-B' Unicode block (U+2900..U+297F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2900.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousMathematicalSymbolsB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Miscellaneous Mathematical Symbols-B' Unicode block (U+2980..U+29FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2980.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalMathematicalOperators">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Supplemental Mathematical Operators' Unicode block (U+2A00..U+2AFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2A00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousSymbolsandArrows">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Miscellaneous Symbols and Arrows' Unicode block (U+2B00..U+2BFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2B00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Glagolitic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Glagolitic' Unicode block (U+2C00..U+2C5F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2C00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedC">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended-C' Unicode block (U+2C60..U+2C7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2C60.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Coptic">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Coptic' Unicode block (U+2C80..U+2CFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2C80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.GeorgianSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Georgian Supplement' Unicode block (U+2D00..U+2D2F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2D00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Tifinagh">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tifinagh' Unicode block (U+2D30..U+2D7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2D30.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ethiopic Extended' Unicode block (U+2D80..U+2DDF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2D80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cyrillic Extended-A' Unicode block (U+2DE0..U+2DFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2DE0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalPunctuation">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Supplemental Punctuation' Unicode block (U+2E00..U+2E7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2E00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkRadicalsSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Radicals Supplement' Unicode block (U+2E80..U+2EFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2E80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.KangxiRadicals">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Kangxi Radicals' Unicode block (U+2F00..U+2FDF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2F00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.IdeographicDescriptionCharacters">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ideographic Description Characters' Unicode block (U+2FF0..U+2FFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U2FF0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkSymbolsandPunctuation">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Symbols and Punctuation' Unicode block (U+3000..U+303F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3000.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Hiragana">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hiragana' Unicode block (U+3040..U+309F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3040.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Katakana">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Katakana' Unicode block (U+30A0..U+30FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U30A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Bopomofo">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Bopomofo' Unicode block (U+3100..U+312F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3100.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HangulCompatibilityJamo">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hangul Compatibility Jamo' Unicode block (U+3130..U+318F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3130.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Kanbun">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Kanbun' Unicode block (U+3190..U+319F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3190.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.BopomofoExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Bopomofo Extended' Unicode block (U+31A0..U+31BF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U31A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkStrokes">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Strokes' Unicode block (U+31C0..U+31EF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U31C0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.KatakanaPhoneticExtensions">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Katakana Phonetic Extensions' Unicode block (U+31F0..U+31FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U31F0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.EnclosedCjkLettersandMonths">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Enclosed CJK Letters and Months' Unicode block (U+3200..U+32FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3200.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibility">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Compatibility' Unicode block (U+3300..U+33FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3300.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkUnifiedIdeographsExtensionA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Unified Ideographs Extension A' Unicode block (U+3400..U+4DBF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U3400.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.YijingHexagramSymbols">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Yijing Hexagram Symbols' Unicode block (U+4DC0..U+4DFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U4DC0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkUnifiedIdeographs">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Unified Ideographs' Unicode block (U+4E00..U+9FFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/U4E00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.YiSyllables">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Yi Syllables' Unicode block (U+A000..U+A48F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA000.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.YiRadicals">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Yi Radicals' Unicode block (U+A490..U+A4CF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA490.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Lisu">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Lisu' Unicode block (U+A4D0..U+A4FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA4D0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Vai">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Vai' Unicode block (U+A500..U+A63F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA500.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cyrillic Extended-B' Unicode block (U+A640..U+A69F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA640.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Bamum">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Bamum' Unicode block (U+A6A0..U+A6FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA6A0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ModifierToneLetters">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Modifier Tone Letters' Unicode block (U+A700..U+A71F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA700.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedD">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended-D' Unicode block (U+A720..U+A7FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA720.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SylotiNagri">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Syloti Nagri' Unicode block (U+A800..U+A82F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA800.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CommonIndicNumberForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Common Indic Number Forms' Unicode block (U+A830..U+A83F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA830.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Phagspa">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Phags-pa' Unicode block (U+A840..U+A87F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA840.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Saurashtra">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Saurashtra' Unicode block (U+A880..U+A8DF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA880.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.DevanagariExtended">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Devanagari Extended' Unicode block (U+A8E0..U+A8FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA8E0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.KayahLi">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Kayah Li' Unicode block (U+A900..U+A92F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA900.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Rejang">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Rejang' Unicode block (U+A930..U+A95F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA930.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamoExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hangul Jamo Extended-A' Unicode block (U+A960..U+A97F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA960.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Javanese">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Javanese' Unicode block (U+A980..U+A9DF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA980.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MyanmarExtendedB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Myanmar Extended-B' Unicode block (U+A9E0..U+A9FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UA9E0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Cham">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cham' Unicode block (U+AA00..U+AA5F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAA00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MyanmarExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Myanmar Extended-A' Unicode block (U+AA60..U+AA7F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAA60.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.TaiViet">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Tai Viet' Unicode block (U+AA80..U+AADF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAA80.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MeeteiMayekExtensions">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Meetei Mayek Extensions' Unicode block (U+AAE0..U+AAFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAAE0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicExtendedA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Ethiopic Extended-A' Unicode block (U+AB00..U+AB2F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAB00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedE">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Latin Extended-E' Unicode block (U+AB30..U+AB6F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAB30.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CherokeeSupplement">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Cherokee Supplement' Unicode block (U+AB70..U+ABBF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAB70.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.MeeteiMayek">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Meetei Mayek' Unicode block (U+ABC0..U+ABFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UABC0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HangulSyllables">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hangul Syllables' Unicode block (U+AC00..U+D7AF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UAC00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamoExtendedB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Hangul Jamo Extended-B' Unicode block (U+D7B0..U+D7FF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UD7B0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibilityIdeographs">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Compatibility Ideographs' Unicode block (U+F900..U+FAFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UF900.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.AlphabeticPresentationForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Alphabetic Presentation Forms' Unicode block (U+FB00..U+FB4F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFB00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ArabicPresentationFormsA">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic Presentation Forms-A' Unicode block (U+FB50..U+FDFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFB50.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.VariationSelectors">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Variation Selectors' Unicode block (U+FE00..U+FE0F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.VerticalForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Vertical Forms' Unicode block (U+FE10..U+FE1F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE10.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CombiningHalfMarks">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Combining Half Marks' Unicode block (U+FE20..U+FE2F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE20.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibilityForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'CJK Compatibility Forms' Unicode block (U+FE30..U+FE4F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE30.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.SmallFormVariants">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Small Form Variants' Unicode block (U+FE50..U+FE6F).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE50.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.ArabicPresentationFormsB">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Arabic Presentation Forms-B' Unicode block (U+FE70..U+FEFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFE70.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.HalfwidthandFullwidthForms">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Halfwidth and Fullwidth Forms' Unicode block (U+FF00..U+FFEF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFF00.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="P:System.Text.Unicode.UnicodeRanges.Specials">
            <summary>
            A <see cref="T:System.Text.Unicode.UnicodeRange"/> corresponding to the 'Specials' Unicode block (U+FFF0..U+FFFF).
            </summary>
            <remarks>
            See https://www.unicode.org/charts/PDF/UFFF0.pdf for the full set of characters in this block.
            </remarks>
        </member>
        <member name="M:System.Text.UnicodeDebug.ToHexString(System.UInt32)">
            <summary>
            Formats a code point as the hex string "U+XXXX".
            </summary>
            <remarks>
            The input value doesn't have to be a real code point in the Unicode codespace. It can be any integer.
            </remarks>
        </member>
        <member name="F:System.Text.UnicodeUtility.ReplacementChar">
            <summary>
            The Unicode replacement character U+FFFD.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.GetPlane(System.UInt32)">
            <summary>
            Returns the Unicode plane (0 through 16, inclusive) which contains this code point.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.GetScalarFromUtf16SurrogatePair(System.UInt32,System.UInt32)">
            <summary>
            Returns a Unicode scalar value from two code points representing a UTF-16 surrogate pair.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.GetUtf16SequenceLength(System.UInt32)">
            <summary>
            Given a Unicode scalar value, gets the number of UTF-16 code units required to represent this value.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.GetUtf16SurrogatesFromSupplementaryPlaneScalar(System.UInt32,System.Char@,System.Char@)">
            <summary>
            Decomposes an astral Unicode scalar into UTF-16 high and low surrogate code units.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.GetUtf8SequenceLength(System.UInt32)">
            <summary>
            Given a Unicode scalar value, gets the number of UTF-8 code units required to represent this value.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsAsciiCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is an ASCII
            character ([ U+0000..U+007F ]).
            </summary>
            <remarks>
            Per http://www.unicode.org/glossary/#ASCII, ASCII is only U+0000..U+007F.
            </remarks>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsBmpCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is in the
            Basic Multilingual Plane (BMP).
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsHighSurrogateCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is a UTF-16 high surrogate code point,
            i.e., is in [ U+D800..U+DBFF ], inclusive.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsInRangeInclusive(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is between
            <paramref name="lowerBound"/> and <paramref name="upperBound"/>, inclusive.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsLowSurrogateCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is a UTF-16 low surrogate code point,
            i.e., is in [ U+DC00..U+DFFF ], inclusive.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsSurrogateCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is a UTF-16 surrogate code point,
            i.e., is in [ U+D800..U+DFFF ], inclusive.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsValidCodePoint(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="codePoint"/> is a valid Unicode code
            point, i.e., is in [ U+0000..U+10FFFF ], inclusive.
            </summary>
        </member>
        <member name="M:System.Text.UnicodeUtility.IsValidUnicodeScalar(System.UInt32)">
            <summary>
            Returns <see langword="true"/> iff <paramref name="value"/> is a valid Unicode scalar
            value, i.e., is in [ U+0000..U+D7FF ], inclusive; or [ U+E000..U+10FFFF ], inclusive.
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference">
            <summary>
            Get a pinnable reference to the builder.
            Does not ensure there is a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/>
            This overload is pattern matched in the C# 7.3+ compiler so you can omit
            the explicit method call, and write eg "fixed (char* c = builder)"
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference(System.Boolean)">
            <summary>
            Get a pinnable reference to the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="P:System.Text.ValueStringBuilder.RawChars">
            <summary>Returns the underlying storage of the builder.</summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.AsSpan(System.Boolean)">
            <summary>
            Returns a span around the contents of the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="M:System.Text.ValueStringBuilder.Grow(System.Int32)">
            <summary>
            Resize the internal buffer either by doubling current buffer size or
            by adding <paramref name="additionalCapacityBeyondPos"/> to
            <see cref="F:System.Text.ValueStringBuilder._pos"/> whichever is greater.
            </summary>
            <param name="additionalCapacityBeyondPos">
            Number of chars requested beyond current position.
            </param>
        </member>
        <member name="P:System.HexConverter.CharToHexLookup">
            <summary>Map from an ASCII char to its hex value, e.g. arr['b'] == 11. 0xFF means it's not a hex digit.</summary>
        </member>
        <member name="P:System.SR.TextEncoderDoesNotImplementMaxOutputCharsPerInputChar">
            <summary>TextEncoder does not implement MaxOutputCharsPerInputChar correctly.</summary>
        </member>
    </members>
</doc>
