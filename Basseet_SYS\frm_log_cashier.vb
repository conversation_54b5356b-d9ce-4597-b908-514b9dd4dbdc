﻿Imports System.Data.SqlClient
Imports Net.Pkcs11Interop.Common

Public Class frm_log_cashier
    Dim connx As New CLS_CON
    Private Sub frm_log_cashier_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ProgressBar1.Visible = False
    End Sub

    Private Sub frm_log_cashier_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        txtCashierName.Focus()
    End Sub
    Private Sub btn_save1_Click(sender As Object, e As EventArgs) Handles btn_save1.Click


        If txtCashierName.Text = "" Or txtpassword.Text = "" Then
            MessageBox.Show("يجب ادخال اسم المستخدم  و كلمة المرور  ")
            Exit Sub
        End If
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("select * from Tbl_cashier where cashier_name=@cashier_name and  cashier_password=@cashier_password", connx.Con)
        connx.cmd.Parameters.AddWithValue("@cashier_name", txtCashierName.Text)
        connx.cmd.Parameters.AddWithValue("@cashier_password", txtpassword.Text)
        'connx.cmd.ExecuteReader()
        connx.rdr = connx.cmd.ExecuteReader()
        connx.rdr.Read()

        If connx.rdr.HasRows Then
            MessageBox.Show("تم تسجيل الدخول بنجاح")
            _cashier_id = connx.rdr("cashier_id").ToString
            _cashier_name = connx.rdr("cashier_name").ToString
            _cashier_fullname = connx.rdr("cashier_fullName").ToString
            _cashier_balance = CDbl(connx.rdr("cashier_balance"))
            If connx.rdr("is_owner") = True Then
                _is_owner = True
            Else
                _is_owner = False
            End If
            If _is_owner = True Then
                FRM_HOME.Show()
                Me.Hide()
            Else
                If connx.rdr("cashier_status") = True Then
                    _cashier_status = True
                    With Frm_pos

                        .istlam.Enabled = False
                        .taslim.Enabled = True
                        .BtnNew.Enabled = True
                        .BtnPaid.Enabled = True
                        .ShowDialog()
                    End With
                    Me.Close()
                Else
                    _cashier_status = False
                    With Frm_pos

                        .istlam.Enabled = True
                        .taslim.Enabled = False
                        .BtnNew.Enabled = False
                        .BtnPaid.Enabled = False
                        .ShowDialog()
                    End With
                    Me.Close()

                End If

            End If
        Else

            txtCashierName.Text = ""
            txtpassword.Text = ""
        End If

        connx.rdr.Close()
        connx.Con.Close()

    End Sub

    Private Sub txtCashierName_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtCashierName.KeyPress
        If e.KeyChar = ChrW(Keys.Enter) Then
            e.Handled = True ' لمنع صوت التنبيه لما تدوس Enter
            txtpassword.Focus()
        End If
    End Sub

    Private Sub txtpassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtpassword.KeyPress
        If e.KeyChar = ChrW(Keys.Enter) Then
            e.Handled = True ' لمنع صوت التنبيه لما تدوس Enter
            btn_save1.Focus()
            btn_save1.PerformClick() ' ينفذ زر الحفظ كأنك ضغط عليه
        End If
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs) Handles BtnClear.Click
        Application.Exit()
    End Sub
End Class