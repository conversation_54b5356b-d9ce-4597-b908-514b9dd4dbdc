﻿Imports System.Data.SqlClient
Imports Net.Pkcs11Interop.Common

Public Class Frm_Users
    Dim connx As New CLS_CON
    Private Sub ToolStripButton2_Click(sender As Object, e As EventArgs) Handles ToolStripButton2.Click
        With Add_Update_User
            .BtnSave.Enabled = True
            .BtnUpdate.Enabled = False
            ' .ClearText()
            .ShowDialog()
        End With
    End Sub
    Public Sub LoadUsers()

        Try
            Dgv.Rows.Clear()
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            Dim cmd As New SqlCommand(" Select * from User_Tbl", connx.Con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dr As SqlDataReader
            dr = cmd.ExecuteReader
            While dr.Read
                Dgv.Rows.Add(dr("User_ID").ToString, dr("UserName").ToString, dr("UserPass").ToString, dr("FullName").ToString, dr("UserRole").ToString)
            End While
            dr.Close()
            connx.Con.Close()
        Catch ex As Exception
            connx.Con.Close()
        End Try
    End Sub

    Private Sub Dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv.CellClick
        If e.ColumnIndex = 5 Then

            With Add_Update_User
                .TxtUser_ID.Text = Dgv.CurrentRow.Cells(0).Value
                .TxtUserName.Text = Dgv.CurrentRow.Cells(1).Value
                .TxtUserPass.Text = Dgv.CurrentRow.Cells(2).Value
                .TxtFullName.Text = Dgv.CurrentRow.Cells(3).Value
                .CmbUserRole.Text = Dgv.CurrentRow.Cells(4).Value
                .TxtUser_ID.Enabled = False
                .BtnSave.Enabled = False
                .BtnUpdate.Enabled = True
                .ShowDialog()

            End With

        ElseIf e.ColumnIndex = 6 Then

            If MessageBox.Show("هل أنت متأكد من مواصلة عملية الحذف؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
                Exit Sub
            Else
                DeleteRowFrom_User_Tbl(Dgv)
            End If
            LoadUsers()

        End If
    End Sub

    Public Sub DeleteRowFrom_User_Tbl(ByVal DGV_Item_Tbl As DataGridView)
        Dim Position As Integer = DGV_Item_Tbl.CurrentRow.Index
        Dim ID_Position As Integer = DGV_Item_Tbl.Rows(Position).Cells(0).Value
        Dim CmdDelete As New SqlCommand
        With CmdDelete
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Delete  From User_Tbl Where User_ID = @User_ID"
            .Parameters.Clear()
            .Parameters.AddWithValue("@User_ID", SqlDbType.Int).Value = ID_Position
        End With
        Try
            If connx.Con.State = 1 Then connx.Con.Close()
            connx.Con.Open()
            CmdDelete.ExecuteNonQuery()
            connx.Con.Close()
            MsgBox("تم حذف المستخدم بنجاح.", MsgBoxStyle.Information, "حذف")
            CmdDelete = Nothing
        Catch ex As Exception
            connx.Con.Close()
            MsgBox(Err.Description, MsgBoxStyle.Information)
        Finally
            If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        End Try
    End Sub

    Private Sub ToolStripButton1_Click(sender As Object, e As EventArgs) Handles ToolStripButton1.Click
        Me.Close()
    End Sub

    Private Sub Label2_Click(sender As Object, e As EventArgs) Handles Label2.Click
        Me.Close()
    End Sub
End Class