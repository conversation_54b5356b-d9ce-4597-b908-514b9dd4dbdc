﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAO0UAAAEAGADUMgAAFgAAACgAAAA7AAAAigAAAAEAGAAAAAAArDIAAMQOAADEDgAAAAAAAAAA
        AAD////////////////////////////////////////////////19fW3t7empqampqampqanp6fY2Nj+
        /v7/////////////////////////////////////////////////////////////////////////////
        ///////////////6+vq+vr6mpqampqampqampqbNzc39/f3///////////////////////////////++
        vgD////////////////////////////////////////+/v7g4OAlJSUAAAAAAAAAAAAAAAAAAAACAgKC
        goL+/v7/////////////////////////////////////////////////////////////////////////
        ///////////w8PA5OTkAAAAAAAAAAAAAAAAAAAAAAABhYWH8/Pz///////////////////////////8A
        AAD////////////////////////////////////////+/v5cXFwAAAAAAAAAAAAAAAAAAAAAAAAAAAAC
        AgLW1tb/////////////////////////////////////////////////////////////////////////
        ///////+/v6AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC1tbX///////////////////////////8A
        AAD////////////////////////////////////////+/v4rKysAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACqqqr/////////////////////////////////////////////////////////////////////////
        ///////+/v5OTk4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACGhob///////////////////////////8A
        AAD////////////////////////////+/v7+/v7+/v7+/v6rq6ucnJycnJycnJycnJycnJycnJycnJyc
        nJzd3d3+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+
        /v7+/v7+/v65ubmcnJycnJycnJycnJycnJycnJycnJycnJzPz8/+/v7+/v7+/v7///////////////+c
        nAD////////////////+/v7ExMSCgoJtbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1t
        bW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1t
        bW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW2AgIC/v7/9/f3///9t
        bQD////////////+/v55eXkBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABra2v+/v4A
        AAD////////////j4+MDAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAQHX19cA
        AAD///////////+wsLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAClpaUA
        AAD///////////+lpaUAAAAAAAAAAAAAAAAAAAAFBQUGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYG
        BgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYG
        BgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYFBQUAAAAAAAAAAAAAAAAAAACXl5cG
        BgD///////////+lpaUAAAAAAAAAAAABAQGtra329vb4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4
        +Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4
        +Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj29va4uLgDAwMAAAAAAAAAAACXl5f4
        +AD///////////+lpaUAAAAAAAAAAAAZGRn9/f3/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////////////////////////////////////9/f0qKioAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////+/v7+/v7+/v7+/v7+/v7////////////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////5+fnV1dVpaWk0NDR+fn709PT////////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7+/v7+/v7+/v7/////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////////+/v76+vo/Pz8AAABWVlb+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh79/f3Z2dmZmZnOzs7+/v7/////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////4+Pjg4OD+/v7///////////////+EhIQAAAARERH+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7Dw8MICAgAAAAFBQXExMT/////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///4+Pg1NTUAAAB7e3v+/v7///////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f+
        /gD////+/v7+/v6kpKQAAAAAAAAAAAAdHR1ZWVkMDAzCwsIBAQFycnL/////////////////////////
        ///////////////////////////////////////////////////////////////////////////////+
        /v6Ojo4AAAAAAAA9PT3+/v7///////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f+
        /gD6+vqWlpZjY2NAQEAAAAAAAAAAAAABAQEAAABRUVH9/f0GBgZra2v/////////////////////////
        ///////////////////////////////////////////////////////////////////////////////n
        5+cQEBAAAAAAAACoqKj///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wCGhoYAAAAQEBAPDw8HBwcJCQkYGBg/Pz+MjIz09PT+/v4GBgZra2v/////////////////////////
        ///////////////////////////////////////////////////////////////////////////+/v5l
        ZWUAAAAAAAA/Pz/8/Pz///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wA+Pj4uLi77+/v8/Pz19fX39/f9/f3+/v7+/v7////+/v4GBgZra2v/////////////////////////
        ///////////////////////////////////////////////////////////////////////+/v7MzMwD
        AwMAAAAEBATMzMz///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wA/Pz8sLCz7+/v9/f329vb39/f9/f3+/v7+/v7////+/v4GBgZra2v/////////////////////////
        ///////////////////////////////////////////////////+/v7w8PDg4ODq6ur9/f38/Pw9PT0A
        AAAAAABmZmb+/v7///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wCKiooAAAAICAgGBgYBAQEBAQEQEBA5OTmFhYXx8fH+/v4GBgZra2v/////////////////////////
        ///////////////////////////////////////+/v7l5eV5eXkmJiYBAQEAAAAAAAAWFhZHR0cAAAAA
        AAARERHo6Oj///////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD7+/udnZ1qampGRkYAAAAAAAAAAAACAgIBAQFNTU39/f0GBgZqamr/////////////////////////
        ///////////////////////////////////+/v6fn58PDw8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACNjY3+/v7///////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh5bW1sLCwu7u7sBAQFzc3P/////////////////////////
        ///////////////////////////////+/v6IiIgAAAAAAAAAAAAAAAAZGRk0NDQlJSUBAQEAAAAAAAAA
        AABHR0f39/f///////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7FxcUKCgoAAAAGBgbHx8f/////////////////////////
        //////////////////////////////+0tLQCAgIAAAAAAAA9PT3ExMT9/f3+/v7+/v7f399oaGgBAQEA
        AAAAAABubm7+/v7///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh79/f3f39+dnZ3U1NT+/v7/////////////////////////
        ///////////////////////////4+PgkJCQAAAAAAABcXFz5+fn+/v7////////////////9/f2dnZ0B
        AQEAAAADAwPQ0ND///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        //////////////////////////+xsbEAAAAAAAAjIyP09PT////////////////////////////+/v5i
        YmIAAAAAAABnZ2f+/v7///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////////+/v72
        9vby8vLy8vLy8vLy8vLw8PDw8PBkZGQAAAAAAACQkJD+/v7////////////////////////////////X
        19cAAAAAAAAeHh79/f3///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////+/v6NjY0E
        BAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADOzs7////////////////////////////////////8
        /PwaGhoAAAAEBATx8fH///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////+/v44ODgA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADb29v////////////////////////////////////+
        /v4mJiYAAAACAgLs7Oz///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////+/v6vr68q
        KioiIiIiIiIiIiIkJCQkJCQkJCQNDQ0AAAAAAAC8vLz////////////////////////////////////4
        +PgMDAwAAAAJCQn39/f///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////////+/v7+
        /v7+/v7+/v7+/v7+/v7+/v7+/v6BgYEAAAAAAABoaGj+/v7////////////////////////////+/v6w
        sLAAAAAAAAA4ODj+/v7///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////U1NQCAgIAAAAHBwfOzs7////////////////////////////y8vIr
        KysAAAAAAACOjo7+/v7///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////+/v5UVFQAAAAAAAAcHBzMzMz+/v7////////////+/v7q6upFRUUA
        AAAAAAAaGhrw8PD///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////l5eUbGxsAAAAAAAAGBgZfX1+wsLDPz8+9vb19fX0WFhYAAAAA
        AAADAwO0tLT+/v7///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////+/v7Q0NAbGxsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACUlJT+/v7///////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////////+/v7n5+daWloDAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAB2dnb+/v7///////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////////////////+/v7a2tqKiopYWFhJSUlQUFB3d3eRkZEBAQEA
        AAAICAja2tr+/v7///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////////////////////////////+/v7+/v7+/v7////9/f1QUFAA
        AAAAAABPT0/9/f3///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////////////////////////////////////////////+/v7b29sI
        CAgAAAAAAAC5ubn///////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////////////////////////////////////////////////////////////////////////+/v54
        eHgAAAAAAAAtLS34+Pj///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////+/v7+/v7+/v7+/v7+/v7+/v7+/v7////////////////////////////////////////////w
        8PAaGhoAAAAAAACTk5P+/v7///////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f+
        /gD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////////////+
        /v7+/v7IyMhsbGw6OjooKCg8PDxwcHDMzMz+/v7////////////////////////////////////////+
        /v6fn58AAAAAAAA/Pz/+/v7///////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f+
        /gD///////////+lpaUAAAAAAAAAAAAeHh79/f20tLRra2unp6f9/f3////////////////////+/v7r
        6+tTU1MAAAAAAAABAQENDQ0BAQEAAAABAQFbW1vv7+/+/v7/////////////////////////////////
        ///8/PxVVVUVFRWioqL///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh6rq6sBAQEGBgYAAACwsLD////////////////////q6uos
        LCwAAAAlJSWkpKRJSUn4+PhDQ0OgoKAfHx8AAAAzMzPu7u7/////////////////////////////////
        ///////+/v78/Pz+/v7///////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD////+/v7+/v6kpKQAAAAAAAAAAAAZGRlFRUUSEhLo6OgCAgJubm7////////////////9/f1NTU0A
        AABYWFhBQUHq6upMTEzq6upTU1Pi4uJERERRUVEAAABZWVn+/v7/////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wDx8fFra2szMzMiIiIAAAAAAAAAAAAAAAAAAABubm79/f0GBgZra2v////////////+/v6+vr4AAAAI
        CAjLy8vKyspBQUEgICAPDw8jIyNAQEDT09PCwsIEBAQBAQHJycn/////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wBxcXECAgI4ODg3NzctLS0uLi5CQkJsbGy4uLj9/f3+/v4GBgZra2v////////////+/v5gYGAAAACV
        lZVcXFxRUVEYGBioqKjf39+jo6MVFRVRUVFhYWGOjo4AAABsbGz+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wA7Ozs2Njb+/v7+/v7+/v7+/v7+/v7+/v7+/v7////+/v4GBgZra2v////////////+/v4qKioBAQGj
        o6PExMQlJSWxsbH+/v7////+/v6lpaUuLi7AwMCZmZkAAAA3Nzf+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wBFRUUhISHb29va2trJycnMzMzj4+P7+/v+/v7+/v7+/v4GBgZra2v////////////9/f0UFBQQEBCc
        nJyAgIADAwPx8fH////////////k5OQICAiFhYWdnZ0GBgYoKCj+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wClpaUBAQEAAAAAAAAAAAAAAAAAAAAPDw9ZWVne3t7+/v4GBgZra2v////////////+/v4nJycICAjF
        xcV2dnYICAi8vLz+/v7///////+xsbEKCgp6enrCwsIDAwM0NDT+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD+/v7Hx8eVlZVhYWEAAAAAAAAAAAAHBwcLCws5OTn8/PwFBQVra2v////////////+/v5ZWVkAAABT
        U1PHx8d0dHQhISHBwcHx8fG7u7sbGxt/f3/ExMRMTEwAAABlZWX+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh5paWkGBgaQkJAAAAB6enr////////////+/v6zs7MAAAA+
        Pj6hoaFWVlZ0dHQMDAwEBAQLCwt8fHxRUVGoqKg1NTUAAAC/v7/+/v7/////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7a2tofHx8AAAAVFRXZ2dn////////////////8/Pw+Pj4A
        AAApKSn39/dZWVne3t5FRUXV1dViYmLy8vIhISEAAABKSkr9/f3/////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh79/f329vbMzMzw8PD+/v7////////////////////h4eEe
        Hh4AAAA8PDxGRkb39/dGRkb29vZAQEA4ODgAAAAkJCTm5ub/////////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7////////////////////////////////////+/v7g
        4OA9PT0AAAAAAAANDQ0HBwcMDAwAAAAAAABERETl5eX+/v7/////////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///8/PywsLBWVlYhISETExMiIiJZWVm1tbX8/Pz/////////////////////////////////////////
        //////////////////////////////////+FhYUAAAAQEBD+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ///////+/v7+/v7+/v7+/v7+/v7+/v7+/v7/////////////////////////////////////////////
        ///////////////////////////////+/v50dHQAAAAhISH+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////+/v79/f3Dw8MPDw8NDQ2vr6/+/v7////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////8/Pza2tqkpKSsrKzq6ur+/v7////////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAeHh7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////////////////+/v7////////////////+/v4yMjIAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAANDQ319fX+/v7/////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////////////////////////////////+/v74+PgXFxcAAAAAAAAAAACXl5f/
        /wD///////////+lpaUAAAAAAAAAAAAAAABAQECLi4uOjo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6O
        jo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6O
        jo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6MjIxHR0cAAAAAAAAAAAAAAACXl5eO
        jgD///////////+np6cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACbm5sA
        AAD////////////Dw8MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC1tbUA
        AAD////////////4+PgfHx8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAXFxfy8vIA
        AAD////////////+/v7Q0NA8PDwFBQUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEBAQ3NzfIyMj+/v4A
        AAD////////////////////+/v7z8/Pm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm
        5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm
        5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5ubm5uby8vL+/v7+/v7////m
        5gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAA=
</value>
  </data>
</root>