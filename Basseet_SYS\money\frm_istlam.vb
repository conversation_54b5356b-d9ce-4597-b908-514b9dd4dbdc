﻿Imports System.Data.SqlClient

Public Class frm_istlam
    Dim connx As New CLS_CON
    Private Sub BtnSave_Status_Click(sender As Object, e As EventArgs) Handles BtnSave_Status.Click
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Update Tbl_cashier Set cashier_balance=@cashier_balance ,start_balance=@start_balance,cashier_status='true' where cashier_id=@cashier_id", connx.Con)
        connx.cmd.Parameters.AddWithValue("@cashier_balance", 0.0)
        connx.cmd.Parameters.AddWithValue("@start_balance", CDbl(txt_total.Text))
        connx.cmd.Parameters.AddWithValue("@cashier_id", _cashier_id)
        connx.cmd.ExecuteNonQuery()
        connx.Con.Close()
        MessageBox.Show(" تم الاستلام بنجاح ")
        Me.Close()
        With Frm_pos
            .istlam.Enabled = False
            .taslim.Enabled = True
            .BtnPaid.Enabled = True
            .BtnNew.Enabled = True
        End With

    End Sub

    Private Sub frm_istlam_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class