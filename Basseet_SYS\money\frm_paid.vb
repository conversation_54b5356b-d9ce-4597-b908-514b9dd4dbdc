﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Runtime.InteropServices
Imports FastReport
Imports System.Drawing.Printing
Imports Net.Pkcs11Interop.Common
Imports System.Xml
Imports System.Xml.Linq
Imports FastReport.Export.Pdf

Public Class frm_paid
    Dim connx As New CLS_CON
    Private Sub BtnPaidOrder_Click(sender As Object, e As EventArgs) Handles BtnPaidOrder.Click

        Dim Field1 As String = ""
        Field1 = convertTohex("01", _CompanyName)
        Dim Field2 As String = ""
        Field2 = convertTohex("02", _vat_No)
        Dim Field3 As String = ""
        Dim myDate As String = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ")
        Field3 = convertTohex("03", myDate)
        Dim Field4 As String = ""
        Field4 = convertTohex("04", TxtFinalTotal_.Text)
        Dim Field5 As String = ""
        Field5 = convertTohex("05", _TaxTotal)
        Dim All_Field As String = Field1 & Field2 & Field3 & Field4 & Field5
        Frm_pos.txtHexToBase64.Text = HexToBase64(All_Field)
        Dim _Result = Base64ToHex(Frm_pos.txtHexToBase64.Text)
        Get_QrCode()
        If Val(TxtPaid.Text) < Val(TxtFinalTotal_.Text) Then
            MsgBox("المبلغ المدفوع اقل من قيمة الفاتورة")
            Exit Sub
        End If
        If CmbPaidType.Text = "" Then

            CmbPaidType.Text = "نقدي"
        End If
        Insert_Sale()
        INSERT_DELIVERY()

        If CmbPaidType.Text = "نقدي" Then
            update_cashier()

        End If
        insert_cashier_move()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Update Order_Tbl Set ord_Status='Close' Where Order_No=@Order_No", connx.Con)
        connx.cmd.Parameters.AddWithValue("@Order_No", _Order_No)
        connx.cmd.ExecuteNonQuery()
        connx.Con.Close()
        With Frm_pos
            .ExportInvoiceToUBL()
            .TxtOrder_No.Text = ""
            .OrderDate.Value = Today
            .TxtFinalTotal.Text = ""
            .TxtCount.Text = 0
            .TxtTotal.Text = ""
            .TxtTableName.Text = ""
            .LblTax.Text = 0.0
            .TxtTotal.Text = ""
            .txtHexToBase64.Text = ""
            .txtBase64ToHex.Text = ""
            .QrCode_Pic.Image = Nothing
            .Dgv.Rows.Clear()

        End With
        Me.Close()
        PrintInvoice_pdf()
        PrintInvoice_reset()
        '   signToXML.Show()
    End Sub


    Private Sub TxtPaid_TextChanged(sender As Object, e As EventArgs) Handles TxtPaid.TextChanged
        TxtUnPaid.Text = Val(TxtPaid.Text) - Val(TxtFinalTotal_.Text)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub

    Private Sub TxtDiscount_TextChanged(sender As Object, e As EventArgs) Handles TxtDiscount.TextChanged
        If TxtDiscount.Text = "" Or Val(TxtDiscount.Text) = 0 Then
            TxtFinalTotal_.Text = _finalTotal
        Else
            TxtFinalTotal_.Text = Frm_pos.TxtFinalTotal.Text - Val(TxtDiscount.Text)
        End If
    End Sub

    Private Sub frm_paid_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        TxtFinalTotal_.Text = _finalTotal
    End Sub

    Private Sub CheckBox1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox1.CheckedChanged
        If CheckBox1.Checked = True Then
            TxtDiscount.Enabled = True
        Else
            TxtDiscount.Enabled = False
        End If
    End Sub
    Public Sub Insert_Sale()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into SaleS_Tbl ( Order_No,Sales_type,SalesDate,cashier,Total,Tax_Value,Tax_Total,Disc_Total,Final_Total,PaidType,UserName,QrCode_Pic)values(@Order_No,@Sales_type,@SalesDate,@cashier,@Total,@Tax_Value,@Tax_Total,@Disc_Total,@Final_Total,@PaidType,@UserName,@QrCode_Pic)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Order_No", SqlDbType.VarChar).Value = _Order_No
            .Parameters.AddWithValue("@Sales_type", SqlDbType.VarChar).Value = _Order_Type
            .Parameters.AddWithValue("@SalesDate", SqlDbType.Date).Value = Today
            .Parameters.AddWithValue("@cashier", SqlDbType.VarChar).Value = frm_log_cashier.txtCashierName.Text
            .Parameters.AddWithValue("@Total", SqlDbType.Decimal).Value = _Order_Total
            .Parameters.AddWithValue("@Tax_Value", SqlDbType.Decimal).Value = _Tax_VALUE
            .Parameters.AddWithValue("@Tax_Total", SqlDbType.Decimal).Value = _TaxTotal
            .Parameters.AddWithValue("@Disc_Total", SqlDbType.Decimal).Value = TxtDiscount.Text
            .Parameters.AddWithValue("@Final_Total", SqlDbType.Decimal).Value = TxtFinalTotal_.Text
            .Parameters.AddWithValue("@PaidType", SqlDbType.VarChar).Value = CmbPaidType.Text
            .Parameters.AddWithValue("@UserName", SqlDbType.VarChar).Value = "Admin"
            Dim ms As New MemoryStream()
            Dim bmpImage As New Bitmap(Frm_pos.QrCode_Pic.Image)
            bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
            Dim data As Byte() = ms.GetBuffer()
            Dim p As New SqlParameter("@QrCode_Pic", SqlDbType.Image)
            p.Value = data
            .Parameters.Add(p)
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        'MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Public Sub PrintInvoice_reset()

        Dim ds As New DataSet1
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        ' تحميل البيانات من Sale_Tbl
        Dim cmd As New SqlCommand("SELECT * FROM Sales_Tbl WHERE Order_No=@Order_No", connx.Con)
        cmd.Parameters.AddWithValue("@Order_No", _Order_No)
        Dim adp As New SqlDataAdapter(cmd)
        adp.Fill(ds, "Sales_Tbl")
        ' تحميل البيانات من View_Order_print
        Dim cmd2 As New SqlCommand("SELECT * FROM View_Order WHERE Order_No=@Order_No", connx.Con)
        cmd2.Parameters.AddWithValue("@Order_No", _Order_No)
        Dim adp2 As New SqlDataAdapter(cmd2)
        adp2.Fill(ds, "View_Order")
        ' تحميل البيانات من Company_Tbl
        Dim cmd1 As New SqlCommand("SELECT * FROM comSetting_Tbl", connx.Con)
        Dim adp1 As New SqlDataAdapter(cmd1)
        adp1.Fill(ds, "comSetting_Tbl")

        ' إنشاء كائن التقرير
        Dim report As New Report()

        ' تحميل تصميم التقرير من ملف
        report.Load("D:\Basseet_SYS -ok\design\aaa.frx")

        ' تسجيل البيانات التي سيتم عرضها في التقرير
        report.RegisterData(ds.Tables("Sales_Tbl"), "Sales_Tbl")
        report.RegisterData(ds.Tables("View_Order"), "View_Order")
        report.RegisterData(ds.Tables("comSetting_Tbl"), "comSetting_Tbl")

        ' التأكد من إظهار البيانات في التقرير
        report.GetDataSource("Sales_Tbl").Enabled = True
        report.GetDataSource("View_Order").Enabled = True
        report.GetDataSource("comSetting_Tbl").Enabled = True
        Dim XXX_PRT As String = My.Settings.PrinterName
        report.PrintSettings.Printer = My.Settings.PrinterName

        ' منع إظهار نافذة اختيار الطابعة

        report.PrintSettings.Printer = My.Settings.PrinterName ' سيستخدم الطابعة الافتراضية إذا كانت القيمة فارغة
        ' عرض التقرير في نافذة المعاينة
        '  report.Show()
        report.PrintSettings.ShowDialog = False
        report.Print()

        ' إغلاق الاتصال بقاعدة البيانات
        connx.Con.Close()

    End Sub


    Public Sub PrintInvoice_pdf()
        Dim ds As New DataSet1
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()

        ' تحميل البيانات من Sale_Tbl
        Dim cmd As New SqlCommand("SELECT * FROM Sales_Tbl WHERE Order_No=@Order_No", connx.Con)
        cmd.Parameters.AddWithValue("@Order_No", _Order_No)
        Dim adp As New SqlDataAdapter(cmd)
        adp.Fill(ds, "Sales_Tbl")

        ' تحميل البيانات من View_Order_print
        Dim cmd2 As New SqlCommand("SELECT * FROM View_Order WHERE Order_No=@Order_No", connx.Con)
        cmd2.Parameters.AddWithValue("@Order_No", _Order_No)
        Dim adp2 As New SqlDataAdapter(cmd2)
        adp2.Fill(ds, "View_Order")

        ' تحميل البيانات من Company_Tbl
        Dim cmd1 As New SqlCommand("SELECT * FROM comSetting_Tbl", connx.Con)
        Dim adp1 As New SqlDataAdapter(cmd1)
        adp1.Fill(ds, "comSetting_Tbl")

        ' إنشاء كائن التقرير
        Dim report As New Report()

        ' تحميل تصميم التقرير من ملف
        report.Load("D:\Basseet_SYS -ok\design\aaa.frx")

        ' تسجيل البيانات التي سيتم عرضها في التقرير
        report.RegisterData(ds.Tables("Sales_Tbl"), "Sales_Tbl")
        report.RegisterData(ds.Tables("View_Order"), "View_Order")
        report.RegisterData(ds.Tables("comSetting_Tbl"), "comSetting_Tbl")

        ' التأكد من إظهار البيانات في التقرير
        report.GetDataSource("Sales_Tbl").Enabled = True
        report.GetDataSource("View_Order").Enabled = True
        report.GetDataSource("comSetting_Tbl").Enabled = True

        ' تحضير التقرير للتصدير
        report.Prepare()

        ' تصدير التقرير إلى ملف PDF
        Dim pdfExport As New PDFExport()
        pdfExport.ShowProgress = False ' إخفاء نافذة التقدم أثناء التصدير
        pdfExport.Compressed = True ' ضغط الملف لتقليل الحجم

        ' تحديد مسار حفظ ملف PDF
        Dim pdfPath As String = $"D:\Basseet_SYS -ok\design\Invoices\Invoice_{_Order_No}.pdf"

        ' التأكد من وجود المجلد
        Dim directory As String = Path.GetDirectoryName(pdfPath)
        If Not System.IO.Directory.Exists(directory) Then
            System.IO.Directory.CreateDirectory(directory)
        End If

        ' تصدير التقرير إلى PDF مع معالجة الأخطاء
        Try
            report.Export(pdfExport, pdfPath)
            '  MessageBox.Show($"تم تصدير الفاتورة بنجاح إلى: {pdfPath}", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"خطأ أثناء تصدير الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        ' إغلاق الاتصال بقاعدة البيانات
        connx.Con.Close()

        ' تحرير الموارد
        report.Dispose()
    End Sub

    Sub INSERT_DELIVERY()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = connx.Con
            .CommandType = CommandType.Text
            .CommandText = "Insert Into order_delivery ( Order_No,customer_id,deleveryman_id,service_price)values(@Order_No,@customer_id,@deleveryman_id,@service_price)"
            .Parameters.Clear()
            .Parameters.AddWithValue("@Order_No", SqlDbType.VarChar).Value = _Order_No
            .Parameters.AddWithValue("@customer_id", SqlDbType.Int).Value = _customer_id
            .Parameters.AddWithValue("@deleveryman_id", SqlDbType.Int).Value = _drliveryman_id
            .Parameters.AddWithValue("@service_price", SqlDbType.Decimal).Value = _servive_fee
        End With
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        Cmd.ExecuteNonQuery()
        connx.Con.Close()
        '  MsgBox("تم الاضافة بنجاح", MsgBoxStyle.Information, "حفظ")
        Cmd = Nothing
    End Sub
    Sub update_cashier()
        If connx.Con.State = 1 Then connx.Con.Close()
        connx.Con.Open()
        connx.cmd = New SqlCommand("Update Tbl_cashier Set cashier_balance=cashier_balance+@cashier_balance ,cashier_status='true' where cashier_id=@cashier_id", connx.Con)
        connx.cmd.Parameters.AddWithValue("@cashier_balance", CDbl(TxtFinalTotal_.Text))
        connx.cmd.Parameters.AddWithValue("@cashier_id", _cashier_id)
        connx.cmd.ExecuteNonQuery()
        connx.Con.Close()
    End Sub

    Private Sub btn_cash_Click(sender As Object, e As EventArgs) Handles btn_cash.Click
        CmbPaidType.Text = "نقدي"
    End Sub

    Private Sub btn_visa_Click(sender As Object, e As EventArgs) Handles btn_visa.Click
        CmbPaidType.Text = "فيزا"
    End Sub
    Sub insert_cashier_move()
        If connx.Con.State = ConnectionState.Open Then connx.Con.Close()
        connx.Con.Open()

        connx.cmd = New SqlCommand("INSERT INTO move_cashier_tbl (chashier_id, order_no, final_total, move_type, move_date,moov_on) VALUES (@cashier_id, @order_no, @final_total, @move_type, @move_date,@moov_on)", connx.Con)

        ' Corrected parameter names and values
        connx.cmd.Parameters.AddWithValue("@cashier_id", SqlDbType.Int).Value = _cashier_id
        connx.cmd.Parameters.AddWithValue("@order_no", SqlDbType.VarChar).Value = _Order_No
        connx.cmd.Parameters.AddWithValue("@final_total", CDbl(TxtFinalTotal_.Text))
        connx.cmd.Parameters.AddWithValue("@moov_on", 0)
        If CmbPaidType.Text = "نقدي" Then
            connx.cmd.Parameters.AddWithValue("@move_type", "نقدي")
        Else
            connx.cmd.Parameters.AddWithValue("@move_type", "فيزا")
        End If

        connx.cmd.Parameters.AddWithValue("@move_date", SqlDbType.Date).Value = Today

        connx.cmd.ExecuteNonQuery()
        connx.Con.Close()
        connx.cmd = Nothing
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        frm_whats.Show()
    End Sub
End Class