﻿Imports System.Data.SqlClient
Imports FastReport.Data

Public Class frm_taslim
    Dim CONNX1 As New CLS_CON
    Public Sub get_cash()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()


        CONNX1.cmd = New SqlCommand("SELECT chashier_id,sum(final_total)as f_total ,move_type as paid FROM move_cashier_tbl WHERE chashier_id=" & _cashier_id & " and move_type='نقدي' and move_date=@move_date and moov_on = 'false' group by chashier_id,move_type ", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@move_date", Today)
        CONNX1.rdr = CONNX1.cmd.ExecuteReader
        CONNX1.rdr.Read()
        If CONNX1.rdr.HasRows Then
            txt_total_cash.Text = CONNX1.rdr("f_total").ToString
        End If

        ' إغلاق القارئ والاتصال
        CONNX1.rdr.Close()
        CONNX1.Con.Close()
    End Sub
    Public Sub get_visa()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        CONNX1.cmd = New SqlCommand("SELECT chashier_id,sum(final_total)as f_total ,move_type as paid FROM move_cashier_tbl WHERE chashier_id=" & _cashier_id & " and move_type='فيزا' and move_date=@move_date and moov_on = 'false' group by chashier_id,move_type ", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@move_date", Today)
        CONNX1.rdr = CONNX1.cmd.ExecuteReader
        CONNX1.rdr.Read()
        If CONNX1.rdr.HasRows Then
            txt_total_visa.Text = CONNX1.rdr("f_total").ToString
        End If

        ' إغلاق القارئ والاتصال
        CONNX1.rdr.Close()
        CONNX1.Con.Close()
    End Sub
    Public Sub get_start()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        CONNX1.cmd = New SqlCommand("SELECT cashier_id,sum(start_balance)as s_total from Tbl_cashier WHERE cashier_id=" & _cashier_id & " group by cashier_id ", CONNX1.Con)
        CONNX1.rdr = CONNX1.cmd.ExecuteReader
        CONNX1.rdr.Read()
        If CONNX1.rdr.HasRows Then
            txt_start.Text = CONNX1.rdr("s_total").ToString
        End If

        ' إغلاق القارئ والاتصال
        CONNX1.rdr.Close()
        CONNX1.Con.Close()
    End Sub
    Public Sub insert_cash()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        CONNX1.cmd = New SqlCommand("insert into get_cash_tbl (cashier_id,cashmoney,visa_pay,getdate) values (@cashier_id,@cashmoney,@visa_pay,@getdate)", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@cashier_id", SqlDbType.Int).Value = _cashier_id
        CONNX1.cmd.Parameters.AddWithValue("@cashmoney", SqlDbType.Decimal).Value = txt_total_cash.Text
        CONNX1.cmd.Parameters.AddWithValue("@visa_pay", SqlDbType.Decimal).Value = txt_total_visa.Text
        CONNX1.cmd.Parameters.AddWithValue("@getdate", SqlDbType.Date).Value = Today
        CONNX1.cmd.ExecuteNonQuery()
        CONNX1.Con.Close()
    End Sub
    Sub update_cashier()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        CONNX1.cmd = New SqlCommand("Update Tbl_cashier Set cashier_balance=@cashier_balance ,start_balance=@start_balance,cashier_status=@cashier_status where cashier_id=@cashier_id", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@cashier_balance", SqlDbType.Decimal).Value = 0.0
        CONNX1.cmd.Parameters.AddWithValue("@start_balance", SqlDbType.Decimal).Value = 0.0
        CONNX1.cmd.Parameters.AddWithValue("@cashier_status", SqlDbType.Decimal).Value = False
        CONNX1.cmd.Parameters.AddWithValue("@cashier_id", SqlDbType.Int).Value = _cashier_id
        CONNX1.cmd.ExecuteNonQuery()
        CONNX1.Con.Close()
        MessageBox.Show(" تم التسليم بنجاح ")
        Me.Close()


    End Sub
    Sub update_move_cash()
        If CONNX1.Con.State = 1 Then CONNX1.Con.Close()
        CONNX1.Con.Open()
        CONNX1.cmd = New SqlCommand("update move_cashier_tbl Set moov_on=@moov_on where chashier_id=@chashier_id", CONNX1.Con)
        CONNX1.cmd.Parameters.AddWithValue("@moov_on", SqlDbType.Bit).Value = 1
        CONNX1.cmd.Parameters.AddWithValue("@chashier_id", SqlDbType.Int).Value = _cashier_id
        CONNX1.cmd.ExecuteNonQuery()
        CONNX1.Con.Close()
        MessageBox.Show(" تم التسليم بنجاح ")
        Me.Close()


    End Sub

    Private Sub BtnSave_Status_Click(sender As Object, e As EventArgs) Handles BtnSave_Status.Click
        insert_cash()
        update_cashier()
        update_move_cash()
        Application.Exit()
    End Sub

End Class