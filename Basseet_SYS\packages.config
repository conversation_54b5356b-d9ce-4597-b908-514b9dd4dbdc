﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="DotNetSeleniumExtras.WaitHelpers" version="3.11.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.Net.Http" version="2.0.20710.0" targetFramework="net48" />
  <package id="Newton.JsonMediaTypeFormatter" version="1.0.6" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="12.0.1" targetFramework="net48" />
  <package id="Selenium.WebDriver" version="4.31.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Runtime.Serialization.Json" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="ZXing.Net" version="0.16.10" targetFramework="net48" />
</packages>