﻿Imports System.IO
Imports System.Net
Imports System.Net.Http
Imports System.Net.Http.Headers
Imports System.Web.Script.Serialization
Imports System.Xml
Imports SDKNETFrameWorkLib.GeneralLogic
Imports Zatca.EInvoice.SDK
Imports Zatca.EInvoice.SDK.Contracts.Models
Public Class signToXML


    'import Zatka SDK https://sandbox.zatca.gov.sa/downloadSDK
    'This Is sample private key And certificate however user can override this by add this file privateKey.txt And certificate.txt
    'The following digital certificate and private keys is default, and you must build a digital certificate that includes your store's information and then approve it.
    'You can generate a digital certificate and private key through an OpenSSL application or using a website
    'https://cert4sign.com

    Dim certificateContent = Nothing
    Dim privateKeyContent = Nothing

    'Privous invoice Hash First invoice 0 > Convert Based64:
    Dim pihContent = "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ=="

    'Select Path File 
    Dim FilePathXMLWithoutSign As String
    Dim FilePathXMLAfterign As String
    Private Sub ButtonSelectFile_Click(sender As Object, e As EventArgs) Handles ButtonSelectFile.Click
        Using openFileDialog1 As New OpenFileDialog()
            ' تعيين المسار الافتراضي
            openFileDialog1.InitialDirectory = "D:\Basseet_SYS\XML_INV"

            ' فلتر لاختيار نوع الملف (يمكن تعديله)
            openFileDialog1.Filter = "All Files|*.*"

            ' بدء دليل الحوار من المسار المحدد
            openFileDialog1.RestoreDirectory = True

            If openFileDialog1.ShowDialog() = DialogResult.OK Then
                ' عرض المسار المختار في TextBox
                TxTSaveTo.Text = openFileDialog1.FileName
            End If
        End Using
    End Sub

    Private Sub signToXML_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        '    Dim directoryPath As String = "D:\Basseet_SYS\XML_INV"
        '    Dim fileName As String = "INVOICETOZATIKA"
        '    Dim fullPath As String = Path.Combine(directoryPath, fileName & ".*") ' للبحث بأي إمتداد

        '    Try
        '        ' البحث عن الملف بأي إمتداد
        '        Dim files() As String = Directory.GetFiles(directoryPath, fileName & ".*")

        '        If files.Length > 0 Then
        '            ' أخذ أول ملف موجود
        '            Dim selectedFile As String = files(0)

        '            ' قراءة محتوى الملف
        '            Dim fileContent As String = File.ReadAllText(selectedFile)

        '            ' عرض المحتوى أو معالجته (يمكن تعديل هذا الجزء)
        '            MessageBox.Show("تم فتح الملف بنجاح!" & Environment.NewLine & fileContent)
        '        Else
        '            MessageBox.Show("الملف غير موجود في المسار المحدد")
        '        End If

        '    Catch ex As Exception
        '        MessageBox.Show("خطأ في فتح الملف: " & ex.Message)
        '    End Try
        'End Sub
        Dim directoryPath As String = "D:\Basseet_SYS\XML_INV"
        Dim fileName As String = "INVOICETOZATIKA"

        Try
            ' البحث عن الملف بأي إمتداد
            Dim files() As String = Directory.GetFiles(directoryPath, fileName & ".*")

            If files.Length > 0 Then
                ' عرض المسار الكامل للملف الأول الذي تم العثور عليه
                TxTSaveTo.Text = files(0)
                TxTXMLFileName.Text = TxTSaveTo.Text
                FilePathXMLWithoutSign = TxTXMLFileName.Text
                FilePathXMLAfterign = TxTSaveTo.Text
            Else
                TxTSaveTo.Text = "الملف غير موجود في المسار المحدد"
            End If

        Catch ex As Exception
            TxTSaveTo.Text = "خطأ في البحث: " & ex.Message
        End Try
    End Sub

    Private Sub ButtonSignNow_Click(sender As Object, e As EventArgs) Handles ButtonSignNow.Click
        Try


            If FilePathXMLAfterign = Nothing Or FilePathXMLWithoutSign = Nothing Then
                MsgBox("You Must Select Path File Name")
                TabControl1.SelectedTab = TabPage1
                Exit Sub
            End If

            If TxTcertificateContent.Text = Nothing Then
                MsgBox("You Must Select Certificate Content")
                TabControl1.SelectedTab = TabPage2
                Exit Sub
            End If

            If TxTprivateKeyContent.Text = Nothing Then
                MsgBox("You Must Select PrivetKey Content")
                TabControl1.SelectedTab = TabPage2
                Exit Sub
            End If




            'Set From TextBox
            certificateContent = TxTcertificateContent.Text.Trim
            privateKeyContent = TxTprivateKeyContent.Text.Trim




            Try
                Dim _IEInvoiceSigningLogic = New EInvoiceSigner()
                Dim objResult As SignResult = New SignResult()
                Dim XmlDoc As XmlDocument = New XmlDocument()
                XmlDoc.PreserveWhitespace = True

                Try
                    XmlDoc.Load(FilePathXMLWithoutSign)
                Catch ex As Exception
                    MsgBox(ex.ToString)
                    Exit Sub
                End Try


                objResult = _IEInvoiceSigningLogic.SignDocument(XmlDoc, certificateContent, privateKeyContent)
                If (objResult.IsValid) Then
                    objResult.SignedEInvoice.PreserveWhitespace = True

                    ' ✅ توليد الهاش من الفاتورة الموقعة
                    Dim EInvoiceHashGenerator = New EInvoiceHashGenerator()
                    Dim objResultH As HashResult = New HashResult()
                    objResultH = EInvoiceHashGenerator.GenerateEInvoiceHashing(objResult.SignedEInvoice)

                    ' ✅ عرض الهاش في TextBox
                    TXTPIH.Text = objResultH.Hash

                    ' ✅ حفظ XML الموقع
                    Dim objWriter As New System.IO.StreamWriter(FilePathXMLAfterign)
                    objWriter.Write(objResult.SignedEInvoice.OuterXml)
                    objWriter.Close()
                End If




                MsgBox("Sign OK")


            Catch ex As Exception
                MsgBox(ex.ToString)
            End Try


        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub
        End Try
    End Sub

    Private Sub ComboBoxPortalType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxPortalType.SelectedIndexChanged
        If ComboBoxPortalType.SelectedIndex = 0 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/clearance/single"
        End If

        If ComboBoxPortalType.SelectedIndex = 1 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/clearance/single"
        End If

        If ComboBoxPortalType.SelectedIndex = 2 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/clearance/single"
        End If
    End Sub




    Public Async Sub GoapiNowIDAsync()

        Dim ApiSelectURLtoPost As String = Nothing
        Dim ApiClearanceStatus As String = Nothing
        Dim XMLFileBase64Encod As String = Nothing


        ' Load XML File After Sign to Convert Based64    
        Try
            Dim XMLFileToConvvertBased64 As String = File.ReadAllText(FilePathXMLAfterign)
            'Convert XML VAL to Base64
            XMLFileBase64Encod = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(XMLFileToConvvertBased64.ToString))
        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub
        End Try
        Try

            If RadioButtonR.Checked = True Then
                ApiSelectURLtoPost = CenterUrlApiReporting.Text
                ApiClearanceStatus = "0"
            End If

            If RadioButtonC.Checked = True Then
                ApiSelectURLtoPost = CenterUrlApiClearance.Text
                ApiClearanceStatus = "1"
            End If
            Using httpClient = New HttpClient()
                Using request = New HttpRequestMessage(New HttpMethod("POST"), ApiSelectURLtoPost.ToString)
                    request.Headers.TryAddWithoutValidation("accept", "application/json")
                    request.Headers.TryAddWithoutValidation("accept-language", "ar")
                    request.Headers.TryAddWithoutValidation("Clearance-Status", ApiClearanceStatus)
                    request.Headers.TryAddWithoutValidation("Accept-Version", "V2")
                    request.Headers.TryAddWithoutValidation("Authorization", ApiAuthorization.Text)


                    request.Content = New StringContent("{" & vbLf & "  ""invoiceHash"": """ & TXTPIH.Text.Trim & """," & vbLf & "  ""uuid"": """ & TxTUUID.Text & """," & vbLf & "  ""invoice"": """ & XMLFileBase64Encod & """" & vbLf & "}")


                    request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json")



                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12


                    'Heder
                    Dim response = Await httpClient.SendAsync(request)
                    TxTAPICerHeder.Text = response.ToString


                    'Body
                    Dim contents = Await response.Content.ReadAsStringAsync()
                    TxTAPICerBody.Text = contents.ToString
                End Using
            End Using

        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub

        End Try
    End Sub

    Private Sub ButtonSend_Click(sender As Object, e As EventArgs) Handles ButtonSend.Click
        GoapiNowIDAsync()
    End Sub

    Private Sub RadioButtonR_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButtonR.CheckedChanged
        If RadioButtonR.Checked = True Then
            RadioButtonC.Checked = False
        End If
    End Sub

    Private Sub RadioButtonC_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButtonC.CheckedChanged
        If RadioButtonC.Checked = True Then
            RadioButtonR.Checked = False
        End If
    End Sub


End Class