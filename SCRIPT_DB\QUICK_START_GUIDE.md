# دليل البدء السريع - نظام فصل وضم الطاولات

## ✅ تم حل مشكلة التكامل!

### المشكلة التي تم حلها:
- **خطأ:** "Type 'frm_table_management' is not defined"
- **السبب:** عدم إضافة النموذج لملف المشروع
- **الحل:** تم إضافة النموذج بنجاح وتبسيط الكود

### خطوات التشغيل السريع

### 1. بناء المشروع
- افتح المشروع في Visual Studio
- اضغط **Build → Build Solution**
- تأكد من عدم وجود أخطاء

### 2. تشغيل التطبيق
- اضغط **F5** أو **Start**
- من الشاشة الرئيسية، اضغط زر **"إدارة الطاولات"**

### 3. تطبيق قاعدة البيانات (اختياري)
```sql
-- لتفعيل الوظائف الكاملة، شغل:
SCRIPT_DB/complete_table_management_system.sql
```

## الاستخدام السريع

### فصل طاولة:
1. **تبويب "الطاولات النشطة"** → اختر طاولة
2. **تبويب "فصل طاولة"** → حدد عدد الأجزاء
3. اضغط **"فصل الطاولة"**

### ضم طاولات:
1. **تبويب "ضم طاولات"**
2. أدخل **اسم المجموعة**
3. أدخل **أسماء الطاولات** (مفصولة بفاصلة)
4. اضغط **"ضم الطاولات"**

### إغلاق مجموعة:
1. **تبويب "مجموعات الطاولات"**
2. اختر المجموعة
3. اضغط **"إغلاق المجموعة"**

## استعلامات مفيدة

### عرض الطاولات المجمعة:
```sql
SELECT * FROM View_GroupedTables WHERE Group_Status = 'نشط'
```

### عرض الطاولات المفصولة:
```sql
SELECT * FROM View_SplitTables WHERE Status = 'نشط'
```

### البحث عن طاولة معينة:
```sql
EXEC SP_GetTableInfo @Table_Name = 'طاولة1'
```

## حل المشاكل الشائعة

### خطأ "Type 'frm_table_management' is not defined"
- تأكد من بناء المشروع (Build → Build Solution)
- تأكد من إضافة الملفات للمشروع

### خطأ في قاعدة البيانات
- تأكد من تشغيل سكريبت قاعدة البيانات
- تأكد من صحة connection string

### الشاشة لا تظهر
- تأكد من وجود زر "إدارة الطاولات" في الشاشة الرئيسية
- تأكد من صحة الـ namespace

## الملفات المهمة

### قاعدة البيانات:
- `SCRIPT_DB/complete_table_management_system.sql` - النظام الكامل
- `SCRIPT_DB/table_management_procedures.sql` - الإجراءات المخزنة

### التطبيق:
- `Basseet_SYS/TABLE_MANAGEMENT/frm_table_management.vb` - الشاشة الرئيسية
- `Basseet_SYS/FRM_HOME.vb` - تم إضافة زر جديد
- `Basseet_SYS/Basseet_SYS.vbproj` - تم تحديث المشروع

## الدعم
راجع ملف `README_TABLE_MANAGEMENT.md` للتفاصيل الكاملة
