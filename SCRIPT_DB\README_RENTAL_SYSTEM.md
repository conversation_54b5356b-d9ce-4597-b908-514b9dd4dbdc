# نظام إدارة الإيجارات - Basseet Rental Management System

## نظرة عامة
تم تحويل نظام إدارة المطاعم (Basseet_SYS) إلى نظام شامل لإدارة الإيجارات والعقارات مع الحفاظ على نفس البنية والمسميات الأساسية.

## التحويلات الأساسية
- **Cat_Tbl**: تمثل الآن العمارات والمباني
- **Item_Tbl**: تمثل الآن الشقق والوحدات السكنية
- **tblCustomers**: تمثل المستأجرين والعملاء

## الجداول الجديدة المضافة

### 1. ExpenseTypes_Tbl - أنواع المصروفات
- إدارة أنواع المصروفات المختلفة (صيانة، كهرباء، مياه، إلخ)

### 2. Expenses_Tbl - المصروفات
- تسجيل جميع مصروفات العقارات والوحدات
- ربط المصروفات بالعمارات والوحدات المحددة

### 3. RentalContracts_Tbl - عقود الإيجار
- إدارة عقود الإيجار بين المالك والمستأجرين
- تتبع تواريخ البداية والانتهاء والشروط

### 4. RentPayments_Tbl - دفعات الإيجار
- تسجيل دفعات الإيجار الشهرية
- تتبع المبالغ المستحقة والمدفوعة

### 5. Maintenance_Tbl - الصيانة
- إدارة طلبات الصيانة والإصلاحات
- تتبع التكاليف والحالة

### 6. Accounts_Tbl - الحسابات المحاسبية
- دليل الحسابات المحاسبية
- هيكل هرمي للحسابات

### 7. JournalEntries_Tbl - القيود المحاسبية
- تسجيل القيود المحاسبية

### 8. JournalEntryDetails_Tbl - تفاصيل القيود
- تفاصيل كل قيد محاسبي (مدين/دائن)

## العروض (Views) المضافة

### 1. View_Buildings - عرض العمارات
```sql
SELECT Cat_ID as BuildingID, CatName as BuildingName, 
       BuildingAddress, FloorsCount, UnitsCount, BuildingStatus
FROM Cat_Tbl
```

### 2. View_Units - عرض الوحدات
```sql
SELECT i.Item_ID as UnitID, i.ItemCode as UnitCode, i.ItemName as UnitName,
       c.CatName as BuildingName, i.FloorNumber, i.RoomsCount, 
       i.Area, i.MonthlyRent, i.UnitStatus
FROM Item_Tbl i INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
```

### 3. View_ActiveContracts - العقود النشطة
```sql
SELECT rc.*, i.ItemName as UnitName, c.CatName as BuildingName,
       cust.CustomerName as TenantName
FROM RentalContracts_Tbl rc
INNER JOIN Item_Tbl i ON rc.Unit_ID = i.Item_ID
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
INNER JOIN tblCustomers cust ON rc.Tenant_ID = cust.CustomerID
WHERE rc.ContractStatus = 'نشط'
```

## الإجراءات المخزنة المضافة

### 1. SP_GetMonthlyRentTotal
حساب إجمالي الإيجارات الشهرية

### 2. SP_GetMonthlyExpensesTotal
حساب إجمالي المصروفات الشهرية

### 3. SP_UpdateUnitStatus
تحديث حالة الوحدة

### 4. SP_GenerateRentPayments
إنشاء دفعات الإيجار التلقائية

## الواجهات الجديدة المضافة

### 1. frm_expenses - شاشة إدارة المصروفات
- إضافة وتعديل وحذف المصروفات
- ربط المصروفات بالعمارات والوحدات
- البحث والتصفية

### 2. frm_manage_units - شاشة إدارة الوحدات
- عرض جميع الوحدات مع تفاصيلها
- تصفية حسب العمارة والحالة
- إدارة الوحدات (إضافة/تعديل/حذف)

### 3. frm_add_update_unit - شاشة إضافة/تعديل الوحدة
- نموذج شامل لإدارة بيانات الوحدة
- إنشاء رمز الوحدة تلقائياً
- التحقق من صحة البيانات

## ملفات قاعدة البيانات

### 1. sc1.sql
الملف الأصلي لقاعدة البيانات (نظام المطعم الأصلي)

### 2. rental_system_additions.sql
الإضافات الجديدة لنظام الإيجارات:
- الجداول الجديدة
- تعديل الجداول الموجودة
- العروض والإجراءات المخزنة
- البيانات الأساسية

### 3. rental_reports_queries.sql
استعلامات التقارير المختلفة:
- تقرير الوحدات الفارغة
- تقرير الإيجارات المتأخرة
- تقرير الإيرادات الشهرية
- تقرير المصروفات
- تقرير أداء العمارات
- وغيرها من التقارير المفيدة

## خطوات التنفيذ

### 1. تنفيذ قاعدة البيانات
```sql
-- تنفيذ الملف الأصلي أولاً
EXEC sp_executesql @sql = 'sc1.sql'

-- ثم تنفيذ الإضافات الجديدة
EXEC sp_executesql @sql = 'rental_system_additions.sql'
```

### 2. إعداد الواجهات
- إضافة الملفات الجديدة إلى المشروع
- تحديث المراجع والاستيراد
- اختبار الواجهات

### 3. اختبار النظام
- إدخال بيانات تجريبية
- اختبار العمليات الأساسية
- التحقق من التقارير

## الميزات الجديدة

### 1. إدارة العقارات
- تسجيل العمارات مع تفاصيل كاملة
- إدارة الوحدات والشقق
- تتبع حالة كل وحدة

### 2. إدارة العقود
- إنشاء عقود إيجار شاملة
- تتبع تواريخ الانتهاء
- إدارة التجديدات

### 3. إدارة المدفوعات
- تسجيل دفعات الإيجار
- تتبع المتأخرات
- تقارير مالية شاملة

### 4. إدارة المصروفات
- تصنيف المصروفات
- ربط المصروفات بالوحدات
- نظام اعتماد المصروفات

### 5. النظام المحاسبي
- دليل حسابات شامل
- قيود محاسبية تلقائية
- تقارير مالية

### 6. التقارير والإحصائيات
- تقارير شاملة للإيرادات والمصروفات
- إحصائيات أداء العمارات
- تقارير المتأخرات

## ملاحظات مهمة

1. **الحفاظ على البنية الأصلية**: تم الحفاظ على جميع الجداول والمسميات الأصلية
2. **التوافق مع النظام القديم**: يمكن استخدام النظام الجديد دون تأثير على البيانات الموجودة
3. **المرونة في التطوير**: يمكن إضافة ميزات جديدة بسهولة
4. **الأمان**: تم تطبيق قيود الأمان والتحقق من صحة البيانات

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تاريخ الإنشاء**: 2024
**الإصدار**: 1.0
**المطور**: Augment Agent
