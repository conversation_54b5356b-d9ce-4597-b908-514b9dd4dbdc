# نظام فصل وضم الطاولات - دليل الاستخدام

## نظرة عامة
تم إضافة نظام شامل لفصل وضم الطاولات في نظام إدارة المطاعم. يتيح هذا النظام:

### 1. فصل الطاولات (Table Splitting)
- تقسيم طاولة واحدة إلى عدة أجزاء منفصلة
- كل جزء يمكن أن يكون له عميل منفصل
- حساب منفصل لكل جزء

### 2. ضم الطاولات (Table Merging)
- دمج عدة طاولات في مجموعة واحدة
- حساب موحد للمجموعة كاملة
- إدارة مركزية للمجموعة

## ملفات النظام

### ملفات قاعدة البيانات
1. **table_management_system.sql** - الجداول الأساسية والعلاقات
2. **table_management_procedures.sql** - الإجراءات المخزنة
3. **complete_table_management_system.sql** - النظام الكامل مع الفهارس والعروض

### ملفات التطبيق
1. **frm_table_management.vb** - الشاشة الرئيسية لإدارة الطاولات
2. **frm_table_management.Designer.vb** - تصميم الشاشة
3. **frm_table_management.resx** - موارد الشاشة

## الجداول الجديدة

### 1. TableGroups_Tbl
جدول مجموعات الطاولات المضمومة
- Group_ID: رقم المجموعة (مفتاح أساسي)
- Group_Name: اسم المجموعة
- Group_Status: حالة المجموعة (نشط/مغلق)
- Total_Amount: إجمالي المبلغ
- Created_Date: تاريخ الإنشاء
- Created_By: منشئ المجموعة

### 2. TableGroupDetails_Tbl
تفاصيل الطاولات في كل مجموعة
- Detail_ID: رقم التفصيل
- Group_ID: رقم المجموعة
- Table_Name: اسم الطاولة
- Order_No: رقم الطلب
- Split_Amount: مبلغ الطاولة

### 3. TableSplitOperations_Tbl
عمليات فصل الطاولات
- Split_ID: رقم عملية الفصل
- Original_Table_Name: اسم الطاولة الأصلية
- Original_Order_No: رقم الطلب الأصلي
- Split_Count: عدد الأجزاء
- Original_Total: الإجمالي الأصلي

### 4. TableSplitDetails_Tbl
تفاصيل الطاولات المفصولة
- Detail_ID: رقم التفصيل
- Split_ID: رقم عملية الفصل
- New_Table_Name: اسم الطاولة الجديدة
- New_Order_No: رقم الطلب الجديد
- Split_Amount: مبلغ الجزء
- Split_Percentage: نسبة الجزء

## الإجراءات المخزنة

### 1. SP_SplitTableSimple
فصل طاولة إلى أجزاء متساوية
```sql
EXEC SP_SplitTableSimple 
    @Original_Table_Name = 'طاولة1',
    @Original_Order_No = 'ORD001',
    @Split_Count = 3,
    @Split_By = 'المستخدم'
```

### 2. SP_MergeTables
ضم عدة طاولات في مجموعة
```sql
EXEC SP_MergeTables 
    @Group_Name = 'مجموعة العائلة',
    @Table_Names = 'طاولة1,طاولة2,طاولة3',
    @Created_By = 'المستخدم',
    @Notes = 'حفلة عائلية'
```

### 3. SP_CloseTableGroup
إغلاق مجموعة طاولات (الدفع)
```sql
EXEC SP_CloseTableGroup 
    @Group_ID = 1,
    @Closed_By = 'المستخدم'
```

### 4. SP_GetTableInfo
الحصول على معلومات طاولة
```sql
EXEC SP_GetTableInfo @Table_Name = 'طاولة1'
```

## العروض (Views)

### 1. View_GroupedTables
عرض الطاولات المجمعة مع الإحصائيات
```sql
SELECT * FROM View_GroupedTables WHERE Group_Status = 'نشط'
```

### 2. View_SplitTables
عرض الطاولات المفصولة مع التفاصيل
```sql
SELECT * FROM View_SplitTables WHERE Status = 'نشط'
```

## كيفية الاستخدام

### 1. تطبيق قاعدة البيانات
```sql
-- تشغيل الملفات بالترتيب:
-- 1. table_management_system.sql
-- 2. table_management_procedures.sql
-- أو تشغيل الملف الكامل:
-- complete_table_management_system.sql
```

### 2. الوصول للشاشة
- من الشاشة الرئيسية، اضغط على زر "إدارة الطاولات"
- ستفتح شاشة بـ 5 تبويبات:
  - الطاولات النشطة
  - فصل طاولة
  - ضم طاولات
  - مجموعات الطاولات
  - عمليات الفصل

### 3. فصل طاولة
1. اختر طاولة من تبويب "الطاولات النشطة"
2. انتقل لتبويب "فصل طاولة"
3. حدد عدد الأجزاء المطلوبة
4. اضغط "فصل الطاولة"

### 4. ضم طاولات
1. انتقل لتبويب "ضم طاولات"
2. أدخل اسم المجموعة
3. أدخل أسماء الطاولات مفصولة بفاصلة
4. اضغط "ضم الطاولات"

### 5. إغلاق مجموعة
1. انتقل لتبويب "مجموعات الطاولات"
2. اختر المجموعة المطلوبة
3. اضغط "إغلاق المجموعة"

## التحديثات على الجداول الموجودة

تم إضافة الأعمدة التالية لجدول Order_Tbl:
- Group_ID: رقم المجموعة (للطاولات المضمومة)
- Split_ID: رقم عملية الفصل (للطاولات المفصولة)
- Original_Order_No: رقم الطلب الأصلي
- Customer_Name: اسم العميل
- Customer_Phone: رقم هاتف العميل

## الفهارس المضافة
- IX_Order_Tbl_Group_ID
- IX_Order_Tbl_Split_ID
- IX_Order_Tbl_Table_Status

## ملاحظات مهمة
1. النظام متوافق مع الجداول الموجودة
2. لا يؤثر على الطلبات الحالية
3. يدعم اللغة العربية بالكامل
4. يحتفظ بسجل كامل لجميع العمليات
5. يمكن التراجع عن العمليات إذا لزم الأمر

## الدعم الفني
في حالة وجود أي مشاكل أو استفسارات، يرجى مراجعة:
1. ملفات السكريبت في مجلد SCRIPT_DB
2. الإجراءات المخزنة في قاعدة البيانات
3. ملفات الكود في مجلد TABLE_MANAGEMENT
