# 🍽️ دليل استخدام نظام إدارة الطاولات

## 🎯 الوظائف المتاحة الآن:

### ✅ **فصل الطاولات (Table Splitting)**
**الغرض:** تقسيم طاولة واحدة إلى عدة أقسام للحساب الفردي

**خطوات الاستخدام:**
1. **اختر الطاولة:** اضغط على الطاولة المطلوبة من قائمة "الطاولات النشطة"
2. **حدد عدد الأقسام:** أدخل رقم من 2 إلى 10 في حقل "عدد الأقسام"
3. **اضغط "فصل الطاولة"**
4. **تأكيد العملية:** اضغط "نعم" للتأكيد

**النتيجة:**
- الطاولة الأصلية تصبح حالتها "split"
- يتم إنشاء طاولات جديدة بأسماء: `Table1_Split_1`, `Table1_Split_2`, إلخ
- المبلغ يقسم بالتساوي على جميع الأقسام

---

### ✅ **ضم الطاولات (Table Merging)**
**الغرض:** دمج عدة طاولات في فاتورة واحدة

**خطوات الاستخدام:**
1. **أدخل اسم المجموعة:** مثل "مجموعة العائلة"
2. **حدد الطاولات:** أدخل أسماء الطاولات مفصولة بفاصلة مثل: `Table1,Table2,Table3`
3. **أضف ملاحظات (اختياري)**
4. **اضغط "ضم الطاولات"**
5. **تأكيد العملية**

**النتيجة:**
- يتم إنشاء مجموعة جديدة تضم جميع الطاولات
- حساب المبلغ الإجمالي لجميع الطاولات
- الطاولات تصبح مرتبطة في مجموعة واحدة

---

### ✅ **عرض البيانات**
- **الطاولات النشطة:** عرض جميع الطاولات المفتوحة حالياً
- **مجموعات الطاولات:** عرض الطاولات المدموجة
- **عمليات الفصل:** تاريخ جميع عمليات فصل الطاولات

---

## 🔧 **المتطلبات التقنية:**

### 📋 **قاعدة البيانات:**
يجب تنفيذ السكريبت التالي أولاً:
```sql
-- تنفيذ ملف: SCRIPT_DB/complete_table_management_system.sql
```

### 📊 **الجداول المطلوبة:**
- `Table_Groups` - مجموعات الطاولات
- `Table_Split_Operations` - عمليات فصل الطاولات  
- `Table_Split_Details` - تفاصيل كل قسم
- `Table_Group_Members` - أعضاء كل مجموعة

### 🔄 **الإجراءات المخزنة:**
- `SP_SplitTable` - فصل الطاولات
- `SP_MergeTables` - ضم الطاولات
- `SP_CloseTableGroup` - إغلاق مجموعة
- `SP_GetTableGroupDetails` - تفاصيل المجموعة
- `SP_GetSplitHistory` - تاريخ الفصل

---

## 🚀 **كيفية الاختبار:**

### 1. **إنشاء بيانات تجريبية:**
```sql
-- إدخال طلبات تجريبية
INSERT INTO Order_Tbl (Table_Name, Order_No, ord_Total, ord_Status, ord_Date) 
VALUES 
('Table1', 'ORD001', 150.00, 'open', GETDATE()),
('Table2', 'ORD002', 200.00, 'open', GETDATE()),
('Table3', 'ORD003', 100.00, 'open', GETDATE())
```

### 2. **اختبار الفصل:**
- اختر Table1
- أدخل 3 في عدد الأقسام
- اضغط فصل الطاولة
- **النتيجة المتوقعة:** 3 طاولات جديدة بمبلغ 50.00 لكل واحدة

### 3. **اختبار الضم:**
- اسم المجموعة: "مجموعة تجريبية"
- الطاولات: "Table2,Table3"
- **النتيجة المتوقعة:** مجموعة بإجمالي 300.00

---

## ⚠️ **ملاحظات مهمة:**

1. **النسخ الاحتياطي:** انسخ قاعدة البيانات قبل التجريب
2. **الصلاحيات:** تأكد من صلاحيات المستخدم لتنفيذ الإجراءات المخزنة
3. **الاتصال:** تحقق من اتصال قاعدة البيانات في `CLS_CON`
4. **الترميز:** النظام يدعم النصوص العربية بالكامل

---

## 🎉 **النظام جاهز للاستخدام!**

**جرب الوظائف الآن وأخبرني إذا واجهت أي مشاكل.** 🚀
