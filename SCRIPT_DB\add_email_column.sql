-- إضا<PERSON>ة عمود Email إلى جدول العملاء
-- تاريخ الإنشاء: 2025-06-29
-- الوصف: إضافة عمود البريد الإلكتروني لجدول tblCustomers

USE [smart_reNTAL]
GO

-- التحقق من وجود العمود أولاً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[tblCustomers]') AND name = 'Email')
BEGIN
    -- إضافة عمود Email
    ALTER TABLE [dbo].[tblCustomers] ADD [Email] [nvarchar](100) NULL
    PRINT 'تم إضافة عمود Email بنجاح'
END
ELSE
BEGIN
    PRINT 'عمود Email موجود بالفعل'
END
GO

-- التحقق من وجود عمود CustomerType
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[tblCustomers]') AND name = 'CustomerType')
BEGIN
    -- إضافة عمود CustomerType
    ALTER TABLE [dbo].[tblCustomers] ADD [CustomerType] [nvarchar](50) NULL DEFAULT('عميل')
    PRINT 'تم إضافة عمود CustomerType بنجاح'
END
ELSE
BEGIN
    PRINT 'عمود CustomerType موجود بالفعل'
END
GO

-- تحديث العملاء الموجودين ليكونوا من نوع "مستأجر" إذا لم يكن لديهم نوع محدد
UPDATE [dbo].[tblCustomers] 
SET [CustomerType] = 'مستأجر' 
WHERE [CustomerType] IS NULL OR [CustomerType] = ''
GO

PRINT 'تم تحديث أنواع العملاء الموجودين'
GO
