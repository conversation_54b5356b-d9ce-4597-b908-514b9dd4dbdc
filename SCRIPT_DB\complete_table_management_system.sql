-- نظام فصل وضم الطاولات الكامل
-- تاريخ الإنشاء: 2025-07-05
-- الوصف: نظام شامل لإدارة فصل وضم الطاولات في المطاعم

USE [smart_reNTAL]
GO

PRINT 'بدء تطبيق نظام فصل وضم الطاولات...'

-- ===================================
-- 1. إنشاء الجداول الأساسية
-- ===================================

-- جدول مجموعات الطاولات (للضم)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableGroups_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableGroups_Tbl](
        [Group_ID] [int] IDENTITY(1,1) NOT NULL,
        [Group_Name] [nvarchar](100) NOT NULL,
        [Group_Status] [nvarchar](50) NOT NULL DEFAULT('نشط'),
        [Total_Amount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Created_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Created_By] [nvarchar](50) NOT NULL,
        [Closed_Date] [datetime] NULL,
        [Closed_By] [nvarchar](50) NULL,
        [Notes] [nvarchar](500) NULL,
        CONSTRAINT [PK_TableGroups_Tbl] PRIMARY KEY CLUSTERED ([Group_ID] ASC)
    )
    PRINT 'تم إنشاء جدول مجموعات الطاولات'
END
ELSE
    PRINT 'جدول مجموعات الطاولات موجود بالفعل'

-- جدول تفاصيل مجموعات الطاولات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableGroupDetails_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableGroupDetails_Tbl](
        [Detail_ID] [int] IDENTITY(1,1) NOT NULL,
        [Group_ID] [int] NOT NULL,
        [Table_Name] [nvarchar](100) NOT NULL,
        [Order_No] [nvarchar](50) NOT NULL,
        [Split_Amount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Added_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Added_By] [nvarchar](50) NOT NULL,
        [Is_Active] [bit] NOT NULL DEFAULT(1),
        CONSTRAINT [PK_TableGroupDetails_Tbl] PRIMARY KEY CLUSTERED ([Detail_ID] ASC),
        CONSTRAINT [FK_TableGroupDetails_Group] FOREIGN KEY([Group_ID]) REFERENCES [dbo].[TableGroups_Tbl] ([Group_ID])
    )
    PRINT 'تم إنشاء جدول تفاصيل مجموعات الطاولات'
END
ELSE
    PRINT 'جدول تفاصيل مجموعات الطاولات موجود بالفعل'

-- جدول عمليات فصل الطاولات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableSplitOperations_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableSplitOperations_Tbl](
        [Split_ID] [int] IDENTITY(1,1) NOT NULL,
        [Original_Table_Name] [nvarchar](100) NOT NULL,
        [Original_Order_No] [nvarchar](50) NOT NULL,
        [Split_Count] [int] NOT NULL,
        [Split_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Split_By] [nvarchar](50) NOT NULL,
        [Split_Reason] [nvarchar](500) NULL,
        [Original_Total] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Status] [nvarchar](50) NOT NULL DEFAULT('نشط'),
        CONSTRAINT [PK_TableSplitOperations_Tbl] PRIMARY KEY CLUSTERED ([Split_ID] ASC)
    )
    PRINT 'تم إنشاء جدول عمليات فصل الطاولات'
END
ELSE
    PRINT 'جدول عمليات فصل الطاولات موجود بالفعل'

-- جدول تفاصيل فصل الطاولات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableSplitDetails_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableSplitDetails_Tbl](
        [Detail_ID] [int] IDENTITY(1,1) NOT NULL,
        [Split_ID] [int] NOT NULL,
        [New_Table_Name] [nvarchar](100) NOT NULL,
        [New_Order_No] [nvarchar](50) NOT NULL,
        [Split_Amount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Split_Percentage] [decimal](5, 2) NOT NULL DEFAULT(0),
        [Customer_Name] [nvarchar](200) NULL,
        [Customer_Phone] [nvarchar](20) NULL,
        [Created_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Is_Active] [bit] NOT NULL DEFAULT(1),
        CONSTRAINT [PK_TableSplitDetails_Tbl] PRIMARY KEY CLUSTERED ([Detail_ID] ASC),
        CONSTRAINT [FK_TableSplitDetails_Split] FOREIGN KEY([Split_ID]) REFERENCES [dbo].[TableSplitOperations_Tbl] ([Split_ID])
    )
    PRINT 'تم إنشاء جدول تفاصيل فصل الطاولات'
END
ELSE
    PRINT 'جدول تفاصيل فصل الطاولات موجود بالفعل'

-- ===================================
-- 2. تحديث جدول الطلبات الموجود
-- ===================================

-- إضافة أعمدة جديدة لجدول Order_Tbl
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Group_ID')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Group_ID] [int] NULL
    PRINT 'تم إضافة عمود Group_ID لجدول الطلبات'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Split_ID')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Split_ID] [int] NULL
    PRINT 'تم إضافة عمود Split_ID لجدول الطلبات'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Original_Order_No')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Original_Order_No] [nvarchar](50) NULL
    PRINT 'تم إضافة عمود Original_Order_No لجدول الطلبات'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Customer_Name')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Customer_Name] [nvarchar](200) NULL
    PRINT 'تم إضافة عمود Customer_Name لجدول الطلبات'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Customer_Phone')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Customer_Phone] [nvarchar](20) NULL
    PRINT 'تم إضافة عمود Customer_Phone لجدول الطلبات'
END

-- ===================================
-- 3. إنشاء الفهارس
-- ===================================

-- فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'IX_Order_Tbl_Group_ID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Order_Tbl_Group_ID] ON [dbo].[Order_Tbl] ([Group_ID])
    PRINT 'تم إنشاء فهرس Group_ID'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'IX_Order_Tbl_Split_ID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Order_Tbl_Split_ID] ON [dbo].[Order_Tbl] ([Split_ID])
    PRINT 'تم إنشاء فهرس Split_ID'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'IX_Order_Tbl_Table_Status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Order_Tbl_Table_Status] ON [dbo].[Order_Tbl] ([Table_Name], [ord_Status])
    PRINT 'تم إنشاء فهرس Table_Name و ord_Status'
END

-- ===================================
-- 4. إنشاء العروض (Views)
-- ===================================

-- عرض الطاولات المجمعة
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[View_GroupedTables]'))
    DROP VIEW [dbo].[View_GroupedTables]
GO

CREATE VIEW [dbo].[View_GroupedTables]
AS
SELECT 
    tg.Group_ID,
    tg.Group_Name,
    tg.Group_Status,
    tg.Total_Amount,
    tg.Created_Date,
    tg.Created_By,
    COUNT(tgd.Detail_ID) as Tables_Count,
    STRING_AGG(tgd.Table_Name, ', ') as Tables_List,
    SUM(tgd.Split_Amount) as Calculated_Total
FROM [dbo].[TableGroups_Tbl] tg
LEFT JOIN [dbo].[TableGroupDetails_Tbl] tgd ON tg.Group_ID = tgd.Group_ID AND tgd.Is_Active = 1
GROUP BY tg.Group_ID, tg.Group_Name, tg.Group_Status, tg.Total_Amount, tg.Created_Date, tg.Created_By
GO

PRINT 'تم إنشاء عرض الطاولات المجمعة'

-- عرض الطاولات المفصولة
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[View_SplitTables]'))
    DROP VIEW [dbo].[View_SplitTables]
GO

CREATE VIEW [dbo].[View_SplitTables]
AS
SELECT 
    tso.Split_ID,
    tso.Original_Table_Name,
    tso.Original_Order_No,
    tso.Split_Count,
    tso.Split_Date,
    tso.Split_By,
    tso.Original_Total,
    tso.Status,
    COUNT(tsd.Detail_ID) as Created_Parts,
    SUM(tsd.Split_Amount) as Total_Split_Amount
FROM [dbo].[TableSplitOperations_Tbl] tso
LEFT JOIN [dbo].[TableSplitDetails_Tbl] tsd ON tso.Split_ID = tsd.Split_ID AND tsd.Is_Active = 1
GROUP BY tso.Split_ID, tso.Original_Table_Name, tso.Original_Order_No, tso.Split_Count, 
         tso.Split_Date, tso.Split_By, tso.Original_Total, tso.Status
GO

PRINT 'تم إنشاء عرض الطاولات المفصولة'

PRINT 'تم الانتهاء من إنشاء نظام فصل وضم الطاولات بنجاح!'
PRINT 'يمكنك الآن استخدام الإجراءات المخزنة والشاشات الجديدة'

-- ===================================
-- 5. بيانات تجريبية (اختيارية)
-- ===================================

-- يمكن إضافة بيانات تجريبية هنا إذا لزم الأمر

GO
