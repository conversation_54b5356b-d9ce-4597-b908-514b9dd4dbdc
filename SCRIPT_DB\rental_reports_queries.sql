-- ===================================
-- استعلامات التقارير لنظام إدارة الإيجارات
-- ===================================

-- 1. تقرير الوحدات الفارغة
SELECT 
    c.CatName as 'اسم العمارة',
    i.ItemName as 'اسم الوحدة',
    i.ItemCode as 'رمز الوحدة',
    i.FloorNumber as 'رقم الطابق',
    i.RoomsCount as 'عدد الغرف',
    i.Area as 'المساحة',
    i.MonthlyRent as 'الإيجار الشهري',
    DATEDIFF(day, ISNULL(i.LastRentDate, i.CreatedDate), GETDATE()) as 'أيام الفراغ'
FROM Item_Tbl i
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
WHERE i.UnitStatus = 'فارغ'
ORDER BY c.CatName, i.FloorNumber, i.ItemName
GO

-- 2. تقرير الإيجارات المتأخرة
SELECT 
    rc.ContractNumber as 'رقم العقد',
    cust.CustomerName as 'اسم المستأجر',
    cust.Mobile as 'رقم الجوال',
    c.CatName as 'العمارة',
    i.ItemName as 'الوحدة',
    rp.DueDate as 'تاريخ الاستحقاق',
    rp.AmountDue as 'المبلغ المستحق',
    rp.AmountPaid as 'المبلغ المدفوع',
    (rp.AmountDue - rp.AmountPaid) as 'المبلغ المتبقي',
    DATEDIFF(day, rp.DueDate, GETDATE()) as 'أيام التأخير'
FROM RentPayments_Tbl rp
INNER JOIN RentalContracts_Tbl rc ON rp.Contract_ID = rc.Contract_ID
INNER JOIN Item_Tbl i ON rc.Unit_ID = i.Item_ID
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
INNER JOIN tblCustomers cust ON rc.Tenant_ID = cust.CustomerID
WHERE rp.PaymentStatus IN ('متأخر', 'جزئي') 
   AND rp.DueDate < GETDATE()
   AND (rp.AmountDue - rp.AmountPaid) > 0
ORDER BY DATEDIFF(day, rp.DueDate, GETDATE()) DESC
GO

-- 3. تقرير الإيرادات الشهرية
SELECT 
    YEAR(rp.PaymentDate) as 'السنة',
    MONTH(rp.PaymentDate) as 'الشهر',
    DATENAME(month, rp.PaymentDate) as 'اسم الشهر',
    COUNT(*) as 'عدد الدفعات',
    SUM(rp.AmountPaid) as 'إجمالي الإيرادات',
    AVG(rp.AmountPaid) as 'متوسط الدفعة'
FROM RentPayments_Tbl rp
WHERE rp.PaymentStatus = 'مدفوع'
   AND rp.PaymentDate IS NOT NULL
GROUP BY YEAR(rp.PaymentDate), MONTH(rp.PaymentDate), DATENAME(month, rp.PaymentDate)
ORDER BY YEAR(rp.PaymentDate) DESC, MONTH(rp.PaymentDate) DESC
GO

-- 4. تقرير المصروفات حسب النوع
SELECT 
    et.ExpenseTypeName as 'نوع المصروف',
    COUNT(*) as 'عدد المصروفات',
    SUM(e.ExpenseAmount) as 'إجمالي المبلغ',
    AVG(e.ExpenseAmount) as 'متوسط المصروف',
    MIN(e.ExpenseAmount) as 'أقل مصروف',
    MAX(e.ExpenseAmount) as 'أعلى مصروف'
FROM Expenses_Tbl e
INNER JOIN ExpenseTypes_Tbl et ON e.ExpenseType_ID = et.ExpenseType_ID
WHERE e.IsApproved = 1
GROUP BY et.ExpenseTypeName
ORDER BY SUM(e.ExpenseAmount) DESC
GO

-- 5. تقرير العقود المنتهية قريباً (خلال 30 يوم)
SELECT 
    rc.ContractNumber as 'رقم العقد',
    cust.CustomerName as 'اسم المستأجر',
    cust.Mobile as 'رقم الجوال',
    c.CatName as 'العمارة',
    i.ItemName as 'الوحدة',
    rc.StartDate as 'تاريخ البداية',
    rc.EndDate as 'تاريخ الانتهاء',
    DATEDIFF(day, GETDATE(), rc.EndDate) as 'أيام متبقية',
    rc.MonthlyRent as 'الإيجار الشهري'
FROM RentalContracts_Tbl rc
INNER JOIN Item_Tbl i ON rc.Unit_ID = i.Item_ID
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
INNER JOIN tblCustomers cust ON rc.Tenant_ID = cust.CustomerID
WHERE rc.ContractStatus = 'نشط'
   AND rc.EndDate BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
ORDER BY rc.EndDate ASC
GO

-- 6. تقرير أداء العمارات
SELECT 
    c.CatName as 'اسم العمارة',
    c.UnitsCount as 'إجمالي الوحدات',
    COUNT(CASE WHEN i.UnitStatus = 'مؤجر' THEN 1 END) as 'الوحدات المؤجرة',
    COUNT(CASE WHEN i.UnitStatus = 'فارغ' THEN 1 END) as 'الوحدات الفارغة',
    COUNT(CASE WHEN i.UnitStatus = 'صيانة' THEN 1 END) as 'وحدات تحت الصيانة',
    CAST(COUNT(CASE WHEN i.UnitStatus = 'مؤجر' THEN 1 END) * 100.0 / c.UnitsCount as DECIMAL(5,2)) as 'نسبة الإشغال %',
    SUM(CASE WHEN i.UnitStatus = 'مؤجر' THEN i.MonthlyRent ELSE 0 END) as 'الإيراد الشهري الحالي',
    SUM(i.MonthlyRent) as 'الإيراد الشهري المحتمل'
FROM Cat_Tbl c
LEFT JOIN Item_Tbl i ON c.Cat_ID = i.Cat_ID
GROUP BY c.Cat_ID, c.CatName, c.UnitsCount
ORDER BY [نسبة الإشغال %] DESC
GO

-- 7. تقرير المستأجرين وعقودهم
SELECT 
    cust.CustomerName as 'اسم المستأجر',
    cust.Mobile as 'رقم الجوال',
    cust.Email as 'البريد الإلكتروني',
    cust.NationalID as 'رقم الهوية',
    COUNT(rc.Contract_ID) as 'عدد العقود',
    SUM(CASE WHEN rc.ContractStatus = 'نشط' THEN 1 ELSE 0 END) as 'العقود النشطة',
    SUM(CASE WHEN rc.ContractStatus = 'نشط' THEN rc.MonthlyRent ELSE 0 END) as 'إجمالي الإيجار الشهري'
FROM tblCustomers cust
LEFT JOIN RentalContracts_Tbl rc ON cust.CustomerID = rc.Tenant_ID
WHERE cust.CustomerType = 'مستأجر'
GROUP BY cust.CustomerID, cust.CustomerName, cust.Mobile, cust.Email, cust.NationalID
ORDER BY [إجمالي الإيجار الشهري] DESC
GO

-- 8. تقرير الصيانة والأعطال
SELECT 
    c.CatName as 'العمارة',
    i.ItemName as 'الوحدة',
    m.MaintenanceType as 'نوع الصيانة',
    m.Description as 'الوصف',
    m.RequestDate as 'تاريخ الطلب',
    m.CompletionDate as 'تاريخ الإنجاز',
    m.Cost as 'التكلفة',
    m.Status as 'الحالة',
    CASE 
        WHEN m.CompletionDate IS NOT NULL THEN DATEDIFF(day, m.RequestDate, m.CompletionDate)
        ELSE DATEDIFF(day, m.RequestDate, GETDATE())
    END as 'أيام الصيانة'
FROM Maintenance_Tbl m
INNER JOIN Item_Tbl i ON m.Unit_ID = i.Item_ID
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
ORDER BY m.RequestDate DESC
GO

-- 9. تقرير التدفق النقدي الشهري
SELECT 
    YEAR(TransactionDate) as 'السنة',
    MONTH(TransactionDate) as 'الشهر',
    DATENAME(month, TransactionDate) as 'اسم الشهر',
    SUM(CASE WHEN TransactionType = 'إيراد' THEN Amount ELSE 0 END) as 'إجمالي الإيرادات',
    SUM(CASE WHEN TransactionType = 'مصروف' THEN Amount ELSE 0 END) as 'إجمالي المصروفات',
    SUM(CASE WHEN TransactionType = 'إيراد' THEN Amount ELSE -Amount END) as 'صافي التدفق النقدي'
FROM (
    -- الإيرادات من الإيجارات
    SELECT PaymentDate as TransactionDate, AmountPaid as Amount, 'إيراد' as TransactionType
    FROM RentPayments_Tbl 
    WHERE PaymentStatus = 'مدفوع' AND PaymentDate IS NOT NULL
    
    UNION ALL
    
    -- المصروفات
    SELECT ExpenseDate as TransactionDate, ExpenseAmount as Amount, 'مصروف' as TransactionType
    FROM Expenses_Tbl 
    WHERE IsApproved = 1
) CashFlow
GROUP BY YEAR(TransactionDate), MONTH(TransactionDate), DATENAME(month, TransactionDate)
ORDER BY YEAR(TransactionDate) DESC, MONTH(TransactionDate) DESC
GO

-- 10. تقرير أعلى المستأجرين دفعاً
SELECT TOP 10
    cust.CustomerName as 'اسم المستأجر',
    cust.Mobile as 'رقم الجوال',
    COUNT(rp.Payment_ID) as 'عدد الدفعات',
    SUM(rp.AmountPaid) as 'إجمالي المدفوعات',
    AVG(rp.AmountPaid) as 'متوسط الدفعة',
    MAX(rp.PaymentDate) as 'آخر دفعة'
FROM tblCustomers cust
INNER JOIN RentalContracts_Tbl rc ON cust.CustomerID = rc.Tenant_ID
INNER JOIN RentPayments_Tbl rp ON rc.Contract_ID = rp.Contract_ID
WHERE rp.PaymentStatus = 'مدفوع'
GROUP BY cust.CustomerID, cust.CustomerName, cust.Mobile
ORDER BY SUM(rp.AmountPaid) DESC
GO

-- 11. تقرير الوحدات الأكثر ربحية
SELECT 
    c.CatName as 'العمارة',
    i.ItemName as 'الوحدة',
    i.MonthlyRent as 'الإيجار الشهري',
    COUNT(rc.Contract_ID) as 'عدد العقود',
    SUM(rp.AmountPaid) as 'إجمالي الإيرادات',
    SUM(CASE WHEN e.Unit_ID = i.Item_ID THEN e.ExpenseAmount ELSE 0 END) as 'إجمالي المصروفات',
    (SUM(rp.AmountPaid) - SUM(CASE WHEN e.Unit_ID = i.Item_ID THEN e.ExpenseAmount ELSE 0 END)) as 'صافي الربح'
FROM Item_Tbl i
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
LEFT JOIN RentalContracts_Tbl rc ON i.Item_ID = rc.Unit_ID
LEFT JOIN RentPayments_Tbl rp ON rc.Contract_ID = rp.Contract_ID AND rp.PaymentStatus = 'مدفوع'
LEFT JOIN Expenses_Tbl e ON i.Item_ID = e.Unit_ID AND e.IsApproved = 1
GROUP BY i.Item_ID, c.CatName, i.ItemName, i.MonthlyRent
HAVING SUM(rp.AmountPaid) > 0
ORDER BY [صافي الربح] DESC
GO

-- 12. تقرير معدل دوران الوحدات
SELECT 
    c.CatName as 'العمارة',
    i.ItemName as 'الوحدة',
    COUNT(rc.Contract_ID) as 'عدد العقود الإجمالي',
    COUNT(CASE WHEN rc.ContractStatus = 'منتهي' THEN 1 END) as 'العقود المنتهية',
    COUNT(CASE WHEN rc.ContractStatus = 'نشط' THEN 1 END) as 'العقود النشطة',
    AVG(DATEDIFF(day, rc.StartDate, ISNULL(rc.EndDate, GETDATE()))) as 'متوسط مدة العقد بالأيام'
FROM Item_Tbl i
INNER JOIN Cat_Tbl c ON i.Cat_ID = c.Cat_ID
LEFT JOIN RentalContracts_Tbl rc ON i.Item_ID = rc.Unit_ID
GROUP BY i.Item_ID, c.CatName, i.ItemName
ORDER BY COUNT(rc.Contract_ID) DESC
GO
