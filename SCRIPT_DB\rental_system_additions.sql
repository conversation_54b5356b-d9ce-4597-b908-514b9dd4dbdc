-- إضافات نظام إدارة الإيجارات
-- تاريخ الإنشاء: 2025-06-29
-- الوصف: جداول إضافية لتحويل نظام المطعم إلى نظام إدارة وحدات إيجار

USE [smart_reNTAL]
GO

-- ===================================
-- 1. جدول أنواع المصروفات
-- ===================================
CREATE TABLE [dbo].[ExpenseTypes_Tbl](
    [ExpenseType_ID] [int] IDENTITY(1,1) NOT NULL,
    [ExpenseTypeName] [nvarchar](150) NOT NULL,
    [ExpenseTypeDescription] [nvarchar](500) NULL,
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_ExpenseTypes_Tbl] PRIMARY KEY CLUSTERED ([ExpenseType_ID] ASC)
)
GO

-- ===================================
-- 2. جدول المصروفات
-- ===================================
CREATE TABLE [dbo].[Expenses_Tbl](
    [Expense_ID] [int] IDENTITY(1,1) NOT NULL,
    [ExpenseType_ID] [int] NOT NULL,
    [ExpenseDescription] [nvarchar](500) NOT NULL,
    [ExpenseAmount] [decimal](18, 2) NOT NULL,
    [ExpenseDate] [date] NOT NULL,
    [Building_ID] [int] NULL, -- ربط بالعمارة (Cat_ID)
    [Unit_ID] [int] NULL, -- ربط بالوحدة (Item_ID)
    [PaymentMethod] [nvarchar](50) NOT NULL, -- نقدي، شيك، تحويل بنكي
    [ReceiptNumber] [nvarchar](100) NULL,
    [Notes] [nvarchar](1000) NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    [IsApproved] [bit] NOT NULL DEFAULT(0),
    [ApprovedBy] [nvarchar](50) NULL,
    [ApprovedDate] [datetime] NULL,
    CONSTRAINT [PK_Expenses_Tbl] PRIMARY KEY CLUSTERED ([Expense_ID] ASC),
    CONSTRAINT [FK_Expenses_ExpenseTypes] FOREIGN KEY([ExpenseType_ID]) REFERENCES [dbo].[ExpenseTypes_Tbl] ([ExpenseType_ID]),
    CONSTRAINT [FK_Expenses_Buildings] FOREIGN KEY([Building_ID]) REFERENCES [dbo].[Cat_Tbl] ([Cat_ID]),
    CONSTRAINT [FK_Expenses_Units] FOREIGN KEY([Unit_ID]) REFERENCES [dbo].[Item_Tbl] ([Item_ID])
)
GO

-- ===================================
-- 3. جدول عقود الإيجار
-- ===================================
CREATE TABLE [dbo].[RentalContracts_Tbl](
    [Contract_ID] [int] IDENTITY(1,1) NOT NULL,
    [ContractNumber] [nvarchar](50) NOT NULL UNIQUE,
    [Unit_ID] [int] NOT NULL,
    [Tenant_ID] [int] NOT NULL, -- ربط بجدول العملاء
    [StartDate] [date] NOT NULL,
    [EndDate] [date] NOT NULL,
    [MonthlyRent] [decimal](18, 2) NOT NULL,
    [SecurityDeposit] [decimal](18, 2) NOT NULL,
    [ContractStatus] [nvarchar](50) NOT NULL DEFAULT('نشط'), -- نشط، منتهي، ملغي
    [PaymentDueDay] [int] NOT NULL DEFAULT(1), -- يوم استحقاق الدفع من كل شهر
    [ContractTerms] [nvarchar](2000) NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    [LastModifiedBy] [nvarchar](50) NULL,
    [LastModifiedDate] [datetime] NULL,
    CONSTRAINT [PK_RentalContracts_Tbl] PRIMARY KEY CLUSTERED ([Contract_ID] ASC),
    CONSTRAINT [FK_RentalContracts_Units] FOREIGN KEY([Unit_ID]) REFERENCES [dbo].[Item_Tbl] ([Item_ID]),
    CONSTRAINT [FK_RentalContracts_Tenants] FOREIGN KEY([Tenant_ID]) REFERENCES [dbo].[tblCustomers] ([CustomerID])
)
GO

-- ===================================
-- 4. جدول دفعات الإيجار
-- ===================================
CREATE TABLE [dbo].[RentPayments_Tbl](
    [Payment_ID] [int] IDENTITY(1,1) NOT NULL,
    [Contract_ID] [int] NOT NULL,
    [PaymentDate] [date] NOT NULL,
    [DueDate] [date] NOT NULL,
    [AmountDue] [decimal](18, 2) NOT NULL,
    [AmountPaid] [decimal](18, 2) NOT NULL,
    [PaymentMethod] [nvarchar](50) NOT NULL,
    [PaymentStatus] [nvarchar](50) NOT NULL DEFAULT('مدفوع'), -- مدفوع، متأخر، جزئي
    [ReceiptNumber] [nvarchar](100) NULL,
    [Notes] [nvarchar](500) NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_RentPayments_Tbl] PRIMARY KEY CLUSTERED ([Payment_ID] ASC),
    CONSTRAINT [FK_RentPayments_Contracts] FOREIGN KEY([Contract_ID]) REFERENCES [dbo].[RentalContracts_Tbl] ([Contract_ID])
)
GO

-- ===================================
-- 5. جدول صيانة الوحدات
-- ===================================
CREATE TABLE [dbo].[Maintenance_Tbl](
    [Maintenance_ID] [int] IDENTITY(1,1) NOT NULL,
    [Unit_ID] [int] NOT NULL,
    [MaintenanceType] [nvarchar](100) NOT NULL, -- صيانة دورية، إصلاح، تجديد
    [Description] [nvarchar](1000) NOT NULL,
    [MaintenanceDate] [date] NOT NULL,
    [Cost] [decimal](18, 2) NOT NULL,
    [Contractor] [nvarchar](200) NULL,
    [Status] [nvarchar](50) NOT NULL DEFAULT('مكتمل'), -- مجدول، قيد التنفيذ، مكتمل
    [Notes] [nvarchar](500) NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_Maintenance_Tbl] PRIMARY KEY CLUSTERED ([Maintenance_ID] ASC),
    CONSTRAINT [FK_Maintenance_Units] FOREIGN KEY([Unit_ID]) REFERENCES [dbo].[Item_Tbl] ([Item_ID])
)
GO

-- ===================================
-- 6. جدول الحسابات المالية
-- ===================================
CREATE TABLE [dbo].[Accounts_Tbl](
    [Account_ID] [int] IDENTITY(1,1) NOT NULL,
    [AccountCode] [nvarchar](20) NOT NULL UNIQUE,
    [AccountName] [nvarchar](200) NOT NULL,
    [AccountType] [nvarchar](50) NOT NULL, -- أصول، خصوم، حقوق ملكية، إيرادات، مصروفات
    [ParentAccount_ID] [int] NULL,
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_Accounts_Tbl] PRIMARY KEY CLUSTERED ([Account_ID] ASC),
    CONSTRAINT [FK_Accounts_Parent] FOREIGN KEY([ParentAccount_ID]) REFERENCES [dbo].[Accounts_Tbl] ([Account_ID])
)
GO

-- ===================================
-- 7. جدول القيود المحاسبية
-- ===================================
CREATE TABLE [dbo].[JournalEntries_Tbl](
    [Entry_ID] [int] IDENTITY(1,1) NOT NULL,
    [EntryNumber] [nvarchar](50) NOT NULL UNIQUE,
    [EntryDate] [date] NOT NULL,
    [Description] [nvarchar](500) NOT NULL,
    [Reference] [nvarchar](100) NULL, -- مرجع العملية (رقم الفاتورة، رقم العقد، إلخ)
    [TotalDebit] [decimal](18, 2) NOT NULL,
    [TotalCredit] [decimal](18, 2) NOT NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    [IsPosted] [bit] NOT NULL DEFAULT(0),
    CONSTRAINT [PK_JournalEntries_Tbl] PRIMARY KEY CLUSTERED ([Entry_ID] ASC)
)
GO

-- ===================================
-- 9. إضافة حقول جديدة لجدول العمارات (Cat_Tbl)
-- ===================================
-- إضافة حقول للعمارات
ALTER TABLE [dbo].[Cat_Tbl] ADD
    [BuildingAddress] [nvarchar](500) NULL,
    [FloorsCount] [int] NULL DEFAULT(1),
    [UnitsCount] [int] NULL DEFAULT(1),
    [ConstructionDate] [date] NULL,
    [BuildingStatus] [nvarchar](50) NULL DEFAULT('نشط'),
    [OwnerName] [nvarchar](200) NULL,
    [OwnerPhone] [nvarchar](20) NULL,
    [Notes] [nvarchar](1000) NULL
GO

-- ===================================
-- 10. إضافة حقول جديدة لجدول الوحدات (Item_Tbl)
-- ===================================
-- إضافة حقول للوحدات
ALTER TABLE [dbo].[Item_Tbl] ADD
    [FloorNumber] [int] NULL DEFAULT(1),
    [RoomsCount] [int] NULL DEFAULT(1),
    [BathroomsCount] [int] NULL DEFAULT(1),
    [Area] [decimal](10, 2) NULL,
    [UnitStatus] [nvarchar](50) NULL DEFAULT('فارغ'), -- فارغ، مؤجر، صيانة
    [LastMaintenanceDate] [date] NULL,
    [MonthlyRent] [decimal](18, 2) NULL,
    [SecurityDeposit] [decimal](18, 2) NULL,
    [UnitFeatures] [nvarchar](1000) NULL, -- مكيف، مفروش، إلخ
    [UnitNotes] [nvarchar](1000) NULL
GO

-- ===================================
-- 11. إضافة حقول جديدة لجدول العملاء (المستأجرين)
-- ===================================
-- إضافة حقول للمستأجرين
ALTER TABLE [dbo].[tblCustomers] ADD
    [NationalID] [nvarchar](20) NULL,
    [Occupation] [nvarchar](100) NULL,
    [EmergencyContact] [nvarchar](100) NULL,
    [EmergencyPhone] [nvarchar](20) NULL,
    [TenantType] [nvarchar](50) NULL DEFAULT('فرد'), -- فرد، عائلة، شركة
    [ContractStartDate] [date] NULL,
    [ContractEndDate] [date] NULL,
    [IsActive] [bit] NULL DEFAULT(1)
GO

-- ===================================
-- 12. Views للتقارير والاستعلامات
-- ===================================

-- فيو لعرض معلومات العمارات مع عدد الوحدات
CREATE VIEW [dbo].[View_Buildings] AS
SELECT
    c.Cat_ID as BuildingID,
    c.CatName as BuildingName,
    c.BuildingAddress,
    c.FloorsCount,
    c.UnitsCount,
    c.ConstructionDate,
    c.BuildingStatus,
    c.OwnerName,
    c.OwnerPhone,
    COUNT(i.Item_ID) as ActualUnitsCount,
    SUM(CASE WHEN i.UnitStatus = 'مؤجر' THEN 1 ELSE 0 END) as RentedUnits,
    SUM(CASE WHEN i.UnitStatus = 'فارغ' THEN 1 ELSE 0 END) as VacantUnits,
    SUM(CASE WHEN i.UnitStatus = 'صيانة' THEN 1 ELSE 0 END) as MaintenanceUnits
FROM [dbo].[Cat_Tbl] c
LEFT JOIN [dbo].[Item_Tbl] i ON c.Cat_ID = i.Cat_ID
GROUP BY c.Cat_ID, c.CatName, c.BuildingAddress, c.FloorsCount, c.UnitsCount,
         c.ConstructionDate, c.BuildingStatus, c.OwnerName, c.OwnerPhone
GO

-- فيو لعرض معلومات الوحدات مع العمارة
CREATE VIEW [dbo].[View_Units] AS
SELECT
    i.Item_ID as UnitID,
    i.ItemName as UnitName,
    i.Itembarcode as UnitCode,
    c.CatName as BuildingName,
    i.FloorNumber,
    i.RoomsCount,
    i.BathroomsCount,
    i.Area,
    i.UnitStatus,
    i.MonthlyRent,
    i.SecurityDeposit,
    i.LastMaintenanceDate,
    i.UnitFeatures,
    i.Item_Status as IsActive
FROM [dbo].[Item_Tbl] i
INNER JOIN [dbo].[Cat_Tbl] c ON i.Cat_ID = c.Cat_ID
GO

-- فيو لعرض العقود النشطة
CREATE VIEW [dbo].[View_ActiveContracts] AS
SELECT
    rc.Contract_ID,
    rc.ContractNumber,
    i.ItemName as UnitName,
    c.CatName as BuildingName,
    cust.CustomerName as TenantName,
    cust.Mobile as TenantPhone,
    rc.StartDate,
    rc.EndDate,
    rc.MonthlyRent,
    rc.SecurityDeposit,
    rc.ContractStatus,
    DATEDIFF(day, GETDATE(), rc.EndDate) as DaysToExpiry
FROM [dbo].[RentalContracts_Tbl] rc
INNER JOIN [dbo].[Item_Tbl] i ON rc.Unit_ID = i.Item_ID
INNER JOIN [dbo].[Cat_Tbl] c ON i.Cat_ID = c.Cat_ID
INNER JOIN [dbo].[tblCustomers] cust ON rc.Tenant_ID = cust.CustomerID
WHERE rc.ContractStatus = 'نشط'
GO

-- فيو للمدفوعات المتأخرة
CREATE VIEW [dbo].[View_OverduePayments] AS
SELECT
    rp.Payment_ID,
    rc.ContractNumber,
    i.ItemName as UnitName,
    c.CatName as BuildingName,
    cust.CustomerName as TenantName,
    cust.Mobile as TenantPhone,
    rp.DueDate,
    rp.AmountDue,
    rp.AmountPaid,
    (rp.AmountDue - rp.AmountPaid) as RemainingAmount,
    DATEDIFF(day, rp.DueDate, GETDATE()) as DaysOverdue
FROM [dbo].[RentPayments_Tbl] rp
INNER JOIN [dbo].[RentalContracts_Tbl] rc ON rp.Contract_ID = rc.Contract_ID
INNER JOIN [dbo].[Item_Tbl] i ON rc.Unit_ID = i.Item_ID
INNER JOIN [dbo].[Cat_Tbl] c ON i.Cat_ID = c.Cat_ID
INNER JOIN [dbo].[tblCustomers] cust ON rc.Tenant_ID = cust.CustomerID
WHERE rp.PaymentStatus IN ('متأخر', 'جزئي')
   AND rp.DueDate < GETDATE()
   AND (rp.AmountDue - rp.AmountPaid) > 0
GO

-- فيو للمصروفات مع التفاصيل
CREATE VIEW [dbo].[View_Expenses] AS
SELECT
    e.Expense_ID,
    et.ExpenseTypeName,
    e.ExpenseDescription,
    e.ExpenseAmount,
    e.ExpenseDate,
    c.CatName as BuildingName,
    i.ItemName as UnitName,
    e.PaymentMethod,
    e.ReceiptNumber,
    e.CreatedBy,
    e.IsApproved,
    e.ApprovedBy
FROM [dbo].[Expenses_Tbl] e
INNER JOIN [dbo].[ExpenseTypes_Tbl] et ON e.ExpenseType_ID = et.ExpenseType_ID
LEFT JOIN [dbo].[Cat_Tbl] c ON e.Building_ID = c.Cat_ID
LEFT JOIN [dbo].[Item_Tbl] i ON e.Unit_ID = i.Item_ID
GO

-- ===================================
-- 13. إدراج البيانات الأساسية
-- ===================================

-- إدراج أنواع المصروفات الأساسية
INSERT INTO [dbo].[ExpenseTypes_Tbl] (ExpenseTypeName, ExpenseTypeDescription) VALUES
('صيانة عامة', 'مصروفات الصيانة العامة للمباني والوحدات'),
('كهرباء', 'فواتير الكهرباء للمناطق المشتركة'),
('مياه', 'فواتير المياه للمناطق المشتركة'),
('أمن وحراسة', 'مصروفات الأمن والحراسة'),
('نظافة', 'مصروفات النظافة والتنظيف'),
('مصاعد', 'صيانة وتشغيل المصاعد'),
('حديقة', 'صيانة الحدائق والمناطق الخضراء'),
('إدارية', 'المصروفات الإدارية والمكتبية'),
('تأمين', 'أقساط التأمين على المباني'),
('ضرائب', 'الضرائب والرسوم الحكومية'),
('إصلاحات طارئة', 'الإصلاحات الطارئة والعاجلة'),
('تجديدات', 'أعمال التجديد والتطوير')
GO

-- إدراج الحسابات المحاسبية الأساسية
INSERT INTO [dbo].[Accounts_Tbl] (AccountCode, AccountName, AccountType) VALUES
-- الأصول
('1000', 'الأصول', 'أصول'),
('1100', 'الأصول المتداولة', 'أصول'),
('1110', 'النقدية', 'أصول'),
('1120', 'البنوك', 'أصول'),
('1130', 'العملاء', 'أصول'),
('1200', 'الأصول الثابتة', 'أصول'),
('1210', 'العقارات', 'أصول'),
('1220', 'المعدات', 'أصول'),

-- الخصوم
('2000', 'الخصوم', 'خصوم'),
('2100', 'الخصوم المتداولة', 'خصوم'),
('2110', 'الموردين', 'خصوم'),
('2120', 'مصروفات مستحقة', 'خصوم'),
('2130', 'تأمينات مستلمة', 'خصوم'),

-- حقوق الملكية
('3000', 'حقوق الملكية', 'حقوق ملكية'),
('3100', 'رأس المال', 'حقوق ملكية'),
('3200', 'الأرباح المحتجزة', 'حقوق ملكية'),

-- الإيرادات
('4000', 'الإيرادات', 'إيرادات'),
('4100', 'إيرادات الإيجارات', 'إيرادات'),
('4200', 'إيرادات أخرى', 'إيرادات'),

-- المصروفات
('5000', 'المصروفات', 'مصروفات'),
('5100', 'مصروفات التشغيل', 'مصروفات'),
('5110', 'مصروفات الصيانة', 'مصروفات'),
('5120', 'مصروفات الكهرباء', 'مصروفات'),
('5130', 'مصروفات المياه', 'مصروفات'),
('5140', 'مصروفات الأمن', 'مصروفات'),
('5150', 'مصروفات النظافة', 'مصروفات'),
('5200', 'المصروفات الإدارية', 'مصروفات'),
('5300', 'مصروفات أخرى', 'مصروفات')
GO

-- تحديث العلاقات الهرمية للحسابات
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '1000') WHERE AccountCode = '1100'
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '1000') WHERE AccountCode = '1200'
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '1100') WHERE AccountCode IN ('1110', '1120', '1130')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '1200') WHERE AccountCode IN ('1210', '1220')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '2000') WHERE AccountCode = '2100'
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '2100') WHERE AccountCode IN ('2110', '2120', '2130')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '3000') WHERE AccountCode IN ('3100', '3200')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '4000') WHERE AccountCode IN ('4100', '4200')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '5000') WHERE AccountCode IN ('5100', '5200', '5300')
UPDATE [dbo].[Accounts_Tbl] SET ParentAccount_ID = (SELECT Account_ID FROM [dbo].[Accounts_Tbl] WHERE AccountCode = '5100') WHERE AccountCode IN ('5110', '5120', '5130', '5140', '5150')
GO

-- ===================================
-- 14. إجراءات مخزنة للعمليات الشائعة
-- ===================================

-- إجراء لحساب إجمالي الإيجارات الشهرية
CREATE PROCEDURE [dbo].[SP_GetMonthlyRentTotal]
    @Year INT,
    @Month INT
AS
BEGIN
    SELECT
        SUM(rp.AmountDue) as TotalRentDue,
        SUM(rp.AmountPaid) as TotalRentPaid,
        SUM(rp.AmountDue - rp.AmountPaid) as TotalRentRemaining
    FROM [dbo].[RentPayments_Tbl] rp
    WHERE YEAR(rp.DueDate) = @Year AND MONTH(rp.DueDate) = @Month
END
GO

-- إجراء لحساب إجمالي المصروفات الشهرية
CREATE PROCEDURE [dbo].[SP_GetMonthlyExpensesTotal]
    @Year INT,
    @Month INT
AS
BEGIN
    SELECT
        et.ExpenseTypeName,
        SUM(e.ExpenseAmount) as TotalAmount,
        COUNT(*) as ExpenseCount
    FROM [dbo].[Expenses_Tbl] e
    INNER JOIN [dbo].[ExpenseTypes_Tbl] et ON e.ExpenseType_ID = et.ExpenseType_ID
    WHERE YEAR(e.ExpenseDate) = @Year AND MONTH(e.ExpenseDate) = @Month
    GROUP BY et.ExpenseTypeName
    ORDER BY TotalAmount DESC
END
GO

-- إجراء لتحديث حالة الوحدة
CREATE PROCEDURE [dbo].[SP_UpdateUnitStatus]
    @Unit_ID INT,
    @NewStatus NVARCHAR(50)
AS
BEGIN
    UPDATE [dbo].[Item_Tbl]
    SET UnitStatus = @NewStatus
    WHERE Item_ID = @Unit_ID
END
GO

-- إجراء لإنشاء دفعات الإيجار التلقائية
CREATE PROCEDURE [dbo].[SP_GenerateRentPayments]
    @Contract_ID INT,
    @StartDate DATE,
    @EndDate DATE
AS
BEGIN
    DECLARE @CurrentDate DATE = @StartDate
    DECLARE @MonthlyRent DECIMAL(18,2)

    -- الحصول على قيمة الإيجار الشهري
    SELECT @MonthlyRent = MonthlyRent
    FROM [dbo].[RentalContracts_Tbl]
    WHERE Contract_ID = @Contract_ID

    -- إنشاء دفعات شهرية
    WHILE @CurrentDate <= @EndDate
    BEGIN
        INSERT INTO [dbo].[RentPayments_Tbl] (Contract_ID, DueDate, AmountDue, PaymentStatus)
        VALUES (@Contract_ID, @CurrentDate, @MonthlyRent, 'مستحق')

        SET @CurrentDate = DATEADD(MONTH, 1, @CurrentDate)
    END
END
GO

-- ===================================
-- 15. مؤشرات لتحسين الأداء
-- ===================================

-- مؤشر على جدول المصروفات
CREATE INDEX IX_Expenses_Date_Type ON [dbo].[Expenses_Tbl] (ExpenseDate, ExpenseType_ID)
GO

-- مؤشر على جدول دفعات الإيجار
CREATE INDEX IX_RentPayments_DueDate_Status ON [dbo].[RentPayments_Tbl] (DueDate, PaymentStatus)
GO

-- مؤشر على جدول العقود
CREATE INDEX IX_RentalContracts_Status_Dates ON [dbo].[RentalContracts_Tbl] (ContractStatus, StartDate, EndDate)
GO

-- ===================================
-- 16. بيانات تجريبية للاختبار
-- ===================================

-- إضافة عمارة تجريبية
INSERT INTO [dbo].[Cat_Tbl] (CatName, BuildingAddress, FloorsCount, UnitsCount, ConstructionDate, BuildingStatus)
VALUES ('عمارة النخيل', 'شارع الملك فهد، الرياض', 5, 20, '2020-01-01', 'نشط')
GO

-- إضافة وحدات تجريبية
DECLARE @BuildingID INT = (SELECT TOP 1 Cat_ID FROM [dbo].[Cat_Tbl] WHERE CatName = 'عمارة النخيل')

INSERT INTO [dbo].[Item_Tbl] (ItemName, ItemCode, Cat_ID, FloorNumber, RoomsCount, BathroomsCount, Area, MonthlyRent, UnitStatus)
VALUES
('شقة 101', 'U101', @BuildingID, 1, 3, 2, 120.5, 2500.00, 'فارغ'),
('شقة 102', 'U102', @BuildingID, 1, 2, 1, 85.0, 2000.00, 'فارغ'),
('شقة 201', 'U201', @BuildingID, 2, 3, 2, 120.5, 2500.00, 'مؤجر'),
('شقة 202', 'U202', @BuildingID, 2, 2, 1, 85.0, 2000.00, 'فارغ'),
('شقة 301', 'U301', @BuildingID, 3, 4, 3, 150.0, 3000.00, 'فارغ')
GO

-- إضافة عميل تجريبي
INSERT INTO [dbo].[tblCustomers] (CustomerName, Mobile, Email, NationalID, CustomerType)
VALUES ('أحمد محمد علي', '0501234567', '<EMAIL>', '1234567890', 'مستأجر')
GO

-- إضافة عقد تجريبي
DECLARE @TenantID INT = (SELECT TOP 1 CustomerID FROM [dbo].[tblCustomers] WHERE CustomerName = 'أحمد محمد علي')
DECLARE @UnitID INT = (SELECT TOP 1 Item_ID FROM [dbo].[Item_Tbl] WHERE ItemCode = 'U201')

INSERT INTO [dbo].[RentalContracts_Tbl] (ContractNumber, Unit_ID, Tenant_ID, StartDate, EndDate, MonthlyRent, SecurityDeposit, ContractStatus)
VALUES ('C2024001', @UnitID, @TenantID, '2024-01-01', '2024-12-31', 2500.00, 5000.00, 'نشط')
GO

-- تحديث حالة الوحدة المؤجرة
UPDATE [dbo].[Item_Tbl] SET UnitStatus = 'مؤجر' WHERE ItemCode = 'U201'
GO

PRINT 'تم إنشاء قاعدة بيانات نظام إدارة الإيجارات بنجاح!'
PRINT 'تم إضافة جميع الجداول والعلاقات والبيانات الأساسية'
PRINT 'يمكنك الآن البدء في استخدام النظام'

-- ===================================
-- 8. جدول تفاصيل القيود المحاسبية
-- ===================================
CREATE TABLE [dbo].[JournalEntryDetails_Tbl](
    [Detail_ID] [int] IDENTITY(1,1) NOT NULL,
    [Entry_ID] [int] NOT NULL,
    [Account_ID] [int] NOT NULL,
    [DebitAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
    [CreditAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
    [Description] [nvarchar](500) NULL,
    CONSTRAINT [PK_JournalEntryDetails_Tbl] PRIMARY KEY CLUSTERED ([Detail_ID] ASC),
    CONSTRAINT [FK_JournalDetails_Entries] FOREIGN KEY([Entry_ID]) REFERENCES [dbo].[JournalEntries_Tbl] ([Entry_ID]),
    CONSTRAINT [FK_JournalDetails_Accounts] FOREIGN KEY([Account_ID]) REFERENCES [dbo].[Accounts_Tbl] ([Account_ID])
)
GO
