-- إجراءات مخزنة لنظام فصل وضم الطاولات
-- تاريخ الإنشاء: 2025-07-05

USE [smart_reNTAL]
GO

-- ===================================
-- 1. إجراء فصل طاولة إلى عدة أجزاء
-- ===================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_SplitTable]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_SplitTable]
GO

CREATE PROCEDURE [dbo].[SP_SplitTable]
    @Original_Table_Name NVARCHAR(100),
    @Original_Order_No NVARCHAR(50),
    @Split_Count INT,
    @Split_By NVARCHAR(50),
    @Split_Reason NVARCHAR(500) = NULL,
    @Split_Details NVARCHAR(MAX) -- JSON format: [{"table_name":"طاولة1-أ","percentage":50,"customer_name":"أحمد","customer_phone":"123"}]
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Split_ID INT
    DECLARE @Original_Total DECIMAL(18,2)
    DECLARE @ErrorMessage NVARCHAR(4000)
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- التحقق من وجود الطاولة والطلب
        IF NOT EXISTS (SELECT 1 FROM [dbo].[Order_Tbl] WHERE Table_Name = @Original_Table_Name AND Order_No = @Original_Order_No AND ord_Status = 'open')
        BEGIN
            RAISERROR(N'الطاولة أو الطلب غير موجود أو مغلق', 16, 1)
            RETURN
        END
        
        -- حساب إجمالي الطلب الأصلي
        SELECT @Original_Total = ISNULL(SUM(ord_Total), 0)
        FROM [dbo].[Order_Tbl] 
        WHERE Table_Name = @Original_Table_Name AND Order_No = @Original_Order_No AND ord_Status = 'open'
        
        -- إنشاء عملية فصل جديدة
        INSERT INTO [dbo].[TableSplitOperations_Tbl] 
        (Original_Table_Name, Original_Order_No, Split_Count, Split_By, Split_Reason, Original_Total)
        VALUES 
        (@Original_Table_Name, @Original_Order_No, @Split_Count, @Split_By, @Split_Reason, @Original_Total)
        
        SET @Split_ID = SCOPE_IDENTITY()
        
        -- للبساطة، سنستخدم الإجراء البسيط للفصل المتساوي
        -- يمكن تطوير هذا لاحقاً لدعم JSON
        
        -- تحديث الطلب الأصلي ليشير إلى عملية الفصل
        UPDATE [dbo].[Order_Tbl] 
        SET Split_ID = @Split_ID, 
            ord_Status = 'split'
        WHERE Table_Name = @Original_Table_Name AND Order_No = @Original_Order_No
        
        COMMIT TRANSACTION
        
        SELECT @Split_ID AS Split_ID, 'تم فصل الطاولة بنجاح' AS Message
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ErrorMessage = ERROR_MESSAGE()
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- ===================================
-- 2. إجراء ضم عدة طاولات في مجموعة واحدة
-- ===================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_MergeTables]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_MergeTables]
GO

CREATE PROCEDURE [dbo].[SP_MergeTables]
    @Group_Name NVARCHAR(100),
    @Table_Names NVARCHAR(MAX), -- أسماء الطاولات مفصولة بفاصلة
    @Created_By NVARCHAR(50),
    @Notes NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Group_ID INT
    DECLARE @Total_Amount DECIMAL(18,2) = 0
    DECLARE @ErrorMessage NVARCHAR(4000)
    DECLARE @Table_Name NVARCHAR(100)
    DECLARE @Order_No NVARCHAR(50)
    DECLARE @Table_Total DECIMAL(18,2)
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- إنشاء مجموعة جديدة
        INSERT INTO [dbo].[TableGroups_Tbl] (Group_Name, Created_By, Notes)
        VALUES (@Group_Name, @Created_By, @Notes)
        
        SET @Group_ID = SCOPE_IDENTITY()
        
        -- معالجة كل طاولة
        DECLARE table_cursor CURSOR FOR
        SELECT TRIM(value) FROM STRING_SPLIT(@Table_Names, ',')
        
        OPEN table_cursor
        FETCH NEXT FROM table_cursor INTO @Table_Name
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- التحقق من وجود طلبات مفتوحة للطاولة
            IF EXISTS (SELECT 1 FROM [dbo].[Order_Tbl] WHERE Table_Name = @Table_Name AND ord_Status = 'open')
            BEGIN
                -- حساب إجمالي الطاولة
                SELECT @Table_Total = ISNULL(SUM(ord_Total), 0),
                       @Order_No = MAX(Order_No)
                FROM [dbo].[Order_Tbl] 
                WHERE Table_Name = @Table_Name AND ord_Status = 'open'
                
                -- إضافة الطاولة للمجموعة
                INSERT INTO [dbo].[TableGroupDetails_Tbl] 
                (Group_ID, Table_Name, Order_No, Split_Amount, Added_By)
                VALUES 
                (@Group_ID, @Table_Name, @Order_No, @Table_Total, @Created_By)
                
                -- تحديث الطلبات لتشير للمجموعة
                UPDATE [dbo].[Order_Tbl] 
                SET Group_ID = @Group_ID
                WHERE Table_Name = @Table_Name AND ord_Status = 'open'
                
                SET @Total_Amount = @Total_Amount + @Table_Total
            END
            
            FETCH NEXT FROM table_cursor INTO @Table_Name
        END
        
        CLOSE table_cursor
        DEALLOCATE table_cursor
        
        -- تحديث إجمالي المجموعة
        UPDATE [dbo].[TableGroups_Tbl] 
        SET Total_Amount = @Total_Amount
        WHERE Group_ID = @Group_ID
        
        COMMIT TRANSACTION
        
        SELECT @Group_ID AS Group_ID, 'تم ضم الطاولات بنجاح' AS Message, @Total_Amount AS Total_Amount
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ErrorMessage = ERROR_MESSAGE()
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- ===================================
-- 3. إجراء إنهاء مجموعة طاولات (الدفع)
-- ===================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_CloseTableGroup]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_CloseTableGroup]
GO

CREATE PROCEDURE [dbo].[SP_CloseTableGroup]
    @Group_ID INT,
    @Closed_By NVARCHAR(50),
    @Payment_Method NVARCHAR(50) = 'نقدي'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000)
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- التحقق من وجود المجموعة
        IF NOT EXISTS (SELECT 1 FROM [dbo].[TableGroups_Tbl] WHERE Group_ID = @Group_ID AND Group_Status = 'نشط')
        BEGIN
            RAISERROR(N'المجموعة غير موجودة أو مغلقة بالفعل', 16, 1)
            RETURN
        END
        
        -- إغلاق جميع الطلبات في المجموعة
        UPDATE [dbo].[Order_Tbl] 
        SET ord_Status = 'close'
        WHERE Group_ID = @Group_ID AND ord_Status = 'open'
        
        -- تحديث حالة المجموعة
        UPDATE [dbo].[TableGroups_Tbl] 
        SET Group_Status = 'مغلق',
            Closed_Date = GETDATE(),
            Closed_By = @Closed_By
        WHERE Group_ID = @Group_ID
        
        -- إلغاء تفعيل تفاصيل المجموعة
        UPDATE [dbo].[TableGroupDetails_Tbl] 
        SET Is_Active = 0
        WHERE Group_ID = @Group_ID
        
        COMMIT TRANSACTION
        
        SELECT 'تم إغلاق المجموعة بنجاح' AS Message
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ErrorMessage = ERROR_MESSAGE()
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- ===================================
-- 4. إجراء فصل طاولة بسيط (تقسيم متساوي)
-- ===================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_SplitTableSimple]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_SplitTableSimple]
GO

CREATE PROCEDURE [dbo].[SP_SplitTableSimple]
    @Original_Table_Name NVARCHAR(100),
    @Original_Order_No NVARCHAR(50),
    @Split_Count INT,
    @Split_By NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Split_ID INT
    DECLARE @Original_Total DECIMAL(18,2)
    DECLARE @Split_Amount DECIMAL(18,2)
    DECLARE @Split_Percentage DECIMAL(5,2)
    DECLARE @Counter INT = 1
    DECLARE @New_Table_Name NVARCHAR(100)
    DECLARE @New_Order_No NVARCHAR(50)
    DECLARE @ErrorMessage NVARCHAR(4000)
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- التحقق من صحة البيانات
        IF @Split_Count <= 1
        BEGIN
            RAISERROR(N'عدد الأجزاء يجب أن يكون أكبر من 1', 16, 1)
            RETURN
        END
        
        -- حساب الإجمالي الأصلي
        SELECT @Original_Total = ISNULL(SUM(ord_Total), 0)
        FROM [dbo].[Order_Tbl] 
        WHERE Table_Name = @Original_Table_Name AND Order_No = @Original_Order_No AND ord_Status = 'open'
        
        IF @Original_Total = 0
        BEGIN
            RAISERROR(N'لا توجد طلبات مفتوحة لهذه الطاولة', 16, 1)
            RETURN
        END
        
        -- حساب المبلغ والنسبة لكل جزء
        SET @Split_Amount = @Original_Total / @Split_Count
        SET @Split_Percentage = 100.0 / @Split_Count
        
        -- إنشاء عملية فصل
        INSERT INTO [dbo].[TableSplitOperations_Tbl] 
        (Original_Table_Name, Original_Order_No, Split_Count, Split_By, Original_Total)
        VALUES 
        (@Original_Table_Name, @Original_Order_No, @Split_Count, @Split_By, @Original_Total)
        
        SET @Split_ID = SCOPE_IDENTITY()
        
        -- إنشاء الأجزاء المفصولة
        WHILE @Counter <= @Split_Count
        BEGIN
            SET @New_Table_Name = @Original_Table_Name + '-' + CAST(@Counter AS NVARCHAR(10))
            SET @New_Order_No = @Original_Order_No + '-' + CAST(@Counter AS NVARCHAR(10))
            
            -- إدراج تفاصيل الفصل
            INSERT INTO [dbo].[TableSplitDetails_Tbl] 
            (Split_ID, New_Table_Name, New_Order_No, Split_Amount, Split_Percentage)
            VALUES 
            (@Split_ID, @New_Table_Name, @New_Order_No, @Split_Amount, @Split_Percentage)
            
            SET @Counter = @Counter + 1
        END
        
        -- تحديث الطلب الأصلي
        UPDATE [dbo].[Order_Tbl] 
        SET Split_ID = @Split_ID, 
            ord_Status = 'split'
        WHERE Table_Name = @Original_Table_Name AND Order_No = @Original_Order_No
        
        COMMIT TRANSACTION
        
        SELECT @Split_ID AS Split_ID, 'تم فصل الطاولة بنجاح' AS Message, @Split_Count AS Parts_Count
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ErrorMessage = ERROR_MESSAGE()
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
END
GO

-- ===================================
-- 5. إجراء الحصول على معلومات الطاولة
-- ===================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetTableInfo]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetTableInfo]
GO

CREATE PROCEDURE [dbo].[SP_GetTableInfo]
    @Table_Name NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- معلومات الطاولة الأساسية
    SELECT 
        Table_Name,
        Order_No,
        SUM(ord_Total) AS Total_Amount,
        COUNT(*) AS Items_Count,
        MAX(OrderDate) AS Last_Order_Date,
        ord_Status,
        Group_ID,
        Split_ID,
        Original_Order_No,
        Customer_Name,
        Customer_Phone
    FROM [dbo].[Order_Tbl]
    WHERE Table_Name = @Table_Name AND ord_Status IN ('open', 'split')
    GROUP BY Table_Name, Order_No, ord_Status, Group_ID, Split_ID, Original_Order_No, Customer_Name, Customer_Phone
    
    -- إذا كانت الطاولة جزء من مجموعة
    IF EXISTS (SELECT 1 FROM [dbo].[Order_Tbl] WHERE Table_Name = @Table_Name AND Group_ID IS NOT NULL)
    BEGIN
        SELECT 
            tg.Group_ID,
            tg.Group_Name,
            tg.Group_Status,
            tg.Total_Amount AS Group_Total,
            tg.Created_Date,
            tg.Created_By
        FROM [dbo].[TableGroups_Tbl] tg
        INNER JOIN [dbo].[Order_Tbl] o ON tg.Group_ID = o.Group_ID
        WHERE o.Table_Name = @Table_Name
    END
    
    -- إذا كانت الطاولة مفصولة
    IF EXISTS (SELECT 1 FROM [dbo].[Order_Tbl] WHERE Table_Name = @Table_Name AND Split_ID IS NOT NULL)
    BEGIN
        SELECT 
            tso.Split_ID,
            tso.Original_Table_Name,
            tso.Original_Order_No,
            tso.Split_Count,
            tso.Split_Date,
            tso.Split_By,
            tso.Original_Total
        FROM [dbo].[TableSplitOperations_Tbl] tso
        INNER JOIN [dbo].[Order_Tbl] o ON tso.Split_ID = o.Split_ID
        WHERE o.Table_Name = @Table_Name
    END
END
GO

PRINT 'تم إنشاء جميع الإجراءات المخزنة بنجاح'
PRINT 'نظام فصل وضم الطاولات جاهز للاستخدام'
GO
