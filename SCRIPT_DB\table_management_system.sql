-- نظام إدارة فصل وضم الطاولات
-- تاريخ الإنشاء: 2025-07-05
-- الوصف: إضافة جداول ووظائف لفصل وضم الطاولات

USE [smart_reNTAL]
GO

-- ===================================
-- 1. جدول مجموعات الطاولات (Table Groups)
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableGroups_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableGroups_Tbl](
        [Group_ID] [int] IDENTITY(1,1) NOT NULL,
        [Group_Name] [nvarchar](100) NOT NULL,
        [Group_Status] [nvarchar](50) NOT NULL DEFAULT('نشط'), -- نشط، مغلق، مؤقت
        [Created_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Created_By] [nvarchar](50) NOT NULL,
        [Closed_Date] [datetime] NULL,
        [Closed_By] [nvarchar](50) NULL,
        [Total_Amount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Notes] [nvarchar](500) NULL,
        CONSTRAINT [PK_TableGroups_Tbl] PRIMARY KEY CLUSTERED ([Group_ID] ASC)
    )
    PRINT 'تم إنشاء جدول مجموعات الطاولات'
END
ELSE
BEGIN
    PRINT 'جدول مجموعات الطاولات موجود بالفعل'
END
GO

-- ===================================
-- 2. جدول تفاصيل مجموعات الطاولات
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableGroupDetails_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableGroupDetails_Tbl](
        [Detail_ID] [int] IDENTITY(1,1) NOT NULL,
        [Group_ID] [int] NOT NULL,
        [Table_Name] [nvarchar](100) NOT NULL,
        [Order_No] [nvarchar](50) NULL, -- رقم الطلب المرتبط بهذه الطاولة
        [Split_Amount] [decimal](18, 2) NOT NULL DEFAULT(0), -- المبلغ المخصص لهذه الطاولة
        [Split_Percentage] [decimal](5, 2) NULL, -- نسبة التقسيم
        [Added_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Added_By] [nvarchar](50) NOT NULL,
        [Is_Active] [bit] NOT NULL DEFAULT(1),
        CONSTRAINT [PK_TableGroupDetails_Tbl] PRIMARY KEY CLUSTERED ([Detail_ID] ASC),
        CONSTRAINT [FK_TableGroupDetails_Groups] FOREIGN KEY([Group_ID]) REFERENCES [dbo].[TableGroups_Tbl] ([Group_ID])
    )
    PRINT 'تم إنشاء جدول تفاصيل مجموعات الطاولات'
END
ELSE
BEGIN
    PRINT 'جدول تفاصيل مجموعات الطاولات موجود بالفعل'
END
GO

-- ===================================
-- 3. جدول عمليات فصل الطاولات
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableSplitOperations_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableSplitOperations_Tbl](
        [Split_ID] [int] IDENTITY(1,1) NOT NULL,
        [Original_Table_Name] [nvarchar](100) NOT NULL,
        [Original_Order_No] [nvarchar](50) NOT NULL,
        [Split_Count] [int] NOT NULL, -- عدد الأجزاء المفصولة
        [Split_Date] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Split_By] [nvarchar](50) NOT NULL,
        [Split_Reason] [nvarchar](500) NULL,
        [Original_Total] [decimal](18, 2) NOT NULL,
        [Status] [nvarchar](50) NOT NULL DEFAULT('نشط'), -- نشط، مكتمل، ملغي
        CONSTRAINT [PK_TableSplitOperations_Tbl] PRIMARY KEY CLUSTERED ([Split_ID] ASC)
    )
    PRINT 'تم إنشاء جدول عمليات فصل الطاولات'
END
ELSE
BEGIN
    PRINT 'جدول عمليات فصل الطاولات موجود بالفعل'
END
GO

-- ===================================
-- 4. جدول تفاصيل فصل الطاولات
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableSplitDetails_Tbl]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableSplitDetails_Tbl](
        [SplitDetail_ID] [int] IDENTITY(1,1) NOT NULL,
        [Split_ID] [int] NOT NULL,
        [New_Table_Name] [nvarchar](100) NOT NULL,
        [New_Order_No] [nvarchar](50) NOT NULL,
        [Split_Amount] [decimal](18, 2) NOT NULL,
        [Split_Percentage] [decimal](5, 2) NOT NULL,
        [Customer_Name] [nvarchar](200) NULL, -- اسم العميل لهذا الجزء
        [Customer_Phone] [nvarchar](20) NULL,
        [Notes] [nvarchar](500) NULL,
        CONSTRAINT [PK_TableSplitDetails_Tbl] PRIMARY KEY CLUSTERED ([SplitDetail_ID] ASC),
        CONSTRAINT [FK_TableSplitDetails_Operations] FOREIGN KEY([Split_ID]) REFERENCES [dbo].[TableSplitOperations_Tbl] ([Split_ID])
    )
    PRINT 'تم إنشاء جدول تفاصيل فصل الطاولات'
END
ELSE
BEGIN
    PRINT 'جدول تفاصيل فصل الطاولات موجود بالفعل'
END
GO

-- ===================================
-- 5. إضافة حقول جديدة لجدول Order_Tbl
-- ===================================
-- إضافة حقل Group_ID
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Group_ID')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Group_ID] [int] NULL
    PRINT 'تم إضافة حقل Group_ID إلى جدول Order_Tbl'
END
ELSE
BEGIN
    PRINT 'حقل Group_ID موجود بالفعل في جدول Order_Tbl'
END
GO

-- إضافة حقل Split_ID
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Split_ID')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Split_ID] [int] NULL
    PRINT 'تم إضافة حقل Split_ID إلى جدول Order_Tbl'
END
ELSE
BEGIN
    PRINT 'حقل Split_ID موجود بالفعل في جدول Order_Tbl'
END
GO

-- إضافة حقل Original_Order_No
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Original_Order_No')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Original_Order_No] [nvarchar](50) NULL
    PRINT 'تم إضافة حقل Original_Order_No إلى جدول Order_Tbl'
END
ELSE
BEGIN
    PRINT 'حقل Original_Order_No موجود بالفعل في جدول Order_Tbl'
END
GO

-- إضافة حقل Customer_Name
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Customer_Name')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Customer_Name] [nvarchar](200) NULL
    PRINT 'تم إضافة حقل Customer_Name إلى جدول Order_Tbl'
END
ELSE
BEGIN
    PRINT 'حقل Customer_Name موجود بالفعل في جدول Order_Tbl'
END
GO

-- إضافة حقل Customer_Phone
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Order_Tbl]') AND name = 'Customer_Phone')
BEGIN
    ALTER TABLE [dbo].[Order_Tbl] ADD [Customer_Phone] [nvarchar](20) NULL
    PRINT 'تم إضافة حقل Customer_Phone إلى جدول Order_Tbl'
END
ELSE
BEGIN
    PRINT 'حقل Customer_Phone موجود بالفعل في جدول Order_Tbl'
END
GO

-- ===================================
-- 6. Views للتقارير والاستعلامات
-- ===================================

-- فيو لعرض الطاولات المجمعة
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[View_GroupedTables]'))
    DROP VIEW [dbo].[View_GroupedTables]
GO

CREATE VIEW [dbo].[View_GroupedTables] AS
SELECT 
    tg.Group_ID,
    tg.Group_Name,
    tg.Group_Status,
    tg.Total_Amount,
    tg.Created_Date,
    tg.Created_By,
    COUNT(tgd.Detail_ID) as Tables_Count,
    STRING_AGG(tgd.Table_Name, ', ') as Tables_List
FROM [dbo].[TableGroups_Tbl] tg
LEFT JOIN [dbo].[TableGroupDetails_Tbl] tgd ON tg.Group_ID = tgd.Group_ID AND tgd.Is_Active = 1
GROUP BY tg.Group_ID, tg.Group_Name, tg.Group_Status, tg.Total_Amount, tg.Created_Date, tg.Created_By
GO

-- فيو لعرض الطاولات المفصولة
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[View_SplitTables]'))
    DROP VIEW [dbo].[View_SplitTables]
GO

CREATE VIEW [dbo].[View_SplitTables] AS
SELECT 
    tso.Split_ID,
    tso.Original_Table_Name,
    tso.Original_Order_No,
    tso.Split_Count,
    tso.Split_Date,
    tso.Split_By,
    tso.Original_Total,
    tso.Status,
    tsd.New_Table_Name,
    tsd.New_Order_No,
    tsd.Split_Amount,
    tsd.Split_Percentage,
    tsd.Customer_Name,
    tsd.Customer_Phone
FROM [dbo].[TableSplitOperations_Tbl] tso
INNER JOIN [dbo].[TableSplitDetails_Tbl] tsd ON tso.Split_ID = tsd.Split_ID
GO

PRINT 'تم إنشاء جميع الجداول والـ Views بنجاح'
PRINT 'نظام فصل وضم الطاولات جاهز للاستخدام'
GO
