﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Form1))
        Me.Label1 = New System.Windows.Forms.Label()
        Me.TabControl1 = New System.Windows.Forms.TabControl()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.ButtonNext2 = New System.Windows.Forms.Button()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.TxTSaveTo = New System.Windows.Forms.TextBox()
        Me.ButtonSelectFile = New System.Windows.Forms.Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.TxTXMLFileName = New System.Windows.Forms.TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.ButtonNext3 = New System.Windows.Forms.Button()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.TxTprivateKeyContent = New System.Windows.Forms.TextBox()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.TxTcertificateContent = New System.Windows.Forms.TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.TabPage3 = New System.Windows.Forms.TabPage()
        Me.GroupBox7 = New System.Windows.Forms.GroupBox()
        Me.TXTPIH = New System.Windows.Forms.TextBox()
        Me.ButtonNext4 = New System.Windows.Forms.Button()
        Me.ButtonSignNow = New System.Windows.Forms.Button()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TabPage5 = New System.Windows.Forms.TabPage()
        Me.ButtonSend = New System.Windows.Forms.Button()
        Me.RadioButtonC = New System.Windows.Forms.RadioButton()
        Me.RadioButtonR = New System.Windows.Forms.RadioButton()
        Me.GroupBox42 = New System.Windows.Forms.GroupBox()
        Me.TxTAPICerBody = New System.Windows.Forms.TextBox()
        Me.GroupBox12 = New System.Windows.Forms.GroupBox()
        Me.TxTAPICerHeder = New System.Windows.Forms.TextBox()
        Me.GroupBox11 = New System.Windows.Forms.GroupBox()
        Me.TxTUUID = New System.Windows.Forms.TextBox()
        Me.GroupBox9 = New System.Windows.Forms.GroupBox()
        Me.CenterUrlApiClearance = New System.Windows.Forms.TextBox()
        Me.GroupBox41 = New System.Windows.Forms.GroupBox()
        Me.CenterUrlApiReporting = New System.Windows.Forms.TextBox()
        Me.GroupBox10 = New System.Windows.Forms.GroupBox()
        Me.ComboBoxPortalType = New System.Windows.Forms.ComboBox()
        Me.GroupBox8 = New System.Windows.Forms.GroupBox()
        Me.ApiAuthorization = New System.Windows.Forms.TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.TabControl1.SuspendLayout()
        Me.TabPage1.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.TabPage2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.TabPage3.SuspendLayout()
        Me.GroupBox7.SuspendLayout()
        Me.TabPage5.SuspendLayout()
        Me.GroupBox42.SuspendLayout()
        Me.GroupBox12.SuspendLayout()
        Me.GroupBox11.SuspendLayout()
        Me.GroupBox9.SuspendLayout()
        Me.GroupBox41.SuspendLayout()
        Me.GroupBox10.SuspendLayout()
        Me.GroupBox8.SuspendLayout()
        Me.SuspendLayout()
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.SystemColors.ControlLightLight
        Me.Label1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label1.Location = New System.Drawing.Point(0, 0)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(1027, 42)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "Free SourceCode - Generate Certificate , PrivetKey from CSR and Sign XML-UBL"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TabControl1
        '
        Me.TabControl1.Controls.Add(Me.TabPage1)
        Me.TabControl1.Controls.Add(Me.TabPage2)
        Me.TabControl1.Controls.Add(Me.TabPage3)
        Me.TabControl1.Controls.Add(Me.TabPage5)
        Me.TabControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TabControl1.Location = New System.Drawing.Point(0, 42)
        Me.TabControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.TabControl1.Name = "TabControl1"
        Me.TabControl1.Padding = New System.Drawing.Point(10, 10)
        Me.TabControl1.SelectedIndex = 0
        Me.TabControl1.Size = New System.Drawing.Size(1027, 538)
        Me.TabControl1.SizeMode = System.Windows.Forms.TabSizeMode.Fixed
        Me.TabControl1.TabIndex = 1
        '
        'TabPage1
        '
        Me.TabPage1.Controls.Add(Me.ButtonNext2)
        Me.TabPage1.Controls.Add(Me.GroupBox5)
        Me.TabPage1.Controls.Add(Me.ButtonSelectFile)
        Me.TabPage1.Controls.Add(Me.GroupBox1)
        Me.TabPage1.Controls.Add(Me.Label2)
        Me.TabPage1.Location = New System.Drawing.Point(4, 39)
        Me.TabPage1.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Padding = New System.Windows.Forms.Padding(4)
        Me.TabPage1.Size = New System.Drawing.Size(1019, 495)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "Step 1"
        Me.TabPage1.UseVisualStyleBackColor = True
        '
        'ButtonNext2
        '
        Me.ButtonNext2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ButtonNext2.Location = New System.Drawing.Point(4, 448)
        Me.ButtonNext2.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonNext2.Name = "ButtonNext2"
        Me.ButtonNext2.Size = New System.Drawing.Size(1011, 43)
        Me.ButtonNext2.TabIndex = 3
        Me.ButtonNext2.Text = "Next"
        Me.ButtonNext2.UseVisualStyleBackColor = True
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.TxTSaveTo)
        Me.GroupBox5.Location = New System.Drawing.Point(39, 265)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(855, 82)
        Me.GroupBox5.TabIndex = 2
        Me.GroupBox5.TabStop = False
        Me.GroupBox5.Text = "Save to"
        '
        'TxTSaveTo
        '
        Me.TxTSaveTo.BackColor = System.Drawing.SystemColors.Info
        Me.TxTSaveTo.Enabled = False
        Me.TxTSaveTo.Location = New System.Drawing.Point(7, 37)
        Me.TxTSaveTo.Name = "TxTSaveTo"
        Me.TxTSaveTo.Size = New System.Drawing.Size(823, 22)
        Me.TxTSaveTo.TabIndex = 1
        '
        'ButtonSelectFile
        '
        Me.ButtonSelectFile.Location = New System.Drawing.Point(39, 84)
        Me.ButtonSelectFile.Name = "ButtonSelectFile"
        Me.ButtonSelectFile.Size = New System.Drawing.Size(340, 41)
        Me.ButtonSelectFile.TabIndex = 0
        Me.ButtonSelectFile.Text = "Step 1 - Select XML File to Sign"
        Me.ButtonSelectFile.UseVisualStyleBackColor = True
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.TxTXMLFileName)
        Me.GroupBox1.Location = New System.Drawing.Point(39, 159)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(855, 82)
        Me.GroupBox1.TabIndex = 1
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "File Name"
        '
        'TxTXMLFileName
        '
        Me.TxTXMLFileName.BackColor = System.Drawing.SystemColors.Info
        Me.TxTXMLFileName.Location = New System.Drawing.Point(7, 37)
        Me.TxTXMLFileName.Name = "TxTXMLFileName"
        Me.TxTXMLFileName.Size = New System.Drawing.Size(823, 22)
        Me.TxTXMLFileName.TabIndex = 1
        '
        'Label2
        '
        Me.Label2.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Label2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label2.Location = New System.Drawing.Point(4, 4)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(1011, 32)
        Me.Label2.TabIndex = 0
        Me.Label2.Text = "Step 1 - Select XML File to Sign"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TabPage2
        '
        Me.TabPage2.Controls.Add(Me.ButtonNext3)
        Me.TabPage2.Controls.Add(Me.Label5)
        Me.TabPage2.Controls.Add(Me.GroupBox3)
        Me.TabPage2.Controls.Add(Me.GroupBox2)
        Me.TabPage2.Controls.Add(Me.Label3)
        Me.TabPage2.Location = New System.Drawing.Point(4, 39)
        Me.TabPage2.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Padding = New System.Windows.Forms.Padding(4)
        Me.TabPage2.Size = New System.Drawing.Size(1019, 495)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "Step 2"
        Me.TabPage2.UseVisualStyleBackColor = True
        '
        'ButtonNext3
        '
        Me.ButtonNext3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ButtonNext3.Location = New System.Drawing.Point(4, 448)
        Me.ButtonNext3.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonNext3.Name = "ButtonNext3"
        Me.ButtonNext3.Size = New System.Drawing.Size(1011, 43)
        Me.ButtonNext3.TabIndex = 5
        Me.ButtonNext3.Text = "Next"
        Me.ButtonNext3.UseVisualStyleBackColor = True
        '
        'Label5
        '
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(11, 384)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(910, 48)
        Me.Label5.TabIndex = 4
        Me.Label5.Text = resources.GetString("Label5.Text")
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.TxTprivateKeyContent)
        Me.GroupBox3.Location = New System.Drawing.Point(8, 246)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(926, 125)
        Me.GroupBox3.TabIndex = 3
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "PrivateKey Content - From Cert4Sign.com"
        '
        'TxTprivateKeyContent
        '
        Me.TxTprivateKeyContent.BackColor = System.Drawing.SystemColors.Info
        Me.TxTprivateKeyContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TxTprivateKeyContent.Location = New System.Drawing.Point(3, 18)
        Me.TxTprivateKeyContent.Multiline = True
        Me.TxTprivateKeyContent.Name = "TxTprivateKeyContent"
        Me.TxTprivateKeyContent.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxTprivateKeyContent.Size = New System.Drawing.Size(920, 104)
        Me.TxTprivateKeyContent.TabIndex = 1
        Me.TxTprivateKeyContent.Text = "MHQCAQEEIBjOFCTCAUQCg9t8P/i8OzfDgH47yEJ+AF7swN5/9gJPoAcGBSuBBAAKoUQDQgAEtj7FhtCGs" &
    "O/l9d37QFGvFSCRPRMm/Dt1uAq2k0v5pQ5QHtwIyG2hmEnrXOqAnXGl1siiSrPqiVztTeX3525EWw=="
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.TxTcertificateContent)
        Me.GroupBox2.Location = New System.Drawing.Point(8, 51)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(926, 189)
        Me.GroupBox2.TabIndex = 2
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "Certificate Content - From Cert4Sign.com"
        '
        'TxTcertificateContent
        '
        Me.TxTcertificateContent.BackColor = System.Drawing.SystemColors.Info
        Me.TxTcertificateContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TxTcertificateContent.Location = New System.Drawing.Point(3, 18)
        Me.TxTcertificateContent.Multiline = True
        Me.TxTcertificateContent.Name = "TxTcertificateContent"
        Me.TxTcertificateContent.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxTcertificateContent.Size = New System.Drawing.Size(920, 168)
        Me.TxTcertificateContent.TabIndex = 0
        Me.TxTcertificateContent.Text = resources.GetString("TxTcertificateContent.Text")
        '
        'Label3
        '
        Me.Label3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Label3.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label3.Location = New System.Drawing.Point(4, 4)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(1011, 32)
        Me.Label3.TabIndex = 1
        Me.Label3.Text = "Step 2- Generate Certificate , PrivetKey from CSR and Sign XML-UBL"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TabPage3
        '
        Me.TabPage3.Controls.Add(Me.GroupBox7)
        Me.TabPage3.Controls.Add(Me.ButtonNext4)
        Me.TabPage3.Controls.Add(Me.ButtonSignNow)
        Me.TabPage3.Controls.Add(Me.Label4)
        Me.TabPage3.Location = New System.Drawing.Point(4, 39)
        Me.TabPage3.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPage3.Name = "TabPage3"
        Me.TabPage3.Size = New System.Drawing.Size(1019, 495)
        Me.TabPage3.TabIndex = 2
        Me.TabPage3.Text = "Step 3"
        Me.TabPage3.UseVisualStyleBackColor = True
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.TXTPIH)
        Me.GroupBox7.Location = New System.Drawing.Point(11, 191)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(985, 115)
        Me.GroupBox7.TabIndex = 7
        Me.GroupBox7.TabStop = False
        Me.GroupBox7.Text = "PIH - InvoiceHashing"
        '
        'TXTPIH
        '
        Me.TXTPIH.BackColor = System.Drawing.SystemColors.Info
        Me.TXTPIH.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TXTPIH.Location = New System.Drawing.Point(3, 18)
        Me.TXTPIH.Multiline = True
        Me.TXTPIH.Name = "TXTPIH"
        Me.TXTPIH.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TXTPIH.Size = New System.Drawing.Size(979, 94)
        Me.TXTPIH.TabIndex = 0
        '
        'ButtonNext4
        '
        Me.ButtonNext4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ButtonNext4.Location = New System.Drawing.Point(0, 452)
        Me.ButtonNext4.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonNext4.Name = "ButtonNext4"
        Me.ButtonNext4.Size = New System.Drawing.Size(1019, 43)
        Me.ButtonNext4.TabIndex = 5
        Me.ButtonNext4.Text = "Next"
        Me.ButtonNext4.UseVisualStyleBackColor = True
        '
        'ButtonSignNow
        '
        Me.ButtonSignNow.Location = New System.Drawing.Point(8, 61)
        Me.ButtonSignNow.Name = "ButtonSignNow"
        Me.ButtonSignNow.Size = New System.Drawing.Size(988, 110)
        Me.ButtonSignNow.TabIndex = 4
        Me.ButtonSignNow.Text = "Step 3 - Sign Now and Save New XML File "
        Me.ButtonSignNow.UseVisualStyleBackColor = True
        '
        'Label4
        '
        Me.Label4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Label4.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label4.Location = New System.Drawing.Point(0, 0)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(1019, 32)
        Me.Label4.TabIndex = 2
        Me.Label4.Text = "Step 3 - Sign XML"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TabPage5
        '
        Me.TabPage5.Controls.Add(Me.ButtonSend)
        Me.TabPage5.Controls.Add(Me.RadioButtonC)
        Me.TabPage5.Controls.Add(Me.RadioButtonR)
        Me.TabPage5.Controls.Add(Me.GroupBox42)
        Me.TabPage5.Controls.Add(Me.GroupBox12)
        Me.TabPage5.Controls.Add(Me.GroupBox11)
        Me.TabPage5.Controls.Add(Me.GroupBox9)
        Me.TabPage5.Controls.Add(Me.GroupBox41)
        Me.TabPage5.Controls.Add(Me.GroupBox10)
        Me.TabPage5.Controls.Add(Me.GroupBox8)
        Me.TabPage5.Controls.Add(Me.Label7)
        Me.TabPage5.Location = New System.Drawing.Point(4, 39)
        Me.TabPage5.Name = "TabPage5"
        Me.TabPage5.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage5.Size = New System.Drawing.Size(1019, 495)
        Me.TabPage5.TabIndex = 4
        Me.TabPage5.Text = "Step5"
        Me.TabPage5.UseVisualStyleBackColor = True
        '
        'ButtonSend
        '
        Me.ButtonSend.Location = New System.Drawing.Point(553, 395)
        Me.ButtonSend.Name = "ButtonSend"
        Me.ButtonSend.Size = New System.Drawing.Size(393, 63)
        Me.ButtonSend.TabIndex = 32323307
        Me.ButtonSend.Text = "Send"
        Me.ButtonSend.UseVisualStyleBackColor = True
        '
        'RadioButtonC
        '
        Me.RadioButtonC.AutoSize = True
        Me.RadioButtonC.Location = New System.Drawing.Point(656, 346)
        Me.RadioButtonC.Name = "RadioButtonC"
        Me.RadioButtonC.Size = New System.Drawing.Size(88, 20)
        Me.RadioButtonC.TabIndex = 32323306
        Me.RadioButtonC.Text = "Clearance"
        Me.RadioButtonC.UseVisualStyleBackColor = True
        '
        'RadioButtonR
        '
        Me.RadioButtonR.AutoSize = True
        Me.RadioButtonR.Checked = True
        Me.RadioButtonR.Location = New System.Drawing.Point(545, 346)
        Me.RadioButtonR.Name = "RadioButtonR"
        Me.RadioButtonR.Size = New System.Drawing.Size(85, 20)
        Me.RadioButtonR.TabIndex = 32323305
        Me.RadioButtonR.TabStop = True
        Me.RadioButtonR.Text = "Reporting"
        Me.RadioButtonR.UseVisualStyleBackColor = True
        '
        'GroupBox42
        '
        Me.GroupBox42.Controls.Add(Me.TxTAPICerBody)
        Me.GroupBox42.Location = New System.Drawing.Point(542, 179)
        Me.GroupBox42.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox42.Name = "GroupBox42"
        Me.GroupBox42.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox42.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox42.Size = New System.Drawing.Size(407, 155)
        Me.GroupBox42.TabIndex = 32323304
        Me.GroupBox42.TabStop = False
        Me.GroupBox42.Text = "APIBody"
        '
        'TxTAPICerBody
        '
        Me.TxTAPICerBody.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TxTAPICerBody.Location = New System.Drawing.Point(3, 19)
        Me.TxTAPICerBody.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxTAPICerBody.Multiline = True
        Me.TxTAPICerBody.Name = "TxTAPICerBody"
        Me.TxTAPICerBody.ReadOnly = True
        Me.TxTAPICerBody.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxTAPICerBody.Size = New System.Drawing.Size(401, 132)
        Me.TxTAPICerBody.TabIndex = 32323298
        '
        'GroupBox12
        '
        Me.GroupBox12.Controls.Add(Me.TxTAPICerHeder)
        Me.GroupBox12.Location = New System.Drawing.Point(539, 53)
        Me.GroupBox12.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox12.Name = "GroupBox12"
        Me.GroupBox12.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox12.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox12.Size = New System.Drawing.Size(407, 118)
        Me.GroupBox12.TabIndex = 32323303
        Me.GroupBox12.TabStop = False
        Me.GroupBox12.Text = "APIHeder"
        '
        'TxTAPICerHeder
        '
        Me.TxTAPICerHeder.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TxTAPICerHeder.Location = New System.Drawing.Point(3, 19)
        Me.TxTAPICerHeder.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxTAPICerHeder.Multiline = True
        Me.TxTAPICerHeder.Name = "TxTAPICerHeder"
        Me.TxTAPICerHeder.ReadOnly = True
        Me.TxTAPICerHeder.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxTAPICerHeder.Size = New System.Drawing.Size(401, 95)
        Me.TxTAPICerHeder.TabIndex = 32323297
        '
        'GroupBox11
        '
        Me.GroupBox11.Controls.Add(Me.TxTUUID)
        Me.GroupBox11.Location = New System.Drawing.Point(26, 177)
        Me.GroupBox11.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox11.Name = "GroupBox11"
        Me.GroupBox11.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox11.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox11.Size = New System.Drawing.Size(498, 56)
        Me.GroupBox11.TabIndex = 32323302
        Me.GroupBox11.TabStop = False
        Me.GroupBox11.Text = "UUID From Your XML File"
        '
        'TxTUUID
        '
        Me.TxTUUID.BackColor = System.Drawing.SystemColors.Info
        Me.TxTUUID.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TxTUUID.Location = New System.Drawing.Point(3, 19)
        Me.TxTUUID.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxTUUID.Name = "TxTUUID"
        Me.TxTUUID.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxTUUID.Size = New System.Drawing.Size(492, 22)
        Me.TxTUUID.TabIndex = 32323297
        Me.TxTUUID.Text = "16e78469-64af-406d-9cfd-895e724198f0"
        '
        'GroupBox9
        '
        Me.GroupBox9.Controls.Add(Me.CenterUrlApiClearance)
        Me.GroupBox9.Location = New System.Drawing.Point(29, 402)
        Me.GroupBox9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox9.Name = "GroupBox9"
        Me.GroupBox9.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox9.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox9.Size = New System.Drawing.Size(498, 56)
        Me.GroupBox9.TabIndex = 32323302
        Me.GroupBox9.TabStop = False
        Me.GroupBox9.Text = "URL api for Clearance"
        '
        'CenterUrlApiClearance
        '
        Me.CenterUrlApiClearance.BackColor = System.Drawing.SystemColors.Info
        Me.CenterUrlApiClearance.Dock = System.Windows.Forms.DockStyle.Fill
        Me.CenterUrlApiClearance.Location = New System.Drawing.Point(3, 19)
        Me.CenterUrlApiClearance.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.CenterUrlApiClearance.Name = "CenterUrlApiClearance"
        Me.CenterUrlApiClearance.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.CenterUrlApiClearance.Size = New System.Drawing.Size(492, 22)
        Me.CenterUrlApiClearance.TabIndex = 32323297
        '
        'GroupBox41
        '
        Me.GroupBox41.Controls.Add(Me.CenterUrlApiReporting)
        Me.GroupBox41.Location = New System.Drawing.Point(29, 325)
        Me.GroupBox41.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox41.Name = "GroupBox41"
        Me.GroupBox41.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox41.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox41.Size = New System.Drawing.Size(498, 56)
        Me.GroupBox41.TabIndex = 32323301
        Me.GroupBox41.TabStop = False
        Me.GroupBox41.Text = "URL api for Reporting"
        '
        'CenterUrlApiReporting
        '
        Me.CenterUrlApiReporting.BackColor = System.Drawing.SystemColors.Info
        Me.CenterUrlApiReporting.Dock = System.Windows.Forms.DockStyle.Fill
        Me.CenterUrlApiReporting.Location = New System.Drawing.Point(3, 19)
        Me.CenterUrlApiReporting.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.CenterUrlApiReporting.Name = "CenterUrlApiReporting"
        Me.CenterUrlApiReporting.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.CenterUrlApiReporting.Size = New System.Drawing.Size(492, 22)
        Me.CenterUrlApiReporting.TabIndex = 32323297
        '
        'GroupBox10
        '
        Me.GroupBox10.Controls.Add(Me.ComboBoxPortalType)
        Me.GroupBox10.Location = New System.Drawing.Point(29, 255)
        Me.GroupBox10.Name = "GroupBox10"
        Me.GroupBox10.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.GroupBox10.Size = New System.Drawing.Size(498, 53)
        Me.GroupBox10.TabIndex = 32323300
        Me.GroupBox10.TabStop = False
        Me.GroupBox10.Text = "Fatoora Portal"
        '
        'ComboBoxPortalType
        '
        Me.ComboBoxPortalType.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ComboBoxPortalType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ComboBoxPortalType.FormattingEnabled = True
        Me.ComboBoxPortalType.Items.AddRange(New Object() {"Developer", "Simulation", "Production"})
        Me.ComboBoxPortalType.Location = New System.Drawing.Point(3, 18)
        Me.ComboBoxPortalType.Name = "ComboBoxPortalType"
        Me.ComboBoxPortalType.Size = New System.Drawing.Size(492, 24)
        Me.ComboBoxPortalType.TabIndex = 0
        '
        'GroupBox8
        '
        Me.GroupBox8.Controls.Add(Me.ApiAuthorization)
        Me.GroupBox8.Location = New System.Drawing.Point(26, 53)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(498, 101)
        Me.GroupBox8.TabIndex = 5
        Me.GroupBox8.TabStop = False
        Me.GroupBox8.Text = "Authorization - From Cert4Sign.com"
        '
        'ApiAuthorization
        '
        Me.ApiAuthorization.BackColor = System.Drawing.SystemColors.Info
        Me.ApiAuthorization.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ApiAuthorization.Location = New System.Drawing.Point(3, 18)
        Me.ApiAuthorization.Multiline = True
        Me.ApiAuthorization.Name = "ApiAuthorization"
        Me.ApiAuthorization.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.ApiAuthorization.Size = New System.Drawing.Size(492, 80)
        Me.ApiAuthorization.TabIndex = 0
        Me.ApiAuthorization.Text = resources.GetString("ApiAuthorization.Text")
        '
        'Label7
        '
        Me.Label7.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Label7.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label7.Location = New System.Drawing.Point(3, 3)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(1013, 32)
        Me.Label7.TabIndex = 4
        Me.Label7.Text = "Step 5 - Send to Zatka"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'Form1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1027, 593)
        Me.Controls.Add(Me.TabControl1)
        Me.Controls.Add(Me.Label1)
        Me.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "Form1"
        Me.Text = "Form1"
        Me.TabControl1.ResumeLayout(False)
        Me.TabPage1.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.TabPage2.ResumeLayout(False)
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.TabPage3.ResumeLayout(False)
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        Me.TabPage5.ResumeLayout(False)
        Me.TabPage5.PerformLayout()
        Me.GroupBox42.ResumeLayout(False)
        Me.GroupBox42.PerformLayout()
        Me.GroupBox12.ResumeLayout(False)
        Me.GroupBox12.PerformLayout()
        Me.GroupBox11.ResumeLayout(False)
        Me.GroupBox11.PerformLayout()
        Me.GroupBox9.ResumeLayout(False)
        Me.GroupBox9.PerformLayout()
        Me.GroupBox41.ResumeLayout(False)
        Me.GroupBox41.PerformLayout()
        Me.GroupBox10.ResumeLayout(False)
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents Label1 As Label
    Friend WithEvents TabControl1 As TabControl
    Friend WithEvents TabPage1 As TabPage
    Friend WithEvents TabPage2 As TabPage
    Friend WithEvents TabPage3 As TabPage
    Friend WithEvents Label2 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents ButtonSelectFile As Button
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents TxTXMLFileName As TextBox
    Friend WithEvents GroupBox3 As GroupBox
    Friend WithEvents TxTprivateKeyContent As TextBox
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents TxTcertificateContent As TextBox
    Friend WithEvents Label5 As Label
    Friend WithEvents ButtonSignNow As Button
    Friend WithEvents GroupBox5 As GroupBox
    Friend WithEvents TxTSaveTo As TextBox
    Friend WithEvents ButtonNext2 As Button
    Friend WithEvents ButtonNext3 As Button
    Friend WithEvents ButtonNext4 As Button
    Friend WithEvents TabPage5 As TabPage
    Friend WithEvents GroupBox10 As GroupBox
    Friend WithEvents ComboBoxPortalType As ComboBox
    Friend WithEvents GroupBox8 As GroupBox
    Friend WithEvents ApiAuthorization As TextBox
    Friend WithEvents Label7 As Label
    Friend WithEvents GroupBox9 As GroupBox
    Friend WithEvents CenterUrlApiClearance As TextBox
    Friend WithEvents GroupBox41 As GroupBox
    Friend WithEvents CenterUrlApiReporting As TextBox
    Friend WithEvents GroupBox11 As GroupBox
    Friend WithEvents TxTUUID As TextBox
    Friend WithEvents GroupBox42 As GroupBox
    Friend WithEvents TxTAPICerBody As TextBox
    Friend WithEvents GroupBox12 As GroupBox
    Friend WithEvents TxTAPICerHeder As TextBox
    Friend WithEvents ButtonSend As Button
    Friend WithEvents RadioButtonC As RadioButton
    Friend WithEvents RadioButtonR As RadioButton
    Friend WithEvents GroupBox7 As GroupBox
    Friend WithEvents TXTPIH As TextBox
End Class
