﻿Imports System.IO
Imports System.Net
Imports System.Net.Http
Imports System.Net.Http.Headers
Imports System.Web.Script.Serialization
Imports System.Xml
Imports SDKNETFrameWorkLib.GeneralLogic
Imports Zatca.EInvoice.SDK
Imports Zatca.EInvoice.SDK.Contracts.Models

Public Class Form1

    'import Zatka SDK https://sandbox.zatca.gov.sa/downloadSDK
    'This Is sample private key And certificate however user can override this by add this file privateKey.txt And certificate.txt
    'The following digital certificate and private keys is default, and you must build a digital certificate that includes your store's information and then approve it.
    'You can generate a digital certificate and private key through an OpenSSL application or using a website
    'https://cert4sign.com

    Dim certificateContent = Nothing
    Dim privateKeyContent = Nothing

    'Privous invoice Hash First invoice 0 > Convert Based64:
    Dim pihContent = "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ=="

    'Select Path File 
    Dim FilePathXMLWithoutSign As String
    Dim FilePathXMLAfterign As String
    Private Sub ButtonSelectFile_Click(sender As Object, e As EventArgs) Handles ButtonSelectFile.Click


        'Step 1 - Select XML WithOut Sign
        Try
            OpenFileDialog1.Title = "Select XML"
            OpenFileDialog1.FileName = Nothing
            OpenFileDialog1.InitialDirectory = Application.StartupPath & "\XMLTemplate\"
            OpenFileDialog1.Filter = "XML Files (.xml)|*.xml|All Files (*.*)|*.*"
            OpenFileDialog1.FilterIndex = 1
            OpenFileDialog1.RestoreDirectory = True
            OpenFileDialog1.ShowDialog()

            TxTXMLFileName.Text = OpenFileDialog1.FileName
            TxTSaveTo.Text = System.IO.Path.GetDirectoryName(OpenFileDialog1.FileName) + "\ResultXML.xml"

            FilePathXMLWithoutSign = TxTXMLFileName.Text
            FilePathXMLAfterign = TxTSaveTo.Text


        Catch ex As Exception
            MsgBox(ex.ToString)
        End Try


    End Sub

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ComboBoxPortalType.SelectedIndex = 0
    End Sub

    Private Sub ButtonSignNow_Click(sender As Object, e As EventArgs) Handles ButtonSignNow.Click
        Try


            If FilePathXMLAfterign = Nothing Or FilePathXMLWithoutSign = Nothing Then
                MsgBox("You Must Select Path File Name")
                TabControl1.SelectedTab = TabPage1
                Exit Sub
            End If

            If TxTcertificateContent.Text = Nothing Then
                MsgBox("You Must Select Certificate Content")
                TabControl1.SelectedTab = TabPage2
                Exit Sub
            End If

            If TxTprivateKeyContent.Text = Nothing Then
                MsgBox("You Must Select PrivetKey Content")
                TabControl1.SelectedTab = TabPage2
                Exit Sub
            End If




            'Set From TextBox
            certificateContent = TxTcertificateContent.Text.Trim
            privateKeyContent = TxTprivateKeyContent.Text.Trim




            Try
                Dim _IEInvoiceSigningLogic = New EInvoiceSigner()
                Dim objResult As SignResult = New SignResult()
                Dim XmlDoc As XmlDocument = New XmlDocument()
                XmlDoc.PreserveWhitespace = True

                Try
                    XmlDoc.Load(FilePathXMLWithoutSign)
                Catch ex As Exception
                    MsgBox(ex.ToString)
                    Exit Sub
                End Try


                objResult = _IEInvoiceSigningLogic.SignDocument(XmlDoc, certificateContent, privateKeyContent)
                If (objResult.IsValid) Then
                    objResult.SignedEInvoice.PreserveWhitespace = True



                    'Get Hash
                    Dim EInvoiceHashGenerator = New EInvoiceHashGenerator()
                    Dim objResultH As HashResult = New HashResult()
                    objResultH = EInvoiceHashGenerator.GenerateEInvoiceHashing(XmlDoc)
                    TXTPIH.Text = objResultH.Hash




                    'SaveFile
                    Dim objWriter As New System.IO.StreamWriter(FilePathXMLAfterign)
                    objWriter.Write(objResult.SignedEInvoice.OuterXml)
                    objWriter.Close()
                End If



                MsgBox("Sign OK")


            Catch ex As Exception
                MsgBox(ex.ToString)
            End Try


        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub
        End Try

    End Sub




    Private Sub ButtonNext2_Click(sender As Object, e As EventArgs) Handles ButtonNext2.Click
        TabControl1.SelectedTab = TabPage2
    End Sub

    Private Sub ButtonNext3_Click(sender As Object, e As EventArgs) Handles ButtonNext3.Click
        TabControl1.SelectedTab = TabPage3
    End Sub



    Private Sub ComboBoxPortalType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxPortalType.SelectedIndexChanged
        If ComboBoxPortalType.SelectedIndex = 0 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/clearance/single"
        End If

        If ComboBoxPortalType.SelectedIndex = 1 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/clearance/single"
        End If

        If ComboBoxPortalType.SelectedIndex = 2 Then
            CenterUrlApiReporting.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/reporting/single"
            CenterUrlApiClearance.Text = "https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/clearance/single"
        End If
    End Sub




    Public Async Sub GoapiNowIDAsync()

        Dim ApiSelectURLtoPost As String = Nothing
        Dim ApiClearanceStatus As String = Nothing
        Dim XMLFileBase64Encod As String = Nothing


        ' Load XML File After Sign to Convert Based64    
        Try
            Dim XMLFileToConvvertBased64 As String = File.ReadAllText(FilePathXMLAfterign)
            'Convert XML VAL to Base64
            XMLFileBase64Encod = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(XMLFileToConvvertBased64.ToString))
        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub
        End Try
        Try

            If RadioButtonR.Checked = True Then
                ApiSelectURLtoPost = CenterUrlApiReporting.Text
                ApiClearanceStatus = "0"
            End If

            If RadioButtonC.Checked = True Then
                ApiSelectURLtoPost = CenterUrlApiClearance.Text
                ApiClearanceStatus = "1"
            End If
            Using httpClient = New HttpClient()
                Using request = New HttpRequestMessage(New HttpMethod("POST"), ApiSelectURLtoPost.ToString)
                    request.Headers.TryAddWithoutValidation("accept", "application/json")
                    request.Headers.TryAddWithoutValidation("accept-language", "ar")
                    request.Headers.TryAddWithoutValidation("Clearance-Status", ApiClearanceStatus)
                    request.Headers.TryAddWithoutValidation("Accept-Version", "V2")
                    request.Headers.TryAddWithoutValidation("Authorization", ApiAuthorization.Text)


                    request.Content = New StringContent("{" & vbLf & "  ""invoiceHash"": """ & TXTPIH.Text.Trim & """," & vbLf & "  ""uuid"": """ & TxTUUID.Text & """," & vbLf & "  ""invoice"": """ & XMLFileBase64Encod & """" & vbLf & "}")


                    request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json")



                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12


                    'Heder
                    Dim response = Await httpClient.SendAsync(request)
                    TxTAPICerHeder.Text = response.ToString


                    'Body
                    Dim contents = Await response.Content.ReadAsStringAsync()
                    TxTAPICerBody.Text = contents.ToString
                End Using
            End Using

        Catch ex As Exception
            MsgBox(ex.ToString)
            Exit Sub

        End Try
    End Sub

    Private Sub ButtonSend_Click(sender As Object, e As EventArgs) Handles ButtonSend.Click
        GoapiNowIDAsync()
    End Sub

    Private Sub RadioButtonR_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButtonR.CheckedChanged
        If RadioButtonR.Checked = True Then
            RadioButtonC.Checked = False
        End If
    End Sub

    Private Sub RadioButtonC_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButtonC.CheckedChanged
        If RadioButtonC.Checked = True Then
            RadioButtonR.Checked = False
        End If
    End Sub

    Private Sub TabPage3_Click(sender As Object, e As EventArgs) Handles TabPage3.Click

    End Sub
End Class
