<?xml version="1.0"?>
<package xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <metadata xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
    <id>ErikEJ.SqlCeScripting</id>
    <version>********</version>
    <authors>ErikEJ</authors>
    <owners>ErikEJ</owners>
    <licenseUrl>http://exportsqlce.codeplex.com/license</licenseUrl>
    <projectUrl>http://exportsqlce.codeplex.com/</projectUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>For SQL Server Compact, for easy inclusion of database scripting in your own application</description>
    <tags>sqlce sql compact script scripting</tags>
    <dependencies>
      <dependency id="Microsoft.SqlServer.Compact" version="4.0.8876.1" />
      <dependency id="QuickGraph" />
    </dependencies>
  </metadata>
  <files>
	  <file src="..\..\SqlCeScripting40.dll" target="lib\net" />
	  <file src="..\..\ISqlCeScripting.dll" target="lib\net" />
  </files>
</package>
