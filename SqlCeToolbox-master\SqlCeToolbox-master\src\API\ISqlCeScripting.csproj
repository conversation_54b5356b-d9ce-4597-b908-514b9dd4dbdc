﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B690CB18-0DC5-4C20-AEB3-F9F4B55B4601}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ErikEJ.SqlCeScripting</RootNamespace>
    <AssemblyName>ISqlCeScripting</AssemblyName>
    <TargetFrameworkVersion>v4.5.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>Key.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Generator\DataContextHelper.cs" />
    <Compile Include="Generator\DescriptionHelper.cs" />
    <Compile Include="Generator\ISqlCeHelper.cs" />
    <Compile Include="Generator\IGenerator.cs" />
    <Compile Include="Generator\RepositoryHelper.cs" />
    <Compile Include="Generator\SqlCeDiff.cs" />
    <Compile Include="Generator\SqlCommandReader.cs" />
    <Compile Include="Generator\SqlCommandReaderStreamed.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Models\Column.cs" />
    <Compile Include="Models\ColumnList.cs" />
    <Compile Include="Models\Constraint.cs" />
    <Compile Include="Models\Table.cs" />
    <Compile Include="Models\TableParameter.cs" />
    <Compile Include="Models\Trigger.cs" />
    <Compile Include="Models\View.cs" />
    <Compile Include="Models\DbDescription.cs" />
    <Compile Include="Models\Index.cs" />
    <Compile Include="Models\PrimaryKey.cs" />
    <Compile Include="Models\YesNoOptionEnum.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repositories\IRepository.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Key.snk" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>