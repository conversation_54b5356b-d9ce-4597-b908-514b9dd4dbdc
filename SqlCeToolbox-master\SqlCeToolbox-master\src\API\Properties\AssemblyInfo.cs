﻿using System.Reflection;
using System.Runtime.InteropServices;
[assembly: AssemblyTitle("SQL Compact Scripting Utility")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("http://erikej.blogspot.com")]
[assembly: AssemblyProduct("SQL Compact Scripting Utility")]
[assembly: AssemblyCopyright("Copyright © Bembeng Arifin/<PERSON> 2011")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly:ComVisible(false)]
[assembly: AssemblyVersion("********")]
[assembly: AssemblyFileVersion("********")]
[assembly: AssemblyInformationalVersion("********")]
