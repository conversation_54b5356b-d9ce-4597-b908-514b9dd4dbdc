﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:Setup"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_00C1A77F436159B4288672201CC179DC"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_00C1A77F436159B4288672201CC179DC"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_01E4628DF701FB9531FE657DFFA2092F"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_01E4628DF701FB9531FE657DFFA2092F"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_02AE2094B6533329D68BF48EE5EC82A8"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_02AE2094B6533329D68BF48EE5EC82A8"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "OwnerKey" = "8:_824D64246050BBF57CA8A13C883F4939"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "OwnerKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_00C1A77F436159B4288672201CC179DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "OwnerKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1418AA5D8B29D007182DBA98CA40BC02"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1418AA5D8B29D007182DBA98CA40BC02"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1418AA5D8B29D007182DBA98CA40BC02"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1418AA5D8B29D007182DBA98CA40BC02"
        "OwnerKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17A9E81E99B2A617C9B731828E7632F4"
        "OwnerKey" = "8:_E00AEF844E20736486E4234BAA697F00"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_17A9E81E99B2A617C9B731828E7632F4"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "OwnerKey" = "8:_4492E36DED39F8FFE3BFAD0BC24D6CA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2C637FBD4192D4BABBB4FD5E80F7E021"
        "OwnerKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2C637FBD4192D4BABBB4FD5E80F7E021"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2C637FBD4192D4BABBB4FD5E80F7E021"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2E6DE3EC1DAD96F43FDBBF1EE165EE83"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2E6DE3EC1DAD96F43FDBBF1EE165EE83"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_330A9158201C17ECCF4A08743F278456"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_330A9158201C17ECCF4A08743F278456"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3332D1B676F484658C275FC93A754044"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_366A8B3F6A6523DCF320D0DA5772FD8A"
        "OwnerKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_366A8B3F6A6523DCF320D0DA5772FD8A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "OwnerKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3D6D5EA6649C85335162B11ECE2C5534"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3D6D5EA6649C85335162B11ECE2C5534"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "OwnerKey" = "8:_99A00969A225E14D075C1839F0848208"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_42716B3591C19060AA77C860F0F4C3C4"
        "OwnerKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_42716B3591C19060AA77C860F0F4C3C4"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_43C052D98FCCBC722DF52F4725127249"
        "OwnerKey" = "8:_5DD220F4DD4128B8A91A40484A092684"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4492E36DED39F8FFE3BFAD0BC24D6CA3"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4492E36DED39F8FFE3BFAD0BC24D6CA3"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45E6CFA42BB40E7BDADA1FD860891206"
        "OwnerKey" = "8:_86AFC7069A15A5AE1C92D1FFB7747225"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45E6CFA42BB40E7BDADA1FD860891206"
        "OwnerKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45E6CFA42BB40E7BDADA1FD860891206"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_47BA2640BA4D331BC851CA7F4899135E"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_47BA2640BA4D331BC851CA7F4899135E"
        "OwnerKey" = "8:_51476C188337C59B8F7FB0426E9EBECD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_51476C188337C59B8F7FB0426E9EBECD"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_575F680F3B3EBD6FE0FDF3178ABEE904"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_575F680F3B3EBD6FE0FDF3178ABEE904"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_64B2690C874778247E8FC308B92CBB4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DD220F4DD4128B8A91A40484A092684"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_620E2C2DC217CB14B6FC1D84BEA270FF"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_620E2C2DC217CB14B6FC1D84BEA270FF"
        "OwnerKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_620E2C2DC217CB14B6FC1D84BEA270FF"
        "OwnerKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_620E2C2DC217CB14B6FC1D84BEA270FF"
        "OwnerKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_64B2690C874778247E8FC308B92CBB4B"
        "OwnerKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_64B2690C874778247E8FC308B92CBB4B"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_8CA4096A934BA20FF09F0E42065FC74A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "OwnerKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "OwnerKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "OwnerKey" = "8:_94A6A1D9E78A4A97FA680CF243E725C9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_722276742BD4E18B30A0E1663BC74E67"
        "OwnerKey" = "8:_A6A5E75931532763F3D7F3E0E1FC8CE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7307067CAEC887A1FEA2D6AF4D8DF6AC"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7307067CAEC887A1FEA2D6AF4D8DF6AC"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "OwnerKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8200B21A98DF67417E8CBFBEA9D8114E"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8200B21A98DF67417E8CBFBEA9D8114E"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_824D64246050BBF57CA8A13C883F4939"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_824D64246050BBF57CA8A13C883F4939"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_824D64246050BBF57CA8A13C883F4939"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "OwnerKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_86AFC7069A15A5AE1C92D1FFB7747225"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_86AFC7069A15A5AE1C92D1FFB7747225"
        "OwnerKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "OwnerKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8CA4096A934BA20FF09F0E42065FC74A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8CA4096A934BA20FF09F0E42065FC74A"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_94A6A1D9E78A4A97FA680CF243E725C9"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_94A6A1D9E78A4A97FA680CF243E725C9"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_99A00969A225E14D075C1839F0848208"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_99A00969A225E14D075C1839F0848208"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_99A00969A225E14D075C1839F0848208"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A1A1CE7C35A60190AC75B36B68C62B4F"
        "OwnerKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A1A1CE7C35A60190AC75B36B68C62B4F"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6A1889CCCDEFCA6415DF714AAB916EC"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6A1889CCCDEFCA6415DF714AAB916EC"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6A5E75931532763F3D7F3E0E1FC8CE0"
        "OwnerKey" = "8:_DA4D15F6AC97F826E44E44801B5ED624"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6A5E75931532763F3D7F3E0E1FC8CE0"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "OwnerKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AC4B0E2A76766D2E7A1BF68842B73159"
        "OwnerKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AC4B0E2A76766D2E7A1BF68842B73159"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ADC75A0DF6396FCBB2257609B5F9E936"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ADC75A0DF6396FCBB2257609B5F9E936"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_A6A1889CCCDEFCA6415DF714AAB916EC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_00C1A77F436159B4288672201CC179DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_3D6D5EA6649C85335162B11ECE2C5534"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_02AE2094B6533329D68BF48EE5EC82A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "OwnerKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_64B2690C874778247E8FC308B92CBB4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_A6A1889CCCDEFCA6415DF714AAB916EC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C128A57D77316D8FF5518ED3C90A6C32"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C128A57D77316D8FF5518ED3C90A6C32"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "OwnerKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_DA4D15F6AC97F826E44E44801B5ED624"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_A6A5E75931532763F3D7F3E0E1FC8CE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_01E4628DF701FB9531FE657DFFA2092F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C7BF933753FCCFF93B6E46D873BD99C7"
        "OwnerKey" = "8:_DA4D15F6AC97F826E44E44801B5ED624"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CE6A162AB02B82855AB9818F92FDACFD"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CE6A162AB02B82855AB9818F92FDACFD"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CF397E246AEEB9D023A16091B8754705"
        "OwnerKey" = "8:_ADC75A0DF6396FCBB2257609B5F9E936"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA4D15F6AC97F826E44E44801B5ED624"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "OwnerKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E00AEF844E20736486E4234BAA697F00"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F033DB7B585171B146BEE1852825F135"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F033DB7B585171B146BEE1852825F135"
        "OwnerKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F40FD359F26F3F4945F10AE369EDF1CF"
        "OwnerKey" = "8:_C128A57D77316D8FF5518ED3C90A6C32"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F40FD359F26F3F4945F10AE369EDF1CF"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F40FD359F26F3F4945F10AE369EDF1CF"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FD16CF1F6C445D16D9E25D00290A34BD"
        "OwnerKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FD54521B21F2C67A8E20754CADD10C5A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FD54521B21F2C67A8E20754CADD10C5A"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FFE0C86BF17925AF0258F2E4634DB86A"
        "OwnerKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FFE0C86BF17925AF0258F2E4634DB86A"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FFE0C86BF17925AF0258F2E4634DB86A"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_620E2C2DC217CB14B6FC1D84BEA270FF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4D31F5658C6A270D112A867F44F88CA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F241E4F20FB2817F1FC9381B1AC71B50"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8CA4096A934BA20FF09F0E42065FC74A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2E6DE3EC1DAD96F43FDBBF1EE165EE83"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_28F0B2B7AEBA4D12461AD4526D31719F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C44C212A98B7CF68BEB94938E4F7AE20"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2B929FE848F52355ADDE36ABA39AAF4F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_42716B3591C19060AA77C860F0F4C3C4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0B0F17C887DD97104A612433A05BE64F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6532FB4D637DC847BDF40025B307E1E8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4492E36DED39F8FFE3BFAD0BC24D6CA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_245B678F1BDCC51243A2B0C90071ADC3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_00C1A77F436159B4288672201CC179DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3D6D5EA6649C85335162B11ECE2C5534"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_991A94A95A099D8CF87941CD817FDF11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C2D01A7E8D6001D37DD8575A5E18A3FD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C5774DFB9CBA77CA2EB867DBC5588692"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_99A00969A225E14D075C1839F0848208"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_784315B8A616CFF46394A5422D5F2839"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3F6541108ECD5CBD1F78E2A10151DDDB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C128A57D77316D8FF5518ED3C90A6C32"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F40FD359F26F3F4945F10AE369EDF1CF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_ADC75A0DF6396FCBB2257609B5F9E936"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F9637B100F84CE8BA562FE32698BF13D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D2CE13D0E65460C3774C4AC9051A1376"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_82F817D15CF8C1C8D516D77560791897"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_824D64246050BBF57CA8A13C883F4939"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0602627B7A92E462406E691C9F11D246"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7307067CAEC887A1FEA2D6AF4D8DF6AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_74F8BBE1C0FE04E46573973C080483D7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C4F44B4A022C1106DC96656D39B97F3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_44E6970BE49492166391DE9F583A4142"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_17062B92EA539A0510287C3F387DBDDA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8ABA58999D7C5407A1669B7B7B4649C0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8200B21A98DF67417E8CBFBEA9D8114E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_01E4628DF701FB9531FE657DFFA2092F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5A15F6D3C8D40085AA9E566911AECEEB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A6A1889CCCDEFCA6415DF714AAB916EC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D3CAA9552AA1D3447A0A2866BB17A843"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B7721D22368FF8E9812FCF0C2BA5EF46"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B0F1DDD7A972BA68DDAE6B549024B67E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1418AA5D8B29D007182DBA98CA40BC02"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E00AEF844E20736486E4234BAA697F00"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_17A9E81E99B2A617C9B731828E7632F4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_51476C188337C59B8F7FB0426E9EBECD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_47BA2640BA4D331BC851CA7F4899135E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_98C2F0372E5DAA6BB997F690E7D67903"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_366A8B3F6A6523DCF320D0DA5772FD8A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_86AFC7069A15A5AE1C92D1FFB7747225"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45E6CFA42BB40E7BDADA1FD860891206"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F033DB7B585171B146BEE1852825F135"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5922234DF1B4296159966BAC758E8BA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FFE0C86BF17925AF0258F2E4634DB86A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FD54521B21F2C67A8E20754CADD10C5A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F7D5E771B1B1A8011CD902B30F2D3EA4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F73232DF9AB444992C6FCF7EF90F8917"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DF9F7216B07E018AF3ED31557EE68FF2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DA4D15F6AC97F826E44E44801B5ED624"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CE6A162AB02B82855AB9818F92FDACFD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C74A02425BFAEEEF17C7D954C227451D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C65FD1C1F56D948D303AC9D91A5D89DE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C659426FF69799AEF5F004CED2ED6325"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C1763EC714B0AA50398E36A7B4DF1BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B36B52E6F0699D416021E26B34D2FFFD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B0D315EDF8332BEB573021D5A3412547"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AC4B0E2A76766D2E7A1BF68842B73159"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A87F72052CFCA3D4C9712A00D5856105"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A6A5E75931532763F3D7F3E0E1FC8CE0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A20ECAC2DD872977D07D577148B70804"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A1A1CE7C35A60190AC75B36B68C62B4F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_94A6A1D9E78A4A97FA680CF243E725C9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8F7963D3C82B471DA88E1C4700B607AB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_814F9736AB5E359E938E7A1F0AA897D2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_793ACAF372FDAC8047B5F4B1E25D40AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6F1430FEB4341967AAA1890C3CAC7FE3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6863AFD1D728A46A52DA27DB39452D2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_64B2690C874778247E8FC308B92CBB4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6468A9C851D13E4CAADC2DDA15C48B22"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5DD220F4DD4128B8A91A40484A092684"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_575F680F3B3EBD6FE0FDF3178ABEE904"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_39F38DD25DF1FC8C591D82BEEF405776"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_398756C760ADC8E1EF3E13BAD38F452B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3332D1B676F484658C275FC93A754044"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_330A9158201C17ECCF4A08743F278456"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2C637FBD4192D4BABBB4FD5E80F7E021"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1CB70780B4DBC22959BA408A273BC05B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_09EAB6F1CC28D09EC91A1E2A382C34B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0373A139268C5FBBBCF8BD7166C4E835"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_02AE2094B6533329D68BF48EE5EC82A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D9BE09CFF60D4218BE321C3664FBD76E"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:bin\\Debug\\Setup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Net.Framework.3.5.SP1"
                    {
                    "Name" = "8:.NET Framework 3.5 SP1"
                    "ProductCode" = "8:Microsoft.Net.Framework.3.5.SP1"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Windows.Installer.3.1"
                    {
                    "Name" = "8:Windows Installer 3.1"
                    "ProductCode" = "8:Microsoft.Windows.Installer.3.1"
                    }
                }
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:bin\\Release\\Setup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Net.Framework.3.5.SP1"
                    {
                    "Name" = "8:.NET Framework 3.5 SP1"
                    "ProductCode" = "8:Microsoft.Net.Framework.3.5.SP1"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Windows.Installer.3.1"
                    {
                    "Name" = "8:Windows Installer 3.1"
                    "ProductCode" = "8:Microsoft.Windows.Installer.3.1"
                    }
                }
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_0FFD838CA35D47A08226CEF19F147A77"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:3.5.30729"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=76617"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_00C1A77F436159B4288672201CC179DC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.DataTools.Interop, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_00C1A77F436159B4288672201CC179DC"
                    {
                    "Name" = "8:Microsoft.VisualStudio.DataTools.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.DataTools.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_01E4628DF701FB9531FE657DFFA2092F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ServiceBrokerEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_01E4628DF701FB9531FE657DFFA2092F"
                    {
                    "Name" = "8:Microsoft.SqlServer.ServiceBrokerEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.ServiceBrokerEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_02AE2094B6533329D68BF48EE5EC82A8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Debugger.Interop, Version=8.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_02AE2094B6533329D68BF48EE5EC82A8"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Debugger.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Debugger.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0373A139268C5FBBBCF8BD7166C4E835"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlTDiagM, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0373A139268C5FBBBCF8BD7166C4E835"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlTDiagM.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlTDiagM.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0602627B7A92E462406E691C9F11D246"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Data.ConnectionUI, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0602627B7A92E462406E691C9F11D246"
                    {
                    "Name" = "8:Microsoft.Data.ConnectionUI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Data.ConnectionUI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_09EAB6F1CC28D09EC91A1E2A382C34B4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Shell.Interop, Version=7.1.40304.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_09EAB6F1CC28D09EC91A1E2A382C34B4"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Shell.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Shell.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0B0F17C887DD97104A612433A05BE64F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SqlStudio.Controls, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0B0F17C887DD97104A612433A05BE64F"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SqlStudio.Controls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SqlStudio.Controls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1418AA5D8B29D007182DBA98CA40BC02"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.VSHelp, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1418AA5D8B29D007182DBA98CA40BC02"
                    {
                    "Name" = "8:Microsoft.VisualStudio.VSHelp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.VSHelp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_17062B92EA539A0510287C3F387DBDDA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.DataWarehouse.SQM, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_17062B92EA539A0510287C3F387DBDDA"
                    {
                    "Name" = "8:Microsoft.DataWarehouse.SQM.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.DataWarehouse.SQM.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_17A9E81E99B2A617C9B731828E7632F4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Data.ConnectionUI, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_17A9E81E99B2A617C9B731828E7632F4"
                    {
                    "Name" = "8:Microsoft.Data.ConnectionUI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Data.ConnectionUI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1CB70780B4DBC22959BA408A273BC05B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_1CB70780B4DBC22959BA408A273BC05B"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_245B678F1BDCC51243A2B0C90071ADC3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.DataWarehouse, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_245B678F1BDCC51243A2B0C90071ADC3"
                    {
                    "Name" = "8:Microsoft.DataWarehouse.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.DataWarehouse.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_28F0B2B7AEBA4D12461AD4526D31719F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SqlMgmt, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_28F0B2B7AEBA4D12461AD4526D31719F"
                    {
                    "Name" = "8:SqlMgmt.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SqlMgmt.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2B929FE848F52355ADDE36ABA39AAF4F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Data, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2B929FE848F52355ADDE36ABA39AAF4F"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Data.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Data.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2C637FBD4192D4BABBB4FD5E80F7E021"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Diagnostics.STrace, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2C637FBD4192D4BABBB4FD5E80F7E021"
                    {
                    "Name" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2E6DE3EC1DAD96F43FDBBF1EE165EE83"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.DataTransformationServices.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2E6DE3EC1DAD96F43FDBBF1EE165EE83"
                    {
                    "Name" = "8:Microsoft.DataTransformationServices.Interfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.DataTransformationServices.Interfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_330A9158201C17ECCF4A08743F278456"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Instapi, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_330A9158201C17ECCF4A08743F278456"
                    {
                    "Name" = "8:Microsoft.SqlServer.Instapi.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Instapi.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3332D1B676F484658C275FC93A754044"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Smo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3332D1B676F484658C275FC93A754044"
                    {
                    "Name" = "8:Microsoft.SqlServer.Smo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Smo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_366A8B3F6A6523DCF320D0DA5772FD8A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_366A8B3F6A6523DCF320D0DA5772FD8A"
                    {
                    "Name" = "8:System.Data.SqlServerCe.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Data.SqlServerCe.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_398756C760ADC8E1EF3E13BAD38F452B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.RegisteredServers, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_398756C760ADC8E1EF3E13BAD38F452B"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.RegisteredServers.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.RegisteredServers.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_39F38DD25DF1FC8C591D82BEEF405776"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.GridControl, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_39F38DD25DF1FC8C591D82BEEF405776"
                    {
                    "Name" = "8:Microsoft.SqlServer.GridControl.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.GridControl.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3D6D5EA6649C85335162B11ECE2C5534"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Data.Interop, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3D6D5EA6649C85335162B11ECE2C5534"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Data.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Data.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3F6541108ECD5CBD1F78E2A10151DDDB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.Diagnostics, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_3F6541108ECD5CBD1F78E2A10151DDDB"
                    {
                    "Name" = "8:Microsoft.ReportingServices.Diagnostics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ReportingServices.Diagnostics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_42716B3591C19060AA77C860F0F4C3C4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.ConnectionUI, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_42716B3591C19060AA77C860F0F4C3C4"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.ConnectionUI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.ConnectionUI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_43C052D98FCCBC722DF52F4725127249"
            {
            "SourcePath" = "8:vsmso.olb"
            "TargetName" = "8:vsmso.olb"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4492E36DED39F8FFE3BFAD0BC24D6CA3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices.Graphing, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4492E36DED39F8FFE3BFAD0BC24D6CA3"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.Graphing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.Graphing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_44E6970BE49492166391DE9F583A4142"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SqlWorkbench.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_44E6970BE49492166391DE9F583A4142"
                    {
                    "Name" = "8:SqlWorkbench.Interfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SqlWorkbench.Interfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45E6CFA42BB40E7BDADA1FD860891206"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:QuickGraph, Version=3.6.61114.0, Culture=neutral, PublicKeyToken=f3fb40175eec2af3, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_45E6CFA42BB40E7BDADA1FD860891206"
                    {
                    "Name" = "8:QuickGraph.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:QuickGraph.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_47BA2640BA4D331BC851CA7F4899135E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Kent.Boogaart.HelperTrinity, Version=1.3.0.0, Culture=neutral, PublicKeyToken=cc96fa93a217f7a1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_47BA2640BA4D331BC851CA7F4899135E"
                    {
                    "Name" = "8:Kent.Boogaart.HelperTrinity.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Kent.Boogaart.HelperTrinity.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4D31F5658C6A270D112A867F44F88CA1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SqlStudio.Explorer, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4D31F5658C6A270D112A867F44F88CA1"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SqlStudio.Explorer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SqlStudio.Explorer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_51476C188337C59B8F7FB0426E9EBECD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Kent.Boogaart.KBCsv, Version=1.2.0.0, Culture=neutral, PublicKeyToken=cc96fa93a217f7a1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_51476C188337C59B8F7FB0426E9EBECD"
                    {
                    "Name" = "8:Kent.Boogaart.KBCsv.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Kent.Boogaart.KBCsv.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_575F680F3B3EBD6FE0FDF3178ABEE904"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.ProjectAggregator, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_575F680F3B3EBD6FE0FDF3178ABEE904"
                    {
                    "Name" = "8:Microsoft.VisualStudio.ProjectAggregator.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.ProjectAggregator.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5922234DF1B4296159966BAC758E8BA3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Controls, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_5922234DF1B4296159966BAC758E8BA3"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Controls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Controls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5A15F6D3C8D40085AA9E566911AECEEB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.RegSvrEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5A15F6D3C8D40085AA9E566911AECEEB"
                    {
                    "Name" = "8:Microsoft.SqlServer.RegSvrEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.RegSvrEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5DD220F4DD4128B8A91A40484A092684"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.CommandBars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5DD220F4DD4128B8A91A40484A092684"
                    {
                    "Name" = "8:Microsoft.VisualStudio.CommandBars.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.CommandBars.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_620E2C2DC217CB14B6FC1D84BEA270FF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlClrProvider, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_620E2C2DC217CB14B6FC1D84BEA270FF"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6468A9C851D13E4CAADC2DDA15C48B22"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Data.Services, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6468A9C851D13E4CAADC2DDA15C48B22"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Data.Services.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Data.Services.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_64B2690C874778247E8FC308B92CBB4B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.MultiServerConnection, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_64B2690C874778247E8FC308B92CBB4B"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.MultiServerConnection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.MultiServerConnection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6532FB4D637DC847BDF40025B307E1E8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SDK.SqlStudio, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6532FB4D637DC847BDF40025B307E1E8"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.SqlStudio.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Sdk.SqlStudio.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6863AFD1D728A46A52DA27DB39452D2A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.TextManager.Interop.8.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6863AFD1D728A46A52DA27DB39452D2A"
                    {
                    "Name" = "8:Microsoft.VisualStudio.TextManager.Interop.8.0.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.TextManager.Interop.8.0.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6F1430FEB4341967AAA1890C3CAC7FE3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SqlParser, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6F1430FEB4341967AAA1890C3CAC7FE3"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SqlParser.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SqlParser.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_722276742BD4E18B30A0E1663BC74E67"
            {
            "SourcePath" = "8:dte80.olb"
            "TargetName" = "8:dte80.olb"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7307067CAEC887A1FEA2D6AF4D8DF6AC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DdsShapesLib, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7307067CAEC887A1FEA2D6AF4D8DF6AC"
                    {
                    "Name" = "8:DdsShapesLib.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DdsShapesLib.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_74F8BBE1C0FE04E46573973C080483D7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SString, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_74F8BBE1C0FE04E46573973C080483D7"
                    {
                    "Name" = "8:Microsoft.SqlServer.SString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_784315B8A616CFF46394A5422D5F2839"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices.Controls, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_784315B8A616CFF46394A5422D5F2839"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.Controls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.Controls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_793ACAF372FDAC8047B5F4B1E25D40AF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Dac, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_793ACAF372FDAC8047B5F4B1E25D40AF"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Dac.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Dac.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_814F9736AB5E359E938E7A1F0AA897D2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Shell, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_814F9736AB5E359E938E7A1F0AA897D2"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Shell.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Shell.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8200B21A98DF67417E8CBFBEA9D8114E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Types, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8200B21A98DF67417E8CBFBEA9D8114E"
                    {
                    "Name" = "8:Microsoft.SqlServer.Types.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Types.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_824D64246050BBF57CA8A13C883F4939"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Data.ConnectionUI.Dialog, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_824D64246050BBF57CA8A13C883F4939"
                    {
                    "Name" = "8:Microsoft.Data.ConnectionUI.Dialog.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Data.ConnectionUI.Dialog.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_82F817D15CF8C1C8D516D77560791897"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_82F817D15CF8C1C8D516D77560791897"
                    {
                    "Name" = "8:Microsoft.ReportingServices.Interfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ReportingServices.Interfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_86AFC7069A15A5AE1C92D1FFB7747225"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:QuickGraph.Data, Version=3.6.61114.0, Culture=neutral, PublicKeyToken=3681435cabd17ad2, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_86AFC7069A15A5AE1C92D1FFB7747225"
                    {
                    "Name" = "8:QuickGraph.Data.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:QuickGraph.Data.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8ABA58999D7C5407A1669B7B7B4649C0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Sqm, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8ABA58999D7C5407A1669B7B7B4649C0"
                    {
                    "Name" = "8:Microsoft.SqlServer.Sqm.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Sqm.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8CA4096A934BA20FF09F0E42065FC74A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Sdk.Scripting, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8CA4096A934BA20FF09F0E42065FC74A"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.Scripting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Sdk.Scripting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8F7963D3C82B471DA88E1C4700B607AB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8F7963D3C82B471DA88E1C4700B607AB"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_94A6A1D9E78A4A97FA680CF243E725C9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.DacSerialization, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_94A6A1D9E78A4A97FA680CF243E725C9"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.DacSerialization.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.DacSerialization.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_98C2F0372E5DAA6BB997F690E7D67903"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SqlCeScripting, Version=*******, Culture=neutral, PublicKeyToken=3681435cabd17ad2, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_98C2F0372E5DAA6BB997F690E7D67903"
                    {
                    "Name" = "8:SqlCeScripting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SqlCeScripting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_991A94A95A099D8CF87941CD817FDF11"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Data, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_991A94A95A099D8CF87941CD817FDF11"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Data.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Data.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_99A00969A225E14D075C1839F0848208"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.RsClient, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_99A00969A225E14D075C1839F0848208"
                    {
                    "Name" = "8:Microsoft.ReportingServices.RsClient.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ReportingServices.RsClient.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A1A1CE7C35A60190AC75B36B68C62B4F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Data.Core, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A1A1CE7C35A60190AC75B36B68C62B4F"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Data.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Data.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A20ECAC2DD872977D07D577148B70804"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.DlgGrid, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A20ECAC2DD872977D07D577148B70804"
                    {
                    "Name" = "8:Microsoft.SqlServer.DlgGrid.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.DlgGrid.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A6A1889CCCDEFCA6415DF714AAB916EC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Collector, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A6A1889CCCDEFCA6415DF714AAB916EC"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Collector.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Collector.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A6A5E75931532763F3D7F3E0E1FC8CE0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:EnvDTE80, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A6A5E75931532763F3D7F3E0E1FC8CE0"
                    {
                    "Name" = "8:EnvDTE80.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:EnvDTE80.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A87F72052CFCA3D4C9712A00D5856105"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.DataStorage, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A87F72052CFCA3D4C9712A00D5856105"
                    {
                    "Name" = "8:Microsoft.SqlServer.DataStorage.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.DataStorage.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AC4B0E2A76766D2E7A1BF68842B73159"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.MSXML, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_AC4B0E2A76766D2E7A1BF68842B73159"
                    {
                    "Name" = "8:Microsoft.MSXML.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.MSXML.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_ADC75A0DF6396FCBB2257609B5F9E936"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Interop.ShDocVw, Version=1.1.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_ADC75A0DF6396FCBB2257609B5F9E936"
                    {
                    "Name" = "8:Interop.ShDocVw.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Interop.ShDocVw.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B0D315EDF8332BEB573021D5A3412547"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Sdk.Sfc, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B0D315EDF8332BEB573021D5A3412547"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B0F1DDD7A972BA68DDAE6B549024B67E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.VSHelp80, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B0F1DDD7A972BA68DDAE6B549024B67E"
                    {
                    "Name" = "8:Microsoft.VisualStudio.VSHelp80.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.VSHelp80.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B36B52E6F0699D416021E26B34D2FFFD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.OLE.Interop, Version=7.1.40304.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B36B52E6F0699D416021E26B34D2FFFD"
                    {
                    "Name" = "8:Microsoft.VisualStudio.OLE.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.OLE.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B7721D22368FF8E9812FCF0C2BA5EF46"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ConnectionInfo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B7721D22368FF8E9812FCF0C2BA5EF46"
                    {
                    "Name" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BF902C22FD97D8D6C13DA8EA69D6D3A1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ConnectionDlg, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BF902C22FD97D8D6C13DA8EA69D6D3A1"
                    {
                    "Name" = "8:ConnectionDlg.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ConnectionDlg.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C128A57D77316D8FF5518ED3C90A6C32"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Office.Interop.Owc11, Version=11.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c"
                "ScatterAssemblies"
                {
                    "_C128A57D77316D8FF5518ED3C90A6C32"
                    {
                    "Name" = "8:Microsoft.Office.Interop.Owc11.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Office.Interop.Owc11.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C1763EC714B0AA50398E36A7B4DF1BDC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.NetEnterpriseServers.ExceptionMessageBox, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C1763EC714B0AA50398E36A7B4DF1BDC"
                    {
                    "Name" = "8:Microsoft.NetEnterpriseServers.ExceptionMessageBox.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.NetEnterpriseServers.ExceptionMessageBox.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C2D01A7E8D6001D37DD8575A5E18A3FD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.QueryDesigners, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_C2D01A7E8D6001D37DD8575A5E18A3FD"
                    {
                    "Name" = "8:Microsoft.ReportingServices.QueryDesigners.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ReportingServices.QueryDesigners.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C44C212A98B7CF68BEB94938E4F7AE20"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlTools.VSIntegration, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_C44C212A98B7CF68BEB94938E4F7AE20"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlTools.VSIntegration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlTools.VSIntegration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C4F44B4A022C1106DC96656D39B97F3A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.UserSettings, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C4F44B4A022C1106DC96656D39B97F3A"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.UserSettings.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.UserSettings.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C5774DFB9CBA77CA2EB867DBC5588692"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.DataExtensions, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_C5774DFB9CBA77CA2EB867DBC5588692"
                    {
                    "Name" = "8:Microsoft.ReportingServices.DataExtensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ReportingServices.DataExtensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C659426FF69799AEF5F004CED2ED6325"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:EnvDTE, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C659426FF69799AEF5F004CED2ED6325"
                    {
                    "Name" = "8:EnvDTE.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:EnvDTE.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C65FD1C1F56D948D303AC9D91A5D89DE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.CustomControls, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C65FD1C1F56D948D303AC9D91A5D89DE"
                    {
                    "Name" = "8:Microsoft.SqlServer.CustomControls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.CustomControls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C74A02425BFAEEEF17C7D954C227451D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C74A02425BFAEEEF17C7D954C227451D"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_C7BF933753FCCFF93B6E46D873BD99C7"
            {
            "SourcePath" = "8:dte90.olb"
            "TargetName" = "8:dte90.olb"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CE6A162AB02B82855AB9818F92FDACFD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:msddsp, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CE6A162AB02B82855AB9818F92FDACFD"
                    {
                    "Name" = "8:msddsp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:msddsp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_CF397E246AEEB9D023A16091B8754705"
            {
            "SourcePath" = "8:ieframe.dll"
            "TargetName" = "8:ieframe.dll"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:4"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D2CE13D0E65460C3774C4AC9051A1376"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices.AdomdClient, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D2CE13D0E65460C3774C4AC9051A1376"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.AdomdClient.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.AdomdClient.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D3CAA9552AA1D3447A0A2866BB17A843"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Dmf, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D3CAA9552AA1D3447A0A2866BB17A843"
                    {
                    "Name" = "8:Microsoft.SqlServer.Dmf.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Dmf.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DA4D15F6AC97F826E44E44801B5ED624"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:EnvDTE90, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DA4D15F6AC97F826E44E44801B5ED624"
                    {
                    "Name" = "8:EnvDTE90.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:EnvDTE90.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DF9F7216B07E018AF3ED31557EE68FF2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.TextManager.Interop, Version=7.1.40304.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DF9F7216B07E018AF3ED31557EE68FF2"
                    {
                    "Name" = "8:Microsoft.VisualStudio.TextManager.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.TextManager.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E00AEF844E20736486E4234BAA697F00"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Data.ConnectionUI.Dialog, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E00AEF844E20736486E4234BAA697F00"
                    {
                    "Name" = "8:Microsoft.Data.ConnectionUI.Dialog.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Data.ConnectionUI.Dialog.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.WizardFramework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E4EEB5F271BC0B1CC3FC975FF1CB7ECF"
                    {
                    "Name" = "8:Microsoft.SqlServer.WizardFramework.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.WizardFramework.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F033DB7B585171B146BEE1852825F135"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ISqlCeScripting, Version=*******, Culture=neutral, PublicKeyToken=3681435cabd17ad2, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F033DB7B585171B146BEE1852825F135"
                    {
                    "Name" = "8:ISqlCeScripting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ISqlCeScripting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F241E4F20FB2817F1FC9381B1AC71B50"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ObjectExplorer, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_F241E4F20FB2817F1FC9381B1AC71B50"
                    {
                    "Name" = "8:ObjectExplorer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ObjectExplorer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F40FD359F26F3F4945F10AE369EDF1CF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:mscomctl, Version=10.0.4504.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                "ScatterAssemblies"
                {
                    "_F40FD359F26F3F4945F10AE369EDF1CF"
                    {
                    "Name" = "8:mscomctl.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:mscomctl.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F73232DF9AB444992C6FCF7EF90F8917"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.VisualStudio.Shell.Interop.8.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F73232DF9AB444992C6FCF7EF90F8917"
                    {
                    "Name" = "8:Microsoft.VisualStudio.Shell.Interop.8.0.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.VisualStudio.Shell.Interop.8.0.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F7D5E771B1B1A8011CD902B30F2D3EA4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.DataWarehouse.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F7D5E771B1B1A8011CD902B30F2D3EA4"
                    {
                    "Name" = "8:Microsoft.DataWarehouse.Interfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.DataWarehouse.Interfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F9637B100F84CE8BA562FE32698BF13D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:MDXQueryGenerator, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F9637B100F84CE8BA562FE32698BF13D"
                    {
                    "Name" = "8:MDXQueryGenerator.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:MDXQueryGenerator.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_FD16CF1F6C445D16D9E25D00290A34BD"
            {
            "SourcePath" = "8:dte80a.olb"
            "TargetName" = "8:dte80a.olb"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FD54521B21F2C67A8E20754CADD10C5A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices.Xmla, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FD54521B21F2C67A8E20754CADD10C5A"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.Xmla.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.Xmla.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FFE0C86BF17925AF0258F2E4634DB86A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ExceptionMessageBox, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FFE0C86BF17925AF0258F2E4634DB86A"
                    {
                    "Name" = "8:Microsoft.ExceptionMessageBox.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.ExceptionMessageBox.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:TRUE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_22EB186404DB482588291EC19DB0BDD9"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_728CBA6BCE324F7D83DD659E51A5BEC8"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_F44CBB8D25974487BB67BE5BFD7A6D8B"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:ExportSqlCe"
        "ProductCode" = "8:{BF30E026-4635-4C08-BEFF-C88D850F9A9E}"
        "PackageCode" = "8:{28A043E5-FA0C-46A1-8136-79ABE1EF3316}"
        "UpgradeCode" = "8:{12E391ED-E6CE-4233-A3DE-584CD3F8288A}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:TRUE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:TRUE"
        "ProductVersion" = "8:3.5.6"
        "Manufacturer" = "8:ErikEJ"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:http://www.codeplex.com/ExportSqlCE"
        "Title" = "8:Setup"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Erik Ejlskov Jensen"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:ExportSqlCe installer"
        "ARPURLINFOABOUT" = "8:http://erikej.blogspot.com"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_5A3B3B8ADA9244888FDD5350E4F79FA0"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_D36925BD94A543C68183A93BB3FEA9ED"
                            {
                            "Name" = "8:Microsoft"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_81599391008644BD88D550200DE414EC"
                                    {
                                    "Name" = "8:Microsoft SQL Server"
                                    "Condition" = "8:"
                                    "AlwaysCreate" = "11:FALSE"
                                    "DeleteAtUninstall" = "11:FALSE"
                                    "Transitive" = "11:FALSE"
                                        "Keys"
                                        {
                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_F11745C53D674B71B40A3C3D571B4012"
                                            {
                                            "Name" = "8:100"
                                            "Condition" = "8:"
                                            "AlwaysCreate" = "11:FALSE"
                                            "DeleteAtUninstall" = "11:FALSE"
                                            "Transitive" = "11:FALSE"
                                                "Keys"
                                                {
                                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_2E5D1B49007F464BBFA6685482E1C752"
                                                    {
                                                    "Name" = "8:Tools"
                                                    "Condition" = "8:"
                                                    "AlwaysCreate" = "11:FALSE"
                                                    "DeleteAtUninstall" = "11:FALSE"
                                                    "Transitive" = "11:FALSE"
                                                        "Keys"
                                                        {
                                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_87CBEEDF2C704F0A88DC5F7143946D02"
                                                            {
                                                            "Name" = "8:Shell"
                                                            "Condition" = "8:"
                                                            "AlwaysCreate" = "11:FALSE"
                                                            "DeleteAtUninstall" = "11:FALSE"
                                                            "Transitive" = "11:FALSE"
                                                                "Keys"
                                                                {
                                                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_F54CC74EDE434EB4AC418D09A2E579C9"
                                                                    {
                                                                    "Name" = "8:Addins"
                                                                    "Condition" = "8:"
                                                                    "AlwaysCreate" = "11:FALSE"
                                                                    "DeleteAtUninstall" = "11:FALSE"
                                                                    "Transitive" = "11:FALSE"
                                                                        "Keys"
                                                                        {
                                                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_598A87A59F5B4918BC5CF9DB94547C79"
                                                                            {
                                                                            "Name" = "8:SqlCeScripter.Connect"
                                                                            "Condition" = "8:"
                                                                            "AlwaysCreate" = "11:FALSE"
                                                                            "DeleteAtUninstall" = "11:TRUE"
                                                                            "Transitive" = "11:FALSE"
                                                                                "Keys"
                                                                                {
                                                                                }
                                                                                "Values"
                                                                                {
                                                                                    "{ADCFDA98-8FDD-45E4-90BC-E3D20B029870}:_4E56CC7A1FC8497389CF5DB3A1576FEA"
                                                                                    {
                                                                                    "Name" = "8:LoadBehavior"
                                                                                    "Condition" = "8:"
                                                                                    "Transitive" = "11:FALSE"
                                                                                    "ValueTypes" = "3:3"
                                                                                    "Value" = "3:1"
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                        "Values"
                                                                        {
                                                                        }
                                                                    }
                                                                }
                                                                "Values"
                                                                {
                                                                }
                                                            }
                                                        }
                                                        "Values"
                                                        {
                                                        }
                                                    }
                                                }
                                                "Values"
                                                {
                                                }
                                            }
                                        }
                                        "Values"
                                        {
                                        }
                                    }
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_F7FB714B9C044441AF666D8B2743FCF3"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_D4ACD545223940BC9DD74C09C4C0EB18"
                            {
                            "Name" = "8:Microsoft"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_E71C158C50FC4B05A62B7FA58DD338BB"
                                    {
                                    "Name" = "8:Microsoft SQL Server"
                                    "Condition" = "8:"
                                    "AlwaysCreate" = "11:FALSE"
                                    "DeleteAtUninstall" = "11:FALSE"
                                    "Transitive" = "11:FALSE"
                                        "Keys"
                                        {
                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_BF6CDCB002334103AD1A0FBC0E757EF5"
                                            {
                                            "Name" = "8:100"
                                            "Condition" = "8:"
                                            "AlwaysCreate" = "11:FALSE"
                                            "DeleteAtUninstall" = "11:FALSE"
                                            "Transitive" = "11:FALSE"
                                                "Keys"
                                                {
                                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_0E6E028B221648FD80DEFC64F6AC505F"
                                                    {
                                                    "Name" = "8:Tools"
                                                    "Condition" = "8:"
                                                    "AlwaysCreate" = "11:FALSE"
                                                    "DeleteAtUninstall" = "11:FALSE"
                                                    "Transitive" = "11:FALSE"
                                                        "Keys"
                                                        {
                                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_FED7C16D8DA14AF69D265EB13614CF54"
                                                            {
                                                            "Name" = "8:Shell"
                                                            "Condition" = "8:"
                                                            "AlwaysCreate" = "11:FALSE"
                                                            "DeleteAtUninstall" = "11:FALSE"
                                                            "Transitive" = "11:FALSE"
                                                                "Keys"
                                                                {
                                                                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_E601733EB5E94B289009F242ACC239F3"
                                                                    {
                                                                    "Name" = "8:Addins"
                                                                    "Condition" = "8:"
                                                                    "AlwaysCreate" = "11:FALSE"
                                                                    "DeleteAtUninstall" = "11:FALSE"
                                                                    "Transitive" = "11:FALSE"
                                                                        "Keys"
                                                                        {
                                                                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_C7B869120BD749CCBC2CF6295FC5AC5A"
                                                                            {
                                                                            "Name" = "8:SqlCeScripter.Connect"
                                                                            "Condition" = "8:"
                                                                            "AlwaysCreate" = "11:FALSE"
                                                                            "DeleteAtUninstall" = "11:TRUE"
                                                                            "Transitive" = "11:FALSE"
                                                                                "Keys"
                                                                                {
                                                                                }
                                                                                "Values"
                                                                                {
                                                                                    "{ADCFDA98-8FDD-45E4-90BC-E3D20B029870}:_CDC44977B7A24A68925BB72901A98099"
                                                                                    {
                                                                                    "Name" = "8:LoadBehavior"
                                                                                    "Condition" = "8:"
                                                                                    "Transitive" = "11:FALSE"
                                                                                    "ValueTypes" = "3:3"
                                                                                    "Value" = "3:1"
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                        "Values"
                                                                        {
                                                                        }
                                                                    }
                                                                }
                                                                "Values"
                                                                {
                                                                }
                                                            }
                                                        }
                                                        "Values"
                                                        {
                                                        }
                                                    }
                                                }
                                                "Values"
                                                {
                                                }
                                            }
                                        }
                                        "Values"
                                        {
                                        }
                                    }
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
        }
        "UserInterface"
        {
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_5DEACEF2C2284A64A9F0B6F53E36E58F"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_6381E631049E4CF8BC88D352E4310290"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_16E0573AED5F4404B6ACD5EA7E438D15"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_DC8D2785C7FD47B58EEED8A28237E75C"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_FFF8CFFD5E66420D9C5D7B4B58E9F3A0"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_6D802576AE174CF58CB56984E7175BCA"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_9A2FE890E6C94EC29B58F9C9A4E6CD7E"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_6E4A91FA95FD48FBBCF7D59E7D5799D0"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_75ACBAC3AA77429E85CEB9990AF2C951"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_83D6A6828BF24F6CB4E02577D3B5504C"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_A58CCFCC6FCE4AB9BC6E4485783FE9E3"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_5E945D4BA6CC40E1BA72C2EB499BA344"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_DA4B7022FA7E48388F41D1F3A52E266C"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_9FF4BFA20EED4EB8953E7E8F4E9988D9"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_EE2C2E29E5594D389AC28B98350BAB0E"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_0F3DC5B3DE9143FAAEF0C38D658CF918"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_3F46528276964F70A82D5B66DD051024"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_DF8ACFDF704B4C25B34E93F2AF2C3424"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_D9BE09CFF60D4218BE321C3664FBD76E"
            {
            "SourcePath" = "8:..\\obj\\Release\\SqlCeScripter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_728CBA6BCE324F7D83DD659E51A5BEC8"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:2"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{2283F49B-5BAF-4A43-B636-48A0202B9C2E}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}
