﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{2283F49B-5BAF-4A43-B636-48A0202B9C2E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <StartupObject>
    </StartupObject>
    <NoStandardLibraries>false</NoStandardLibraries>
    <AssemblyName>SqlCeScripter</AssemblyName>
    <RootNamespace>SqlCeScripter</RootNamespace>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <IncrementalBuild>false</IncrementalBuild>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <CodeAnalysisIgnoreGeneratedCode>true</CodeAnalysisIgnoreGeneratedCode>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <IncrementalBuild>false</IncrementalBuild>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;V35</DefineConstants>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE;V35</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'DebugR2|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugR2\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'DebugR2|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\DebugR2\</OutputPath>
    <DefineConstants>TRACE;DEBUG;V35;R2</DefineConstants>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'ReleaseR2|AnyCPU' ">
    <OutputPath>bin\ReleaseR2\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'ReleaseR2|x86' ">
    <OutputPath>bin\x86\ReleaseR2\</OutputPath>
    <DefineConstants>TRACE;V35</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EnvDTE, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files\Microsoft Visual Studio 9.0\Common7\IDE\PublicAssemblies\EnvDTE.dll</HintPath>
    </Reference>
    <Reference Include="EnvDTE80, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files\Microsoft Visual Studio 9.0\Common7\IDE\PublicAssemblies\EnvDTE80.dll</HintPath>
    </Reference>
    <Reference Include="Extensibility, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
    <Reference Include="Kent.Boogaart.HelperTrinity, Version=1.2.0.0, Culture=neutral, PublicKeyToken=cc96fa93a217f7a1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\Kent.Boogaart.HelperTrinity.dll</HintPath>
    </Reference>
    <Reference Include="Kent.Boogaart.KBCsv, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\Kent.Boogaart.KBCsv.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.ConnectionUI, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\Microsoft.Data.ConnectionUI.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.ConnectionUI.Dialog, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\Microsoft.Data.ConnectionUI.Dialog.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.SqlServer.Management.Sdk.SqlStudio, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files\Microsoft SQL Server\100\Tools\Binn\VSShell\Common7\IDE\Microsoft.SqlServer.Management.Sdk.SqlStudio.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.SqlStudio.Explorer, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\VSShell\Common7\IDE\Microsoft.SqlServer.Management.SqlStudio.Explorer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.RegSvrEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.SqlServer.SqlTools.VSIntegration, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\VSShell\Common7\IDE\Microsoft.SqlServer.SqlTools.VSIntegration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.OLE.Interop, Version=7.1.40304.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.VisualStudio.OLE.Interop.dll</HintPath>
    </Reference>
    <Reference Include="SqlWorkbench.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\VSShell\Common7\IDE\SqlWorkbench.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SqlServerCe, Version=*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Scripter\AboutDlg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Scripter\AboutDlg.designer.cs">
      <DependentUpon>AboutDlg.cs</DependentUpon>
    </Compile>
    <Compile Include="Scripter\Connect.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Scripter\ScriptIndexMenuItem.cs" />
    <Compile Include="Scripter\ServerMenuItem.cs" />
    <Compile Include="Scripter\DataGridViewSearch.cs" />
    <Compile Include="Scripter\EditTableMenuItem.cs" />
    <Compile Include="Scripter\PanelQuickSearch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Scripter\PanelQuickSearch.Designer.cs">
      <DependentUpon>PanelQuickSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Scripter\RenameOptions.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Scripter\RenameOptions.Designer.cs">
      <DependentUpon>RenameOptions.cs</DependentUpon>
    </Compile>
    <Compile Include="Scripter\RenameTableMenuItem.cs" />
    <Compile Include="Scripter\ImportOptions.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Scripter\ImportOptions.Designer.cs">
      <DependentUpon>ImportOptions.cs</DependentUpon>
    </Compile>
    <Compile Include="Scripter\ImportTableMenuItem.cs" />
    <Compile Include="Scripter\DatabaseMenuItem.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Scripter\ResultsetGrid.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Scripter\ResultsetGrid.Designer.cs">
      <DependentUpon>ResultsetGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="Scripter\ScriptTableMenuItem.cs" />
    <Compile Include="Scripter\ToolStripSeparatorMenuItem.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="EnvDTE90">
      <Guid>{2CE2370E-D744-4936-A090-3FFFE667B0E1}</Guid>
      <VersionMajor>9</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="Microsoft.VisualStudio.CommandBars">
      <Guid>{1CBA492E-7263-47BB-87FE-639000619B15}</Guid>
      <VersionMajor>8</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripter\AboutDlg.resx">
      <DependentUpon>AboutDlg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Scripter\PanelQuickSearch.resx">
      <DependentUpon>PanelQuickSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Scripter\RenameOptions.resx">
      <DependentUpon>RenameOptions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Scripter\ImportOptions.resx">
      <DependentUpon>ImportOptions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Scripter\ResultsetGrid.resx">
      <DependentUpon>ResultsetGrid.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\data_out.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\data_out1.ico" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\Critical.bmp" />
    <None Include="Resources\RefreshDocViewHS.png" />
    <None Include="Resources\saveHS.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="ISqlCeScripting.csproj">
      <Project>{B690CB18-0DC5-4C20-AEB3-F9F4B55B4601}</Project>
      <Name>ISqlCeScripting</Name>
    </ProjectReference>
    <ProjectReference Include="SqlCeScripting.csproj">
      <Project>{09B046AF-A861-4D57-B8B4-40D9371EADB6}</Project>
      <Name>SqlCeScripting</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSHARP.Targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <ProjectExtensions>
    <VisualStudio>
    </VisualStudio>
  </ProjectExtensions>
</Project>