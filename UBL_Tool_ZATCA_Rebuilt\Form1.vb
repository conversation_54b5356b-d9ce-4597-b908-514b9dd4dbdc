
Public Class Form1

    Private Sub btnGenerateQRPIH_Click(sender As Object, e As EventArgs) Handles btnGenerateQRPIH.Click
        Try
            Dim inputXmlPath As String = TxtXmlInput.Text.Trim()
            Dim outputXmlPath As String = "D:\Basseet_SYS\XML_INV\UBL_Final_Signed.xml"

            Dim cert As String = TxtCert.Text.Trim()
            Dim key As String = TxtPrivateKey.Text.Trim()

            Dim company = TxtCompany.Text.Trim()
            Dim vat = TxtVat.Text.Trim()
            Dim dateIssued = DateTime.Now.ToString("yyyy-MM-dd")
            Dim invoiceNo = TxtInvoiceID.Text.Trim()
            Dim total As Decimal = Decimal.Parse(TxtTotal.Text)
            Dim tax As Decimal = Decimal.Parse(TxtTax.Text)

            Dim qrRaw As String = ZatcaHelper.GenerateQR(company, vat, dateIssued, total, tax, invoiceNo)

            Dim pih = ZatcaHelper.GenerateSignedXmlWithQRandPIH(inputXmlPath, outputXmlPath, cert, key, qrRaw)

            TxtPIH.Text = pih
            MessageBox.Show("✅ تم توليد QR + PIH وحفظ الملف الموقع بنجاح!", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show("❌ خطأ: " & ex.Message, "فشل", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Class
