
Imports System.Text
Imports System.Xml
Imports Zatca.EInvoice.SDK
Imports Zatca.EInvoice.SDK.Contracts.Models

Public Class ZatcaHelper

    Public Shared Function GenerateQR(companyName As String, vatNo As String, issueDate As String, total As Decimal, tax As Decimal, invoiceId As String) As String
        Dim qrString = "|" & companyName & "|" & vatNo & "|" & issueDate & "|" & total.ToString("F2") & "|" & tax.ToString("F2") & "|" & invoiceId & "|"
        Dim bytes = Encoding.UTF8.GetBytes(qrString)
        Return Convert.ToBase64String(bytes)
    End Function

    Public Shared Function GeneratePIH(xmlDoc As XmlDocument) As String
        Dim hasher As New EInvoiceHashGenerator()
        Dim result As HashResult = hasher.GenerateEInvoiceHashing(xmlDoc)
        Return result.Hash
    End Function

    Public Shared Function SignUBL(xmlDoc As XmlDocument, certContent As String, keyContent As String) As XmlDocument
        Dim signer As New EInvoiceSigner()
        Dim result As SignResult = signer.SignDocument(xmlDoc, certContent, keyContent)
        If result.IsValid Then
            Return result.SignedEInvoice
        Else
            Throw New Exception("فشل التوقيع الإلكتروني")
        End If
    End Function

    Public Shared Function GenerateSignedXmlWithQRandPIH(inputPath As String, outputPath As String, certContent As String, keyContent As String, qrData As String) As String
        Dim xmlDoc As New XmlDocument()
        xmlDoc.PreserveWhitespace = True
        xmlDoc.Load(inputPath)

        Dim base64QR As String = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData))

        Dim signedXml = SignUBL(xmlDoc, certContent, keyContent)

        Dim finalDoc As New XmlDocument()
        finalDoc.PreserveWhitespace = True
        finalDoc.LoadXml(signedXml.OuterXml)

        Dim pihValue = GeneratePIH(signedXml)

        XMLHelper.InsertQRCode(finalDoc, base64QR)
        XMLHelper.InsertPIH(finalDoc, pihValue)

        finalDoc.Save(outputPath)
        Return pihValue
    End Function

End Class
