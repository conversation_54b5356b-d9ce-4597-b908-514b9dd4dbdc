﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http.Json</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.Json.HttpClientJsonExtensions">
      <summary>Contains extension methods to send and receive HTTP content as JSON.</summary>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as J<PERSON><PERSON> in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="context">Source generated JsonSerializerContext used to control the deserialization behavior.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="context">Source generated JsonSerializerContext used to control the deserialization behavior.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.DeleteFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Threading.CancellationToken)">
      <summary>Sends a DELETE request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="context">Source generated JsonSerializerContext used to control the deserialization behavior.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.String,System.Type,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="context">Source generated JsonSerializerContext used to control the deserialization behavior.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync(System.Net.Http.HttpClient,System.Uri,System.Type,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.String,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync``1(System.Net.Http.HttpClient,System.Uri,System.Threading.CancellationToken)">
      <summary>Sends a GET request to the specified Uri and returns the value that results from deserializing the response body as JSON in an asynchronous operation.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PatchAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="client" /> is <see langword="null" />.</exception>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during serialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during serialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
      <summary>Sends a POST request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during serialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="options">Options to control the behavior during serialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during serialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpClientJsonExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
      <summary>Send a PUT request to the specified Uri containing the <paramref name="value" /> serialized as JSON in the request body.</summary>
      <param name="client">The client used to send the request.</param>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="value">The value to serialize.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="T:System.Net.Http.Json.HttpContentJsonExtensions">
      <summary>Contains extension methods to read and then parse the <see cref="T:System.Net.Http.HttpContent" /> from JSON.</summary>
    </member>
    <member name="M:System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsync(System.Net.Http.HttpContent,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Reads the HTTP content and returns the value that results from deserializing the content as JSON in an asynchronous operation.</summary>
      <param name="content">The content to read from.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsync(System.Net.Http.HttpContent,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Reads the HTTP content and returns the value that results from deserializing the content as JSON in an asynchronous operation.</summary>
      <param name="content">The content to read from.</param>
      <param name="type">The type of the object to deserialize to and return.</param>
      <param name="context">Source generated JsonSerializerContext used to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsync``1(System.Net.Http.HttpContent,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Reads the HTTP content and returns the value that results from deserializing the content as JSON in an asynchronous operation.</summary>
      <param name="content">The content to read from.</param>
      <param name="options">Options to control the behavior during deserialization. The default options are those specified by <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="T">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsync``1(System.Net.Http.HttpContent,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Reads the HTTP content and returns the value that results from deserializing the content as JSON in an asynchronous operation.</summary>
      <param name="content">The content to read from.</param>
      <param name="jsonTypeInfo">Source generated JsonTypeInfo to control the behavior during deserialization.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <typeparam name="T">The target type to deserialize to.</typeparam>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="T:System.Net.Http.Json.JsonContent">
      <summary>Provides HTTP content based on JSON.</summary>
    </member>
    <member name="M:System.Net.Http.Json.JsonContent.Create(System.Object,System.Type,System.Net.Http.Headers.MediaTypeHeaderValue,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.Json.JsonContent" /> class that will contain the <paramref name="inputValue" /> serialized as JSON.</summary>
      <param name="inputValue">The value to serialize.</param>
      <param name="inputType">The type of the value to serialize.</param>
      <param name="mediaType">The media type to use for the content.</param>
      <param name="options">Options to control the behavior during serialization, the default options are <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <returns>A <see cref="T:System.Net.Http.Json.JsonContent" /> instance.</returns>
    </member>
    <member name="M:System.Net.Http.Json.JsonContent.Create``1(``0,System.Net.Http.Headers.MediaTypeHeaderValue,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.Json.JsonContent" /> class that will contain the <paramref name="inputValue" /> serialized as JSON.</summary>
      <param name="inputValue">The value to serialize.</param>
      <param name="mediaType">The media type to use for the content.</param>
      <param name="options">Options to control the behavior during serialization, the default options are <see cref="F:System.Text.Json.JsonSerializerDefaults.Web" />.</param>
      <typeparam name="T">The type of the value to serialize.</typeparam>
      <returns>A <see cref="T:System.Net.Http.Json.JsonContent" /> instance.</returns>
    </member>
    <member name="P:System.Net.Http.Json.JsonContent.ObjectType">
      <summary>Gets the type of the <see cref="P:System.Net.Http.Json.JsonContent.Value" /> to be serialized by this instance.</summary>
      <returns>The type of the <see cref="P:System.Net.Http.Json.JsonContent.Value" /> to be serialized by this instance.</returns>
    </member>
    <member name="P:System.Net.Http.Json.JsonContent.Value">
      <summary>Gets the value to be serialized and used as the body of the <see cref="T:System.Net.Http.HttpRequestMessage" /> that sends this instance.</summary>
      <returns>The value to be serialized and used as the body of the <see cref="T:System.Net.Http.HttpRequestMessage" /> that sends this instance.</returns>
    </member>
  </members>
</doc>