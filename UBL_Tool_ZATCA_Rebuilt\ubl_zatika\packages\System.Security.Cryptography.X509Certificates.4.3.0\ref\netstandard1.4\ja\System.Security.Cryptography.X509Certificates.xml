﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>X.509 のチェーンを表すセーフ ハンドルを提供します。詳細については、「<see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />」を参照してください。</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>X.509 証明書ストアを開く方法を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>X.509 証明書ストアを開いて、アーカイブされた証明書を格納します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>許可された最も高いレベルのアクセスで X.509 証明書ストアを開きます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>既存のストアだけを開きます。ストアが存在しない場合、<see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> メソッドは新しいストアを作成しません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>読み取り専用で 509 証明書ストアを開きます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>読み取りおよび書き込み用に 509 証明書を開きます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>証明書の公開キー情報を表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>公開キー、公開キー パラメーターの ASN.1 エンコードされた表現、および公開キー値の ASN.1 エンコードされた表現のオブジェクト識別子 (OID) を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="oid">公開キーを表すオブジェクト識別子 (OID) オブジェクト。</param>
      <param name="parameters">公開キー パラメーターの ASN.1 エンコードされた表現。</param>
      <param name="keyValue">公開キー値の ASN.1 エンコードされた表現。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>公開キー値の ASN.1 エンコードされた表現を取得します。</summary>
      <returns>公開キー値の ASN.1 エンコードされた表現。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>公開キー パラメーターの ASN.1 エンコードされた表現を取得します。</summary>
      <returns>公開キー パラメーターの ASN.1 エンコードされた表現。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>公開キーを表す <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" /> オブジェクトまたは <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" /> オブジェクトを取得します。</summary>
      <returns>公開キーを表す <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> オブジェクト。</returns>
      <exception cref="T:System.NotSupportedException">キー アルゴリズムはサポートされていません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>公開キーのオブジェクト識別子 (OID: Object Identifier) オブジェクトを取得します。</summary>
      <returns>公開キーのオブジェクト識別子 (OID) オブジェクト。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>X.509 証明書ストアの場所を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>現在のユーザーが使用する X.509 証明書ストア。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>ローカル マシンに割り当てられた X.509 証明書ストア。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>開く X.509 証明書ストアの名前を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>他のユーザーの X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>サードパーティ証明機関 (CA: Certificate Authority) の X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>中間証明機関 (CA) の X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>失効した証明書の X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>個人用証明書の X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>信頼されたルート証明機関 (CA) の X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>直接信頼されたユーザーやリソースの X.509 証明書ストア</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>直接信頼された発行者の X.509 証明書ストア</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>X509 証明書の識別名を表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>指定されたバイト配列の情報に基づいて、<see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedDistinguishedName">識別名情報を保持するバイト配列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>指定された <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクトを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedDistinguishedName">識別名を表す <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>指定された <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> オブジェクトを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="distinguishedName">
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>指定された文字列の情報に基づいて、<see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="distinguishedName">識別名を表す文字列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>指定された文字列および <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" /> フラグを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="distinguishedName">識別名を表す文字列。</param>
      <param name="flag">識別名の特性を指定する列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>
        <paramref name="flag" /> パラメーターで指定された特性を使用して識別名をデコードします。</summary>
      <returns>デコードされた識別名。</returns>
      <param name="flag">識別名の特性を指定する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書の名前が無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>X500 の識別名を書式設定して返します。識別名を印刷したり、テキスト ウィンドウやコンソールに出力するときに使用します。</summary>
      <returns>X500 識別名を表す、書式設定済みの文字列。</returns>
      <param name="multiLine">戻り値の文字列にキャリッジ リターンを含める場合は true を、それ以外の場合は false を指定します。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>X500 証明書からコンマ区切りの識別名を取得します。</summary>
      <returns>X509 証明書のコンマ区切りの識別名。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>X.500 の識別名の特性を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>識別名で正符号 (+) が使用されていません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>識別名で引用符が使用されていません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>強制的に、識別名が印刷可能な Unicode 文字列ではなく、UTF-8 文字列を使用して、特定の X.500 キーをエンコードするようにします。詳細および影響を受ける X.500 キーの一覧については、「X500NameFlags 列挙体」を参照してください。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>識別名に特殊属性がありません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>識別名は予約されています。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>識別名でコンマが使用されています。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>識別名で改行文字が使用されています。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>識別名でセミコロン (;) が使用されています。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>識別名に T61 エンコーディングが使用されています。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>識別名に、Unicode 文字エンコーディングではなく UTF8 エンコーディングが使用されています。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>証明書に設定された制約を定義します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> クラスの新しいインスタンスを初期化します。パラメーターでは、証明書が証明機関 (CA) の証明書であるかどうかを表す値、証明書で許可するパス レベルの数に制限があるかどうかを示す値、証明書のパスで許可されるレベルの数、および拡張機能が重要かどうかを示す値を指定します。</summary>
      <param name="certificateAuthority">証明書が証明機関 (CA) の証明書である場合は true。それ以外の場合は false。</param>
      <param name="hasPathLengthConstraint">証明書で許可するパス レベルの数に制限がある場合は true。それ以外の場合は false。</param>
      <param name="pathLengthConstraint">証明書のパスで許可されるレベルの数。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedBasicConstraints">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>証明書が証明機関 (CA: Certificate Authority) の証明書であるかどうかを示す値を取得します。</summary>
      <returns>証明書が証明機関 (CA) の証明書である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクトを使用して <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> クラスの新しいインスタンスを 初期化します。</summary>
      <param name="asnEncodedData">拡張機能の作成に使用するエンコード済みのデータ。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>証明書で許可するパス レベルの数に制限があるかどうかを表す値を取得します。</summary>
      <returns>証明書で許可するパス レベルの数に制限がある場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">拡張機能をデコードできません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>証明書のパスで許可されるレベルの数を取得します。</summary>
      <returns>証明書のパスで許可されるレベルの数を表す整数。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">拡張機能をデコードできません。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>X.509 v.3 証明書を使用するために役立つメソッドを提供します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>X.509v3 証明書を表すバイトのシーケンスから定義された <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="data">X.509 証明書のデータを格納しているバイト配列。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> パラメーターが null です。または<paramref name="rawData" /> パラメーターの長さが 0 です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>バイト配列とパスワードを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> パラメーターが null です。または<paramref name="rawData" /> パラメーターの長さが 0 です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>バイト配列、パスワード、およびキー格納フラグを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする場所と方法を制御する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> パラメーターが null です。または<paramref name="rawData" /> パラメーターの長さが 0 です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[セキュリティ クリティカル] アンマネージ PCCERT_CONTEXT 構造体を識別するハンドルを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="handle">アンマネージ PCCERT_CONTEXT 構造体を識別するハンドル。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>PKCS7 で署名されたファイルの名前を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">PKCS7 で署名されたファイルの名前。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>PKCS7 で署名されたファイルの名前と証明書にアクセスするためのパスワードを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">PKCS7 で署名されたファイルの名前。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>PKCS7 で署名されたファイルの名前、証明書にアクセスするためのパスワード、およびキー格納フラグを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">PKCS7 で署名されたファイルの名前。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする場所と方法を制御する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次に例を示します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトによって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>これによって使用されるアンマネージ リソースのすべてを解放<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />し必要に応じてマネージ リソースも解放します。 </summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>2 つの <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトが等しいかどうかを比較します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトが <paramref name="other" /> パラメーターで指定されたオブジェクトと等しい場合は true。それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクト。 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>2 つの <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトが等しいかどうかを比較します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトが <paramref name="other" /> パラメーターで指定されたオブジェクトと等しい場合は true。それ以外の場合は false。</returns>
      <param name="other">現在のオブジェクトと比較する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>いずれかの <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 値で記述する形式で、現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトをバイト配列にエクスポートします。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトを表すバイトの配列。</returns>
      <param name="contentType">出力データの形式を記述する <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 値の 1 つ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" />、または <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 以外の値が <paramref name="contentType" /> パラメーターに渡されました。または証明書をエクスポートできませんでした。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>指定したパスワードを使用し、いずれかの <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 値で記述する形式で、現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトをバイト配列にエクスポートします。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトを表すバイトの配列。</returns>
      <param name="contentType">出力データの形式を記述する <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 値の 1 つ。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" />、または <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 以外の値が <paramref name="contentType" /> パラメーターに渡されました。または証明書をエクスポートできませんでした。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>X.509v3 証明書のハッシュ値をバイト配列として返します。</summary>
      <returns>X.509 証明書のハッシュ値。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>この X.509v3 証明書の形式の名前を返します。</summary>
      <returns>X.509 証明書の形式。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>X.509v3 証明書の整数形式のハッシュ コードを返します。</summary>
      <returns>X.509 証明書の整数形式のハッシュ コード。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>この X.509v3 証明書の文字列形式のキー アルゴリズム情報を返します。</summary>
      <returns>X.509 証明書の文字列形式のキー アルゴリズム情報。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>X.509v3 証明書のバイト配列形式のキー アルゴリズム パラメーターを返します。</summary>
      <returns>X.509 証明書のバイト配列形式のキー アルゴリズム パラメーター。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>この X.509v3 証明書の 16 進数文字列形式のキー アルゴリズム パラメーターを返します。</summary>
      <returns>X.509 証明書の 16 進数文字列形式のキー アルゴリズム パラメーター。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>X.509v3 証明書のバイト配列形式の公開キーを返します。</summary>
      <returns>X.509 証明書のバイト配列形式の公開キー。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>X.509v3 証明書のバイト配列形式のシリアル番号を返します。</summary>
      <returns>X.509 証明書のバイト配列形式のシリアル番号。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[セキュリティ クリティカル] アンマネージ PCCERT_CONTEXT 構造体で指定された Microsoft Cryptographic API 証明書コンテキストを識別するハンドルを取得します。</summary>
      <returns>アンマネージ PCCERT_CONTEXT 構造体を表す <see cref="T:System.IntPtr" /> 構造体。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>X.509v3 証明書を発行した証明機関の名前を取得します。</summary>
      <returns>X.509v3 証明書を発行した証明機関の名前。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書ハンドルが無効です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>証明書からサブジェクトの識別名を取得します。</summary>
      <returns>証明書から取得したサブジェクトの識別名。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書ハンドルが無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの文字列形式を返します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの文字列形式。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>補足情報が指定されている場合はその情報を含めて、現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの文字列形式を返します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの文字列形式。</returns>
      <param name="fVerbose">true to produce the verbose form of the string representation; otherwise, false.</param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>X.509 証明書を表します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>バイト配列の情報を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>バイト配列とパスワードを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>バイト配列、パスワード、およびキー格納フラグを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする場所と方法を制御する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>アンマネージ ハンドルを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="handle">アンマネージ コードの証明書コンテキストへのポインター。C 構造体は、PCCERT_CONTEXT と呼ばれます。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>証明書ファイル名を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">証明書ファイルの名前。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>証明書ファイル名と証明書にアクセスするためのパスワードを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">証明書ファイルの名前。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>証明書ファイル名、証明書にアクセスするためのパスワード、およびキー格納フラグを使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="fileName">証明書ファイルの名前。</param>
      <param name="password">X.509 証明書データにアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする場所と方法を制御する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書でエラーが発生しました。次のように入力します。証明書ファイルが存在しません。証明書が無効です。証明書のパスワードが正しくありません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>X.509 証明書がアーカイブされていることを示す値を取得または設定します。</summary>
      <returns>証明書がアーカイブされている場合は true。証明書がアーカイブされていない場合は false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>証明書に関連付けられたエイリアスを取得または設定します。</summary>
      <returns>証明書の表示名。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>バイト配列に格納された証明書の種類を示します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> オブジェクト。</returns>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> の長さが 0 であるか、またはその値が null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>ファイルに格納された証明書の種類を示します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> オブジェクト。</returns>
      <param name="fileName">証明書ファイルの名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>証明書からサブジェクト名と発行者名を取得します。</summary>
      <returns>証明書の名前。</returns>
      <param name="nameType">サブジェクトの <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> 値。</param>
      <param name="forIssuer">発行者名を含める場合は true。それ以外の場合は false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトに秘密キーが格納されているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトに秘密キーが格納されている場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>証明書発行者の識別名を取得します。</summary>
      <returns>証明書発行者の名前を格納する <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>証明書の有効期限を表す日付を現地時間で取得します。</summary>
      <returns>証明書の有効期限を表す <see cref="T:System.DateTime" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>証明書が有効になる日付を現地時間で取得します。</summary>
      <returns>証明書の発効日を表す <see cref="T:System.DateTime" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>証明書に関連付けられた秘密キーを表す <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> オブジェクトを取得または設定します。</summary>
      <returns>RSA 暗号化サービス プロバイダーまたは DSA 暗号化サービス プロバイダーである <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">キーの値が RSA キーと DSA キーのどちらでもないか、またはキーを読み取ることができません。</exception>
      <exception cref="T:System.ArgumentNullException">このプロパティに設定されている値が null です。</exception>
      <exception cref="T:System.NotSupportedException">この秘密キーのキー アルゴリズムはサポートされていません。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">X.509 キーが一致しません。</exception>
      <exception cref="T:System.ArgumentException">暗号化サービス プロバイダーのキーが null です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>証明書に関連付けられた <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">キーの値が RSA キーと DSA キーのどちらでもないか、またはキーを読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>証明書の生データを取得します。</summary>
      <returns>バイト配列としての証明書の生データ。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>証明書のシリアル番号を取得します。</summary>
      <returns>証明書のシリアル番号。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>証明書の署名を作成するために使用するアルゴリズムを取得します。</summary>
      <returns>署名アルゴリズムのオブジェクト識別子 (<see cref="T:System.Security.Cryptography.Oid" />) を返します。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>証明書からサブジェクトの識別名を取得します。</summary>
      <returns>証明書のサブジェクトの名前を表す <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> オブジェクト。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書のコンテキストが無効です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>証明書のサムプリントを取得します。</summary>
      <returns>証明書のサムプリント。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>X.509 証明書をテキスト形式で表示します。</summary>
      <returns>証明書情報。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>X.509 証明書をテキスト形式で表示します。</summary>
      <returns>証明書情報。</returns>
      <param name="verbose">公開キー、秘密キー、拡張機能などを表示する場合は true。サムプリント、シリアル番号、サブジェクト名、発行者名など <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> クラスと類似した情報を表示する場合は false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>X.509 形式のバージョンの証明書を取得します。</summary>
      <returns>証明書の形式。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができません。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトのコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 情報を指定せずに、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトを使用して <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> クラスの新しいインスタンスを 初期化します。</summary>
      <param name="certificate">コレクションの最初の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトの配列を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトの配列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>証明書コレクションを指定して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> の末尾にオブジェクトを追加します。</summary>
      <returns>
        <paramref name="certificate" /> が追加された位置の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> インデックス。</returns>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトとして表される X.509 証明書。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>配列内の複数の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトを <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトに追加します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトの配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の複数の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトを別の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトに追加します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトに特定の証明書が格納されているかどうかを確認します。</summary>
      <returns>指定した <paramref name="certificate" /> が <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> に含まれている場合は true。それ以外の場合は false。</returns>
      <param name="certificate">コレクション内で検索する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>X.509 証明書情報をバイト配列にエクスポートします。</summary>
      <returns>バイト配列内の X.509 証明書情報。</returns>
      <param name="contentType">サポートされる <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>パスワードを使用して X.509 証明書情報をバイト配列にエクスポートします。</summary>
      <returns>バイト配列内の X.509 証明書情報。</returns>
      <param name="contentType">サポートされる <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> オブジェクト。</param>
      <param name="password">バイト配列を保護するために使用する文字列。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書を読み取ることができないか、内容が無効であるか、または (証明書でパスワードが必要な場合) 指定されたパスワードが無効なために秘密キーをエクスポートできませんでした。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 列挙体および <paramref name="findValue" /> オブジェクトで指定された検索条件を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトを検索します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト。</returns>
      <param name="findType">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 値の 1 つ。</param>
      <param name="findValue">検索条件を表すオブジェクト。</param>
      <param name="validOnly">検索で有効な証明書だけが返されるようにする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> が無効です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトを反復処理できる列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトを反復処理できる <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>証明書をバイト配列形式で <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトにインポートします。</summary>
      <param name="rawData">X.509 証明書のデータを格納しているバイト配列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>証明書にアクセスするためのパスワードを必要とするバイト配列形式で証明書を <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトにインポートします。</summary>
      <param name="rawData">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトのデータを格納しているバイト配列。</param>
      <param name="password">証明書情報にアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする方法と場所を制御する列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>証明書ファイルを <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトにインポートします。</summary>
      <param name="fileName">証明書情報を格納しているファイルの名前。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>パスワードが必要な証明書ファイルを <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトにインポートします。</summary>
      <param name="fileName">証明書情報を格納しているファイルの名前。</param>
      <param name="password">証明書情報にアクセスするために必要なパスワード。</param>
      <param name="keyStorageFlags">証明書をインポートする方法と場所を制御する列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の指定したインデックス位置にオブジェクトを挿入します。</summary>
      <param name="index">
        <paramref name="certificate" /> を挿入する位置の、0 から始まるインデックス。</param>
      <param name="certificate">挿入する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.CollectionBase.Count" /> プロパティより大きい値です。</exception>
      <exception cref="T:System.NotSupportedException">コレクションは読み取り専用です。またはコレクションが固定サイズです。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null なので、</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックスにある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.CollectionBase.Count" /> プロパティ以上です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内で最初に見つかった証明書を削除します。</summary>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトから削除される <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>配列内の複数の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトを <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトから削除します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトの配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の複数の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクトを別の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトから削除します。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> は null なので、</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトに対する単純な反復処理をサポートします。このクラスは継承できません。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の現在の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトの次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>列挙子を初期位置、つまり <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクトの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>このメンバーの説明については、<see cref="P:System.Collections.IEnumerator.Current" /> のトピックを参照してください。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>このメンバーの説明については、<see cref="M:System.Collections.IEnumerator.MoveNext" /> のトピックを参照してください。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>このメンバーの説明については、<see cref="M:System.Collections.IEnumerator.Reset" /> のトピックを参照してください。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトを格納するコレクションを定義します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの配列から <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいオブジェクトを初期化するために使用する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトの配列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>別の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> から <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいオブジェクトを初期化するために使用する <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>指定した値を持つ <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> を現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に追加します。</summary>
      <returns>新しい <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> が挿入された位置である現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> へのインデックス。</returns>
      <param name="value">現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に追加する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 型の配列の要素を現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の末尾にコピーします。</summary>
      <param name="value">現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に追加するオブジェクトを格納している <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 型の配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の要素を現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の末尾にコピーします。</summary>
      <param name="value">コレクションに追加するオブジェクトを格納している <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> が現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に格納されているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> がこのコレクションに格納されている場合は true。それ以外の場合は false。</returns>
      <param name="value">検索する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>1 次元の <see cref="T:System.Array" /> インスタンスの指定したインデックスに、現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 値をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の値のコピー先である 1 次元の <see cref="T:System.Array" />。</param>
      <param name="index">コピーを開始する <paramref name="array" /> へのインデックス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> パラメーターが多次元です。または<see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の要素数が、<paramref name="arrayIndex" /> と <paramref name="array" /> の末尾との間に格納できる数を超えています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> パラメーターが null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> パラメーターが、<paramref name="array" /> パラメーターの下限より小さい値です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> を反復処理できる列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> のサブ要素の列挙子。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に格納されているすべての値に基づいたハッシュ値を構築します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> に格納されているすべての値に基づいたハッシュ値。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内の、指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> のインデックスを返します。</summary>
      <returns>
        <paramref name="value" /> パラメーターで指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> が <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内に存在する場合は、そのインデックス。それ以外の場合は -1。</returns>
      <param name="value">検索する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内の指定したインデックス位置に <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> を挿入します。</summary>
      <param name="index">
        <paramref name="value" /> を挿入する位置の 0 から始まるインデックス番号。</param>
      <param name="value">挿入する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の、指定したインデックスにあるエントリを取得または設定します。</summary>
      <returns>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の、指定したインデックスにある <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <param name="index">現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内で特定するエントリの 0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> パラメーターが、コレクションの有効なインデックスの範囲外です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> から特定の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> を削除します。</summary>
      <param name="value">現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> から削除する <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> パラメーターで指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> が現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内に見つかりません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> オブジェクトを列挙します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>指定した <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> の <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="mappings">列挙する <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内の現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 内の現在の <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>列挙子をコレクションの次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子がインスタンス化された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子がインスタンス化された後に、コレクションが変更されます。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>このメンバーの説明については、<see cref="P:System.Collections.IEnumerator.Current" /> のトピックを参照してください。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> オブジェクト内の現在の X.509 証明書オブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>このメンバーの説明については、<see cref="M:System.Collections.IEnumerator.MoveNext" /> のトピックを参照してください。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子がインスタンス化された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>このメンバーの説明については、<see cref="M:System.Collections.IEnumerator.Reset" /> のトピックを参照してください。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子がインスタンス化された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 証明書のチェーン作成エンジンを表します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> で指定したポリシーを使用して X.509 チェーンを作成します。</summary>
      <returns>X.509 証明書が有効な場合は true。それ以外の場合は false。</returns>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="certificate" /> が有効な証明書でないか、または値が null です。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="certificate" /> を読み取ることができません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> オブジェクトのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>X.509 証明書チェーンを作成するときに使用する <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> を取得または設定します。</summary>
      <returns>この X.509 チェーンに関連付けられている <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> オブジェクト。</returns>
      <exception cref="T:System.ArgumentNullException">このプロパティに設定されている値が null です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> オブジェクト内の各要素の状態を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> オブジェクトの配列。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>この <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>この <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> によって使用されているアンマネージ リソースを解放します。オプションとして、マネージ リソースを解放することもできます。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>この <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> インスタンスのセーフ ハンドルを取得します。</summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" /> を返します。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>X.509 チェーンの要素を表します。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>特定のチェーン要素にある X.509 証明書を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>チェーン内にある現在の X.509 証明書のエラー状態を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> オブジェクトの配列。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>アンマネージ証明書チェーン構造体から追加のエラー情報を取得します。</summary>
      <returns>Crypto API のアンマネージ CERT_CHAIN_ELEMENT 構造体の pwszExtendedErrorInfo メンバーを表す文字列。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> オブジェクトのコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>指定したインデックスを開始位置として、配列に <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> オブジェクトをコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> オブジェクトの配列。</param>
      <param name="index">インデックス値を表す整数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定された <paramref name="index" /> が 0 未満であるか、この配列の長さ以上です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> と現在のカウントの合計が配列の長さ以上です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>コレクション内の要素の数を取得します。</summary>
      <returns>コレクション内の要素の数を表す整数。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>チェーン要素のコレクション内を移動するために使用できる <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>チェーン要素のコレクションの同期がとられているかどうかを示す値を取得します。</summary>
      <returns>常に false を返します。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> オブジェクト。</returns>
      <param name="index">整数値。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> がコレクションの長さ以上です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> オブジェクトへのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>現在のオブジェクトへのポインター参照。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>指定したインデックスを開始位置として、配列に <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> オブジェクトをコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> オブジェクトのコピー先の配列。</param>
      <param name="index">コピーの開始位置を示す <paramref name="array" /> のインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定された <paramref name="index" /> が 0 未満であるか、この配列の長さ以上です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> と現在のカウントの合計が配列の長さ以上です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>チェーンの要素のコレクション内を移動するために使用できる <see cref="T:System.Collections.IEnumerator" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> に対する単純な反復処理をサポートします。このクラスは継承できません。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 内の現在の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>列挙子を初期位置、つまり <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> の最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 内の現在の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>X509 証明書チェーンを構築する際に適用されるチェーン ポリシーを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>証明書でサポートされるアプリケーション ポリシーまたは拡張されたキー使用目的 (EKU) を指定するオブジェクト識別子 (OID) のコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>証明書がサポートする証明書ポリシーを指定するオブジェクト識別子 (OID) のコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>証明書チェーンの検証時にチェーン エンジンによって検索される証明書の追加のコレクションを表します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> の各メンバーを既定値にリセットします。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>X509 失効フラグの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> オブジェクト。</returns>
      <exception cref="T:System.ArgumentException">指定された <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 値は、有効なフラグではありません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>X509 証明書失効モードの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> オブジェクト。</returns>
      <exception cref="T:System.ArgumentException">指定された <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 値は、有効なフラグではありません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>オンライン失効検証時、または証明書失効リスト (CRL) のダウンロード時のタイムアウト時間を取得します。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>証明書の検証フラグを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 列挙体の値。</returns>
      <exception cref="T:System.ArgumentException">指定された <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 値は、有効なフラグではありません。既定値は <see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" /> です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>証明書が検証された時刻 (現地時刻)。</summary>
      <returns>
        <see cref="T:System.DateTime" /> オブジェクト。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>X509 チェインのステータスおよびエラー情報を格納するための単純な構造体を提供します。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>X509 チェインのステータスを指定します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" /> 値です。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> 値の説明を指定します。</summary>
      <returns>ローカライズ可能な文字列です。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>X509 チェインのステータスを定義します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>証明書信頼リスト (CTL) に無効な署名が含まれていることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>時刻値が無効であるため (有効期限切れなど)、有効な証明書信頼リスト (CTL) ではないことを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>証明書信頼リスト (CTL) の使用目的が無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>X509 チェインを確立できないことを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>証明書によって名前制約が除外されているため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>証明書に未定義の名前制約が存在することを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>証明書に、許可されない名前制約が存在することを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>証明書にサポートされている名前制約が存在しないか、サポートされていない名前制約が存在することを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>無効な基本制約のため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>無効な拡張機能のため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>無効な名前制約のため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>無効なポリシー制約のため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>X509 チェインにエラーがないことを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>証明書ポリシー拡張機能が証明書に存在しないことを示します。このエラーは、グループ ポリシーにより、すべての証明書が証明書ポリシーを持つように指定されている場合に発生します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>証明書の署名が無効であるため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>使用しないでください。証明書の検証時、有効期間が正しくネストされていない、つまり、発行された証明書の有効期間が CA (証明機関) 証明書の有効期間を超えていることを指定します。たとえば、CA 証明書の有効期間が 1 月 1 日から 12 月 1 日であるにもかかわらず、発行された証明書の有効期間が 2 月 2 日から 12 月 2 日であった場合、有効期間が正しくネストされていないことになります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>時刻値が無効であるため (証明書の有効期限切れなど)、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>キーの使用目的が無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>X509 チェインで使用されているオンラインの証明書失効リスト (CRL) が現在、オフラインになっていることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>ルート証明書までの X509 チェインを確立できないことを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>証明書の失効状態を検証できないことを示します。原因としては、証明書失効リスト (CRL) がオフラインまたはアクセスできない状態になっていることが考えられます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>証明書が失効されているため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>信頼関係のないルート証明書のため、X509 チェインが無効であることを示します。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>X.509 証明書の形式を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Authenticode X.509 証明書。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>単一の X.509 証明書。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>PFX 形式の証明書。Pfx 値は、Pkcs12 値と同じです。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>PKCS #12 形式の証明書。Pkcs12 値は、Pfx 値と同じです。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>PKCS #7 形式の証明書。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>単一のシリアル化された X.509 証明書。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>シリアル化されたストア。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>不明な X.509 証明書。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>キーを使用するアプリケーションを示すオブジェクト識別子 (OID: Object Identifier) のコレクションを定義します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト、および拡張機能が重要であるかどうか識別する値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedEnhancedKeyUsages">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="enhancedKeyUsages">
        <see cref="T:System.Security.Cryptography.OidCollection" /> コレクション。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">指定した <see cref="T:System.Security.Cryptography.OidCollection" /> に、破損した値が 1 つ以上格納されています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクトを使用して <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="asnEncodedData">拡張機能の作成に使用するエンコード済みのデータ。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>キーを使用するアプリケーションを示すオブジェクト識別子 (OID) のコレクションを取得します。</summary>
      <returns>キーを使用するアプリケーションを示す <see cref="T:System.Security.Cryptography.OidCollection" /> オブジェクト。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>X509 拡張機能を表します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedExtension">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="oid">拡張機能の識別に使用するオブジェクト識別子。</param>
      <param name="rawData">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" /> が空の文字列 ("") です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="oid">オブジェクト識別子を表す文字列。</param>
      <param name="rawData">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>指定された <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクトの拡張機能プロパティをコピーします。</summary>
      <param name="asnEncodedData">コピーする <see cref="T:System.Security.Cryptography.AsnEncodedData" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" /> に有効な X.509 拡張機能がありません。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>拡張機能がクリティカルであるかどうかを示すブール値を取得します。</summary>
      <returns>拡張機能がクリティカルである場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトのコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトを <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトに追加します。</summary>
      <returns>
        <paramref name="extension" /> パラメーターが追加された位置のインデックス。</returns>
      <param name="extension">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトに追加する <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extension" /> パラメーターの値が null です。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>指定したインデックスを開始位置として、配列にコレクションをコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトの配列。</param>
      <param name="index">配列内でコピーを開始する位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> は長さ 0 の文字列であるか、無効な値が格納されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> に配列の範囲外の値が指定されています。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクト内の <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトの数を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクト内の <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトの数を表す整数。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトを反復処理できる列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトを反復処理するために使用する <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>コレクションでスレッド セーフが確実に行われるかどうかを示す値を取得します。</summary>
      <returns>コレクションがスレッド セーフである場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクト。</returns>
      <param name="index">取得する <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトの場所。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が配列の長さ以上です。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>値または表示名がオブジェクト識別子 (OID: Object Identifier) によって指定される最初の <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクト。</returns>
      <param name="oid">取得する拡張機能のオブジェクト識別子 (OID)。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトへのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトへのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>指定したインデックスを開始位置として、配列にコレクションをコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> オブジェクトの配列。</param>
      <param name="index">配列内でコピーを開始する位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> は長さ 0 の文字列であるか、無効な値が格納されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> に配列の範囲外の値が指定されています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトを反復処理できる列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> オブジェクトを反復処理するために使用する <see cref="T:System.Collections.IEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> に対する単純な反復処理をサポートします。このクラスは継承できません。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 内の現在の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>列挙子を初期位置、つまり <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> の最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>コレクションからオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 内の現在の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドで検索する値の種類を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、アプリケーション ポリシーの表示名または証明書のオブジェクト識別子 (OID、または <see cref="T:System.Security.Cryptography.Oid" />) を表す文字列であることが必要です。たとえば、"Encrypting File System" や "1.3.6.1.4.1.311.10.3.4" などを使用できます。ローカライズを予定しているアプリケーションの場合、フレンドリ名がローカライズされることを考慮し、OID 値を使用する必要があります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書ポリシーの表示名またはオブジェクト識別子 (OID、または <see cref="T:System.Security.Cryptography.Oid" />) を表す文字列であることが必要です。できるだけ、"1.3.6.1.4.1.311.10.3.4" などの OID を使用することをお勧めします。ローカライズを予定しているアプリケーションの場合、フレンドリ名がローカライズされることを考慮し、OID を使用する必要があります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、検索する拡張機能を表す文字列であることが必要です。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドで特定の拡張機能を持つすべての証明書を検索する場合は、一致条件にオブジェクト識別子 (OID) を使用するのが一般的です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書発行者の識別名を表す文字列であることが必要です。この検索は、<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 列挙値で提供される場合よりも、条件がはっきりします。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 値を使用した場合、識別名全体について、大文字と小文字を区別しない文字列比較が実行されます。発行者名での検索は、これよりも条件があいまいになります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書の発行者名を表す文字列であることが必要です。この検索は、<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 列挙値で提供される場合よりも、条件があいまいになります。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 値を使用した場合、指定した値に基づいて、大文字と小文字を区別しない文字列比較が実行されます。たとえば、<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに "MyCA" を指定した場合、発行者の他の値に関係なく、発行者名にこの文字列が使われている、すべての証明書が検索されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターには、キーの使用目的を表す文字列を指定するか、要求されたキー使用目的をすべて満たしたビット マスクを整数で指定する必要があります。文字列値の場合、同時に指定できるキーの使用目的は 1 つだけですが、<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドで検索範囲を段階的に狭めてゆくことにより、要求した使用目的を論理積的に得ることもできます。たとえば、<paramref name="findValue" /> パラメーターには、"KeyEncipherment" または整数値 (0x30 は "KeyEncipherment" および "DataEncipherment" に相当) を設定できるほか、<see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 列挙型の値を使用することもできます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書のダイアログ ボックスで表示される証明書のシリアル番号を表す文字列、または <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" /> メソッドが返す文字列である必要があります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書サブジェクトの識別名を表す文字列であることが必要です。この検索は、<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 列挙値で提供される場合よりも、条件がはっきりします。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 値を使用した場合、識別名全体について、大文字と小文字を区別しない文字列比較が実行されます。サブジェクト名での検索は、これよりも条件があいまいになります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、サブジェクトのキー識別子 ("F3E815D45E83B8477B9284113C64EF208E897112" などの 16 進数で表される) を UI 上の文字列で指定する必要があります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書のサブジェクト名を表す文字列であることが必要です。この検索は、<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 列挙値で提供される場合よりも、条件があいまいになります。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 値を使用した場合、指定した値に基づいて、大文字と小文字を区別しない文字列比較が実行されます。たとえば、<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドに "MyCert" を指定した場合、サブジェクトの他の値に関係なく、サブジェクト名にこの文字列が使われている、すべての証明書が検索されます。識別名での検索の方が、より具体的な条件になります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、"ClientAuth" など、証明書のテンプレート名を表す文字列であることが必要です。テンプレート名は、証明書の使用目的を指定する、X509 バージョン 3 の拡張機能です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、証明書のサムプリントを表す文字列であることが必要です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、現地時間の <see cref="T:System.DateTime" /> 値であることが必要です。たとえば、年末まで有効なすべての証明書を調べるには、<see cref="P:System.DateTime.Now" /> の <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作の結果から年の最後の日の <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> の <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作の結果を除外します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、現地時間の <see cref="T:System.DateTime" /> 値であることが必要です。この値は将来存在する必要はありません。たとえば、<see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> を使用して、現在の年に有効になった証明書を調べることができます。そのためには、去年の最後の日に対する <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> の <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作の結果と、<see cref="P:System.DateTime.Now" /> の <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> の <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 操作の結果との交差部分を求めます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> メソッドの <paramref name="findValue" /> パラメーターは、現地時間の <see cref="T:System.DateTime" /> 値であることが必要です。<see cref="P:System.DateTime.Now" /> を使用して、現在有効なすべての証明書を検索できます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>X.509 証明書の秘密キーをインポートする場所と方法を定義します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>既定のキー セットが使用されます。既定のキー セットは、通常、ユーザーのキー セットです。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>インポートされたキーは、エクスポート可能とマークされます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>秘密キーが現在のユーザーのストアではなく、ローカル コンピューターのストアに格納されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>PFX ファイルに関連付けられたキーは、証明書をインポートするときに永続化されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>秘密キーがローカル コンピューターのストアではなく、現在のユーザーのストアに格納されます。これは、キーをローカル コンピューター ストアに格納することを証明書で指定した場合も同様です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>ダイアログ ボックスなどを使用してキーがアクセスされたことをユーザーに通知します。正確な動作は、使用中の暗号化サービス プロバイダー (CSP: Cryptographic Service Provider) によって定義されます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>X.509 証明書内に格納されたキーの使用法を定義します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedKeyUsage">拡張機能の作成に使用するエンコード済みのデータ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 値、および拡張機能が重要であるかどうかを示す値を指定して、<see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="keyUsages">キーの使用方法を指定する <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 値の 1 つ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクトを使用して <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> クラスの新しいインスタンスを 初期化します。</summary>
      <param name="asnEncodedData">拡張機能の作成に使用するエンコード済みのデータ。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>証明書に関連付けられたキー使用法フラグを取得します。</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" /> 値のいずれか。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">拡張機能をデコードできません。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>証明書キーの使用方法を定義します。この値が定義されていない場合、キーは任意の目的に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>キーを証明書失効リスト (CRL: Certificate Revocation List) への署名に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>キーをデータの暗号化に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>キーを復号化だけに使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>キーをデジタル署名として使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>キーを暗号化だけに使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>Diffie-Hellman キーの承諾アルゴリズムによって作成したキーのように、キーをキーの承諾の確認に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>キーを証明書への署名に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>キーをキーの暗号化に使用できます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>キー使用法のパラメーターを指定しません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>キーを認証に使用できます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>X509 証明書が保持する名前の種類を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>X509 証明書のサブジェクトまたは発行者の代替名に関連付けられた DNS 名です。この値は、<see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" /> 値と等価です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>X509 証明書のサブジェクトまたは発行者の代替名に関連付けられた DNS 名です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>X509 証明書のサブジェクトまたは発行者に関連付けられた電子メール アドレスです。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>X509 証明書のサブジェクトまたは発行者の通常の名前です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>X509 証明書のサブジェクトまたは発行者の UPN 名です。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>X509 証明書のサブジェクトまたは発行者の代替名に関連付けられた URL アドレスです。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>チェインにおいて、失効状態を検証する X509 証明書を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>終了証明書の失効状態のみ検証されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>証明書チェイン全体を対象に失効状態が検証されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>ルート証明書を除く、証明書チェイン全体を対象に失効状態が検証されます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>X509 証明書の失効状態を検証するときに使用するモードを指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>証明書に対する失効状態の検証は実行されません。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>失効状態の検証は、キャッシュされている証明書失効リスト (CRL) を使って実行されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>失効状態の検証は、オンラインの証明書失効リスト (CRL) を使って実行されます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>証明書を永続化して管理する物理ストアである X.509 ストアを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>現在のユーザー ストアの個人用証明書を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>指定した <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 値および <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="storeName">X.509 証明書ストアの名前を指定する列挙値の 1 つ。</param>
      <param name="storeLocation">X.509 証明書ストアの場所を指定する列挙値の 1 つ。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> が有効な場所ではないか、<paramref name="storeName" /> が有効な名前ではありません。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 列挙体の値と <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 列挙体の値を表す文字列を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="storeName">
        <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 列挙体の値を表す文字列。</param>
      <param name="storeLocation">X.509 証明書ストアの場所を指定する列挙値の 1 つ。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> に無効な値が含まれています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>証明書を X.509 証明書ストアに追加します。</summary>
      <param name="certificate">追加する証明書。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null です。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">証明書をストアに追加できませんでした。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>X.509 証明書ストア内にある証明書のコレクションを返します。</summary>
      <returns>証明書のコレクション。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>これによって使用されるリソースを解放<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />です。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>X.509 証明書ストアの場所を取得します。</summary>
      <returns>証明書ストアの場所。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>X.509 証明書ストアの名前を取得します。</summary>
      <returns>証明書ストアの名前。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" /> フラグの設定に応じて、X.509 証明書ストアを開くか、または新しいストアを作成します。</summary>
      <param name="flags">X.509 証明書ストアを開く方法を指定する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">ストアを読み取ることができません。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentException">ストアに無効な値が含まれています。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>X.509 証明書ストアから証明書を削除します。</summary>
      <param name="certificate">削除する証明書。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> は null です。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>証明書のサブジェクト キー識別子 (SKI: Subject Key Identifier) を示す文字列を定義します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>バイト配列、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="subjectKeyIdentifier">拡張機能を作成するために使用するデータを表すバイト配列。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>エンコードされたデータ、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="encodedSubjectKeyIdentifier">拡張機能の作成に使用する <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>公開キー、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="key">サブジェクト キー識別子 (SKI) の作成元となる <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> オブジェクト。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>公開キー、ハッシュ アルゴリズム識別子、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="key">サブジェクト キー識別子 (SKI) の作成元となる <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> オブジェクト。</param>
      <param name="algorithm">使用するハッシュ アルゴリズムを識別する <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" /> 値の 1 つ。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>文字列、および拡張機能が重要であるかどうかを示す値を使用して、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="subjectKeyIdentifier">証明書の サブジェクト キー識別子 (SKI) を表す 16 進形式でエンコードされた文字列。</param>
      <param name="critical">拡張機能がクリティカルである場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>エンコード済みデータから情報をコピーして、<see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="asnEncodedData">拡張機能の作成に使用する <see cref="T:System.Security.Cryptography.AsnEncodedData" /> オブジェクト。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>証明書のサブジェクト キー識別子 (SKI) を表す文字列を取得します。</summary>
      <returns>サブジェクト キー識別子 (SKI) を表す 16 進形式でエンコードされた文字列。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">拡張機能をデコードできません。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> クラスで使用するハッシュ アルゴリズムの種類を定義します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>サブジェクト キー識別子 (SKI) は、エンコードされた公開キー (タグ、長さ、未使用ビットの数を含む) の 160 ビット SHA-1 ハッシュで構成されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>SKI は、公開キー (タグ、長さ、および未使用ビットの数を除く) の値の 160 ビット SHA-1 ハッシュで構成されます。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>SKI は、値 0100 が格納された 4 ビット タイプのフィールド、それに続く公開キー (タグ、長さ、および文字列ビット中の未使用ビットの数を除く) 値の SHA-1 ハッシュの 60 下位ビットで構成されます。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>X509 チェインで証明書を検証する際の条件を指定します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>検証に関するすべてのフラグが有効になります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>証明機関 (CA) が不明であるためにチェインを検証できなかった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>証明書の検証時に証明機関の失効状態が不明であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>証明書の検証時に、CTL の有効期限切れなど、証明書信頼リスト (CTL) が無効であっても無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>証明書の検証時に、証明書信頼リスト (CTL) の署名主の失効状態が不明であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>証明書の検証時に、終了証明書 (ユーザー証明書) の失効状態が不明であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>証明書の検証時に、基本制約が無効であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>証明書の検証時に、証明書の名前が無効であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>証明書の検証時に、証明書に無効なポリシーが存在していた場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>証明書の検証時、有効期間が正しくネストされていない、つまり、発行された証明書の有効期間が CA (証明機関) 証明書の有効期間を超えていた場合でも無視します。たとえば、CA 証明書の有効期間が 1 月 1 日から 12 月 1 日であるにもかかわらず、発行された証明書の有効期間が 2 月 2 日から 12 月 2 日であった場合、有効期間が正しくネストされていないことになります。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>証明書の検証時、チェインに存在する証明書が、有効期限切れや、まだ有効開始日が来ていないなどの理由で無効であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>証明書の検証時にルートの失効状態が不明であった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>証明書の検証時に、証明書の使用目的上の違反が見つかった場合でも無視します。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>検証に関するフラグはすべて無効になります。</summary>
    </member>
  </members>
</doc>