﻿
Imports System.Xml

Public Class XMLHelper

    Public Shared Sub InsertQRCode(doc As XmlDocument, base64QR As String)
        Dim nsMgr As New XmlNamespaceManager(doc.NameTable)
        nsMgr.AddNamespace("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2")
        nsMgr.AddNamespace("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2")

        Dim root = doc.DocumentElement
        Dim qrNode As XmlElement = doc.CreateElement("cac", "AdditionalDocumentReference", root.NamespaceURI)

        Dim idNode = doc.CreateElement("cbc", "ID", nsMgr.LookupNamespace("cbc"))
        idNode.InnerText = "QR"
        qrNode.AppendChild(idNode)

        Dim attachment = doc.CreateElement("cac", "Attachment", root.NamespaceURI)
        Dim embedded = doc.CreateElement("cbc", "EmbeddedDocumentBinaryObject", nsMgr.LookupNamespace("cbc"))
        embedded.SetAttribute("mimeCode", "image/png")
        embedded.SetAttribute("encodingCode", "Base64")
        embedded.InnerText = base64QR
        attachment.AppendChild(embedded)
        qrNode.AppendChild(attachment)

        root.AppendChild(qrNode)
    End Sub

    Public Shared Sub InsertPIH(doc As XmlDocument, hash As String)
        Dim nsMgr As New XmlNamespaceManager(doc.NameTable)
        nsMgr.AddNamespace("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2")
        nsMgr.AddNamespace("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2")

        Dim root = doc.DocumentElement
        Dim pihNode As XmlElement = doc.CreateElement("cac", "AdditionalDocumentReference", root.NamespaceURI)

        Dim idNode = doc.CreateElement("cbc", "ID", nsMgr.LookupNamespace("cbc"))
        idNode.InnerText = "PIH"
        pihNode.AppendChild(idNode)

        Dim attachment = doc.CreateElement("cac", "Attachment", root.NamespaceURI)
        Dim embedded = doc.CreateElement("cbc", "EmbeddedDocumentBinaryObject", nsMgr.LookupNamespace("cbc"))
        embedded.SetAttribute("mimeCode", "text/plain")
        embedded.SetAttribute("encodingCode", "Base64")
        embedded.InnerText = hash
        attachment.AppendChild(embedded)
        pihNode.AppendChild(attachment)

        root.AppendChild(pihNode)
    End Sub

End Class
