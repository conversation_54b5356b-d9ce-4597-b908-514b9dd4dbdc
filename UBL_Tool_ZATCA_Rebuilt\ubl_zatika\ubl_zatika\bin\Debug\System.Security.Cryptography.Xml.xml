<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.Cryptography.Xml</name>
    </assembly>
    <members>
        <member name="P:System.SR.ArgumentOutOfRange_IndexMustBeLess">
            <summary>Index was out of range. Must be non-negative and less than the size of the collection.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_IndexMustBeLessOrEqual">
            <summary>Index was out of range. Must be non-negative and less than or equal to the size of the collection.</summary>
        </member>
        <member name="P:System.SR.Arg_EmptyOrNullString">
            <summary>String cannot be empty or null.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Partial_Chain">
            <summary>A certificate chain could not be built to a trusted root authority.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_BadWrappedKeySize">
            <summary>Bad wrapped key size.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CipherValueElementRequired">
            <summary>A Cipher Data element should have either a CipherValue or a CipherReference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreateHashAlgorithmFailed">
            <summary>Could not create hash algorithm object.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreateTransformFailed">
            <summary>Could not create the XML transformation identified by the URI {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_CreatedKeyFailed">
            <summary>Failed to create signing key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_DigestMethodRequired">
            <summary>A DigestMethod must be specified on a Reference prior to generating XML.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_DigestValueRequired">
            <summary>A Reference must contain a DigestValue.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_EnvelopedSignatureRequiresContext">
            <summary>An XmlDocument context is required for enveloped transforms.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidElement">
            <summary>Malformed element {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidEncryptionProperty">
            <summary>Malformed encryption property element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidKeySize">
            <summary>The key size should be a non negative integer.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidReference">
            <summary>Malformed reference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidSignatureLength">
            <summary>The length of the signature with a MAC should be less than the hash output length.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidSignatureLength2">
            <summary>The length in bits of the signature with a MAC should be a multiple of 8.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_InvalidX509IssuerSerialNumber">
            <summary>X509 issuer serial number is invalid.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_KeyInfoRequired">
            <summary>A KeyInfo element is required to check the signature.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_KW_BadKeySize">
            <summary>The length of the encrypted data in Key Wrap is either 32, 40 or 48 bytes.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_LoadKeyFailed">
            <summary>Signing key is not loaded.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingAlgorithm">
            <summary>Symmetric algorithm is not specified.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingCipherData">
            <summary>Cipher data is not specified.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingDecryptionKey">
            <summary>Unable to retrieve the decryption key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_MissingEncryptionKey">
            <summary>Unable to retrieve the encryption key.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_NotSupportedCryptographicTransform">
            <summary>The specified cryptographic transform is not supported.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_ReferenceElementRequired">
            <summary>At least one Reference element is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_ReferenceTypeRequired">
            <summary>The Reference type must be set in an EncryptedReference object.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SelfReferenceRequiresContext">
            <summary>An XmlDocument context is required to resolve the Reference Uri {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureDescriptionNotCreated">
            <summary>SignatureDescription could not be created for the signature algorithm supplied.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureMethodKeyMismatch">
            <summary>The key does not fit the SignatureMethod.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureMethodRequired">
            <summary>A signature method is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignatureValueRequired">
            <summary>Signature requires a SignatureValue.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_SignedInfoRequired">
            <summary>Signature requires a SignedInfo.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_TransformIncorrectInputType">
            <summary>The input type was invalid for this transform.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_IncorrectObjectType">
            <summary>Type of input object is invalid.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UnknownTransform">
            <summary>Unknown transform has been encountered.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriNotResolved">
            <summary>Unable to resolve Uri {0}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriNotSupported">
            <summary>The specified Uri is not supported.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_UriRequired">
            <summary>A Uri attribute is required for a CipherReference element.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingContext">
            <summary>Null Context property encountered.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingIRelDecryptor">
            <summary>IRelDecryptor is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingIssuer">
            <summary>Issuer node is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlMissingLicence">
            <summary>License node is required.</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_XrmlUnableToDecryptGrant">
            <summary>Unable to decrypt grant content.</summary>
        </member>
        <member name="P:System.SR.NotSupported_KeyAlgorithm">
            <summary>The certificate key algorithm is not supported.</summary>
        </member>
        <member name="P:System.SR.Log_ActualHashValue">
            <summary>Actual hash value: {0}</summary>
        </member>
        <member name="P:System.SR.Log_BeginCanonicalization">
            <summary>Beginning canonicalization using "{0}" ({1}).</summary>
        </member>
        <member name="P:System.SR.Log_BeginSignatureComputation">
            <summary>Beginning signature computation.</summary>
        </member>
        <member name="P:System.SR.Log_BeginSignatureVerification">
            <summary>Beginning signature verification.</summary>
        </member>
        <member name="P:System.SR.Log_BuildX509Chain">
            <summary>Building and verifying the X509 chain for certificate {0}.</summary>
        </member>
        <member name="P:System.SR.Log_CanonicalizationSettings">
            <summary>Canonicalization transform is using resolver {0} and base URI "{1}".</summary>
        </member>
        <member name="P:System.SR.Log_CanonicalizedOutput">
            <summary>Output of canonicalization transform: {0}</summary>
        </member>
        <member name="P:System.SR.Log_CertificateChain">
            <summary>Certificate chain:</summary>
        </member>
        <member name="P:System.SR.Log_CheckSignatureFormat">
            <summary>Checking signature format using format validator "[{0}] {1}.{2}".</summary>
        </member>
        <member name="P:System.SR.Log_CheckSignedInfo">
            <summary>Checking signature on SignedInfo with id "{0}".</summary>
        </member>
        <member name="P:System.SR.Log_FormatValidationSuccessful">
            <summary>Signature format validation was successful.</summary>
        </member>
        <member name="P:System.SR.Log_FormatValidationNotSuccessful">
            <summary>Signature format validation failed.</summary>
        </member>
        <member name="P:System.SR.Log_KeyUsages">
            <summary>Found key usages "{0}" in extension {1} on certificate {2}.</summary>
        </member>
        <member name="P:System.SR.Log_NoNamespacesPropagated">
            <summary>No namespaces are being propagated.</summary>
        </member>
        <member name="P:System.SR.Log_PropagatingNamespace">
            <summary>Propagating namespace {0}="{1}".</summary>
        </member>
        <member name="P:System.SR.Log_RawSignatureValue">
            <summary>Raw signature: {0}</summary>
        </member>
        <member name="P:System.SR.Log_ReferenceHash">
            <summary>Reference {0} hashed with "{1}" ({2}) has hash value {3}, expected hash value {4}.</summary>
        </member>
        <member name="P:System.SR.Log_RevocationMode">
            <summary>Revocation mode for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_RevocationFlag">
            <summary>Revocation flag for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningAsymmetric">
            <summary>Calculating signature with key {0} using signature description {1}, hash algorithm {2}, and asymmetric signature formatter {3}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningHmac">
            <summary>Calculating signature using keyed hash algorithm {0}.</summary>
        </member>
        <member name="P:System.SR.Log_SigningReference">
            <summary>Hashing reference {0}, Uri "{1}", Id "{2}", Type "{3}" with hash algorithm "{4}" ({5}).</summary>
        </member>
        <member name="P:System.SR.Log_TransformedReferenceContents">
            <summary>Transformed reference contents: {0}</summary>
        </member>
        <member name="P:System.SR.Log_UnsafeCanonicalizationMethod">
            <summary>Canonicalization method "{0}" is not on the safe list. Safe canonicalization methods are: {1}.</summary>
        </member>
        <member name="P:System.SR.Log_UrlTimeout">
            <summary>URL retrieval timeout for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed">
            <summary>Verification failed checking {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_References">
            <summary>references</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_SignedInfo">
            <summary>SignedInfo</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_X509Chain">
            <summary>X509 chain verification</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFailed_X509KeyUsage">
            <summary>X509 key usage verification</summary>
        </member>
        <member name="P:System.SR.Log_VerificationFlag">
            <summary>Verification flags for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationTime">
            <summary>Verification time for chain building: {0}.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationWithKeySuccessful">
            <summary>Verification with key {0} was successful.</summary>
        </member>
        <member name="P:System.SR.Log_VerificationWithKeyNotSuccessful">
            <summary>Verification with key {0} was not successful.</summary>
        </member>
        <member name="P:System.SR.Log_VerifyReference">
            <summary>Processing reference {0}, Uri "{1}", Id "{2}", Type "{3}".</summary>
        </member>
        <member name="P:System.SR.Log_VerifySignedInfoAsymmetric">
            <summary>Verifying SignedInfo using key {0}, signature description {1}, hash algorithm {2}, and asymmetric signature deformatter {3}.</summary>
        </member>
        <member name="P:System.SR.Log_VerifySignedInfoHmac">
            <summary>Verifying SignedInfo using keyed hash algorithm {0}.</summary>
        </member>
        <member name="P:System.SR.Log_X509ChainError">
            <summary>Error building X509 chain: {0}: {1}.</summary>
        </member>
        <member name="P:System.SR.Log_XmlContext">
            <summary>Using context: {0}</summary>
        </member>
        <member name="P:System.SR.Log_SignedXmlRecursionLimit">
            <summary>Signed xml recursion limit hit while trying to decrypt the key. Reference {0} hashed with "{1}" and ({2}).</summary>
        </member>
        <member name="P:System.SR.Log_UnsafeTransformMethod">
            <summary>Transform method "{0}" is not on the safe list. Safe transform methods are: {1}.</summary>
        </member>
        <member name="P:System.SR.ElementCombinationMissing">
            <summary>{0} and {1} can only occur in combination</summary>
        </member>
        <member name="P:System.SR.ElementMissing">
            <summary>{0} is missing</summary>
        </member>
        <member name="P:System.SR.MustContainChildElement">
            <summary>{0} must contain child element {1}</summary>
        </member>
        <member name="P:System.SR.WrongRootElement">
            <summary>Root element must be {0} element in namespace {1}</summary>
        </member>
        <member name="P:System.SR.Cryptography_Xml_EntityResolutionNotSupported">
            <summary>External entity resolution is not supported.</summary>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.ObsoletedOSPlatformAttribute">
            <summary>
            Marks APIs that were obsoleted in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that should not be used anymore.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>
