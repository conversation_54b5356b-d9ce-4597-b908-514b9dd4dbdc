Imports OpenQA.Selenium
Imports OpenQA.Selenium.Chrome
Imports OpenQA.Selenium.Support.UI
Imports System.Text
Imports System.Threading
Imports System.IO
Imports Excel = Microsoft.Office.Interop.Excel
Imports SeleniumExtras.WaitHelpers
Imports System.Diagnostics ' إضافة مكتبة للتحكم في العمليات
Imports System.Runtime.InteropServices ' إضافة مكتبة للتحكم في النوافذ

Public Class frm_whats
    Private m_driver As IWebDriver
    Private wait As WebDriverWait
    Private strLogs As New StringBuilder()
    Private Delegate Sub SafeCallDelegate(text As String)
    Private bStopSendingMessage As Boolean = False

    ' إضافة تعريفات لتصغير نافذة الأوامر
    <DllImport("user32.dll")>
    Private Shared Function ShowWindow(hWnd As IntPtr, nCmdShow As Integer) As Boolean
    End Function

    Private Const SW_MINIMIZE As Integer = 6

    Public Sub New()
        InitializeComponent()
        Dim options As New ChromeOptions()
        options.AddArgument("user-data-dir=C:\SeleniumProfile") ' لتخزين الجلسة
        options.AddArgument("--profile-directory=Default")
        options.AddArgument("--disable-notifications")
        options.AddArgument("--disable-popup-blocking")
        options.AddArgument("--disable-gpu")
        options.AddArgument("--no-sandbox")
        ' إضافة خيار لتشغيل المتصفح في وضع التصغير
        options.AddArgument("--window-position=-32000,-32000") ' نقل النافذة خارج الشاشة مؤقتًا

        m_driver = New ChromeDriver(options) ' تأكد من وضع مسار ChromeDriver
        wait = New WebDriverWait(m_driver, TimeSpan.FromSeconds(60)) ' زيادة المهلة الزمنية إلى60 ثانية

        ' تصغير نافذة المتصفح فور إنشائها
        m_driver.Manage().Window.Minimize()

        ' تصغير نافذة الأوامر (Command Prompt)
        MinimizeConsoleWindow()
    End Sub

    ' دالة لتصغير نافذة الأوامر
    Private Sub MinimizeConsoleWindow()
        Try
            Dim processes = Process.GetProcessesByName("cmd")
            For Each proc As Process In processes
                If proc.MainWindowHandle <> IntPtr.Zero Then
                    ShowWindow(proc.MainWindowHandle, SW_MINIMIZE)
                End If
            Next
        Catch ex As Exception
            ' تجاهل أي أخطاء إذا لم يتم العثور على النافذة
        End Try
    End Sub

    Private Sub btnLaunchWhatsapp_Click(sender As Object, e As EventArgs) Handles btnLaunchWhatsapp.Click
        Try
            m_driver.Url = "https://web.whatsapp.com/"
            If Not WaitForWhatsAppLoad() Then
                MessageBox.Show("فشل تحميل واتساب ويب. يرجى المحاولة مرة أخرى.")
                Return
            End If
            SetText("تم تشغيل واتساب ويب بنجاح. يرجى مسح رمز QR إذا لزم الأمر.")
        Catch ex As Exception
            MessageBox.Show($"خطأ في تشغيل واتساب: {ex.Message}")
        End Try
    End Sub

    Private Function WaitForWhatsAppLoad() As Boolean
        Try
            Thread.Sleep(5000)
            wait.Until(Function(d) CType(d, IJavaScriptExecutor).ExecuteScript("return document.readyState").Equals("complete"))
            Return True
        Catch
            Return False
        End Try
    End Function

    Private Sub SendMessagesWithPdf(phoneNumbers As List(Of String), message As String, pdfPath As String)
        bStopSendingMessage = False
        strLogs.Clear()
        SetText("============Starting=============")

        Dim sleepTime As Integer = 3000 ' وقت الانتظار بين الرسائل
        For Each phoneNumber As String In phoneNumbers
            Try
                Dim formattedPhone As String = phoneNumber.Trim().Replace(" ", "").Replace("-", "").Replace("+", "")
                If Not formattedPhone.StartsWith("20") Then
                    formattedPhone = "20" & formattedPhone ' إضافة رمز الدولة
                End If

                ' بناء رابط WhatsApp
                Dim whatsappUrl As String = $"https://web.whatsapp.com/send/?phone={formattedPhone}&text={Uri.EscapeDataString(message)}"
                m_driver.Navigate().GoToUrl(whatsappUrl)
                Thread.Sleep(sleepTime * 3)

                ' إرسال الرسالة
                Try
                    Dim messageTextBox = FindMessageInput()
                    Thread.Sleep(sleepTime)
                    If messageTextBox IsNot Nothing Then
                        messageTextBox.Click()
                        Thread.Sleep(sleepTime)
                        messageTextBox.SendKeys(message)
                        SetText($"Message Placed Successfully for: {formattedPhone}")

                        ' إرسال الرسالة باستخدام Enter
                        messageTextBox.SendKeys(OpenQA.Selenium.Keys.Enter)
                        SetText($"Message Sent Successfully to: {formattedPhone}")
                    End If

                    ' إرسال ملف PDF
                    If Not String.IsNullOrEmpty(pdfPath) AndAlso File.Exists(pdfPath) Then
                        Thread.Sleep(sleepTime)
                        Dim attachButton = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath("//button[@title='إرفاق']")))

                        attachButton.Click()
                        Thread.Sleep(sleepTime)

                        Dim fileInput = wait.Until(ExpectedConditions.ElementExists(By.XPath("//input[@accept='*']")))
                        fileInput.SendKeys(pdfPath)
                        Thread.Sleep(sleepTime * 2)

                        Dim sendButton = wait.Until(ExpectedConditions.ElementToBeClickable(By.XPath("//span[@data-icon='send']")))
                        sendButton.Click()
                        Thread.Sleep(sleepTime * 4)
                        SetText($"PDF Sent Successfully to: {formattedPhone}")
                    End If
                Catch ex As Exception
                    SetText($"Error sending message or PDF to {formattedPhone}: {ex.Message}")
                    Continue For
                End Try

                If bStopSendingMessage Then
                    SetText("============Ending==============")
                    Return
                End If

                Thread.Sleep(sleepTime)
            Catch ex1 As Exception
                SetText($"Error processing phone {phoneNumber}: {ex1.Message}")
            End Try
        Next

        SetText("============Ending==============")
    End Sub

    Private Function FindMessageInput() As IWebElement
        Dim possibleSelectors As New List(Of By) From {
            By.XPath("//div[@contenteditable='true'][@data-tab='10']"),
            By.XPath("//div[@role='textbox'][@title='Type a message']"),
            By.CssSelector("div[title='Type a message']"),
            By.XPath("//footer//div[@contenteditable='true']")
        }

        For Each selector In possibleSelectors
            Try
                Dim element = wait.Until(ExpectedConditions.ElementExists(selector))
                If element IsNot Nothing AndAlso element.Displayed Then
                    Return element
                End If
            Catch
                Continue For
            End Try
        Next

        Throw New NoSuchElementException("Could not find message input using any known selector")
    End Function

    Private Sub SetText(logString As String)
        strLogs.AppendLine(logString)
        txtLogs.Text = strLogs.ToString()
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        MyBase.OnFormClosing(e)
        Try
            If m_driver IsNot Nothing Then
                m_driver.Quit()
            End If
        Catch
        End Try
    End Sub

    'Private Sub btnSendMessage_Click(sender As Object, e As EventArgs) Handles btnSendMessage.Click
    '    Dim phoneNumbers As New List(Of String) From {"************"} ' ضع أرقام الهواتف هنا
    '    Dim message As String = "Test message"
    '    Dim pdfPath As String = "F:\aaa\Accounting-Management-System-CSharp.pdf" ' ضع مسار ملف PDF هنا
    '    SendMessagesWithPdf(phoneNumbers, message, pdfPath)
    'End Sub
    Private Sub btnSendMessage_Click(sender As Object, e As EventArgs) Handles btnSendMessage.Click
        ' قراءة أرقام الهواتف
        Dim phoneNumbers As New List(Of String)
        Dim input As String = txtPhoneNumbers.Text.Trim()

        If String.IsNullOrEmpty(input) Then
            MessageBox.Show("يرجى إدخال أرقام الهواتف في الحقل.")
            Return
        End If

        Dim separators As Char() = {","c, vbCr, vbLf}
        Dim phoneArray As String() = input.Split(separators, StringSplitOptions.RemoveEmptyEntries)

        For Each phone In phoneArray
            Dim cleanedPhone As String = phone.Trim()
            If Not String.IsNullOrEmpty(cleanedPhone) Then
                phoneNumbers.Add(cleanedPhone)
            End If
        Next

        If phoneNumbers.Count = 0 Then
            MessageBox.Show("لم يتم العثور على أرقام هواتف صالحة.")
            Return
        End If

        ' قراءة الرسالة من TextBox (إذا كان موجودًا)
        Dim message As String = txtMessage.Text.Trim() ' افتراض أن اسم TextBox هو txtMessage
        If String.IsNullOrEmpty(message) Then
            message = "deyafa thank you" ' رسالة افتراضية إذا كان الحقل فارغًا
        End If

        ' قراءة مسار ملف PDF من TextBox (إذا كان موجودًا)
        Dim pdfPath As String = txtPdfPath.Text.Trim() ' افتراض أن اسم TextBox هو txtPdfPath
        If String.IsNullOrEmpty(pdfPath) OrElse Not File.Exists(pdfPath) Then
            pdfPath = "F:\aaa\Accounting-Management-System-CSharp.pdf" ' مسار افتراضي
        End If

        SendMessagesWithPdf(phoneNumbers, message, pdfPath)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Try
            ' فتح واتساب ويب فقط لمسح QR بدون إرسال رسائل
            m_driver.Url = "https://web.whatsapp.com/"
            m_driver.Manage().Window.Maximize() ' أو Use .Window.Size لتحديد حجم معين
            If Not WaitForWhatsAppLoad() Then
                MessageBox.Show("فشل تحميل واتساب ويب. يرجى المحاولة مرة أخرى.")
                Return
            End If

            MessageBox.Show("يرجى مسح رمز QR من هاتفك ثم اضغط إرسال عند الانتهاء.")
            SetText("تم فتح واتساب ويب. انتظر تسجيل الدخول...")
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ أثناء محاولة فتح واتساب: {ex.Message}")
        End Try
    End Sub
End Class