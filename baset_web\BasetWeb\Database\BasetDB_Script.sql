-- سكريبت إنشاء قاعدة بيانات باسط ويب
-- إنشاء قاعدة البيانات
CREATE DATABASE BasetDB;
GO

USE BasetDB;
GO

-- إن<PERSON><PERSON>ء جدول العملاء
CREATE TABLE Customers (
    CustomerID INT IDENTITY(1,1) PRIMARY KEY,
    CustomerName NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20) NOT NULL,
    Email NVARCHAR(100),
    Address NVARCHAR(255),
    City NVARCHAR(50),
    Country NVARCHAR(50) DEFAULT N'جمهورية مصر العربية',
    PostalCode NVARCHAR(20),
    VATNumber NVARCHAR(50),
    Notes NVARCHAR(MAX),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME
);
GO

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول فئات المنتجات
CREATE TABLE Categories (
    CategoryID INT IDENTITY(1,1) PRIMARY KEY,
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME
);
GO

-- إنشاء جدول المنتجات
CREATE TABLE Products (
    ProductID INT IDENTITY(1,1) PRIMARY KEY,
    ProductName NVARCHAR(100) NOT NULL,
    SKU NVARCHAR(50),
    Barcode NVARCHAR(50),
    Description NVARCHAR(MAX),
    CategoryID INT REFERENCES Categories(CategoryID),
    PurchasePrice DECIMAL(18, 2) DEFAULT 0,
    SellingPrice DECIMAL(18, 2) DEFAULT 0,
    DiscountPrice DECIMAL(18, 2) DEFAULT 0,
    VATRate DECIMAL(5, 2) DEFAULT 15.00,
    StockQuantity INT DEFAULT 0,
    MinStockLevel INT DEFAULT 5,
    ImageURL NVARCHAR(255),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME
);
GO

-- إنشاء جدول الطلبات
CREATE TABLE Orders (
    OrderID INT IDENTITY(1,1) PRIMARY KEY,
    CustomerID INT REFERENCES Customers(CustomerID),
    OrderDate DATETIME DEFAULT GETDATE(),
    DeliveryDate DATETIME,
    Status NVARCHAR(50) DEFAULT N'جديد',
    TotalAmount DECIMAL(18, 2) DEFAULT 0,
    VATAmount DECIMAL(18, 2) DEFAULT 0,
    DiscountAmount DECIMAL(18, 2) DEFAULT 0,
    NetAmount DECIMAL(18, 2) DEFAULT 0,
    PaymentMethod NVARCHAR(50),
    PaymentStatus NVARCHAR(50) DEFAULT N'غير مدفوع',
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME
);
GO

-- إنشاء جدول تفاصيل الطلبات
CREATE TABLE OrderDetails (
    OrderDetailID INT IDENTITY(1,1) PRIMARY KEY,
    OrderID INT REFERENCES Orders(OrderID),
    ProductID INT REFERENCES Products(ProductID),
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(18, 2) NOT NULL,
    VATRate DECIMAL(5, 2) DEFAULT 15.00,
    VATAmount DECIMAL(18, 2) DEFAULT 0,
    DiscountAmount DECIMAL(18, 2) DEFAULT 0,
    TotalAmount DECIMAL(18, 2) DEFAULT 0
);
GO

-- إنشاء جدول الفواتير
CREATE TABLE Invoices (
    InvoiceID INT IDENTITY(1,1) PRIMARY KEY,
    OrderID INT REFERENCES Orders(OrderID),
    InvoiceNumber NVARCHAR(50) NOT NULL,
    InvoiceDate DATETIME DEFAULT GETDATE(),
    DueDate DATETIME,
    TotalAmount DECIMAL(18, 2) DEFAULT 0,
    VATAmount DECIMAL(18, 2) DEFAULT 0,
    DiscountAmount DECIMAL(18, 2) DEFAULT 0,
    NetAmount DECIMAL(18, 2) DEFAULT 0,
    PaidAmount DECIMAL(18, 2) DEFAULT 0,
    RemainingAmount DECIMAL(18, 2) DEFAULT 0,
    PaymentStatus NVARCHAR(50) DEFAULT N'غير مدفوع',
    QRCode NVARCHAR(MAX),
    PIH NVARCHAR(MAX),
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME
);
GO

-- إدخال بيانات تجريبية لفئات المنتجات
INSERT INTO Categories (CategoryName, Description)
VALUES
(N'خضار', N'تشكيلة متنوعة من الخضروات الطازجة'),
(N'فواكه', N'تشكيلة متنوعة من الفواكه الطازجة'),
(N'لحوم', N'لحوم طازجة بأنواعها المختلفة'),
(N'أسماك', N'أسماك طازجة ومأكولات بحرية');
GO

-- إدخال بيانات تجريبية للمنتجات
INSERT INTO Products (ProductName, SKU, Description, CategoryID, PurchasePrice, SellingPrice, StockQuantity)
VALUES
(N'طماطم', 'VEG-TOM-001', N'طماطم طازجة محلية', 1, 4.00, 5.99, 50),
(N'خيار', 'VEG-CUC-002', N'خيار طازج محلي', 1, 3.00, 4.50, 40),
(N'تفاح أحمر', 'FRU-APP-001', N'تفاح أحمر طازج مستورد', 2, 10.00, 12.99, 30),
(N'لحم بقري', 'MEAT-BEEF-001', N'لحم بقري طازج - كيلو', 3, 75.00, 89.99, 15),
(N'سمك سلمون', 'FISH-SAL-001', N'سمك سلمون طازج - كيلو', 4, 100.00, 120.00, 10);
GO

-- إدخال بيانات تجريبية للعملاء
INSERT INTO Customers (CustomerName, PhoneNumber, Email, Address, City)
VALUES
(N'محمد أحمد', '0501234567', '<EMAIL>', N'شارع الملك فهد', N'الرياض'),
(N'فاطمة علي', '0551234567', '<EMAIL>', N'شارع الأمير محمد بن عبدالعزيز', N'جدة'),
(N'عبدالله محمد', '0561234567', '<EMAIL>', N'شارع الخليج', N'الدمام');
GO

-- إنشاء إجراء مخزن لإضافة عميل جديد
CREATE PROCEDURE sp_AddCustomer
    @CustomerName NVARCHAR(100),
    @PhoneNumber NVARCHAR(20),
    @Email NVARCHAR(100) = NULL,
    @Address NVARCHAR(255) = NULL,
    @City NVARCHAR(50) = NULL,
    @Country NVARCHAR(50) = NULL,
    @PostalCode NVARCHAR(20) = NULL,
    @VATNumber NVARCHAR(50) = NULL,
    @Notes NVARCHAR(MAX) = NULL
AS
BEGIN
    INSERT INTO Customers (CustomerName, PhoneNumber, Email, Address, City, Country, PostalCode, VATNumber, Notes)
    VALUES (@CustomerName, @PhoneNumber, @Email, @Address, @City, @Country, @PostalCode, @VATNumber, @Notes);

    SELECT SCOPE_IDENTITY() AS CustomerID;
END
GO

-- إنشاء إجراء مخزن لإضافة منتج جديد
CREATE PROCEDURE sp_AddProduct
    @ProductName NVARCHAR(100),
    @SKU NVARCHAR(50) = NULL,
    @Barcode NVARCHAR(50) = NULL,
    @Description NVARCHAR(MAX) = NULL,
    @CategoryID INT,
    @PurchasePrice DECIMAL(18, 2),
    @SellingPrice DECIMAL(18, 2),
    @DiscountPrice DECIMAL(18, 2) = 0,
    @VATRate DECIMAL(5, 2) = 15.00,
    @StockQuantity INT = 0,
    @MinStockLevel INT = 5,
    @ImageURL NVARCHAR(255) = NULL
AS
BEGIN
    INSERT INTO Products (ProductName, SKU, Barcode, Description, CategoryID, PurchasePrice, SellingPrice,
                         DiscountPrice, VATRate, StockQuantity, MinStockLevel, ImageURL)
    VALUES (@ProductName, @SKU, @Barcode, @Description, @CategoryID, @PurchasePrice, @SellingPrice,
            @DiscountPrice, @VATRate, @StockQuantity, @MinStockLevel, @ImageURL);

    SELECT SCOPE_IDENTITY() AS ProductID;
END
GO

-- إنشاء إجراء مخزن لإنشاء طلب جديد
CREATE PROCEDURE sp_CreateOrder
    @CustomerID INT,
    @DeliveryDate DATETIME = NULL,
    @Status NVARCHAR(50) = N'جديد',
    @PaymentMethod NVARCHAR(50) = NULL,
    @PaymentStatus NVARCHAR(50) = N'غير مدفوع',
    @Notes NVARCHAR(MAX) = NULL
AS
BEGIN
    INSERT INTO Orders (CustomerID, DeliveryDate, Status, PaymentMethod, PaymentStatus, Notes)
    VALUES (@CustomerID, @DeliveryDate, @Status, @PaymentMethod, @PaymentStatus, @Notes);

    SELECT SCOPE_IDENTITY() AS OrderID;
END
GO

-- إنشاء إجراء مخزن لإضافة تفاصيل الطلب
CREATE PROCEDURE sp_AddOrderDetail
    @OrderID INT,
    @ProductID INT,
    @Quantity INT,
    @UnitPrice DECIMAL(18, 2),
    @VATRate DECIMAL(5, 2) = 15.00,
    @DiscountAmount DECIMAL(18, 2) = 0
AS
BEGIN
    DECLARE @VATAmount DECIMAL(18, 2);
    DECLARE @TotalAmount DECIMAL(18, 2);

    SET @VATAmount = (@UnitPrice * @Quantity * @VATRate) / 100;
    SET @TotalAmount = (@UnitPrice * @Quantity) + @VATAmount - @DiscountAmount;

    INSERT INTO OrderDetails (OrderID, ProductID, Quantity, UnitPrice, VATRate, VATAmount, DiscountAmount, TotalAmount)
    VALUES (@OrderID, @ProductID, @Quantity, @UnitPrice, @VATRate, @VATAmount, @DiscountAmount, @TotalAmount);

    -- تحديث إجماليات الطلب
    UPDATE Orders
    SET TotalAmount = (SELECT SUM(UnitPrice * Quantity) FROM OrderDetails WHERE OrderID = @OrderID),
        VATAmount = (SELECT SUM(VATAmount) FROM OrderDetails WHERE OrderID = @OrderID),
        DiscountAmount = (SELECT SUM(DiscountAmount) FROM OrderDetails WHERE OrderID = @OrderID),
        NetAmount = (SELECT SUM(TotalAmount) FROM OrderDetails WHERE OrderID = @OrderID),
        ModifiedDate = GETDATE()
    WHERE OrderID = @OrderID;

    -- تحديث كمية المخزون
    UPDATE Products
    SET StockQuantity = StockQuantity - @Quantity,
        ModifiedDate = GETDATE()
    WHERE ProductID = @ProductID;
END
GO

-- إنشاء إجراء مخزن لإنشاء فاتورة من طلب
CREATE PROCEDURE sp_CreateInvoiceFromOrder
    @OrderID INT,
    @InvoiceNumber NVARCHAR(50),
    @DueDate DATETIME = NULL,
    @QRCode NVARCHAR(MAX) = NULL,
    @PIH NVARCHAR(MAX) = NULL,
    @Notes NVARCHAR(MAX) = NULL
AS
BEGIN
    DECLARE @TotalAmount DECIMAL(18, 2);
    DECLARE @VATAmount DECIMAL(18, 2);
    DECLARE @DiscountAmount DECIMAL(18, 2);
    DECLARE @NetAmount DECIMAL(18, 2);

    -- الحصول على بيانات الطلب
    SELECT @TotalAmount = TotalAmount, @VATAmount = VATAmount,
           @DiscountAmount = DiscountAmount, @NetAmount = NetAmount
    FROM Orders
    WHERE OrderID = @OrderID;

    -- إنشاء الفاتورة
    INSERT INTO Invoices (OrderID, InvoiceNumber, DueDate, TotalAmount, VATAmount,
                         DiscountAmount, NetAmount, RemainingAmount, QRCode, PIH, Notes)
    VALUES (@OrderID, @InvoiceNumber, @DueDate, @TotalAmount, @VATAmount,
            @DiscountAmount, @NetAmount, @NetAmount, @QRCode, @PIH, @Notes);

    -- تحديث حالة الطلب
    UPDATE Orders
    SET Status = N'تم إنشاء الفاتورة',
        ModifiedDate = GETDATE()
    WHERE OrderID = @OrderID;

    SELECT SCOPE_IDENTITY() AS InvoiceID;
END
GO

-- إنشاء وظيفة لتوليد رقم فاتورة جديد
CREATE FUNCTION fn_GenerateInvoiceNumber()
RETURNS NVARCHAR(50)
AS
BEGIN
    DECLARE @Year NVARCHAR(4) = CAST(YEAR(GETDATE()) AS NVARCHAR(4));
    DECLARE @Month NVARCHAR(2) = RIGHT('0' + CAST(MONTH(GETDATE()) AS NVARCHAR(2)), 2);
    DECLARE @LastNumber INT;

    -- الحصول على آخر رقم فاتورة
    SELECT @LastNumber = ISNULL(MAX(CAST(SUBSTRING(InvoiceNumber, 8, 5) AS INT)), 0)
    FROM Invoices
    WHERE InvoiceNumber LIKE @Year + @Month + '-%';

    -- إنشاء رقم فاتورة جديد
    RETURN @Year + @Month + '-' + RIGHT('00000' + CAST(@LastNumber + 1 AS NVARCHAR(5)), 5);
END
GO

-- إنشاء وظيفة لتوليد رقم SKU جديد
CREATE FUNCTION fn_GenerateSKU
(
    @CategoryName NVARCHAR(100),
    @ProductName NVARCHAR(100)
)
RETURNS NVARCHAR(50)
AS
BEGIN
    DECLARE @CategoryPrefix NVARCHAR(3);
    DECLARE @ProductPrefix NVARCHAR(3);
    DECLARE @LastNumber INT;

    -- الحصول على بادئة الفئة
    SET @CategoryPrefix = UPPER(LEFT(@CategoryName, 3));

    -- الحصول على بادئة المنتج
    SET @ProductPrefix = UPPER(LEFT(@ProductName, 3));

    -- الحصول على آخر رقم
    SELECT @LastNumber = ISNULL(MAX(CAST(SUBSTRING(SKU, 8, 4) AS INT)), 0)
    FROM Products
    WHERE SKU LIKE @CategoryPrefix + '-' + @ProductPrefix + '-%';

    -- إنشاء رقم SKU جديد
    RETURN @CategoryPrefix + '-' + @ProductPrefix + '-' + RIGHT('0000' + CAST(@LastNumber + 1 AS NVARCHAR(4)), 4);
END
GO

-- إنشاء مشغل (Trigger) لتحديث تاريخ التعديل للعملاء
CREATE TRIGGER trg_UpdateCustomerModifiedDate
ON Customers
AFTER UPDATE
AS
BEGIN
    UPDATE Customers
    SET ModifiedDate = GETDATE()
    FROM Customers c
    INNER JOIN inserted i ON c.CustomerID = i.CustomerID;
END
GO

-- إنشاء مشغل (Trigger) لتحديث تاريخ التعديل للمنتجات
CREATE TRIGGER trg_UpdateProductModifiedDate
ON Products
AFTER UPDATE
AS
BEGIN
    UPDATE Products
    SET ModifiedDate = GETDATE()
    FROM Products p
    INNER JOIN inserted i ON p.ProductID = i.ProductID;
END
GO

-- إنشاء مشغل (Trigger) لتحديث تاريخ التعديل للطلبات
CREATE TRIGGER trg_UpdateOrderModifiedDate
ON Orders
AFTER UPDATE
AS
BEGIN
    UPDATE Orders
    SET ModifiedDate = GETDATE()
    FROM Orders o
    INNER JOIN inserted i ON o.OrderID = i.OrderID;
END
GO

-- إنشاء مشغل (Trigger) لتحديث تاريخ التعديل للفواتير
CREATE TRIGGER trg_UpdateInvoiceModifiedDate
ON Invoices
AFTER UPDATE
AS
BEGIN
    UPDATE Invoices
    SET ModifiedDate = GETDATE()
    FROM Invoices inv
    INNER JOIN inserted i ON inv.InvoiceID = i.InvoiceID;
END
GO

-- إنشاء مشغل (Trigger) للتحقق من توفر المخزون قبل إضافة تفاصيل الطلب
CREATE TRIGGER trg_CheckStockBeforeOrderDetail
ON OrderDetails
INSTEAD OF INSERT
AS
BEGIN
    DECLARE @ProductID INT;
    DECLARE @Quantity INT;
    DECLARE @StockQuantity INT;
    DECLARE @ProductName NVARCHAR(100);

    SELECT @ProductID = ProductID, @Quantity = Quantity
    FROM inserted;

    SELECT @StockQuantity = StockQuantity, @ProductName = ProductName
    FROM Products
    WHERE ProductID = @ProductID;

    IF @StockQuantity < @Quantity
    BEGIN
        RAISERROR(N'لا يوجد مخزون كافٍ للمنتج %s. المتوفر: %d، المطلوب: %d', 16, 1, @ProductName, @StockQuantity, @Quantity);
        RETURN;
    END

    -- إذا كان المخزون كافياً، قم بإدراج السجل
    INSERT INTO OrderDetails (OrderID, ProductID, Quantity, UnitPrice, VATRate, VATAmount, DiscountAmount, TotalAmount)
    SELECT OrderID, ProductID, Quantity, UnitPrice, VATRate, VATAmount, DiscountAmount, TotalAmount
    FROM inserted;
END
GO

PRINT N'تم إنشاء قاعدة البيانات والجداول والإجراءات المخزنة بنجاح';
GO
