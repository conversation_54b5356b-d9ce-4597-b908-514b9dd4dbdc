using System;
using System.Text;
using System.Xml;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;

namespace BasetWeb.Helpers
{
    public class ZatcaHelper
    {
        public static string GenerateQR(string companyName, string vatNo, string issueDate, decimal total, decimal tax, string invoiceId)
        {
            string qrString = $"|{companyName}|{vatNo}|{issueDate}|{total:F2}|{tax:F2}|{invoiceId}|";
            byte[] bytes = Encoding.UTF8.GetBytes(qrString);
            return Convert.ToBase64String(bytes);
        }

        public static string GeneratePIH(XmlDocument xmlDoc)
        {
            // This is a simplified version of the hash generation
            // In a real implementation, you would use the ZATCA SDK
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = Encoding.UTF8.GetBytes(xmlDoc.OuterXml);
                byte[] hash = sha256.ComputeHash(bytes);
                return Convert.ToBase64String(hash);
            }
        }

        public static XmlDocument SignXml(XmlDocument xmlDoc, string certificateContent, string privateKeyContent)
        {
            // This is a simplified version of XML signing
            // In a real implementation, you would use the ZATCA SDK and proper XML signing

            try
            {
                // Create a SignedXml object
                SignedXml signedXml = new SignedXml(xmlDoc);

                // In a real implementation, you would properly load the certificate and private key
                // This is just a placeholder for demonstration purposes
                // For production, use X509Certificate2Collection.Import methods

                // Create a reference to be signed
                Reference reference = new Reference();
                reference.Uri = "";

                // Add an enveloped transformation to the reference
                XmlDsigEnvelopedSignatureTransform env = new XmlDsigEnvelopedSignatureTransform();
                reference.AddTransform(env);

                // Add the reference to the SignedXml object
                signedXml.AddReference(reference);

                // For demonstration purposes, we're using a dummy RSA key
                using (RSA rsa = RSA.Create())
                {
                    signedXml.SigningKey = rsa;

                    // Compute the signature
                    signedXml.ComputeSignature();

                    // Get the XML representation of the signature
                    XmlElement xmlDigitalSignature = signedXml.GetXml();

                    // Append the signature to the XML document
                    if (xmlDoc.DocumentElement != null)
                    {
                        xmlDoc.DocumentElement.AppendChild(xmlDoc.ImportNode(xmlDigitalSignature, true));
                    }
                }

                return xmlDoc;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error signing XML: {ex.Message}");

                // Return the original document if signing fails
                return xmlDoc;
            }
        }

        public static string GenerateSignedXmlWithQRandPIH(string inputPath, string outputPath, string certContent, string keyContent, string qrData)
        {
            try
            {
                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.PreserveWhitespace = true;
                xmlDoc.Load(inputPath);

                string base64QR = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData));

                XmlDocument signedXml = SignXml(xmlDoc, certContent, keyContent);

                XmlDocument finalDoc = new XmlDocument();
                finalDoc.PreserveWhitespace = true;
                finalDoc.LoadXml(signedXml.OuterXml);

                string pihValue = GeneratePIH(signedXml);

                // Insert QR code and PIH into the XML
                // This is a simplified version
                // In a real implementation, you would need to properly insert these values at the correct locations

                // Ensure the directory exists
                string? directory = Path.GetDirectoryName(outputPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                finalDoc.Save(outputPath);
                return pihValue;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error processing XML: {ex.Message}");

                // Return a default value if processing fails
                return "Error: " + ex.Message;
            }
        }
    }
}
