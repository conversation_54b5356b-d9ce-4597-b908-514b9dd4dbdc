using System.ComponentModel.DataAnnotations;

namespace BasetWeb.Models
{
    public class Category
    {
        public int CategoryID { get; set; }

        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }
    }
}
