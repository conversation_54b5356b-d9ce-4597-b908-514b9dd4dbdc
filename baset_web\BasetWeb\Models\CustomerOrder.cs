using System.ComponentModel.DataAnnotations;

namespace BasetWeb.Models
{
    public class CustomerOrder
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الزبون مطلوب")]
        [Display(Name = "اسم الزبون")]
        public string CustomerName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Display(Name = "رقم الهاتف")]
        [Phone(ErrorMessage = "يرجى إدخال رقم هاتف صحيح")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress(ErrorMessage = "يرجى إدخال بريد إلكتروني صحيح")]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Required(ErrorMessage = "الصنف مطلوب")]
        [Display(Name = "الصنف")]
        public int ProductId { get; set; }

        [Display(Name = "اسم الصنف")]
        public string? ProductName { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Display(Name = "الكمية")]
        [Range(1, 1000, ErrorMessage = "يجب أن تكون الكمية بين 1 و 1000")]
        public int Quantity { get; set; } = 1;

        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الطلب")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Display(Name = "حالة الطلب")]
        public string Status { get; set; } = "جديد";

        [Display(Name = "إجمالي السعر")]
        [DataType(DataType.Currency)]
        public decimal TotalPrice { get; set; }
    }
}
