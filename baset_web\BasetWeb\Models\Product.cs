using System.ComponentModel.DataAnnotations;

namespace BasetWeb.Models
{
    public class Product
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الصنف مطلوب")]
        [Display(Name = "اسم الصنف")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "السعر مطلوب")]
        [Display(Name = "السعر")]
        [DataType(DataType.Currency)]
        public decimal Price { get; set; }

        [Display(Name = "الكمية المتاحة")]
        public int AvailableQuantity { get; set; }

        [Display(Name = "صورة الصنف")]
        public string? ImageUrl { get; set; }

        [Display(Name = "رمز الصنف")]
        public string? SKU { get; set; }

        [Display(Name = "الفئة")]
        public string? Category { get; set; }
    }
}
