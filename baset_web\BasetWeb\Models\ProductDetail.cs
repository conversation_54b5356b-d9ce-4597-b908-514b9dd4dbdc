using System.ComponentModel.DataAnnotations;

namespace BasetWeb.Models
{
    public class ProductDetail
    {
        public int ProductID { get; set; }

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [Display(Name = "اسم المنتج")]
        public string ProductName { get; set; } = string.Empty;

        [Display(Name = "رمز المنتج")]
        public string? SKU { get; set; }

        [Display(Name = "الباركود")]
        public string? Barcode { get; set; }

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الفئة مطلوبة")]
        [Display(Name = "الفئة")]
        public int CategoryID { get; set; }

        [Display(Name = "اسم الفئة")]
        public string? CategoryName { get; set; }

        [Required(ErrorMessage = "سعر الشراء مطلوب")]
        [Display(Name = "سعر الشراء")]
        [DataType(DataType.Currency)]
        public decimal PurchasePrice { get; set; }

        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Display(Name = "سعر البيع")]
        [DataType(DataType.Currency)]
        public decimal SellingPrice { get; set; }

        [Display(Name = "سعر الخصم")]
        [DataType(DataType.Currency)]
        public decimal DiscountPrice { get; set; }

        [Display(Name = "نسبة الضريبة %")]
        public decimal VATRate { get; set; } = 15.00m;

        [Display(Name = "الكمية المتاحة")]
        public int StockQuantity { get; set; }

        [Display(Name = "الحد الأدنى للمخزون")]
        public int MinStockLevel { get; set; } = 5;

        [Display(Name = "صورة المنتج")]
        public string? ImageURL { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        // خصائص إضافية للعرض
        [Display(Name = "الربح")]
        public decimal Profit => SellingPrice - PurchasePrice;

        [Display(Name = "نسبة الربح %")]
        public decimal ProfitMargin => PurchasePrice > 0 ? Math.Round((Profit / PurchasePrice) * 100, 2) : 0;

        [Display(Name = "حالة المخزون")]
        public string StockStatus => StockQuantity <= 0 ? "نفذت الكمية" : 
                                    StockQuantity <= MinStockLevel ? "منخفض" : "متوفر";
    }
}
