using System;
using System.ComponentModel.DataAnnotations;

namespace BasetWeb.Models
{
    public class ZatcaInvoice
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Company name is required")]
        [Display(Name = "Company Name")]
        public string CompanyName { get; set; } = string.Empty;

        [Required(ErrorMessage = "VAT number is required")]
        [Display(Name = "VAT Number")]
        public string VatNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Invoice number is required")]
        [Display(Name = "Invoice Number")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Issue date is required")]
        [Display(Name = "Issue Date")]
        [DataType(DataType.Date)]
        public DateTime IssueDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "Total amount is required")]
        [Display(Name = "Total Amount")]
        [DataType(DataType.Currency)]
        public decimal TotalAmount { get; set; }

        [Required(ErrorMessage = "Tax amount is required")]
        [Display(Name = "Tax Amount")]
        [DataType(DataType.Currency)]
        public decimal TaxAmount { get; set; }

        [Display(Name = "QR Code")]
        public string? QrCode { get; set; }

        [Display(Name = "Previous Invoice Hash")]
        public string? PreviousInvoiceHash { get; set; }

        [Display(Name = "XML Input Path")]
        public string? XmlInputPath { get; set; }

        [Display(Name = "XML Output Path")]
        public string? XmlOutputPath { get; set; }

        [Display(Name = "Certificate")]
        public string? Certificate { get; set; }

        [Display(Name = "Private Key")]
        public string? PrivateKey { get; set; }
    }
}
