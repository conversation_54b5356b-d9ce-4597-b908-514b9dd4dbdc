@page
@model BasetWeb.Pages.AccessDeniedModel
@{
    ViewData["Title"] = "الوصول مرفوض";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white text-center">
                    <h4><i class="bi bi-exclamation-triangle-fill me-2"></i> الوصول مرفوض</h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-lock-fill text-danger" style="font-size: 4rem;"></i>
                    </div>
                    <h5>ليس لديك صلاحية للوصول إلى هذه الصفحة</h5>
                    <p class="text-muted">يرجى تسجيل الدخول بحساب يملك الصلاحيات المناسبة.</p>
                    <div class="mt-4">
                        <a asp-page="/Index" class="btn btn-primary me-2">
                            <i class="bi bi-house-fill me-1"></i> العودة للصفحة الرئيسية
                        </a>
                        <a asp-page="/Login" class="btn btn-outline-primary">
                            <i class="bi bi-box-arrow-in-right me-1"></i> تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
