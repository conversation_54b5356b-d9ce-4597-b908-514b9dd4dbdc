@page "/Catalog"
@model BasetWeb.Pages.CatalogModel
@{
    ViewData["Title"] = "المنتجات المتاحة";
}

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>المنتجات المتاحة</h1>
        <a href="/Catalog" class="btn btn-outline-primary">
            <i class="bi bi-arrow-clockwise"></i> تحديث
        </a>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i> @Model.ErrorMessage
            <div class="mt-2">
                <a href="/Products/Register" class="btn btn-sm btn-success">
                    <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                </a>
                <a href="/Catalog" class="btn btn-sm btn-primary ms-2">
                    <i class="bi bi-arrow-clockwise"></i> إعادة المحاولة
                </a>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        @if (!Model.UsingSampleData && Model.Products.Any())
        {
            @foreach (var product in Model.Products)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            @if (!string.IsNullOrEmpty(product.ImageURL))
                            {
                                <img src="@product.ImageURL" class="img-fluid" alt="@product.ProductName" style="max-height: 180px;">
                            }
                            else
                            {
                                @if (product.CategoryName == "خضار")
                                {
                                    <i class="bi bi-flower1 text-success" style="font-size: 5rem;"></i>
                                }
                                else if (product.CategoryName == "فواكه")
                                {
                                    <i class="bi bi-apple text-danger" style="font-size: 5rem;"></i>
                                }
                                else if (product.CategoryName == "لحوم")
                                {
                                    <i class="bi bi-egg-fried text-warning" style="font-size: 5rem;"></i>
                                }
                                else if (product.CategoryName == "أسماك")
                                {
                                    <i class="bi bi-water text-info" style="font-size: 5rem;"></i>
                                }
                                else
                                {
                                    <i class="bi bi-box text-primary" style="font-size: 5rem;"></i>
                                }
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.ProductName</h5>
                            <p class="card-text">@product.Description</p>
                            <div class="mt-auto">
                                <p class="card-text"><strong>السعر:</strong> @product.SellingPrice.ToString("C")</p>
                                <p class="card-text"><strong>الكمية المتاحة:</strong> @product.StockQuantity</p>
                                <a asp-page="/Order" asp-route-id="@product.ProductID" class="btn btn-primary w-100">طلب هذا المنتج</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else if (Model.UsingSampleData && Model.SampleProducts.Any())
        {
            @foreach (var product in Model.SampleProducts)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="img-fluid" alt="@product.Name" style="max-height: 180px;">
                            }
                            else
                            {
                                @if (product.Category == "خضار")
                                {
                                    <i class="bi bi-flower1 text-success" style="font-size: 5rem;"></i>
                                }
                                else if (product.Category == "فواكه")
                                {
                                    <i class="bi bi-apple text-danger" style="font-size: 5rem;"></i>
                                }
                                else if (product.Category == "لحوم")
                                {
                                    <i class="bi bi-egg-fried text-warning" style="font-size: 5rem;"></i>
                                }
                                else if (product.Category == "أسماك")
                                {
                                    <i class="bi bi-water text-info" style="font-size: 5rem;"></i>
                                }
                                else
                                {
                                    <i class="bi bi-box text-primary" style="font-size: 5rem;"></i>
                                }
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text">@product.Description</p>
                            <div class="mt-auto">
                                <p class="card-text"><strong>السعر:</strong> @product.Price.ToString("C")</p>
                                <p class="card-text"><strong>الكمية المتاحة:</strong> @product.AvailableQuantity</p>
                                <a asp-page="/Order" asp-route-id="@product.Id" class="btn btn-primary w-100">طلب هذا المنتج</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center py-5">
                <i class="bi bi-basket text-muted" style="font-size: 5rem;"></i>
                <h3 class="mt-3">لا توجد منتجات متاحة حالياً</h3>
                <p class="text-muted">يمكنك إضافة منتجات جديدة أو التحقق من إعدادات قاعدة البيانات</p>
                <div class="mt-4">
                    <a href="/Products/Register" class="btn btn-success me-2">
                        <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                    </a>
                    <a href="/Catalog" class="btn btn-primary">
                        <i class="bi bi-arrow-clockwise"></i> تحديث الصفحة
                    </a>
                </div>
            </div>
        }
    </div>
</div>
