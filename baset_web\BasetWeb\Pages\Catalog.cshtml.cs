using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.Data.SqlClient;

namespace BasetWeb.Pages
{
    public class CatalogModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly ProductService _productService;
        private readonly ILogger<CatalogModel> _logger;

        public List<ProductDetail> Products { get; set; } = new List<ProductDetail>();
        public List<Product> SampleProducts { get; set; } = new List<Product>();
        public bool UsingSampleData { get; set; } = false;
        public string ErrorMessage { get; set; } = string.Empty;

        public CatalogModel(DatabaseService databaseService, ProductService productService, ILogger<CatalogModel> logger)
        {
            _databaseService = databaseService;
            _productService = productService;
            _logger = logger;
        }

        public async Task OnGetAsync()
        {
            try
            {
                // محاولة جلب البيانات من قاعدة البيانات
                Products = await _databaseService.GetAllProductsAsync();

                // إذا لم تكن هناك منتجات في قاعدة البيانات، استخدم البيانات الثابتة
                if (Products == null || Products.Count == 0)
                {
                    _logger.LogWarning("لم يتم العثور على منتجات في قاعدة البيانات. استخدام البيانات الثابتة بدلاً من ذلك.");
                    SampleProducts = _productService.GetAllProducts();
                    UsingSampleData = true;

                    if (SampleProducts == null || SampleProducts.Count == 0)
                    {
                        ErrorMessage = "لا توجد منتجات متاحة حالياً. يرجى إضافة منتجات جديدة.";
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, "حدث خطأ في قاعدة البيانات أثناء جلب المنتجات: {ErrorMessage}, رقم الخطأ: {ErrorNumber}",
                    sqlEx.Message, sqlEx.Number);

                if (sqlEx.Number == 4060) // خطأ عدم وجود قاعدة البيانات
                {
                    ErrorMessage = "قاعدة البيانات غير موجودة. يرجى التأكد من إعداد قاعدة البيانات بشكل صحيح.";
                }
                else if (sqlEx.Number == 18456) // خطأ تسجيل الدخول
                {
                    ErrorMessage = "خطأ في الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.";
                }
                else if (sqlEx.Number == 208) // خطأ الجدول غير موجود
                {
                    ErrorMessage = "جداول قاعدة البيانات غير موجودة. يرجى التأكد من تهيئة قاعدة البيانات بشكل صحيح.";
                }
                else
                {
                    ErrorMessage = "حدث خطأ في قاعدة البيانات. يتم عرض بيانات نموذجية بدلاً من ذلك.";
                }

                // استخدام البيانات الثابتة في حالة حدوث خطأ
                SampleProducts = _productService.GetAllProducts();
                UsingSampleData = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "حدث خطأ عام أثناء جلب المنتجات من قاعدة البيانات: {ErrorMessage}", ex.Message);
                ErrorMessage = "حدث خطأ أثناء الاتصال بقاعدة البيانات. يتم عرض بيانات نموذجية بدلاً من ذلك.";

                // استخدام البيانات الثابتة في حالة حدوث خطأ
                SampleProducts = _productService.GetAllProducts();
                UsingSampleData = true;
            }
        }
    }
}
