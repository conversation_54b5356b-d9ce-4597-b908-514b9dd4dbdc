@page
@model BasetWeb.Pages.Customers.IndexModel
@{
    ViewData["Title"] = "قائمة العملاء";
}

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>قائمة العملاء</h1>
        <a asp-page="/Customers/Register" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> إضافة عميل جديد
        </a>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill"></i> @Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill"></i> @Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card shadow">
        <div class="card-body">
            @if (Model.Customers.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-primary">
                            <tr>
                                <th>#</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المدينة</th>
                                <th>الرقم الضريبي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var customer in Model.Customers)
                            {
                                <tr>
                                    <td>@customer.CustomerID</td>
                                    <td>@customer.CustomerName</td>
                                    <td>@customer.PhoneNumber</td>
                                    <td>@(customer.Email ?? "-")</td>
                                    <td>@(customer.City ?? "-")</td>
                                    <td>@(customer.VATNumber ?? "-")</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-page="/Customers/Details" asp-route-id="@customer.CustomerID" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a asp-page="/Customers/Edit" asp-route-id="@customer.CustomerID" class="btn btn-sm btn-warning">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a asp-page="/Customers/Delete" asp-route-id="@customer.CustomerID" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3">لا يوجد عملاء مسجلين</h4>
                    <p class="text-muted">قم بإضافة عملاء جدد باستخدام زر "إضافة عميل جديد"</p>
                </div>
            }
        </div>
    </div>
</div>
