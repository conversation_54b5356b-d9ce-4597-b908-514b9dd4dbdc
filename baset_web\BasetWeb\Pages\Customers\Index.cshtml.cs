using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.AspNetCore.Authorization;

namespace BasetWeb.Pages.Customers
{
    [Authorize(Roles = "Admin")]
    public class IndexModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly ILogger<IndexModel> _logger;

        public List<Customer> Customers { get; set; } = new List<Customer>();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public IndexModel(DatabaseService databaseService, ILogger<IndexModel> logger)
        {
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task OnGetAsync()
        {
            try
            {
                Customers = await _databaseService.GetAllCustomersAsync();

                if (Customers == null || !Customers.Any())
                {
                    // إذا لم تكن هناك عملاء، قم بإنشاء قائمة افتراضية
                    Customers = new List<Customer>
                    {
                        new Customer
                        {
                            CustomerID = 1,
                            CustomerName = "محمد أحمد",
                            PhoneNumber = "0501234567",
                            Email = "<EMAIL>",
                            Address = "شارع الملك فهد",
                            City = "الرياض"
                        },
                        new Customer
                        {
                            CustomerID = 2,
                            CustomerName = "فاطمة علي",
                            PhoneNumber = "0551234567",
                            Email = "<EMAIL>",
                            Address = "شارع الأمير محمد بن عبدالعزيز",
                            City = "جدة"
                        },
                        new Customer
                        {
                            CustomerID = 3,
                            CustomerName = "عبدالله محمد",
                            PhoneNumber = "0561234567",
                            Email = "<EMAIL>",
                            Address = "شارع الخليج",
                            City = "الدمام"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                ErrorMessage = $"حدث خطأ أثناء جلب بيانات العملاء: {ex.Message}";

                // في حالة حدوث خطأ، قم بإنشاء قائمة افتراضية
                Customers = new List<Customer>
                {
                    new Customer
                    {
                        CustomerID = 1,
                        CustomerName = "محمد أحمد",
                        PhoneNumber = "0501234567",
                        Email = "<EMAIL>",
                        Address = "شارع الملك فهد",
                        City = "الرياض"
                    },
                    new Customer
                    {
                        CustomerID = 2,
                        CustomerName = "فاطمة علي",
                        PhoneNumber = "0551234567",
                        Email = "<EMAIL>",
                        Address = "شارع الأمير محمد بن عبدالعزيز",
                        City = "جدة"
                    },
                    new Customer
                    {
                        CustomerID = 3,
                        CustomerName = "عبدالله محمد",
                        PhoneNumber = "0561234567",
                        Email = "<EMAIL>",
                        Address = "شارع الخليج",
                        City = "الدمام"
                    }
                };
            }
        }
    }
}
