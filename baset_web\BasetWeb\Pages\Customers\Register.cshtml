@page
@model BasetWeb.Pages.Customers.RegisterModel
@{
    ViewData["Title"] = "تسجيل عميل جديد";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">تسجيل عميل جديد</h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @Model.ErrorMessage
                        </div>
                    }

                    <form method="post">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Customer.CustomerName" class="form-label"></label>
                                    <input asp-for="Customer.CustomerName" class="form-control" />
                                    <span asp-validation-for="Customer.CustomerName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Customer.PhoneNumber" class="form-label"></label>
                                    <input asp-for="Customer.PhoneNumber" class="form-control" />
                                    <span asp-validation-for="Customer.PhoneNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Customer.Email" class="form-label"></label>
                                    <input asp-for="Customer.Email" class="form-control" type="email" />
                                    <span asp-validation-for="Customer.Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Customer.VATNumber" class="form-label"></label>
                                    <input asp-for="Customer.VATNumber" class="form-control" />
                                    <span asp-validation-for="Customer.VATNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label asp-for="Customer.Address" class="form-label"></label>
                                    <input asp-for="Customer.Address" class="form-control" />
                                    <span asp-validation-for="Customer.Address" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Customer.City" class="form-label"></label>
                                    <input asp-for="Customer.City" class="form-control" />
                                    <span asp-validation-for="Customer.City" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Customer.Country" class="form-label"></label>
                                    <input asp-for="Customer.Country" class="form-control" />
                                    <span asp-validation-for="Customer.Country" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Customer.PostalCode" class="form-label"></label>
                                    <input asp-for="Customer.PostalCode" class="form-control" />
                                    <span asp-validation-for="Customer.PostalCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Customer.Notes" class="form-label"></label>
                            <textarea asp-for="Customer.Notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Customer.Notes" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-page="/Customers/Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-right"></i> العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i> تسجيل العميل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
