using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Authorization;

namespace BasetWeb.Pages.Customers
{
    [Authorize(Roles = "Admin")]
    public class RegisterModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly ILogger<RegisterModel> _logger;

        [BindProperty]
        public Customer Customer { get; set; } = new Customer();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public RegisterModel(DatabaseService databaseService, ILogger<RegisterModel> logger)
        {
            _databaseService = databaseService;
            _logger = logger;
        }

        public void OnGet()
        {
            // تهيئة نموذج العميل الجديد
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // التحقق من وجود قاعدة البيانات والجداول
                try
                {
                    var customerId = await _databaseService.AddCustomerAsync(Customer);

                    if (customerId > 0)
                    {
                        SuccessMessage = $"تم تسجيل العميل {Customer.CustomerName} بنجاح!";
                        return RedirectToPage("/Customers/Index");
                    }
                    else
                    {
                        ErrorMessage = "حدث خطأ أثناء تسجيل العميل. يرجى المحاولة مرة أخرى.";
                        return Page();
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL Error registering customer: {CustomerName}", Customer.CustomerName);

                    // التحقق من نوع الخطأ
                    if (sqlEx.Number == 208) // جدول غير موجود
                    {
                        ErrorMessage = "جدول العملاء غير موجود. يرجى التأكد من تهيئة قاعدة البيانات.";
                    }
                    else if (sqlEx.Number == 4060) // قاعدة بيانات غير موجودة
                    {
                        ErrorMessage = "قاعدة البيانات غير موجودة. يرجى التأكد من تهيئة قاعدة البيانات.";
                    }
                    else
                    {
                        ErrorMessage = $"حدث خطأ في قاعدة البيانات: {sqlEx.Message}";
                    }

                    return Page();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering customer: {CustomerName}", Customer.CustomerName);
                ErrorMessage = $"حدث خطأ أثناء تسجيل العميل: {ex.Message}";
                return Page();
            }
        }
    }
}
