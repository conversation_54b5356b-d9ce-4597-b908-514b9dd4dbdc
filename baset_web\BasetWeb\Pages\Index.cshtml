﻿@page
@model IndexModel
@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<div class="text-center">
    <h1 class="display-4">مرحباً بك في تطبيق باسط للمواد الغذائية</h1>
    <p class="lead">تطبيق متكامل لإدارة وتسوق المنتجات الغذائية الطازجة</p>
</div>

<div class="row mt-4">
    @if (User.Identity?.IsAuthenticated == true)
    {
        <div class="col-md-12 mb-4">
            <div class="alert alert-success">
                <h5><i class="bi bi-person-check-fill me-2"></i> مرحباً بك @User.FindFirst("FullName")?.Value</h5>
                <p>أنت مسجل الدخول كـ @(User.IsInRole("Admin") ? "مسؤول" : "عميل")</p>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>تصفح المنتجات</h5>
                </div>
                <div class="card-body">
                    <p>استعرض مجموعة متنوعة من المنتجات الغذائية الطازجة وقم بطلب ما تحتاجه بسهولة.</p>
                    <a asp-page="/Catalog" class="btn btn-primary">تصفح المنتجات</a>
                </div>
            </div>
        </div>

        @if (User.IsInRole("Admin"))
        {
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>إدارة المخزون</h5>
                    </div>
                    <div class="card-body">
                        <p>إدارة مخزون المنتجات الغذائية وتتبع المبيعات بطريقة سهلة وفعالة.</p>
                        <a asp-page="/Products/Index" class="btn btn-primary">إدارة المنتجات</a>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-md-7">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="bi bi-shop me-2"></i> متجر المنتجات الغذائية</h5>
                </div>
                <div class="card-body">
                    <p>مرحباً بك في تطبيق باسط للمواد الغذائية، حيث يمكنك تصفح مجموعة متنوعة من المنتجات الغذائية الطازجة وطلبها بسهولة.</p>
                    <p>يمكنك تسجيل الدخول كعميل لتصفح المنتجات وإجراء الطلبات، أو كمسؤول للوصول إلى جميع ميزات النظام.</p>
                    <div class="d-flex gap-2">
                        <a asp-page="/Catalog" class="btn btn-success">
                            <i class="bi bi-basket me-2"></i> تصفح المنتجات
                        </a>
                        <a asp-page="/Login" class="btn btn-outline-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i> تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-5">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="bi bi-person-fill-lock me-2"></i> تسجيل الدخول</h5>
                </div>
                <div class="card-body">
                    <form method="post" asp-page="/Login">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person-fill"></i></span>
                                <input type="text" name="LoginInput.Username" class="form-control" placeholder="أدخل اسم المستخدم" required />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                                <input type="password" name="LoginInput.Password" class="form-control" placeholder="أدخل كلمة المرور" required />
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" name="LoginInput.RememberMe" class="form-check-input" id="rememberMe" />
                            <label class="form-check-label" for="rememberMe">تذكرني</label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                    <div class="mt-3 small text-muted">
                        <p class="mb-0">بيانات تسجيل الدخول التجريبية:</p>
                        <p class="mb-0">مسؤول: admin / admin123</p>
                        <p class="mb-0">عميل: customer / customer123</p>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<div class="row mt-2">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>المنتجات المميزة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-flower1 text-success" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">الخضروات</h5>
                                <p>تشكيلة متنوعة من الخضروات الطازجة يومياً</p>
                                <a asp-page="/Catalog" class="btn btn-outline-success">عرض المنتجات</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-apple text-danger" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">الفواكه</h5>
                                <p>فواكه طازجة محلية ومستوردة بأفضل الأسعار</p>
                                <a asp-page="/Catalog" class="btn btn-outline-danger">عرض المنتجات</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-egg-fried text-warning" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">اللحوم</h5>
                                <p>لحوم طازجة بأنواعها المختلفة وبجودة عالية</p>
                                <a asp-page="/Catalog" class="btn btn-outline-warning">عرض المنتجات</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-water text-info" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">الأسماك</h5>
                                <p>أسماك طازجة ومأكولات بحرية متنوعة</p>
                                <a asp-page="/Catalog" class="btn btn-outline-info">عرض المنتجات</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
