@page
@model BasetWeb.Pages.LoginModel
@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white text-center">
                    <h4>تسجيل الدخول</h4>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i> @Model.ErrorMessage
                        </div>
                    }

                    <form method="post">
                        <div class="mb-3">
                            <label asp-for="LoginInput.Username" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person-fill"></i></span>
                                <input asp-for="LoginInput.Username" class="form-control" placeholder="أدخل اسم المستخدم" />
                            </div>
                            <span asp-validation-for="LoginInput.Username" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="LoginInput.Password" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                                <input asp-for="LoginInput.Password" class="form-control" placeholder="أدخل كلمة المرور" />
                            </div>
                            <span asp-validation-for="LoginInput.Password" class="text-danger"></span>
                        </div>
                        <div class="mb-3 form-check">
                            <input asp-for="LoginInput.RememberMe" class="form-check-input" />
                            <label asp-for="LoginInput.RememberMe" class="form-check-label"></label>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <div class="small text-muted">
                        <p class="mb-0">بيانات تسجيل الدخول التجريبية:</p>
                        <p class="mb-0">مسؤول: admin / admin123</p>
                        <p class="mb-0">عميل: customer / customer123</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
