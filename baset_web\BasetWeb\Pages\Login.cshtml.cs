using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;

namespace BasetWeb.Pages
{
    public class LoginModel : PageModel
    {
        private readonly AuthService _authService;
        private readonly ILogger<LoginModel> _logger;

        [BindProperty]
        public Models.LoginModel LoginInput { get; set; } = new Models.LoginModel();

        public string ErrorMessage { get; set; } = string.Empty;

        [TempData]
        public string? SuccessMessage { get; set; }

        public LoginModel(AuthService authService, ILogger<LoginModel> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        public void OnGet()
        {
            // إذا كان المستخدم مسجل دخوله بالفعل، قم بتوجيهه إلى الصفحة الرئيسية
            if (User.Identity?.IsAuthenticated == true)
            {
                Response.Redirect("/");
            }
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");

            if (!ModelState.IsValid)
            {
                return Page();
            }

            var user = await _authService.AuthenticateAsync(LoginInput.Username, LoginInput.Password, HttpContext);

            if (user == null)
            {
                ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                return Page();
            }

            _logger.LogInformation("تم تسجيل دخول المستخدم {Username} بنجاح", user.Username);

            return LocalRedirect(returnUrl);
        }
    }
}
