using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Services;

namespace BasetWeb.Pages
{
    public class LogoutModel : PageModel
    {
        private readonly AuthService _authService;
        private readonly ILogger<LogoutModel> _logger;

        public LogoutModel(AuthService authService, ILogger<LogoutModel> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                await _authService.SignOutAsync(HttpContext);
                _logger.LogInformation("تم تسجيل خروج المستخدم بنجاح");
            }

            return RedirectToPage("/Index");
        }
    }
}
