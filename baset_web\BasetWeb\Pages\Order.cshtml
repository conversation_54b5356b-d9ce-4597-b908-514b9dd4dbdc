@page
@model BasetWeb.Pages.OrderModel
@{
    ViewData["Title"] = "طلب منتج";
}

<div class="container">
    <h1 class="mb-4 text-center">طلب منتج</h1>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @Model.ErrorMessage
        </div>
    }

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>تفاصيل المنتج</h5>
                </div>
                <div class="card-body">
                    @if (Model.SelectedProductDetail != null)
                    {
                        <div class="row">
                            <div class="col-md-4">
                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                    @if (!string.IsNullOrEmpty(Model.SelectedProductDetail.ImageURL))
                                    {
                                        <img src="@Model.SelectedProductDetail.ImageURL" class="img-fluid" alt="@Model.SelectedProductDetail.ProductName" style="max-height: 130px;">
                                    }
                                    else
                                    {
                                        @if (Model.SelectedProductDetail.CategoryName == "خضار")
                                        {
                                            <i class="bi bi-flower1 text-success" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProductDetail.CategoryName == "فواكه")
                                        {
                                            <i class="bi bi-apple text-danger" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProductDetail.CategoryName == "لحوم")
                                        {
                                            <i class="bi bi-egg-fried text-warning" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProductDetail.CategoryName == "أسماك")
                                        {
                                            <i class="bi bi-water text-info" style="font-size: 4rem;"></i>
                                        }
                                        else
                                        {
                                            <i class="bi bi-box text-primary" style="font-size: 4rem;"></i>
                                        }
                                    }
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>@Model.SelectedProductDetail.ProductName</h5>
                                <p>@Model.SelectedProductDetail.Description</p>
                                <p><strong>السعر:</strong> @Model.SelectedProductDetail.SellingPrice.ToString("C")</p>
                                <p><strong>الكمية المتاحة:</strong> @Model.SelectedProductDetail.StockQuantity</p>
                            </div>
                        </div>
                    }
                    else if (Model.SelectedProduct != null)
                    {
                        <div class="row">
                            <div class="col-md-4">
                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                    @if (!string.IsNullOrEmpty(Model.SelectedProduct.ImageUrl))
                                    {
                                        <img src="@Model.SelectedProduct.ImageUrl" class="img-fluid" alt="@Model.SelectedProduct.Name" style="max-height: 130px;">
                                    }
                                    else
                                    {
                                        @if (Model.SelectedProduct.Category == "خضار")
                                        {
                                            <i class="bi bi-flower1 text-success" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProduct.Category == "فواكه")
                                        {
                                            <i class="bi bi-apple text-danger" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProduct.Category == "لحوم")
                                        {
                                            <i class="bi bi-egg-fried text-warning" style="font-size: 4rem;"></i>
                                        }
                                        else if (Model.SelectedProduct.Category == "أسماك")
                                        {
                                            <i class="bi bi-water text-info" style="font-size: 4rem;"></i>
                                        }
                                        else
                                        {
                                            <i class="bi bi-box text-primary" style="font-size: 4rem;"></i>
                                        }
                                    }
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>@Model.SelectedProduct.Name</h5>
                                <p>@Model.SelectedProduct.Description</p>
                                <p><strong>السعر:</strong> @Model.SelectedProduct.Price.ToString("C")</p>
                                <p><strong>الكمية المتاحة:</strong> @Model.SelectedProduct.AvailableQuantity</p>
                            </div>
                        </div>
                    }
                    else
                    {
                        <p>المنتج غير متوفر</p>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <input type="hidden" asp-for="Order.ProductId" />

                        <div class="mb-3">
                            <label asp-for="Order.CustomerName" class="form-label"></label>
                            <input asp-for="Order.CustomerName" class="form-control" />
                            <span asp-validation-for="Order.CustomerName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Order.PhoneNumber" class="form-label"></label>
                            <input asp-for="Order.PhoneNumber" class="form-control" />
                            <span asp-validation-for="Order.PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Order.Email" class="form-label"></label>
                            <input asp-for="Order.Email" class="form-control" type="email" />
                            <span asp-validation-for="Order.Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Order.Address" class="form-label"></label>
                            <textarea asp-for="Order.Address" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Order.Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Order.Quantity" class="form-label"></label>
                            <input asp-for="Order.Quantity" class="form-control" type="number" min="1" max="@(Model.SelectedProductDetail?.StockQuantity ?? Model.SelectedProduct?.AvailableQuantity ?? 1)" />
                            <span asp-validation-for="Order.Quantity" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Order.Notes" class="form-label"></label>
                            <textarea asp-for="Order.Notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Order.Notes" class="text-danger"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تأكيد الطلب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
