using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;

namespace BasetWeb.Pages
{
    public class OrderModel : PageModel
    {
        private readonly ProductService _productService;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<OrderModel> _logger;

        [BindProperty]
        public CustomerOrder Order { get; set; } = new CustomerOrder();

        public Product? SelectedProduct { get; set; }
        public ProductDetail? SelectedProductDetail { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public OrderModel(ProductService productService, DatabaseService databaseService, ILogger<OrderModel> logger)
        {
            _productService = productService;
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return RedirectToPage("/ProductsCatalog");
            }

            try
            {
                // Try to get product from database first
                SelectedProductDetail = await _databaseService.GetProductByIdAsync(id.Value);

                if (SelectedProductDetail != null)
                {
                    // If found in database, use it
                    Order.ProductId = SelectedProductDetail.ProductID;
                    return Page();
                }

                // Fallback to sample products if not found in database
                SelectedProduct = _productService.GetProductById(id.Value);

                if (SelectedProduct == null)
                {
                    return RedirectToPage("/ProductsCatalog");
                }

                Order.ProductId = SelectedProduct.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product with ID {ProductId}", id);
                return RedirectToPage("/ProductsCatalog");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                try
                {
                    // Try to get product from database first
                    SelectedProductDetail = await _databaseService.GetProductByIdAsync(Order.ProductId);

                    if (SelectedProductDetail == null)
                    {
                        // Fallback to sample products if not found in database
                        SelectedProduct = _productService.GetProductById(Order.ProductId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrieving product with ID {ProductId}", Order.ProductId);
                }

                return Page();
            }

            try
            {
                var createdOrder = _productService.CreateOrder(Order);
                SuccessMessage = $"تم إنشاء طلبك بنجاح! رقم الطلب: {createdOrder.Id}";
                return RedirectToPage("/OrderConfirmation", new { id = createdOrder.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order: {ErrorMessage}", ex.Message);
                ErrorMessage = $"حدث خطأ أثناء إنشاء الطلب: {ex.Message}";

                try
                {
                    // Try to get product from database first
                    SelectedProductDetail = await _databaseService.GetProductByIdAsync(Order.ProductId);

                    if (SelectedProductDetail == null)
                    {
                        // Fallback to sample products if not found in database
                        SelectedProduct = _productService.GetProductById(Order.ProductId);
                    }
                }
                catch (Exception getEx)
                {
                    _logger.LogError(getEx, "Error retrieving product with ID {ProductId}", Order.ProductId);
                }

                return Page();
            }
        }
    }
}
