@page
@model BasetWeb.Pages.OrderConfirmationModel
@{
    ViewData["Title"] = "تأكيد الطلب";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0 text-center">تم استلام طلبك بنجاح!</h3>
                </div>
                <div class="card-body">
                    @if (Model.Order != null)
                    {
                        <div class="text-center mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                            <h4 class="mt-3">رقم الطلب: @Model.Order.Id</h4>
                            <p class="text-muted">تاريخ الطلب: @Model.Order.OrderDate.ToString("yyyy-MM-dd HH:mm")</p>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>معلومات الزبون</h5>
                                <p><strong>الاسم:</strong> @Model.Order.CustomerName</p>
                                <p><strong>رقم الهاتف:</strong> @Model.Order.PhoneNumber</p>
                                <p><strong>البريد الإلكتروني:</strong> @Model.Order.Email</p>
                                @if (!string.IsNullOrEmpty(Model.Order.Address))
                                {
                                    <p><strong>العنوان:</strong> @Model.Order.Address</p>
                                }
                            </div>
                            <div class="col-md-6">
                                <h5>تفاصيل الطلب</h5>
                                <p><strong>المنتج:</strong> @Model.Order.ProductName</p>
                                <p><strong>الكمية:</strong> @Model.Order.Quantity</p>
                                <p><strong>السعر الإجمالي:</strong> @Model.Order.TotalPrice.ToString("C")</p>
                                <p><strong>حالة الطلب:</strong> <span class="badge bg-primary">@Model.Order.Status</span></p>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Order.Notes))
                        {
                            <div class="mb-4">
                                <h5>ملاحظات</h5>
                                <p>@Model.Order.Notes</p>
                            </div>
                        }

                        <div class="alert alert-info">
                            <p class="mb-0">سيتم التواصل معك قريباً لتأكيد الطلب وترتيب عملية التسليم.</p>
                        </div>
                    }
                    else
                    {
                        <p class="text-center">لم يتم العثور على معلومات الطلب.</p>
                    }

                    <div class="text-center mt-4">
                        <a asp-page="/Products" class="btn btn-primary">العودة إلى المنتجات</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
