using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;

namespace BasetWeb.Pages
{
    public class OrderConfirmationModel : PageModel
    {
        private readonly ProductService _productService;

        public CustomerOrder? Order { get; set; }
        public Product? Product { get; set; }

        public OrderConfirmationModel(ProductService productService)
        {
            _productService = productService;
        }

        public IActionResult OnGet(int? id)
        {
            if (id == null)
            {
                return RedirectToPage("/Products");
            }

            Order = _productService.GetOrderById(id.Value);
            
            if (Order == null)
            {
                return RedirectToPage("/Products");
            }

            Product = _productService.GetProductById(Order.ProductId);
            
            return Page();
        }
    }
}
