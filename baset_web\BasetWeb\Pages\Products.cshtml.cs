using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.Data.SqlClient;

namespace BasetWeb.Pages
{
    public class ProductsModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly ProductService _productService;
        private readonly ILogger<ProductsModel> _logger;

        public List<ProductDetail> Products { get; set; } = new List<ProductDetail>();
        public List<Product> SampleProducts { get; set; } = new List<Product>();
        public bool UsingSampleData { get; set; } = false;
        public string ErrorMessage { get; set; } = string.Empty;

        public ProductsModel(DatabaseService databaseService, ProductService productService, ILogger<ProductsModel> logger)
        {
            _databaseService = databaseService;
            _productService = productService;
            _logger = logger;
        }

        public IActionResult OnGet()
        {
            return RedirectToPage("/Catalog");
        }
    }
}
