@page
@model BasetWeb.Pages.Products.IndexModel
@{
    ViewData["Title"] = "قائمة المنتجات";
}

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>قائمة المنتجات</h1>
        <a asp-page="/Products/Register" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> إضافة منتج جديد
        </a>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill"></i> @Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill"></i> @Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card shadow">
        <div class="card-body">
            @if (Model.Products.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-primary">
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>الفئة</th>
                                <th>رمز المنتج</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in Model.Products)
                            {
                                <tr>
                                    <td>@product.ProductID</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(product.ImageURL))
                                            {
                                                <img src="@product.ImageURL" alt="@product.ProductName" class="me-2" style="width: 40px; height: 40px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <div class="me-2 d-flex align-items-center justify-content-center bg-light" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-box text-primary"></i>
                                                </div>
                                            }
                                            <span>@product.ProductName</span>
                                        </div>
                                    </td>
                                    <td>@product.CategoryName</td>
                                    <td>@(product.SKU ?? "-")</td>
                                    <td>@product.PurchasePrice.ToString("N2") ر.س</td>
                                    <td>@product.SellingPrice.ToString("N2") ر.س</td>
                                    <td>@product.StockQuantity</td>
                                    <td>
                                        @if (product.StockQuantity <= 0)
                                        {
                                            <span class="badge bg-danger">نفذت الكمية</span>
                                        }
                                        else if (product.StockQuantity <= product.MinStockLevel)
                                        {
                                            <span class="badge bg-warning text-dark">منخفض</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">متوفر</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-page="/Products/Details" asp-route-id="@product.ProductID" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a asp-page="/Products/Edit" asp-route-id="@product.ProductID" class="btn btn-sm btn-warning">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a asp-page="/Products/Delete" asp-route-id="@product.ProductID" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-box-seam text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3">لا توجد منتجات مسجلة</h4>
                    <p class="text-muted">قم بإضافة منتجات جديدة باستخدام زر "إضافة منتج جديد"</p>
                </div>
            }
        </div>
    </div>
</div>
