using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.AspNetCore.Authorization;

namespace BasetWeb.Pages.Products
{
    [Authorize(Roles = "Admin")]
    public class IndexModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly ILogger<IndexModel> _logger;

        public List<ProductDetail> Products { get; set; } = new List<ProductDetail>();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public IndexModel(DatabaseService databaseService, ILogger<IndexModel> logger)
        {
            _databaseService = databaseService;
            _logger = logger;
        }

        public async Task OnGetAsync()
        {
            try
            {
                Products = await _databaseService.GetAllProductsAsync();

                if (Products == null || !Products.Any())
                {
                    // إذا لم تكن هناك منتجات، قم بإنشاء قائمة افتراضية
                    Products = new List<ProductDetail>
                    {
                        new ProductDetail
                        {
                            ProductID = 1,
                            ProductName = "لابتوب ديل XPS 15",
                            Description = "لابتوب ديل XPS 15 بمعالج Intel Core i7، ذاكرة 16GB، وقرص SSD سعة 512GB",
                            CategoryID = 1,
                            CategoryName = "أجهزة الكمبيوتر",
                            PurchasePrice = 4500.00m,
                            SellingPrice = 5999.99m,
                            StockQuantity = 10,
                            SKU = "COM-LAP-0001"
                        },
                        new ProductDetail
                        {
                            ProductID = 2,
                            ProductName = "آيفون 15 برو",
                            Description = "هاتف آيفون 15 برو بسعة 256GB، لون أزرق داكن",
                            CategoryID = 2,
                            CategoryName = "الهواتف الذكية",
                            PurchasePrice = 3800.00m,
                            SellingPrice = 4999.99m,
                            StockQuantity = 15,
                            SKU = "PHO-IPH-0001"
                        },
                        new ProductDetail
                        {
                            ProductID = 3,
                            ProductName = "سماعات سوني WH-1000XM5",
                            Description = "سماعات سوني لاسلكية بخاصية إلغاء الضوضاء",
                            CategoryID = 3,
                            CategoryName = "الصوتيات",
                            PurchasePrice = 1100.00m,
                            SellingPrice = 1499.99m,
                            StockQuantity = 20,
                            SKU = "AUD-SON-0001"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting products");
                ErrorMessage = $"حدث خطأ أثناء جلب بيانات المنتجات: {ex.Message}";

                // في حالة حدوث خطأ، قم بإنشاء قائمة افتراضية
                Products = new List<ProductDetail>
                {
                    new ProductDetail
                    {
                        ProductID = 1,
                        ProductName = "لابتوب ديل XPS 15",
                        Description = "لابتوب ديل XPS 15 بمعالج Intel Core i7، ذاكرة 16GB، وقرص SSD سعة 512GB",
                        CategoryID = 1,
                        CategoryName = "أجهزة الكمبيوتر",
                        PurchasePrice = 4500.00m,
                        SellingPrice = 5999.99m,
                        StockQuantity = 10,
                        SKU = "COM-LAP-0001"
                    },
                    new ProductDetail
                    {
                        ProductID = 2,
                        ProductName = "آيفون 15 برو",
                        Description = "هاتف آيفون 15 برو بسعة 256GB، لون أزرق داكن",
                        CategoryID = 2,
                        CategoryName = "الهواتف الذكية",
                        PurchasePrice = 3800.00m,
                        SellingPrice = 4999.99m,
                        StockQuantity = 15,
                        SKU = "PHO-IPH-0001"
                    },
                    new ProductDetail
                    {
                        ProductID = 3,
                        ProductName = "سماعات سوني WH-1000XM5",
                        Description = "سماعات سوني لاسلكية بخاصية إلغاء الضوضاء",
                        CategoryID = 3,
                        CategoryName = "الصوتيات",
                        PurchasePrice = 1100.00m,
                        SellingPrice = 1499.99m,
                        StockQuantity = 20,
                        SKU = "AUD-SON-0001"
                    }
                };
            }
        }
    }
}
