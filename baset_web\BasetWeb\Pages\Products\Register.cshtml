@page
@model BasetWeb.Pages.Products.RegisterModel
@{
    ViewData["Title"] = "تسجيل منتج جديد";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">تسجيل منتج جديد</h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @Model.ErrorMessage
                        </div>
                    }

                    <form method="post" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Product.ProductName" class="form-label"></label>
                                    <input asp-for="Product.ProductName" class="form-control" />
                                    <span asp-validation-for="Product.ProductName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Product.CategoryID" class="form-label">الفئة</label>
                                    <select asp-for="Product.CategoryID" asp-items="Model.Categories" class="form-select">
                                        <option value="">-- اختر الفئة --</option>
                                    </select>
                                    <span asp-validation-for="Product.CategoryID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.SKU" class="form-label"></label>
                                    <input asp-for="Product.SKU" class="form-control" placeholder="سيتم إنشاؤه تلقائياً إذا تركته فارغاً" />
                                    <span asp-validation-for="Product.SKU" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.Barcode" class="form-label"></label>
                                    <input asp-for="Product.Barcode" class="form-control" />
                                    <span asp-validation-for="Product.Barcode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.VATRate" class="form-label"></label>
                                    <div class="input-group">
                                        <input asp-for="Product.VATRate" class="form-control" />
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <span asp-validation-for="Product.VATRate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label asp-for="Product.Description" class="form-label"></label>
                                    <textarea asp-for="Product.Description" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Product.Description" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.PurchasePrice" class="form-label"></label>
                                    <div class="input-group">
                                        <span class="input-group-text">ر.س</span>
                                        <input asp-for="Product.PurchasePrice" class="form-control" type="number" step="0.01" />
                                    </div>
                                    <span asp-validation-for="Product.PurchasePrice" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.SellingPrice" class="form-label"></label>
                                    <div class="input-group">
                                        <span class="input-group-text">ر.س</span>
                                        <input asp-for="Product.SellingPrice" class="form-control" type="number" step="0.01" />
                                    </div>
                                    <span asp-validation-for="Product.SellingPrice" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Product.DiscountPrice" class="form-label"></label>
                                    <div class="input-group">
                                        <span class="input-group-text">ر.س</span>
                                        <input asp-for="Product.DiscountPrice" class="form-control" type="number" step="0.01" />
                                    </div>
                                    <span asp-validation-for="Product.DiscountPrice" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Product.StockQuantity" class="form-label"></label>
                                    <input asp-for="Product.StockQuantity" class="form-control" type="number" />
                                    <span asp-validation-for="Product.StockQuantity" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Product.MinStockLevel" class="form-label"></label>
                                    <input asp-for="Product.MinStockLevel" class="form-control" type="number" />
                                    <span asp-validation-for="Product.MinStockLevel" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="ProductImage" class="form-label">صورة المنتج</label>
                            <input type="file" id="ProductImage" name="ProductImage" class="form-control" accept="image/*" />
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-page="/Products/Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-right"></i> العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> تسجيل المنتج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
