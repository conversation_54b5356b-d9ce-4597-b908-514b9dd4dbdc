using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using BasetWeb.Models;
using BasetWeb.Services;
using Microsoft.AspNetCore.Authorization;

namespace BasetWeb.Pages.Products
{
    [Authorize(Roles = "Admin")]
    public class RegisterModel : PageModel
    {
        private readonly DatabaseService _databaseService;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<RegisterModel> _logger;

        [BindProperty]
        public ProductDetail Product { get; set; } = new ProductDetail();

        [BindProperty]
        public IFormFile? ProductImage { get; set; }

        public SelectList? Categories { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public RegisterModel(DatabaseService databaseService, IWebHostEnvironment environment, ILogger<RegisterModel> logger)
        {
            _databaseService = databaseService;
            _environment = environment;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadCategoriesAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadCategoriesAsync();
                return Page();
            }

            try
            {
                // معالجة الصورة إذا تم تحميلها
                if (ProductImage != null && ProductImage.Length > 0)
                {
                    string uploadsFolder = Path.Combine(_environment.WebRootPath, "images", "products");
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + ProductImage.FileName;
                    string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await ProductImage.CopyToAsync(fileStream);
                    }

                    Product.ImageURL = "/images/products/" + uniqueFileName;
                }

                // إنشاء رمز SKU إذا لم يتم تحديده
                if (string.IsNullOrEmpty(Product.SKU))
                {
                    var category = await _databaseService.GetAllCategoriesAsync();
                    var categoryName = category.FirstOrDefault(c => c.CategoryID == Product.CategoryID)?.CategoryName ?? "";
                    Product.SKU = await _databaseService.GenerateSKUAsync(categoryName, Product.ProductName);
                }

                var productId = await _databaseService.AddProductAsync(Product);

                if (productId > 0)
                {
                    SuccessMessage = $"تم تسجيل المنتج {Product.ProductName} بنجاح!";
                    return RedirectToPage("/Products/Index");
                }
                else
                {
                    ErrorMessage = "حدث خطأ أثناء تسجيل المنتج. يرجى المحاولة مرة أخرى.";
                    await LoadCategoriesAsync();
                    return Page();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering product: {ProductName}", Product.ProductName);
                ErrorMessage = $"حدث خطأ أثناء تسجيل المنتج: {ex.Message}";
                await LoadCategoriesAsync();
                return Page();
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await _databaseService.GetAllCategoriesAsync();

                if (categories == null || !categories.Any())
                {
                    // إذا لم تكن هناك فئات، قم بإنشاء قائمة افتراضية
                    categories = new List<Category>
                    {
                        new Category { CategoryID = 1, CategoryName = "خضار" },
                        new Category { CategoryID = 2, CategoryName = "فواكه" },
                        new Category { CategoryID = 3, CategoryName = "لحوم" },
                        new Category { CategoryID = 4, CategoryName = "أسماك" }
                    };
                }

                Categories = new SelectList(categories, "CategoryID", "CategoryName");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الفئات");

                // في حالة حدوث خطأ، قم بإنشاء قائمة افتراضية
                var defaultCategories = new List<Category>
                {
                    new Category { CategoryID = 1, CategoryName = "خضار" },
                    new Category { CategoryID = 2, CategoryName = "فواكه" },
                    new Category { CategoryID = 3, CategoryName = "لحوم" },
                    new Category { CategoryID = 4, CategoryName = "أسماك" }
                };

                Categories = new SelectList(defaultCategories, "CategoryID", "CategoryName");

                // إضافة رسالة خطأ
                ErrorMessage = "حدث خطأ أثناء تحميل الفئات. تم استخدام قائمة افتراضية.";
            }
        }
    }
}
