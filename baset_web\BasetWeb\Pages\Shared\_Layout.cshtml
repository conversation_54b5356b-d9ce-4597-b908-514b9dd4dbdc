﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - باسط للمواد الغذائية</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" integrity="sha384-nU14brUcp6StFntEOOEBvcJm4huWjB0OcIeQ3fltAfSmuZFrkAif0T+UtNGlKKQv" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BasetWeb.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-page="/Index">باسط للمواد الغذائية</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-page="/Index">الرئيسية</a>
                        </li>

                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            @if (User.IsInRole("Admin"))
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle text-dark" href="#" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-basket"></i> المنتجات الغذائية
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                                        <li><a class="dropdown-item" asp-area="" asp-page="/Products/Index">قائمة المنتجات</a></li>
                                        <li><a class="dropdown-item" asp-area="" asp-page="/Products/Register">تسجيل منتج جديد</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-area="" asp-page="/Catalog">تصفح المنتجات</a></li>
                                    </ul>
                                </li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle text-dark" href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-people"></i> العملاء
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="customersDropdown">
                                        <li><a class="dropdown-item" asp-area="" asp-page="/Customers/Index">قائمة العملاء</a></li>
                                        <li><a class="dropdown-item" asp-area="" asp-page="/Customers/Register">تسجيل عميل جديد</a></li>
                                    </ul>
                                </li>
                            }
                            else
                            {
                                <li class="nav-item">
                                    <a class="nav-link text-dark" asp-area="" asp-page="/Catalog">
                                        <i class="bi bi-basket"></i> تصفح المنتجات
                                    </a>
                                </li>
                            }
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-page="/Catalog">
                                    <i class="bi bi-basket"></i> تصفح المنتجات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-page="/Login">
                                    <i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول
                                </a>
                            </li>
                        }

                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-page="/Privacy">
                                <i class="bi bi-shield-check"></i> الخصوصية
                            </a>
                        </li>
                    </ul>

                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-dark" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle me-1"></i> @User.FindFirst("FullName")?.Value
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li class="dropdown-item-text text-muted small">
                                        <i class="bi bi-person-badge me-1"></i> @(User.IsInRole("Admin") ? "مسؤول" : "عميل")
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-page="/Logout"><i class="bi bi-box-arrow-right me-1"></i> تسجيل الخروج</a></li>
                                </ul>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - باسط للمواد الغذائية - <a asp-area="" asp-page="/Privacy">الخصوصية</a>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
