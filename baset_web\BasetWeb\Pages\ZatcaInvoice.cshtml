@page
@model BasetWeb.Pages.ZatcaInvoiceModel
@{
    ViewData["Title"] = "ZATCA Invoice Processing";
}

<div class="container">
    <h1 class="mb-4">ZATCA Invoice Processing</h1>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @Model.ErrorMessage
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ProcessingResult))
    {
        <div class="alert alert-success" role="alert">
            @Model.ProcessingResult
        </div>
    }

    <form method="post" enctype="multipart/form-data">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Invoice Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Invoice.CompanyName" class="form-label"></label>
                            <input asp-for="Invoice.CompanyName" class="form-control" />
                            <span asp-validation-for="Invoice.CompanyName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Invoice.VatNumber" class="form-label"></label>
                            <input asp-for="Invoice.VatNumber" class="form-control" />
                            <span asp-validation-for="Invoice.VatNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Invoice.InvoiceNumber" class="form-label"></label>
                            <input asp-for="Invoice.InvoiceNumber" class="form-control" />
                            <span asp-validation-for="Invoice.InvoiceNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Invoice.IssueDate" class="form-label"></label>
                            <input asp-for="Invoice.IssueDate" class="form-control" type="date" />
                            <span asp-validation-for="Invoice.IssueDate" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Invoice.TotalAmount" class="form-label"></label>
                            <input asp-for="Invoice.TotalAmount" class="form-control" type="number" step="0.01" />
                            <span asp-validation-for="Invoice.TotalAmount" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Invoice.TaxAmount" class="form-label"></label>
                            <input asp-for="Invoice.TaxAmount" class="form-control" type="number" step="0.01" />
                            <span asp-validation-for="Invoice.TaxAmount" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Files</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="XmlFile" class="form-label">XML Invoice File</label>
                            <input asp-for="XmlFile" class="form-control" type="file" accept=".xml" />
                        </div>

                        <div class="mb-3">
                            <label asp-for="CertificateFile" class="form-label">Certificate File</label>
                            <input asp-for="CertificateFile" class="form-control" type="file" />
                        </div>

                        <div class="mb-3">
                            <label asp-for="PrivateKeyFile" class="form-label">Private Key File</label>
                            <input asp-for="PrivateKeyFile" class="form-control" type="file" />
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Invoice.QrCode))
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>QR Code Data</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">QR Code Content</label>
                                <textarea class="form-control" rows="3" readonly>@Model.Invoice.QrCode</textarea>
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Invoice.PreviousInvoiceHash))
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>Previous Invoice Hash (PIH)</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <textarea class="form-control" rows="3" readonly>@Model.Invoice.PreviousInvoiceHash</textarea>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="d-grid gap-2 col-md-6 mx-auto">
            <button type="submit" class="btn btn-primary btn-lg">Process Invoice</button>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
