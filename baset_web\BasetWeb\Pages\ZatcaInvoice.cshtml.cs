using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BasetWeb.Models;
using BasetWeb.Services;
using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace BasetWeb.Pages
{
    public class ZatcaInvoiceModel : PageModel
    {
        private readonly XmlService _xmlService;
        private readonly QrCodeService _qrCodeService;
        private readonly IWebHostEnvironment _environment;

        [BindProperty]
        public ZatcaInvoice Invoice { get; set; } = new ZatcaInvoice();

        [BindProperty]
        public IFormFile? XmlFile { get; set; }

        [BindProperty]
        public IFormFile? CertificateFile { get; set; }

        [BindProperty]
        public IFormFile? PrivateKeyFile { get; set; }

        public string? QrCodeImageBase64 { get; set; }
        public string? ProcessingResult { get; set; }
        public string? ErrorMessage { get; set; }

        public ZatcaInvoiceModel(XmlService xmlService, QrCodeService qrCodeService, IWebHostEnvironment environment)
        {
            _xmlService = xmlService;
            _qrCodeService = qrCodeService;
            _environment = environment;
        }

        public void OnGet()
        {
            // Initialize with default values if needed
            Invoice.IssueDate = DateTime.Now;
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Process XML file if uploaded
                string inputXmlPath = "";
                if (XmlFile != null && XmlFile.Length > 0)
                {
                    string uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads");
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + XmlFile.FileName;
                    inputXmlPath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var fileStream = new FileStream(inputXmlPath, FileMode.Create))
                    {
                        await XmlFile.CopyToAsync(fileStream);
                    }

                    Invoice.XmlInputPath = inputXmlPath;
                }

                // Process certificate and private key files
                string certificateContent = "";
                string privateKeyContent = "";

                if (CertificateFile != null && CertificateFile.Length > 0)
                {
                    using (var reader = new StreamReader(CertificateFile.OpenReadStream()))
                    {
                        certificateContent = await reader.ReadToEndAsync();
                    }
                    Invoice.Certificate = certificateContent;
                }

                if (PrivateKeyFile != null && PrivateKeyFile.Length > 0)
                {
                    using (var reader = new StreamReader(PrivateKeyFile.OpenReadStream()))
                    {
                        privateKeyContent = await reader.ReadToEndAsync();
                    }
                    Invoice.PrivateKey = privateKeyContent;
                }

                // Generate QR code
                string qrData = _xmlService.GenerateQrCode(
                    Invoice.CompanyName,
                    Invoice.VatNumber,
                    Invoice.IssueDate.ToString("yyyy-MM-dd"),
                    Invoice.TotalAmount,
                    Invoice.TaxAmount,
                    Invoice.InvoiceNumber
                );

                Invoice.QrCode = qrData;
                QrCodeImageBase64 = _qrCodeService.GenerateQrCodeBase64(qrData);

                // Process the invoice if XML file was uploaded
                if (!string.IsNullOrEmpty(inputXmlPath))
                {
                    string outputsFolder = Path.Combine(_environment.WebRootPath, "outputs");
                    if (!Directory.Exists(outputsFolder))
                    {
                        Directory.CreateDirectory(outputsFolder);
                    }

                    string outputXmlPath = Path.Combine(outputsFolder, Guid.NewGuid().ToString() + "_output.xml");

                    string pihValue = _xmlService.ProcessInvoice(
                        inputXmlPath,
                        outputXmlPath,
                        certificateContent,
                        privateKeyContent,
                        qrData
                    );

                    Invoice.PreviousInvoiceHash = pihValue;
                    Invoice.XmlOutputPath = outputXmlPath;

                    ProcessingResult = "Invoice processed successfully. PIH: " + pihValue;
                }
                else
                {
                    ProcessingResult = "QR code generated successfully.";
                }

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "Error processing invoice: " + ex.Message;
                return Page();
            }
        }
    }
}
