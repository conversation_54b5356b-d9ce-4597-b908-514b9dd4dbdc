using Microsoft.Data.SqlClient;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages();

// Add authentication
builder.Services.AddAuthentication("Cookies")
    .AddCookie(options =>
    {
        options.Cookie.Name = "BasetWebAuth";
        options.LoginPath = "/Login";
        options.LogoutPath = "/Logout";
        options.AccessDeniedPath = "/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromHours(3);
    });

// Register custom services
builder.Services.AddScoped<BasetWeb.Services.XmlService>();
builder.Services.AddScoped<BasetWeb.Services.QrCodeService>();
builder.Services.AddSingleton<BasetWeb.Services.ProductService>();
builder.Services.AddScoped<BasetWeb.Services.DatabaseService>();
builder.Services.AddScoped<BasetWeb.Services.DbInitializer>();
builder.Services.AddSingleton<BasetWeb.Services.AuthService>();

var app = builder.Build();

// تهيئة قاعدة البيانات عند بدء التطبيق
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    logger.LogInformation("بدء تهيئة قاعدة البيانات...");

    try
    {
        var dbInitializer = services.GetRequiredService<BasetWeb.Services.DbInitializer>();
        dbInitializer.InitializeAsync().Wait();
        logger.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
    }
    catch (SqlException sqlEx)
    {
        logger.LogError(sqlEx, "حدث خطأ في قاعدة البيانات أثناء التهيئة: {ErrorMessage}, رقم الخطأ: {ErrorNumber}",
            sqlEx.Message, sqlEx.Number);

        if (sqlEx.Number == 4060) // خطأ عدم وجود قاعدة البيانات
        {
            logger.LogError("قاعدة البيانات غير موجودة. يرجى التأكد من إعداد قاعدة البيانات بشكل صحيح.");
        }
        else if (sqlEx.Number == 18456) // خطأ تسجيل الدخول
        {
            logger.LogError("خطأ في الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.");
        }
        else if (sqlEx.Number == 208) // خطأ الجدول غير موجود
        {
            logger.LogError("جداول قاعدة البيانات غير موجودة. يرجى التأكد من تهيئة قاعدة البيانات بشكل صحيح.");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "حدث خطأ عام أثناء تهيئة قاعدة البيانات: {ErrorMessage}", ex.Message);
    }
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapStaticAssets();
app.MapRazorPages()
   .WithStaticAssets();

app.Run();
