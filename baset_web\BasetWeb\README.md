# تطبيق باسط ويب

تطبيق ويب متكامل لإدارة المنتجات والعملاء والفواتير الإلكترونية.

## المتطلبات

- .NET 9.0 SDK أو أحدث
- Microsoft SQL Server (يمكن استخدام SQL Server Express)
- متصفح ويب حديث

## إعداد قاعدة البيانات

يقوم التطبيق تلقائياً بإنشاء قاعدة البيانات وتهيئتها عند بدء التشغيل. إذا واجهت أي مشاكل، يمكنك إعداد قاعدة البيانات يدوياً باتباع الخطوات التالية:

1. افتح SQL Server Management Studio
2. قم بإنشاء قاعدة بيانات جديدة باسم `BasetDB`
3. قم بتنفيذ سكريبت إنشاء قاعدة البيانات الموجود في ملف `Database/BasetDB_Script.sql`

## تعديل سلسلة الاتصال

إذا كنت تستخدم خادم SQL Server مختلف أو لديك إعدادات مخصصة، قم بتعديل سلسلة الاتصال في ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER;Database=BasetDB;Trusted_Connection=True;TrustServerCertificate=True;"
  },
  ...
}
```

## تشغيل التطبيق

1. انتقل إلى مجلد المشروع
2. قم بتشغيل التطبيق باستخدام الأمر التالي:

```
dotnet run
```

3. افتح متصفح الويب وانتقل إلى `https://localhost:5001` أو `http://localhost:5000`

## الميزات

- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **إدارة العملاء**: تسجيل العملاء وإدارة بياناتهم
- **معالجة فواتير ZATCA**: إنشاء ومعالجة فواتير متوافقة مع متطلبات هيئة الزكاة والضريبة والجمارك (ZATCA)
- **طلب المنتجات**: إمكانية طلب المنتجات من قبل العملاء

## ملفات نموذجية

يتضمن التطبيق ملفات نموذجية للاختبار:

- نموذج فاتورة XML: `/wwwroot/xml/sample_invoice.xml`
- نموذج شهادة: `/wwwroot/xml/sample_certificate.txt`
- نموذج مفتاح خاص: `/wwwroot/xml/sample_privatekey.txt`

## استكشاف الأخطاء وإصلاحها

### مشكلة في الاتصال بقاعدة البيانات

إذا واجهت مشكلة في الاتصال بقاعدة البيانات، تأكد من:

1. تشغيل خدمة SQL Server
2. صحة سلسلة الاتصال في ملف `appsettings.json`
3. وجود الصلاحيات اللازمة للوصول إلى قاعدة البيانات

### مشكلة في تسجيل أو عرض المنتجات

إذا واجهت مشكلة في تسجيل أو عرض المنتجات، يمكنك:

1. التأكد من وجود قاعدة البيانات وتهيئتها بشكل صحيح
2. التحقق من وجود جدول المنتجات وجدول الفئات
3. التأكد من وجود الإجراءات المخزنة اللازمة

يمكنك تنفيذ سكريبت إنشاء قاعدة البيانات مرة أخرى للتأكد من وجود جميع الجداول والإجراءات المخزنة.

## ملاحظات

- هذا تطبيق تجريبي لأغراض العرض فقط
- في بيئة الإنتاج، يجب تنفيذ إجراءات أمان إضافية
- يجب استخدام SDK رسمي من ZATCA للتكامل الكامل مع نظام الفواتير الإلكترونية

## الترخيص

هذا المشروع مرخص تحت رخصة MIT
