using BasetWeb.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;

namespace BasetWeb.Services
{
    public class AuthService
    {
        private readonly ILogger<AuthService> _logger;
        private readonly List<User> _users;

        public AuthService(ILogger<AuthService> logger)
        {
            _logger = logger;
            
            // قائمة المستخدمين الافتراضية (في تطبيق حقيقي، ستكون هذه البيانات في قاعدة البيانات)
            _users = new List<User>
            {
                new User
                {
                    Id = 1,
                    Username = "admin",
                    Password = "admin123", // في تطبيق حقيقي، يجب تشفير كلمات المرور
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    PhoneNumber = "0500000000",
                    Role = UserRole.Admin,
                    IsActive = true
                },
                new User
                {
                    Id = 2,
                    Username = "customer",
                    Password = "customer123", // في تطبيق حقيقي، يجب تشفير كلمات المرور
                    FullName = "عميل نموذجي",
                    Email = "<EMAIL>",
                    PhoneNumber = "0511111111",
                    Role = UserRole.Customer,
                    IsActive = true
                }
            };
        }

        public async Task<User?> AuthenticateAsync(string username, string password, HttpContext httpContext)
        {
            // التحقق من اسم المستخدم وكلمة المرور
            var user = _users.FirstOrDefault(u => u.Username == username && u.Password == password && u.IsActive);
            
            if (user == null)
            {
                _logger.LogWarning("محاولة تسجيل دخول فاشلة لاسم المستخدم: {Username}", username);
                return null;
            }

            // إنشاء مطالبات المستخدم (claims)
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Role, user.Role.ToString()),
                new Claim("FullName", user.FullName),
                new Claim("UserId", user.Id.ToString())
            };

            // إنشاء هوية المستخدم
            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var authProperties = new AuthenticationProperties
            {
                IsPersistent = true,
                ExpiresUtc = DateTimeOffset.UtcNow.AddHours(3) // تنتهي صلاحية الجلسة بعد 3 ساعات
            };

            // تسجيل دخول المستخدم
            await httpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                new ClaimsPrincipal(claimsIdentity),
                authProperties);

            // تحديث تاريخ آخر تسجيل دخول
            user.LastLoginDate = DateTime.Now;
            
            _logger.LogInformation("تم تسجيل دخول المستخدم: {Username} بنجاح", username);
            
            return user;
        }

        public async Task SignOutAsync(HttpContext httpContext)
        {
            await httpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            _logger.LogInformation("تم تسجيل خروج المستخدم بنجاح");
        }

        public User? GetUserByUsername(string username)
        {
            return _users.FirstOrDefault(u => u.Username == username);
        }

        public List<User> GetAllUsers()
        {
            return _users;
        }
    }
}
