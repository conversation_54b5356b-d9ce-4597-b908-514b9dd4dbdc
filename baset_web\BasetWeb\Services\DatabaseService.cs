using System.Data;
using Microsoft.Data.SqlClient;
using BasetWeb.Models;

namespace BasetWeb.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseService> _logger;

        public DatabaseService(IConfiguration configuration, ILogger<DatabaseService> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                                "Server=localhost;Database=BasetDB;Trusted_Connection=True;TrustServerCertificate=True;";
            _logger = logger;
        }

        #region Customer Methods

        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            var customers = new List<Customer>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("SELECT * FROM Customers WHERE IsActive = 1 ORDER BY CustomerName", connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                customers.Add(MapCustomerFromReader(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all customers");
                throw;
            }

            return customers;
        }

        public async Task<Customer?> GetCustomerByIdAsync(int customerId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("SELECT * FROM Customers WHERE CustomerID = @CustomerID", connection))
                    {
                        command.Parameters.AddWithValue("@CustomerID", customerId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return MapCustomerFromReader(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by ID: {CustomerId}", customerId);
                throw;
            }

            return null;
        }

        public async Task<int> AddCustomerAsync(Customer customer)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("sp_AddCustomer", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@CustomerName", customer.CustomerName);
                        command.Parameters.AddWithValue("@PhoneNumber", customer.PhoneNumber);
                        command.Parameters.AddWithValue("@Email", customer.Email ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Address", customer.Address ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@City", customer.City ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Country", customer.Country ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PostalCode", customer.PostalCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@VATNumber", customer.VATNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Notes", customer.Notes ?? (object)DBNull.Value);

                        var result = await command.ExecuteScalarAsync();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding customer: {CustomerName}", customer.CustomerName);
                throw;
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(@"
                        UPDATE Customers
                        SET CustomerName = @CustomerName,
                            PhoneNumber = @PhoneNumber,
                            Email = @Email,
                            Address = @Address,
                            City = @City,
                            Country = @Country,
                            PostalCode = @PostalCode,
                            VATNumber = @VATNumber,
                            Notes = @Notes,
                            IsActive = @IsActive
                        WHERE CustomerID = @CustomerID", connection))
                    {
                        command.Parameters.AddWithValue("@CustomerID", customer.CustomerID);
                        command.Parameters.AddWithValue("@CustomerName", customer.CustomerName);
                        command.Parameters.AddWithValue("@PhoneNumber", customer.PhoneNumber);
                        command.Parameters.AddWithValue("@Email", customer.Email ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Address", customer.Address ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@City", customer.City ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Country", customer.Country ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PostalCode", customer.PostalCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@VATNumber", customer.VATNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Notes", customer.Notes ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", customer.IsActive);

                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer: {CustomerId}", customer.CustomerID);
                throw;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int customerId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Soft delete - just mark as inactive
                    using (var command = new SqlCommand("UPDATE Customers SET IsActive = 0 WHERE CustomerID = @CustomerID", connection))
                    {
                        command.Parameters.AddWithValue("@CustomerID", customerId);

                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer: {CustomerId}", customerId);
                throw;
            }
        }

        private Customer MapCustomerFromReader(SqlDataReader reader)
        {
            return new Customer
            {
                CustomerID = reader.GetInt32(reader.GetOrdinal("CustomerID")),
                CustomerName = reader.GetString(reader.GetOrdinal("CustomerName")),
                PhoneNumber = reader.GetString(reader.GetOrdinal("PhoneNumber")),
                Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email")),
                Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                City = reader.IsDBNull(reader.GetOrdinal("City")) ? null : reader.GetString(reader.GetOrdinal("City")),
                Country = reader.IsDBNull(reader.GetOrdinal("Country")) ? null : reader.GetString(reader.GetOrdinal("Country")),
                PostalCode = reader.IsDBNull(reader.GetOrdinal("PostalCode")) ? null : reader.GetString(reader.GetOrdinal("PostalCode")),
                VATNumber = reader.IsDBNull(reader.GetOrdinal("VATNumber")) ? null : reader.GetString(reader.GetOrdinal("VATNumber")),
                Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                ModifiedDate = reader.IsDBNull(reader.GetOrdinal("ModifiedDate")) ? null : reader.GetDateTime(reader.GetOrdinal("ModifiedDate")) as DateTime?
            };
        }

        #endregion

        #region Category Methods

        public async Task<List<Category>> GetAllCategoriesAsync()
        {
            var categories = new List<Category>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("SELECT * FROM Categories WHERE IsActive = 1 ORDER BY CategoryName", connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                categories.Add(MapCategoryFromReader(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all categories");
                throw;
            }

            return categories;
        }

        private Category MapCategoryFromReader(SqlDataReader reader)
        {
            return new Category
            {
                CategoryID = reader.GetInt32(reader.GetOrdinal("CategoryID")),
                CategoryName = reader.GetString(reader.GetOrdinal("CategoryName")),
                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                ModifiedDate = reader.IsDBNull(reader.GetOrdinal("ModifiedDate")) ? null : reader.GetDateTime(reader.GetOrdinal("ModifiedDate")) as DateTime?
            };
        }

        #endregion

        #region Product Methods

        public async Task<List<ProductDetail>> GetAllProductsAsync()
        {
            var products = new List<ProductDetail>();

            try
            {
                // نستخدم سلسلة الاتصال التي تم تكوينها مسبقًا والتي تحتوي على إعدادات المهلة
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(@"
                        SELECT p.*, c.CategoryName
                        FROM Products p
                        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
                        WHERE p.IsActive = 1
                        ORDER BY p.ProductName", connection))
                    {
                        // تعيين مهلة تنفيذ الاستعلام
                        command.CommandTimeout = 30; // 30 seconds timeout

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                products.Add(MapProductFromReader(reader));
                            }
                        }
                    }
                }

                _logger.LogInformation("تم جلب {Count} منتج من قاعدة البيانات بنجاح", products.Count);
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, "خطأ في قاعدة البيانات أثناء جلب المنتجات: {ErrorMessage}, رقم الخطأ: {ErrorNumber}",
                    sqlEx.Message, sqlEx.Number);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ عام أثناء جلب المنتجات: {ErrorMessage}", ex.Message);
                throw;
            }

            return products;
        }

        public async Task<ProductDetail?> GetProductByIdAsync(int productId)
        {
            try
            {
                // نستخدم سلسلة الاتصال التي تم تكوينها مسبقًا والتي تحتوي على إعدادات المهلة
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(@"
                        SELECT p.*, c.CategoryName
                        FROM Products p
                        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
                        WHERE p.ProductID = @ProductID", connection))
                    {
                        // تعيين مهلة تنفيذ الاستعلام
                        command.CommandTimeout = 30; // 30 seconds timeout

                        command.Parameters.AddWithValue("@ProductID", productId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var product = MapProductFromReader(reader);
                                _logger.LogInformation("تم جلب المنتج برقم {ProductId} بنجاح", productId);
                                return product;
                            }
                        }
                    }
                }

                _logger.LogWarning("لم يتم العثور على منتج برقم {ProductId}", productId);
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, "خطأ في قاعدة البيانات أثناء جلب المنتج برقم {ProductId}: {ErrorMessage}, رقم الخطأ: {ErrorNumber}",
                    productId, sqlEx.Message, sqlEx.Number);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ عام أثناء جلب المنتج برقم {ProductId}: {ErrorMessage}",
                    productId, ex.Message);
                throw;
            }

            return null;
        }

        public async Task<int> AddProductAsync(ProductDetail product)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("sp_AddProduct", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@ProductName", product.ProductName);
                        command.Parameters.AddWithValue("@SKU", product.SKU ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Barcode", product.Barcode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Description", product.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CategoryID", product.CategoryID);
                        command.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
                        command.Parameters.AddWithValue("@SellingPrice", product.SellingPrice);
                        command.Parameters.AddWithValue("@DiscountPrice", product.DiscountPrice);
                        command.Parameters.AddWithValue("@VATRate", product.VATRate);
                        command.Parameters.AddWithValue("@StockQuantity", product.StockQuantity);
                        command.Parameters.AddWithValue("@MinStockLevel", product.MinStockLevel);
                        command.Parameters.AddWithValue("@ImageURL", product.ImageURL ?? (object)DBNull.Value);

                        var result = await command.ExecuteScalarAsync();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding product: {ProductName}", product.ProductName);
                throw;
            }
        }

        public async Task<bool> UpdateProductAsync(ProductDetail product)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(@"
                        UPDATE Products
                        SET ProductName = @ProductName,
                            SKU = @SKU,
                            Barcode = @Barcode,
                            Description = @Description,
                            CategoryID = @CategoryID,
                            PurchasePrice = @PurchasePrice,
                            SellingPrice = @SellingPrice,
                            DiscountPrice = @DiscountPrice,
                            VATRate = @VATRate,
                            StockQuantity = @StockQuantity,
                            MinStockLevel = @MinStockLevel,
                            ImageURL = @ImageURL,
                            IsActive = @IsActive
                        WHERE ProductID = @ProductID", connection))
                    {
                        command.Parameters.AddWithValue("@ProductID", product.ProductID);
                        command.Parameters.AddWithValue("@ProductName", product.ProductName);
                        command.Parameters.AddWithValue("@SKU", product.SKU ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Barcode", product.Barcode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Description", product.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CategoryID", product.CategoryID);
                        command.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
                        command.Parameters.AddWithValue("@SellingPrice", product.SellingPrice);
                        command.Parameters.AddWithValue("@DiscountPrice", product.DiscountPrice);
                        command.Parameters.AddWithValue("@VATRate", product.VATRate);
                        command.Parameters.AddWithValue("@StockQuantity", product.StockQuantity);
                        command.Parameters.AddWithValue("@MinStockLevel", product.MinStockLevel);
                        command.Parameters.AddWithValue("@ImageURL", product.ImageURL ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", product.IsActive);

                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product: {ProductId}", product.ProductID);
                throw;
            }
        }

        public async Task<bool> DeleteProductAsync(int productId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Soft delete - just mark as inactive
                    using (var command = new SqlCommand("UPDATE Products SET IsActive = 0 WHERE ProductID = @ProductID", connection))
                    {
                        command.Parameters.AddWithValue("@ProductID", productId);

                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product: {ProductId}", productId);
                throw;
            }
        }

        public async Task<string> GenerateSKUAsync(string categoryName, string productName)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // التحقق من وجود الوظيفة
                    using (var checkCommand = new SqlCommand("SELECT COUNT(*) FROM sys.objects WHERE type_desc = 'SQL_SCALAR_FUNCTION' AND name = 'fn_GenerateSKU'", connection))
                    {
                        int functionCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                        if (functionCount == 0)
                        {
                            // إذا لم تكن الوظيفة موجودة، قم بإنشاء رمز SKU يدوياً
                            string categoryPrefix = categoryName.Length >= 3 ? categoryName.Substring(0, 3).ToUpper() : categoryName.ToUpper();
                            string productPrefix = productName.Length >= 3 ? productName.Substring(0, 3).ToUpper() : productName.ToUpper();
                            string randomNumber = new Random().Next(1000, 9999).ToString();

                            return $"{categoryPrefix}-{productPrefix}-{randomNumber}";
                        }
                        else
                        {
                            // استخدام الوظيفة إذا كانت موجودة
                            using (var command = new SqlCommand("SELECT dbo.fn_GenerateSKU(@CategoryName, @ProductName)", connection))
                            {
                                command.Parameters.AddWithValue("@CategoryName", categoryName);
                                command.Parameters.AddWithValue("@ProductName", productName);

                                var result = await command.ExecuteScalarAsync();
                                return result?.ToString() ?? string.Empty;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SKU for product: {ProductName}", productName);

                // في حالة حدوث خطأ، قم بإنشاء رمز SKU يدوياً
                string categoryPrefix = categoryName.Length >= 3 ? categoryName.Substring(0, 3).ToUpper() : categoryName.ToUpper();
                string productPrefix = productName.Length >= 3 ? productName.Substring(0, 3).ToUpper() : productName.ToUpper();
                string randomNumber = new Random().Next(1000, 9999).ToString();

                return $"{categoryPrefix}-{productPrefix}-{randomNumber}";
            }
        }

        private ProductDetail MapProductFromReader(SqlDataReader reader)
        {
            return new ProductDetail
            {
                ProductID = reader.GetInt32(reader.GetOrdinal("ProductID")),
                ProductName = reader.GetString(reader.GetOrdinal("ProductName")),
                SKU = reader.IsDBNull(reader.GetOrdinal("SKU")) ? null : reader.GetString(reader.GetOrdinal("SKU")),
                Barcode = reader.IsDBNull(reader.GetOrdinal("Barcode")) ? null : reader.GetString(reader.GetOrdinal("Barcode")),
                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                CategoryID = reader.GetInt32(reader.GetOrdinal("CategoryID")),
                CategoryName = reader.GetString(reader.GetOrdinal("CategoryName")),
                PurchasePrice = reader.GetDecimal(reader.GetOrdinal("PurchasePrice")),
                SellingPrice = reader.GetDecimal(reader.GetOrdinal("SellingPrice")),
                DiscountPrice = reader.GetDecimal(reader.GetOrdinal("DiscountPrice")),
                VATRate = reader.GetDecimal(reader.GetOrdinal("VATRate")),
                StockQuantity = reader.GetInt32(reader.GetOrdinal("StockQuantity")),
                MinStockLevel = reader.GetInt32(reader.GetOrdinal("MinStockLevel")),
                ImageURL = reader.IsDBNull(reader.GetOrdinal("ImageURL")) ? null : reader.GetString(reader.GetOrdinal("ImageURL")),
                IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                ModifiedDate = reader.IsDBNull(reader.GetOrdinal("ModifiedDate")) ? null : reader.GetDateTime(reader.GetOrdinal("ModifiedDate")) as DateTime?
            };
        }

        #endregion
    }
}
