using Microsoft.Data.SqlClient;
using System.Data;

namespace BasetWeb.Services
{
    public class DbInitializer
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DbInitializer> _logger;

        public DbInitializer(IConfiguration configuration, ILogger<DbInitializer> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection") ??
                                         "Server=localhost;Database=BasetDB;Trusted_Connection=True;TrustServerCertificate=True;";

                // تحقق من وجود قاعدة البيانات وإنشائها إذا لم تكن موجودة
                await EnsureDatabaseExistsAsync(connectionString);

                // تحقق من وجود الجداول وإنشائها إذا لم تكن موجودة
                await EnsureTablesExistAsync(connectionString);

                // إضافة بيانات تجريبية إذا كانت الجداول فارغة
                await SeedDataAsync(connectionString);

                _logger.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "حدث خطأ أثناء تهيئة قاعدة البيانات");
                throw;
            }
        }

        private async Task EnsureDatabaseExistsAsync(string connectionString)
        {
            // استخراج اسم قاعدة البيانات من سلسلة الاتصال
            SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
            string databaseName = builder.InitialCatalog;

            // إنشاء سلسلة اتصال بدون اسم قاعدة البيانات للاتصال بالخادم
            builder.InitialCatalog = "master";
            string masterConnectionString = builder.ConnectionString;

            using (SqlConnection connection = new SqlConnection(masterConnectionString))
            {
                await connection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                using (SqlCommand command = new SqlCommand($"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'", connection))
                {
                    int databaseCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                    if (databaseCount == 0)
                    {
                        // إنشاء قاعدة البيانات إذا لم تكن موجودة
                        _logger.LogInformation($"إنشاء قاعدة البيانات {databaseName}");
                        using (SqlCommand createCommand = new SqlCommand($"CREATE DATABASE [{databaseName}]", connection))
                        {
                            await createCommand.ExecuteNonQueryAsync();
                        }
                    }
                }
            }
        }

        private async Task EnsureTablesExistAsync(string connectionString)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // التحقق من وجود جدول العملاء
                if (!await TableExistsAsync(connection, "Customers"))
                {
                    _logger.LogInformation("إنشاء جدول العملاء");
                    using (SqlCommand command = new SqlCommand(@"
                        CREATE TABLE Customers (
                            CustomerID INT IDENTITY(1,1) PRIMARY KEY,
                            CustomerName NVARCHAR(100) NOT NULL,
                            PhoneNumber NVARCHAR(20) NOT NULL,
                            Email NVARCHAR(100),
                            Address NVARCHAR(255),
                            City NVARCHAR(50),
                            Country NVARCHAR(50) DEFAULT N'المملكة العربية السعودية',
                            PostalCode NVARCHAR(20),
                            VATNumber NVARCHAR(50),
                            Notes NVARCHAR(MAX),
                            IsActive BIT DEFAULT 1,
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            ModifiedDate DATETIME
                        )", connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // التحقق من وجود جدول الفئات
                if (!await TableExistsAsync(connection, "Categories"))
                {
                    _logger.LogInformation("إنشاء جدول الفئات");
                    using (SqlCommand command = new SqlCommand(@"
                        CREATE TABLE Categories (
                            CategoryID INT IDENTITY(1,1) PRIMARY KEY,
                            CategoryName NVARCHAR(100) NOT NULL,
                            Description NVARCHAR(255),
                            IsActive BIT DEFAULT 1,
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            ModifiedDate DATETIME
                        )", connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // التحقق من وجود جدول المنتجات
                if (!await TableExistsAsync(connection, "Products"))
                {
                    _logger.LogInformation("إنشاء جدول المنتجات");
                    using (SqlCommand command = new SqlCommand(@"
                        CREATE TABLE Products (
                            ProductID INT IDENTITY(1,1) PRIMARY KEY,
                            ProductName NVARCHAR(100) NOT NULL,
                            SKU NVARCHAR(50),
                            Barcode NVARCHAR(50),
                            Description NVARCHAR(MAX),
                            CategoryID INT REFERENCES Categories(CategoryID),
                            PurchasePrice DECIMAL(18, 2) DEFAULT 0,
                            SellingPrice DECIMAL(18, 2) DEFAULT 0,
                            DiscountPrice DECIMAL(18, 2) DEFAULT 0,
                            VATRate DECIMAL(5, 2) DEFAULT 15.00,
                            StockQuantity INT DEFAULT 0,
                            MinStockLevel INT DEFAULT 5,
                            ImageURL NVARCHAR(255),
                            IsActive BIT DEFAULT 1,
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            ModifiedDate DATETIME
                        )", connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // إنشاء الإجراءات المخزنة
                await CreateStoredProceduresAsync(connection);
            }
        }

        private async Task<bool> TableExistsAsync(SqlConnection connection, string tableName)
        {
            using (SqlCommand command = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'", connection))
            {
                int tableCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                return tableCount > 0;
            }
        }

        private async Task CreateStoredProceduresAsync(SqlConnection connection)
        {
            // إنشاء إجراء مخزن لإضافة عميل جديد
            if (!await StoredProcedureExistsAsync(connection, "sp_AddCustomer"))
            {
                _logger.LogInformation("إنشاء الإجراء المخزن sp_AddCustomer");
                using (SqlCommand command = new SqlCommand(@"
                    CREATE PROCEDURE sp_AddCustomer
                        @CustomerName NVARCHAR(100),
                        @PhoneNumber NVARCHAR(20),
                        @Email NVARCHAR(100) = NULL,
                        @Address NVARCHAR(255) = NULL,
                        @City NVARCHAR(50) = NULL,
                        @Country NVARCHAR(50) = NULL,
                        @PostalCode NVARCHAR(20) = NULL,
                        @VATNumber NVARCHAR(50) = NULL,
                        @Notes NVARCHAR(MAX) = NULL
                    AS
                    BEGIN
                        INSERT INTO Customers (CustomerName, PhoneNumber, Email, Address, City, Country, PostalCode, VATNumber, Notes)
                        VALUES (@CustomerName, @PhoneNumber, @Email, @Address, @City, @Country, @PostalCode, @VATNumber, @Notes);

                        SELECT SCOPE_IDENTITY() AS CustomerID;
                    END", connection))
                {
                    await command.ExecuteNonQueryAsync();
                }
            }

            // إنشاء إجراء مخزن لإضافة منتج جديد
            if (!await StoredProcedureExistsAsync(connection, "sp_AddProduct"))
            {
                _logger.LogInformation("إنشاء الإجراء المخزن sp_AddProduct");
                using (SqlCommand command = new SqlCommand(@"
                    CREATE PROCEDURE sp_AddProduct
                        @ProductName NVARCHAR(100),
                        @SKU NVARCHAR(50) = NULL,
                        @Barcode NVARCHAR(50) = NULL,
                        @Description NVARCHAR(MAX) = NULL,
                        @CategoryID INT,
                        @PurchasePrice DECIMAL(18, 2),
                        @SellingPrice DECIMAL(18, 2),
                        @DiscountPrice DECIMAL(18, 2) = 0,
                        @VATRate DECIMAL(5, 2) = 15.00,
                        @StockQuantity INT = 0,
                        @MinStockLevel INT = 5,
                        @ImageURL NVARCHAR(255) = NULL
                    AS
                    BEGIN
                        INSERT INTO Products (ProductName, SKU, Barcode, Description, CategoryID, PurchasePrice, SellingPrice,
                                             DiscountPrice, VATRate, StockQuantity, MinStockLevel, ImageURL)
                        VALUES (@ProductName, @SKU, @Barcode, @Description, @CategoryID, @PurchasePrice, @SellingPrice,
                                @DiscountPrice, @VATRate, @StockQuantity, @MinStockLevel, @ImageURL);

                        SELECT SCOPE_IDENTITY() AS ProductID;
                    END", connection))
                {
                    await command.ExecuteNonQueryAsync();
                }
            }

            // إنشاء وظيفة لتوليد رمز SKU
            if (!await FunctionExistsAsync(connection, "fn_GenerateSKU"))
            {
                _logger.LogInformation("إنشاء الوظيفة fn_GenerateSKU");
                using (SqlCommand command = new SqlCommand(@"
                    CREATE FUNCTION fn_GenerateSKU
                    (
                        @CategoryName NVARCHAR(100),
                        @ProductName NVARCHAR(100)
                    )
                    RETURNS NVARCHAR(50)
                    AS
                    BEGIN
                        DECLARE @CategoryPrefix NVARCHAR(3);
                        DECLARE @ProductPrefix NVARCHAR(3);
                        DECLARE @LastNumber INT;

                        -- الحصول على بادئة الفئة
                        SET @CategoryPrefix = UPPER(LEFT(@CategoryName, 3));

                        -- الحصول على بادئة المنتج
                        SET @ProductPrefix = UPPER(LEFT(@ProductName, 3));

                        -- الحصول على آخر رقم
                        SELECT @LastNumber = ISNULL(MAX(CAST(SUBSTRING(SKU, 8, 4) AS INT)), 0)
                        FROM Products
                        WHERE SKU LIKE @CategoryPrefix + '-' + @ProductPrefix + '-%';

                        -- إنشاء رقم SKU جديد
                        RETURN @CategoryPrefix + '-' + @ProductPrefix + '-' + RIGHT('0000' + CAST(@LastNumber + 1 AS NVARCHAR(4)), 4);
                    END", connection))
                {
                    await command.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task<bool> StoredProcedureExistsAsync(SqlConnection connection, string procedureName)
        {
            using (SqlCommand command = new SqlCommand($"SELECT COUNT(*) FROM sys.procedures WHERE name = '{procedureName}'", connection))
            {
                int procedureCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                return procedureCount > 0;
            }
        }

        private async Task<bool> FunctionExistsAsync(SqlConnection connection, string functionName)
        {
            using (SqlCommand command = new SqlCommand($"SELECT COUNT(*) FROM sys.objects WHERE type_desc = 'SQL_SCALAR_FUNCTION' AND name = '{functionName}'", connection))
            {
                int functionCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                return functionCount > 0;
            }
        }

        private async Task SeedDataAsync(string connectionString)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // إضافة بيانات تجريبية للفئات إذا كان الجدول فارغاً
                using (SqlCommand command = new SqlCommand("SELECT COUNT(*) FROM Categories", connection))
                {
                    int categoryCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                    if (categoryCount == 0)
                    {
                        _logger.LogInformation("إضافة بيانات تجريبية للفئات");
                        using (SqlCommand insertCommand = new SqlCommand(@"
                            INSERT INTO Categories (CategoryName, Description)
                            VALUES
                            (N'خضار', N'تشكيلة متنوعة من الخضروات الطازجة'),
                            (N'فواكه', N'تشكيلة متنوعة من الفواكه الطازجة'),
                            (N'لحوم', N'لحوم طازجة بأنواعها المختلفة'),
                            (N'أسماك', N'أسماك طازجة ومأكولات بحرية')", connection))
                        {
                            await insertCommand.ExecuteNonQueryAsync();
                        }
                    }
                }

                // إضافة بيانات تجريبية للمنتجات إذا كان الجدول فارغاً
                using (SqlCommand command = new SqlCommand("SELECT COUNT(*) FROM Products", connection))
                {
                    int productCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                    if (productCount == 0)
                    {
                        _logger.LogInformation("إضافة بيانات تجريبية للمنتجات");
                        using (SqlCommand insertCommand = new SqlCommand(@"
                            INSERT INTO Products (ProductName, SKU, Description, CategoryID, PurchasePrice, SellingPrice, StockQuantity)
                            VALUES
                            (N'طماطم', 'VEG-TOM-001', N'طماطم طازجة محلية', 1, 4.00, 5.99, 50),
                            (N'خيار', 'VEG-CUC-002', N'خيار طازج محلي', 1, 3.00, 4.50, 40),
                            (N'تفاح أحمر', 'FRU-APP-001', N'تفاح أحمر طازج مستورد', 2, 10.00, 12.99, 30),
                            (N'لحم بقري', 'MEAT-BEEF-001', N'لحم بقري طازج - كيلو', 3, 75.00, 89.99, 15),
                            (N'سمك سلمون', 'FISH-SAL-001', N'سمك سلمون طازج - كيلو', 4, 100.00, 120.00, 10)", connection))
                        {
                            await insertCommand.ExecuteNonQueryAsync();
                        }
                    }
                }

                // إضافة بيانات تجريبية للعملاء إذا كان الجدول فارغاً
                using (SqlCommand command = new SqlCommand("SELECT COUNT(*) FROM Customers", connection))
                {
                    int customerCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                    if (customerCount == 0)
                    {
                        _logger.LogInformation("إضافة بيانات تجريبية للعملاء");
                        using (SqlCommand insertCommand = new SqlCommand(@"
                            INSERT INTO Customers (CustomerName, PhoneNumber, Email, Address, City)
                            VALUES
                            (N'محمد أحمد', '0501234567', '<EMAIL>', N'شارع الملك فهد', N'الرياض'),
                            (N'فاطمة علي', '0551234567', '<EMAIL>', N'شارع الأمير محمد بن عبدالعزيز', N'جدة'),
                            (N'عبدالله محمد', '0561234567', '<EMAIL>', N'شارع الخليج', N'الدمام')", connection))
                        {
                            await insertCommand.ExecuteNonQueryAsync();
                        }
                    }
                }
            }
        }
    }
}
