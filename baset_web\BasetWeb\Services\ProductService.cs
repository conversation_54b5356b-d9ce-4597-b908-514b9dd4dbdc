using BasetWeb.Models;

namespace BasetWeb.Services
{
    public class ProductService
    {
        // في تطبيق حقيقي، ستقوم بجلب هذه البيانات من قاعدة البيانات
        // هذه قائمة ثابتة للعرض فقط
        private readonly List<Product> _products = new List<Product>
        {
            new Product
            {
                Id = 1,
                Name = "طماطم",
                Description = "طماطم طازجة محلية",
                Price = 5.99m,
                AvailableQuantity = 50,
                ImageUrl = null, // سيتم استخدام أيقونة بديلة
                SKU = "VEG-TOM-001",
                Category = "خضار"
            },
            new Product
            {
                Id = 2,
                Name = "خيار",
                Description = "خيار طازج محلي",
                Price = 4.50m,
                AvailableQuantity = 40,
                ImageUrl = null, // سيتم استخدام أيقونة بديلة
                SKU = "VEG-CUC-002",
                Category = "خضار"
            },
            new Product
            {
                Id = 3,
                Name = "تفاح أحمر",
                Description = "تفاح أحمر طازج مستورد",
                Price = 12.99m,
                AvailableQuantity = 30,
                ImageUrl = null, // سيتم استخدام أيقونة بديلة
                SKU = "FRU-APP-001",
                Category = "فواكه"
            },
            new Product
            {
                Id = 4,
                Name = "لحم بقري",
                Description = "لحم بقري طازج - كيلو",
                Price = 89.99m,
                AvailableQuantity = 15,
                ImageUrl = null, // سيتم استخدام أيقونة بديلة
                SKU = "MEAT-BEEF-001",
                Category = "لحوم"
            },
            new Product
            {
                Id = 5,
                Name = "سمك سلمون",
                Description = "سمك سلمون طازج - كيلو",
                Price = 120.00m,
                AvailableQuantity = 10,
                ImageUrl = null, // سيتم استخدام أيقونة بديلة
                SKU = "FISH-SAL-001",
                Category = "أسماك"
            }
        };

        private readonly List<CustomerOrder> _orders = new List<CustomerOrder>();
        private int _nextOrderId = 1;

        public List<Product> GetAllProducts()
        {
            return _products;
        }

        public Product? GetProductById(int id)
        {
            return _products.FirstOrDefault(p => p.Id == id);
        }

        public List<CustomerOrder> GetAllOrders()
        {
            return _orders;
        }

        public CustomerOrder? GetOrderById(int id)
        {
            return _orders.FirstOrDefault(o => o.Id == id);
        }

        public CustomerOrder CreateOrder(CustomerOrder order)
        {
            // تعيين معرف للطلب
            order.Id = _nextOrderId++;

            // الحصول على المنتج
            var product = GetProductById(order.ProductId);
            if (product != null)
            {
                // تعيين اسم المنتج
                order.ProductName = product.Name;

                // حساب السعر الإجمالي
                order.TotalPrice = product.Price * order.Quantity;

                // تحديث الكمية المتاحة
                if (product.AvailableQuantity >= order.Quantity)
                {
                    product.AvailableQuantity -= order.Quantity;
                }
                else
                {
                    throw new InvalidOperationException("الكمية المطلوبة غير متوفرة");
                }
            }

            // إضافة الطلب إلى القائمة
            _orders.Add(order);

            return order;
        }
    }
}
