using System;
using System.Text;

namespace BasetWeb.Services
{
    public class QrCodeService
    {
        // Simplified QR code service that just returns the base64 encoded string
        // In a real implementation, you would use a proper QR code library
        public byte[] GenerateQrCodeImage(string content)
        {
            // This is a placeholder - in a real implementation, you would generate an actual QR code image
            // For now, we'll just return the content as bytes
            return Encoding.UTF8.GetBytes(content);
        }

        public string GenerateQrCodeBase64(string content)
        {
            // In a real implementation, this would return a base64 encoded image
            // For now, we'll just return the content as base64
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(content));
        }
    }
}
