using System;
using System.Xml;
using BasetWeb.Helpers;

namespace BasetWeb.Services
{
    public class XmlService
    {
        private readonly IWebHostEnvironment _environment;

        public XmlService(IWebHostEnvironment environment)
        {
            _environment = environment;
        }

        public string GetXmlFilePath(string fileName)
        {
            return Path.Combine(_environment.WebRootPath, "xml", fileName);
        }

        public string SaveXmlFile(string content, string fileName)
        {
            string directoryPath = Path.Combine(_environment.WebRootPath, "xml");
            
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            string filePath = Path.Combine(directoryPath, fileName);
            File.WriteAllText(filePath, content);
            
            return filePath;
        }

        public string ProcessInvoice(string inputXmlPath, string outputXmlPath, string certContent, string keyContent, string qrData)
        {
            return ZatcaHelper.GenerateSignedXmlWithQRandPIH(inputXmlPath, outputXmlPath, certContent, keyContent, qrData);
        }

        public string GenerateQrCode(string companyName, string vatNo, string issueDate, decimal total, decimal tax, string invoiceId)
        {
            return ZatcaHelper.GenerateQR(companyName, vatNo, issueDate, total, tax, invoiceId);
        }
    }
}
