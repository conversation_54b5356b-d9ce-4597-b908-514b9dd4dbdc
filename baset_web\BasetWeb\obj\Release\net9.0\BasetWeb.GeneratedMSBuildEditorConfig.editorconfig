is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = BasetWeb
build_property.RootNamespace = BasetWeb
build_property.ProjectDir = D:\Basseet_SYS - Copy (2)\baset_web\BasetWeb\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Basseet_SYS - Copy (2)\baset_web\BasetWeb
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Customers/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ3VzdG9tZXJzXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Customers/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ3VzdG9tZXJzXFJlZ2lzdGVyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Order.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcT3JkZXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/OrderConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcT3JkZXJDb25maXJtYXRpb24uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJpdmFjeS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Products.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZHVjdHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Products/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZHVjdHNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Products/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZHVjdHNcUmVnaXN0ZXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/ZatcaInvoice.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcWmF0Y2FJbnZvaWNlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Basseet_SYS - Copy (2)/baset_web/BasetWeb/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-tm9l5q7ucv
