/* _content/BasetWeb/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-tm9l5q7ucv] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-tm9l5q7ucv] {
  color: #0077cc;
}

.btn-primary[b-tm9l5q7ucv] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-tm9l5q7ucv], .nav-pills .show > .nav-link[b-tm9l5q7ucv] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-tm9l5q7ucv] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-tm9l5q7ucv] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-tm9l5q7ucv] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-tm9l5q7ucv] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-tm9l5q7ucv] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
