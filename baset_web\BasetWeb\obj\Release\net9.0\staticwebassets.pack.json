{"Files": [{"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\obj\\Release\\net9.0\\scopedcss\\projectbundle\\BasetWeb.bundle.scp.css", "PackagePath": "staticwebassets\\BasetWeb.r2mo95j9bo.bundle.scp.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\images\\products\\dbc26f91-e9a0-4449-b18f-884dd6080ac3_18-06-2022 08-16-23 م.png", "PackagePath": "staticwebassets\\images\\products\\dbc26f91-e9a0-4449-b18f-884dd6080ac3_18-06-2022 08-16-23 م.png"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\images\\products\\ec1cff31-42e4-4e34-8922-cb736497455b_18-06-2022 08-12-50 م.png", "PackagePath": "staticwebassets\\images\\products\\ec1cff31-42e4-4e34-8922-cb736497455b_18-06-2022 08-12-50 م.png"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.min.js"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.slim.min.map"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\xml\\sample_certificate.txt", "PackagePath": "staticwebassets\\xml\\sample_certificate.txt"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\xml\\sample_invoice.xml", "PackagePath": "staticwebassets\\xml\\sample_invoice.xml"}, {"Id": "D:\\Basseet_SYS - Copy (2)\\baset_web\\BasetWeb\\wwwroot\\xml\\sample_privatekey.txt", "PackagePath": "staticwebassets\\xml\\sample_privatekey.txt"}, {"Id": "obj\\Release\\net9.0\\staticwebassets\\msbuild.BasetWeb.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Release\\net9.0\\staticwebassets\\msbuild.BasetWeb.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Release\\net9.0\\staticwebassets\\msbuild.build.BasetWeb.props", "PackagePath": "build\\BasetWeb.props"}, {"Id": "obj\\Release\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.BasetWeb.props", "PackagePath": "buildMultiTargeting\\BasetWeb.props"}, {"Id": "obj\\Release\\net9.0\\staticwebassets\\msbuild.buildTransitive.BasetWeb.props", "PackagePath": "buildTransitive\\BasetWeb.props"}], "ElementsToRemove": []}