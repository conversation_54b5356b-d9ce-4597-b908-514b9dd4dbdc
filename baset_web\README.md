# Baset Web - ZATCA E-Invoicing Web Application

This is a web-based application for processing ZATCA (Zakat, Tax and Customs Authority) e-invoices. It provides functionality for generating QR codes, signing XML invoices, and calculating Previous Invoice Hash (PIH) values.

## Features

- Generate QR codes for ZATCA-compliant invoices
- Process and sign XML invoices
- Calculate Previous Invoice Hash (PIH) values
- Web-based interface for easy access

## Prerequisites

- .NET 9.0 SDK or later
- Microsoft SQL Server (for database functionality)

## Getting Started

1. Clone the repository
2. Navigate to the project directory
3. Run the application using the following command:

```
cd BasetWeb
dotnet run
```

4. Open a web browser and navigate to `https://localhost:5001` or `http://localhost:5000`

## Usage

1. Navigate to the ZATCA Invoice page
2. Fill in the invoice details (company name, VAT number, invoice number, etc.)
3. Upload an XML invoice file (optional)
4. Upload certificate and private key files (optional)
5. Click "Process Invoice" to generate the QR code and process the invoice

## Sample Files

The application includes sample files for testing:

- Sample XML invoice: `/wwwroot/xml/sample_invoice.xml`
- Sample certificate: `/wwwroot/xml/sample_certificate.txt`
- Sample private key: `/wwwroot/xml/sample_privatekey.txt`

## Notes

- This is a simplified implementation for demonstration purposes
- In a production environment, you would need to use the official ZATCA SDK for proper XML signing and validation
- The application does not include actual integration with ZATCA's API for invoice reporting

## License

This project is licensed under the MIT License - see the LICENSE file for details.
