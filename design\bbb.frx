﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="10/31/2024 16:29:17" ReportInfo.Modified="10/31/2024 16:31:43" ReportInfo.CreatorVersion="2024.2.0.0">
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGiDb8DbfAluRQe2kAdJ1efhYV5DjXfHFwmEtJWiyR5ZtBLmDcVJUtwjDZhPc+NPzP0f5GBn3kGBe67l6/8EePjgN6L8k/SkbMVvm5UFBUKus0NNehj2mzre9ZOvksqvOTnzKL9ue+5oNeiZRwhOTH06JxwSVDbVHRHY1ZQghjXl3oYYu7q3ArKJ2TulUKtA6Aw==">
      <TableDataSource Name="comSetting_Tbl" DataType="System.Int32" Enabled="true" TableName="comSetting_Tbl">
        <Column Name="Company_ID" DataType="System.Int32"/>
        <Column Name="CompanyName" DataType="System.String"/>
        <Column Name="Address" DataType="System.String"/>
        <Column Name="Phone" DataType="System.String"/>
        <Column Name="Mobile" DataType="System.String"/>
        <Column Name="CompanyLogo" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="Phone1" DataType="System.String"/>
        <Column Name="Phone2" DataType="System.String"/>
        <Column Name="TELEGRAM" DataType="System.String"/>
        <Column Name="WHATSAPP" DataType="System.String"/>
        <Column Name="Mobile1" DataType="System.String"/>
        <Column Name="Mobile2" DataType="System.String"/>
        <Column Name="GOOGLE_LOC" DataType="System.String"/>
        <Column Name="PCNAME" DataType="System.String"/>
        <Column Name="COM_NUM" DataType="System.Int32"/>
        <Column Name="commonName" DataType="System.String"/>
        <Column Name="serialNumer" DataType="System.String"/>
        <Column Name="orgnizationIdentifier" DataType="System.String"/>
        <Column Name="orgnizationUnitName" DataType="System.String"/>
        <Column Name="orgnizationName" DataType="System.String"/>
        <Column Name="countryName" DataType="System.String"/>
        <Column Name="invoiceType" DataType="System.String"/>
        <Column Name="location" DataType="System.String"/>
        <Column Name="industry" DataType="System.String"/>
        <Column Name="emailId" DataType="System.Int32"/>
        <Column Name="fax" DataType="System.String"/>
        <Column Name="city" DataType="System.String"/>
        <Column Name="vat_no" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="View_prtBDep" DataType="System.Int32" Enabled="true" TableName="View_prtBDep">
        <Column Name="Order_ID" DataType="System.Int32"/>
        <Column Name="Order_No" DataType="System.String"/>
        <Column Name="OrderDate" DataType="System.DateTime"/>
        <Column Name="Item_ID" DataType="System.Int32"/>
        <Column Name="Itembarcode" DataType="System.String"/>
        <Column Name="ItemName" DataType="System.String"/>
        <Column Name="Cat_ID" DataType="System.Int32"/>
        <Column Name="CatName" DataType="System.String"/>
        <Column Name="CatColor" DataType="System.String"/>
        <Column Name="depID" DataType="System.Int32"/>
        <Column Name="depName" DataType="System.String"/>
        <Column Name="printerName" DataType="System.String"/>
        <Column Name="status" DataType="System.Boolean" BindableControl="CheckBox"/>
      </TableDataSource>
    </MsSqlDataConnection>
  </Dictionary>
  <ReportPage Name="Page1" PaperWidth="80" PaperHeight="500" Watermark.Font="Arial, 60pt">
    <ReportTitleBand Name="ReportTitle1" Width="226.8" Height="37.8"/>
    <PageHeaderBand Name="PageHeader1" Top="41.8" Width="226.8" Height="28.35"/>
    <DataBand Name="Data1" Top="74.15" Width="226.8" Height="75.6"/>
    <PageFooterBand Name="PageFooter1" Top="153.75" Width="226.8" Height="160.65"/>
  </ReportPage>
</Report>
