﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http.WebRequest</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.RtcRequestFactory">
      <summary>Represents the class that is used to create special <see cref="T:System.Net.Http.HttpRequestMessage" /> for use with the Real-Time-Communications (RTC) background notification infrastructure.</summary>
    </member>
    <member name="M:System.Net.Http.RtcRequestFactory.Create(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Creates a special <see cref="T:System.Net.Http.HttpRequestMessage" /> for use with the Real-Time-Communications (RTC) background notification infrastructure.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpRequestMessage" />.An HTTP request message for use with the RTC background notification infrastructure.</returns>
      <param name="method">The HTTP method.</param>
      <param name="uri">The Uri the request is sent to.</param>
    </member>
    <member name="T:System.Net.Http.WebRequestHandler">
      <summary>Provides desktop-specific features not available to Windows Store apps or other environments. </summary>
    </member>
    <member name="M:System.Net.Http.WebRequestHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.WebRequestHandler" /> class.</summary>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.AllowPipelining">
      <summary> Gets or sets a value that indicates whether to pipeline the request to the Internet resource.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if the request should be pipelined; otherwise, false. The default is true. </returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.AuthenticationLevel">
      <summary>Gets or sets a value indicating the level of authentication and impersonation used for this request.</summary>
      <returns>Returns <see cref="T:System.Net.Security.AuthenticationLevel" />.A bitwise combination of the <see cref="T:System.Net.Security.AuthenticationLevel" /> values.  The default value is <see cref="F:System.Net.Security.AuthenticationLevel.MutualAuthRequested" />.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.CachePolicy">
      <summary>Gets or sets the cache policy for this request.</summary>
      <returns>Returns <see cref="T:System.Net.Cache.RequestCachePolicy" />.A <see cref="T:System.Net.Cache.RequestCachePolicy" /> object that defines a cache policy. The default is <see cref="P:System.Net.WebRequest.DefaultCachePolicy" />.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.ClientCertificates">
      <summary>Gets or sets the collection of security certificates that are associated with this request.</summary>
      <returns>Returns <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.The collection of security certificates associated with this request.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.ContinueTimeout">
      <summary>Gets or sets the amount of time, in milliseconds, the application will wait for 100-continue from the server before uploading data.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.The amount of time, in milliseconds, the application will wait for 100-continue from the server before uploading data. The default value is 350 milliseconds.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.ImpersonationLevel">
      <summary>Gets or sets the impersonation level for the current request.</summary>
      <returns>Returns <see cref="T:System.Security.Principal.TokenImpersonationLevel" />.The impersonation level for the request. The default is <see cref="F:System.Security.Principal.TokenImpersonationLevel.Delegation" />.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.MaxResponseHeadersLength">
      <summary>Gets or sets the maximum allowed length of the response headers.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The length, in kilobytes (1024 bytes), of the response headers.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.ReadWriteTimeout">
      <summary>Gets or sets a time-out in milliseconds when writing a request to or reading a response from a server.</summary>
      <returns>Returns <see cref="T:System.Int32" />.The number of milliseconds before the writing or reading times out. The default value is 300,000 milliseconds (5 minutes). </returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.ServerCertificateValidationCallback">
      <summary>Gets or sets a callback method to validate the server certificate.</summary>
      <returns>Returns <see cref="T:System.Net.Security.RemoteCertificateValidationCallback" />.A callback method to validate the server certificate.</returns>
    </member>
    <member name="P:System.Net.Http.WebRequestHandler.UnsafeAuthenticatedConnectionSharing">
      <summary>Gets or sets a value that indicates whether to allow high-speed NTLM-authenticated connection sharing.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true to keep the authenticated connection open; otherwise, false.</returns>
    </member>
  </members>
</doc>