<?xml version="1.0" encoding="utf-8" ?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <Selenium_Manager_BinariesRootPath Condition="$(Selenium_Manager_BinariesRootPath) == ''">$(MSBuildThisFileDirectory)..\manager</Selenium_Manager_BinariesRootPath>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="$(Selenium_Manager_BinariesRootPath)\linux\selenium-manager">
      <Link>selenium-manager\linux\%(Filename)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
      <Pack>false</Pack>
    </Content>

    <Content Include="$(Selenium_Manager_BinariesRootPath)\macos\selenium-manager">
      <Link>selenium-manager\macos\%(Filename)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
      <Pack>false</Pack>
    </Content>

    <Content Include="$(Selenium_Manager_BinariesRootPath)\windows\selenium-manager.exe">
      <Link>selenium-manager\windows\%(Filename)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
      <Pack>false</Pack>
    </Content>
  </ItemGroup>

</Project>
