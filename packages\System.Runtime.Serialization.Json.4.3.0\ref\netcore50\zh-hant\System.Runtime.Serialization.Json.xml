﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization.Json</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.Serialization.DateTimeFormat">
      <summary>指定日期時間格式選項。</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String)">
      <summary>使用格式字串初始化 <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> 類別的新執行個體。</summary>
      <param name="formatString">格式字串。</param>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String,System.IFormatProvider)">
      <summary>使用格式字串和格式提供者，初始化 <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> 類別的新執行個體。</summary>
      <param name="formatString">格式字串。</param>
      <param name="formatProvider">格式提供者。</param>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.DateTimeStyles">
      <summary>取得或設定格式化選項，這些選項會自訂一些日期和時間剖析方法的字串剖析。</summary>
      <returns>格式化選項，這些選項會自訂一些日期和時間剖析方法的字串剖析。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatProvider">
      <summary>取得控制格式的物件。</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatString">
      <summary>取得當日期或時間表示為字串時，用來控制所產生格式的格式字串。</summary>
      <returns>當日期或時間表示為字串時，用來控制生成格式的格式字串。</returns>
    </member>
    <member name="T:System.Runtime.Serialization.EmitTypeInformation">
      <summary>指定發出型別資訊的頻率。</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Always">
      <summary>永遠要發出型別資訊。</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.AsNeeded">
      <summary>視需要發出型別資訊。</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Never">
      <summary>永遠不要發出型別資訊。</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializer">
      <summary>將物件序列化為 JavaScript 物件標記法 (JSON) 以及將 JSON 資料還原序列化為物件。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 類別的新執行個體，以序列化或還原序列化指定之型別的物件。</summary>
      <param name="type">已序列化或還原序列化之執行個體的型別。</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>初始化 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 類別的新執行個體，以序列化或還原序列化所指定型別的物件，以及可能存在物件圖形中的已知型別集合。</summary>
      <param name="type">已序列化或還原序列化之執行個體的型別。</param>
      <param name="knownTypes">
        <see cref="T:System.Type" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含可能存在物件圖形中的型別。</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Runtime.Serialization.Json.DataContractJsonSerializerSettings)">
      <summary>初始化 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 類別的新執行個體，以序列化或還原序列化所指定型別及序列化程式設定的物件。</summary>
      <param name="type">已序列化或還原序列化之執行個體的型別。</param>
      <param name="settings">JSON 序列化程式的序列化程式設定。</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.DateTimeFormat">
      <summary>取得物件圖形中日期和時間型別項目的格式。</summary>
      <returns>物件圖形中日期和時間型別項目的格式。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.EmitTypeInformation">
      <summary>取得或設定要發出型別資訊的資料合約 JSON 序列化程式設定。</summary>
      <returns>要發出型別資訊的資料合約 JSON 序列化程式設定。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.KnownTypes">
      <summary>取得型別的集合，這些型別可能會存在於使用這個 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 執行個體所序列化的物件圖形中。</summary>
      <returns>
        <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，其中包含已當做已知型別傳入至 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 建構函式的預期型別。</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.IO.Stream)">
      <summary>讀取 JSON (JavaScript 物件標記法) 格式的文件資料流，然後傳回已還原序列化的物件。</summary>
      <returns>還原序列化的物件。</returns>
      <param name="stream">要讀取的 <see cref="T:System.IO.Stream" />。</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.SerializeReadOnlyTypes">
      <summary>取得或設定值，指定是否要序列化唯讀型別。</summary>
      <returns>true 表示序列化唯讀型別，否則為 false。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.UseSimpleDictionaryFormat">
      <summary>取得或設定值，指定是否要使用簡單的字典格式。</summary>
      <returns>true 表示使用一個簡單的字典格式，否則為 false。</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.IO.Stream,System.Object)">
      <summary>將指定物件序列化為 JavaScript 物件標記法 (JSON) 資料，然後將產生的 JSON 寫入資料流。</summary>
      <param name="stream">要寫入的 <see cref="T:System.IO.Stream" />。</param>
      <param name="graph">包含要寫入至資料流之資料的物件。</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">正在序列化的型別不符合資料合約規則。例如，<see cref="T:System.Runtime.Serialization.DataContractAttribute" /> 屬性尚未套用至此型別。</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">正在寫入的執行個體發生問題。</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">已超過要序列化的物件數目上限。檢查 <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> 屬性。</exception>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings">
      <summary>指定 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 設定。</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.DateTimeFormat">
      <summary>取得或設定 DateTimeFormat，定義日期和時間在文化特性上適當的顯示格式。</summary>
      <returns>DateTimeFormat，可定義日期和時間在文化特性上適當的顯示格式。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.EmitTypeInformation">
      <summary>取得或設定要發出型別資訊的資料合約 JSON 序列化程式設定。</summary>
      <returns>要發出型別資訊的資料合約 JSON 序列化程式設定。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.KnownTypes">
      <summary>取得或設定型別的集合，這些型別可能會存在於使用這個 DataContractJsonSerializerSettings 執行個體所序列化的物件圖形中。</summary>
      <returns>型別的集合，這些型別可能會存在於使用這個 DataContractJsonSerializerSettings 執行個體所序列化的物件圖形中。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.MaxItemsInObjectGraph">
      <summary>取得或設定物件圖形中要序列化或還原序列化的最大項目數。</summary>
      <returns>物件圖形中要序列化或還原序列化的最大項目數。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.RootName">
      <summary>取得或設定所選取物件的根名稱。</summary>
      <returns>所選物件的根名稱。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.SerializeReadOnlyTypes">
      <summary>取得或設定值，指定是否要序列化唯讀型別。</summary>
      <returns>True 表示序列化唯讀型別，否則為 false。</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.UseSimpleDictionaryFormat">
      <summary>取得或設定值，指定是否要使用簡單的字典格式。</summary>
      <returns>True 表示使用一個簡單的字典格式，否則為 false。</returns>
    </member>
  </members>
</doc>