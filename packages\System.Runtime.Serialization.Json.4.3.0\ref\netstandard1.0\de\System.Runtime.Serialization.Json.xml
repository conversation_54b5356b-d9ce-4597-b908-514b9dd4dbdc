﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization.Json</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.Serialization.DateTimeFormat">
      <summary>Gibt Datum/Zeit-Formatoptionen an.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.DateTimeFormat" />-Klasse unter Verwendung der Formatzeichenfolge.</summary>
      <param name="formatString">Die Formatzeichenfolge.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String,System.IFormatProvider)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.DateTimeFormat" />-Klasse unter Verwendung der Formatzeichenfolge und Formatanbieters.</summary>
      <param name="formatString">Die Formatzeichenfolge.</param>
      <param name="formatProvider">Der Formatanbieter.</param>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.DateTimeStyles">
      <summary>Ruft die Formatierungsoptionen ab bzw. legt diese fest, mit denen die Art der Analyse einer Zeichenfolge für eine Reihe von Datums- und Uhrzeit-Analysemethoden angepasst wird.</summary>
      <returns>Die Formatierungsoptionen, mit denen die Art der Analyse einer Zeichenfolge für eine Reihe von Datums- und Uhrzeit-Analysemethoden angepasst wird.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatProvider">
      <summary>Ruft ein Objekt zum Steuern der Formatierung ab.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatString">
      <summary>Ruft die Formatzeichenfolgen zur Festlegung des Formatierungsergebnisses ab, wenn ein Datums- oder Zeitwert als Zeichenfolge dargestellt werden soll.</summary>
      <returns>Die Formatzeichenfolgen zur Festlegung des Formatierungsergebnisses, wenn ein Datums- oder Zeitwert als Zeichenfolge dargestellt werden soll.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.EmitTypeInformation">
      <summary>Gibt an, wie oft Typinformationen ausgegeben werden.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Always">
      <summary>immer Typinformationen ausgeben.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.AsNeeded">
      <summary>Bei Bedarf Typinformationen ausgeben.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Never">
      <summary>Typinformationen sollen niemals ausgeben werden.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializer">
      <summary>Serialisiert Objekte in die JavaScript Object Notation (JSON) und deserialisiert JSON-Daten zu Objekten.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Klasse, um ein Objekt des genannten Typs zu serialisieren bzw. zu deserialisieren.</summary>
      <param name="type">Der Typ der Instanzen, die serialisiert oder deserialisiert werden.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Klasse, um ein Objekt des genannten Typs mit einer Auflistung bekannter Typen, die ggf. im Objektdiagramm vorhanden sind, zu serialisieren bzw. zu deserialisieren. </summary>
      <param name="type">Der Typ der Instanzen, die serialisiert oder deserialisiert werden.</param>
      <param name="knownTypes">Ein <see cref="T:System.Collections.Generic.IEnumerable`1" />-Objekt des Typs <see cref="T:System.Type" />, das die in dem Objektdiagramm enthaltenen Typen angibt (falls vorhanden).</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Runtime.Serialization.Json.DataContractJsonSerializerSettings)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Klasse, um ein Objekt des genannten Typs und Serialisierungsprogrammeinstellungen zu serialisieren bzw. zu deserialisieren.</summary>
      <param name="type">Der Typ der Instanzen, die serialisiert oder deserialisiert werden.</param>
      <param name="settings">Die Serialisierungsprogrammeinstellungen für das JSON-Serialisierungsprogramm.</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.DateTimeFormat">
      <summary>Ruft das Format der Elemente des Typs Datum/Zeit im Objektdiagramm ab.</summary>
      <returns>Das Format der Elemente des Typs Datum/Zeit im Objektdiagramm.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.EmitTypeInformation">
      <summary>Ruft die JSON-Serialisierungseinstellungen für den Datenvertrag zum Ausgeben von Typinformationen ab oder legt diese fest.</summary>
      <returns>Die Einstellungen der JSON-Serialisierungseinstellungen für den Datenvertrag zum Ausgeben von Typinformationen.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.KnownTypes">
      <summary>Ruft eine Auflistung der Typen in einem Objektdiagramm ab, die mithilfe einer Instanz des <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Objekts serialisiert werden.</summary>
      <returns>Ein <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />-Objekt, das die erwarteten Typen enthält, die als bekannte Typen an den <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Konstruktor übergeben werden.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.IO.Stream)">
      <summary>Liest einen Dokumentstream im JSON-Format (JavaScript Object Notation) und gibt das deserialisierte Objekt zurück.</summary>
      <returns>Das deserialisierte Objekt.</returns>
      <param name="stream">Das <see cref="T:System.IO.Stream" />-Objekt, das gelesen werden soll.</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.SerializeReadOnlyTypes">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob schreibgeschützte Typen serialisiert werden sollen.</summary>
      <returns>true, um schreibgeschützte Typen zu serialisieren; andernfalls false.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.UseSimpleDictionaryFormat">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob ein einfaches Wörterbuchformat verwendet werden soll.</summary>
      <returns>true, um ein einfaches Wörterbuchformat zu verwenden; andernfalls false.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.IO.Stream,System.Object)">
      <summary>Serialisiert ein angegebenes Objekt in JSON-Daten (JavaScript Object Notation) und schreibt die resultierenden JSON-Daten in einen Stream.</summary>
      <param name="stream">Das <see cref="T:System.IO.Stream" />-Objekt, in das geschrieben wird.</param>
      <param name="graph">Das Objekt, das die in den Stream zu schreibenden Daten enthält.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">Der serialisierte Typ entspricht nicht den Datenvertragsregeln.Das <see cref="T:System.Runtime.Serialization.DataContractAttribute" />-Attribut wurde z. B. nicht auf den Typ angewendet.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">Es liegt ein Problem mit der Instanz vor, die geschrieben wird.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">Die maximale Anzahl von zu serialisierenden Objekten wurde überschritten.Überprüfen Sie die <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" />-Eigenschaft.</exception>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings">
      <summary>Gibt <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />-Einstellungen an.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings" />-Klasse.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.DateTimeFormat">
      <summary>Ruft ein DateTimeFormat ab, das das für die Kultur spezifische Format zum Anzeigen von Datumsangaben und Uhrzeiten definiert, oder legt es fest.</summary>
      <returns>Das Datums- und Zeitformat, das für die Kultur spezifische Format zum Anzeigen von Datumsangaben und Uhrzeiten definiert, oder legt diese fest.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.EmitTypeInformation">
      <summary>Ruft die JSON-Serialisierungseinstellungen für den Datenvertrag zum Ausgeben von Typinformationen ab oder legt diese fest.</summary>
      <returns>Die Einstellungen der JSON-Serialisierungseinstellungen für den Datenvertrag zum Ausgeben von Typinformationen.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.KnownTypes">
      <summary>Ruft eine Auflistung der Typen im Objektdiagramm ab, die mithilfe dieser Instanz von DataContractJsonSerializerSettings serialisiert wurden, oder legt sie fest.</summary>
      <returns>Eine Sammlung der Typen im Objektdiagramm ab, die mithilfe dieser Instanz von DataContractJsonSerializerSettings serialisiert wurden.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.MaxItemsInObjectGraph">
      <summary>Ruft die maximale Anzahl von Elementen in einem Objektdiagramm ab oder legt sie fest, die serialisiert oder deserialisiert werden.</summary>
      <returns>Die maximale Anzahl von Elementen in einem Objektdiagramm, die serialisiert oder deserialisiert werden.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.RootName">
      <summary>Übernimmt oder bestimmt den Stammnamen des ausgewählten Objekts.</summary>
      <returns>Der Stammname des ausgewählten Objekts.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.SerializeReadOnlyTypes">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob schreibgeschützte Typen serialisiert werden sollen.</summary>
      <returns>True, um schreibgeschützte Typen zu serialisieren; andernfalls false.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.UseSimpleDictionaryFormat">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob ein einfaches Wörterbuchformat verwendet werden soll.</summary>
      <returns>True, um ein einfaches Wörterbuchformat zu verwenden; andernfalls false.</returns>
    </member>
  </members>
</doc>