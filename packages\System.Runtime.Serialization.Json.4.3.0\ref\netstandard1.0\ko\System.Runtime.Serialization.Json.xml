﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization.Json</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.Serialization.DateTimeFormat">
      <summary>날짜-시간 형식 옵션을 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String)">
      <summary>형식 문자열을 사용하여 <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="formatString">서식 문자열입니다.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String,System.IFormatProvider)">
      <summary>형식 문자열과 형식 공급자를 사용하여 <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="formatString">형식 문자열입니다.</param>
      <param name="formatProvider">서식 공급자입니다.</param>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.DateTimeStyles">
      <summary>여러 날짜 및 시간 구문 분석 메서드의 문자열 구문 분석 방법을 사용자 지정하는 형식 지정 옵션을 가져오거나 설정합니다.</summary>
      <returns>여러 날짜 및 시간 구문 분석 메서드의 문자열 구문 분석 방법을 사용자 지정하는 형식 지정 옵션입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatProvider">
      <summary>형식을 제어하는 개체를 가져옵니다.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatString">
      <summary>날짜 또는 시간이 문자열로 표현되는 경우 생성된 서식을 제어하는 서식 문자열을 가져옵니다.</summary>
      <returns>날짜 또는 시간이 문자열로 표시되는 경우 만들어지는 서식을 제어하는 형식 문자열입니다.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.EmitTypeInformation">
      <summary>형식 정보를 내보내는 빈도를 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Always">
      <summary>항상 형식 정보를 내보냅니다.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.AsNeeded">
      <summary>필요에 따라 형식 정보를 내보냅니다.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Never">
      <summary>형식 정보를 절대 내보내지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializer">
      <summary>개체를 JSON(JavaScript Object Notation)으로 serialize하고 JSON 데이터를 개체로 deserialize합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type)">
      <summary>지정된 형식의 개체를 serialize하거나 deserialize하기 위해 <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">serialize되거나 deserialize되는 인스턴스 형식입니다.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 클래스의 새 인스턴스를 초기화하여 지정된 형식의 개체와 함께 개체 그래프에 있을 수 있는 알려진 형식 컬렉션을 serialize하거나 deserialize합니다. </summary>
      <param name="type">serialize되거나 deserialize되는 인스턴스 형식입니다.</param>
      <param name="knownTypes">개체 그래프에 있을 수 있는 형식을 포함하는 <see cref="T:System.Type" />의 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Runtime.Serialization.Json.DataContractJsonSerializerSettings)">
      <summary>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 클래스의 새 인스턴스를 초기화하여 지정된 형식 및 serializer 설정의 개체를 serialize하거나 deserialize합니다.</summary>
      <param name="type">serialize되거나 deserialize되는 인스턴스 형식입니다.</param>
      <param name="settings">JSON serializer에 대한 serializer 설정입니다.</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.DateTimeFormat">
      <summary>개체 그래프에서 날짜 및 시간 형식 항목의 서식을 가져옵니다.</summary>
      <returns>개체 그래프에서 날짜 및 시간 형식 항목의 서식입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.EmitTypeInformation">
      <summary>형식 정보를 내보내는 데이터 계약 JSON serializer 설정을 가져오거나 설정합니다.</summary>
      <returns>형식 정보를 내보내는 데이터 계약 JSON serializer 설정입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.KnownTypes">
      <summary>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />의 이 인스턴스를 사용하여 serialize된 개체 그래프에 있을 수 있는 형식의 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 생성자에 알려진 형식으로 전달되는 예상 형식을 포함하는 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.IO.Stream)">
      <summary>JSON(JavaScript Object Notation) 형식의 문서 스트림을 읽고 deserialize된 개체를 반환합니다.</summary>
      <returns>deserialize된 개체입니다.</returns>
      <param name="stream">읽을 <see cref="T:System.IO.Stream" />입니다.</param>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.SerializeReadOnlyTypes">
      <summary>읽기 전용 형식을 serialize하는지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>serialize 할 형식만 읽으려면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.UseSimpleDictionaryFormat">
      <summary>간단한 사전 형식을 사용할지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>간단한 사전 형식을 사용하려면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.IO.Stream,System.Object)">
      <summary>지정된 개체를 JSON(JavaScript Object Notation) 데이터로 serialize하고 결과 JSON을 스트림에 씁니다.</summary>
      <param name="stream">결과를 쓸 대상 <see cref="T:System.IO.Stream" />입니다.</param>
      <param name="graph">스트림에 쓸 데이터를 포함하는 개체입니다.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">serialize하고 있는 형식이 데이터 계약 규칙을 따르지 않는 경우.<see cref="T:System.Runtime.Serialization.DataContractAttribute" /> 특성이 형식에 적용되지 않은 경우를 예로 들 수 있습니다.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">쓰고 있는 인스턴스에 문제가 있는 경우</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">serialize할 최대 개체 수가 초과된 경우.<see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> 속성을 확인합니다.</exception>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings">
      <summary>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> 설정을 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.#ctor">
      <summary>
        <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.DateTimeFormat">
      <summary>날짜와 시간 표시를 위한 문화권 형식을 정의하는 DateTimeFormat을 가져오거나 설정합니다.</summary>
      <returns>날짜와 시간 표시를 위한 문화권 형식을 정의하는 DateTimeFormat입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.EmitTypeInformation">
      <summary>형식 정보를 내보내는 데이터 계약 JSON serializer 설정을 가져오거나 설정합니다.</summary>
      <returns>형식 정보를 내보내는 데이터 계약 JSON serializer 설정입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.KnownTypes">
      <summary>DataContractJsonSerializerSettings의 이 인스턴스를 사용하여 serialize된 개체 그래프에 있을 수 있는 형식의 컬렉션을 가져오거나 설정합니다.</summary>
      <returns>DataContractJsonSerializerSettings의 이 인스턴스를 사용하여 serialize된 개체 그래프에 있을 수 있는 형식의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.MaxItemsInObjectGraph">
      <summary>개체 그래프에서 serialize하거나 deserialize할 최대 항목 수를 가져오거나 설정합니다.</summary>
      <returns>개체 그래프에서 serialize하거나 deserialize할 최대 항목 수입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.RootName">
      <summary>선택한 개체의 루트 이름을 가져오거나 설정합니다.</summary>
      <returns>선택한 개체의 루트 이름입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.SerializeReadOnlyTypes">
      <summary>읽기 전용 형식을 serialize하는지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>serialize 할 형식만 읽으려면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.UseSimpleDictionaryFormat">
      <summary>간단한 사전 형식을 사용할지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>간단한 사전 형식을 사용하려면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
  </members>
</doc>