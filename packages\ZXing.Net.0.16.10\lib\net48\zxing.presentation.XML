<?xml version="1.0"?>
<doc>
    <assembly>
        <name>zxing.presentation</name>
    </assembly>
    <members>
        <member name="T:ZXing.Presentation.BarcodeReader">
            <summary>
            A smart class to decode the barcode inside a bitmap object which is derived from BitmapSource
            </summary>
        </member>
        <member name="M:ZXing.Presentation.BarcodeReader.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Presentation.BarcodeReader"/> class.
            </summary>
        </member>
        <member name="M:ZXing.Presentation.BarcodeReader.#ctor(ZXing.Reader,System.Func{System.Windows.Media.Imaging.BitmapSource,ZXing.LuminanceSource},System.Func{ZXing.LuminanceSource,ZXing.Binarizer})">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Presentation.BarcodeReader"/> class.
            </summary>
            <param name="reader">Sets the reader which should be used to find and decode the barcode.
            If null then MultiFormatReader is used</param>
            <param name="createLuminanceSource">Sets the function to create a luminance source object for a bitmap.
            If null, default is used</param>
            <param name="createBinarizer">Sets the function to create a binarizer object for a luminance source.
            If null then HybridBinarizer is used</param>
        </member>
        <member name="M:ZXing.Presentation.BarcodeReader.#ctor(ZXing.Reader,System.Func{System.Windows.Media.Imaging.BitmapSource,ZXing.LuminanceSource},System.Func{ZXing.LuminanceSource,ZXing.Binarizer},System.Func{System.Byte[],System.Int32,System.Int32,ZXing.RGBLuminanceSource.BitmapFormat,ZXing.LuminanceSource})">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Presentation.BarcodeReader"/> class.
            </summary>
            <param name="reader">Sets the reader which should be used to find and decode the barcode.
            If null then MultiFormatReader is used</param>
            <param name="createLuminanceSource">Sets the function to create a luminance source object for a bitmap.
            If null, default is used</param>
            <param name="createBinarizer">Sets the function to create a binarizer object for a luminance source.
            If null then HybridBinarizer is used</param>
            <param name="createRGBLuminanceSource">The create RGB luminance source.</param>
        </member>
        <member name="T:ZXing.Presentation.BarcodeWriter">
            <summary>
            A smart class to encode some content to a barcode image
            </summary>
        </member>
        <member name="M:ZXing.Presentation.BarcodeWriter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Presentation.BarcodeWriter"/> class.
            </summary>
        </member>
        <member name="T:ZXing.Presentation.BarcodeWriterGeometry">
            <summary>
            A smart class to encode some content to a barcode image into a geometry
            Autor: Rob Fonseca-Ensor
            </summary>
        </member>
        <member name="M:ZXing.Presentation.BarcodeWriterGeometry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Presentation.BarcodeWriterGeometry"/> class.
            </summary>
        </member>
        <member name="T:ZXing.BarcodeReaderExtensions">
            <summary>
            extensions methods which are working directly on any IBarcodeReaderGeneric implementation
            </summary>
        </member>
        <member name="M:ZXing.BarcodeReaderExtensions.Decode(ZXing.IBarcodeReaderGeneric,System.Windows.Media.Imaging.BitmapSource)">
            <summary>
            uses the IBarcodeReaderGeneric implementation and the <see cref="T:ZXing.BitmapSourceLuminanceSource"/> class for decoding
            </summary>
            <param name="reader"></param>
            <param name="image"></param>
            <returns></returns>
        </member>
        <member name="M:ZXing.BarcodeReaderExtensions.DecodeMultiple(ZXing.IBarcodeReaderGeneric,System.Windows.Media.Imaging.BitmapSource)">
            <summary>
            uses the IBarcodeReaderGeneric implementation and the <see cref="T:ZXing.BitmapSourceLuminanceSource"/> class for decoding
            </summary>
            <param name="reader"></param>
            <param name="image"></param>
            <returns></returns>
        </member>
        <member name="T:ZXing.BarcodeWriterGeometryExtensions">
            <summary>
            extensions methods which are working directly on any BarcodeWriterGeneric implementation
            </summary>
        </member>
        <member name="M:ZXing.BarcodeWriterGeometryExtensions.WriteAsGeometry(ZXing.IBarcodeWriterGeneric,System.String)">
            <summary>
            uses the BarcodeWriterGeneric implementation and the <see cref="T:ZXing.Rendering.GeometryRenderer"/> class for decoding
            </summary>
            <param name="writer"></param>
            <param name="content"></param>
            <returns></returns>
        </member>
        <member name="T:ZXing.BarcodeWriterExtensions">
            <summary>
            extensions methods which are working directly on any BarcodeWriterGeneric implementation
            </summary>
        </member>
        <member name="M:ZXing.BarcodeWriterExtensions.WriteAsWriteableBitmap(ZXing.IBarcodeWriterGeneric,System.String)">
            <summary>
            uses the BarcodeWriterGeneric implementation and the <see cref="T:ZXing.Rendering.WriteableBitmapRenderer"/> class for decoding
            </summary>
            <param name="writer"></param>
            <param name="content"></param>
            <returns></returns>
        </member>
        <member name="T:ZXing.BitmapSourceLuminanceSource">
            <summary>
            class which represents the luminance source values for bitmap source objects
            </summary>
        </member>
        <member name="M:ZXing.BitmapSourceLuminanceSource.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.BitmapSourceLuminanceSource"/> class.
            </summary>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
        </member>
        <member name="M:ZXing.BitmapSourceLuminanceSource.#ctor(System.Windows.Media.Imaging.BitmapSource)">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.BitmapSourceLuminanceSource"/> class.
            </summary>
            <param name="bitmap">The bitmap.</param>
        </member>
        <member name="M:ZXing.BitmapSourceLuminanceSource.CreateLuminanceSource(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Should create a new luminance source with the right class type.
            The method is used in methods crop and rotate.
            </summary>
            <param name="newLuminances">The new luminances.</param>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
            <returns></returns>
        </member>
        <member name="T:ZXing.Rendering.GeometryRenderer">
            <summary>
            Renders a barcode into a geometry
            Autor: Rob Fonseca-Ensor
            </summary>
        </member>
        <member name="M:ZXing.Rendering.GeometryRenderer.Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String)">
            <summary>
            Renders the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <param name="format">The format.</param>
            <param name="content">The content.</param>
            <returns></returns>
        </member>
        <member name="M:ZXing.Rendering.GeometryRenderer.Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String,ZXing.Common.EncodingOptions)">
            <summary>
            Renders the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <param name="format">The format.</param>
            <param name="content">The content.</param>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
        <member name="T:ZXing.Rendering.WriteableBitmapRenderer">
            <summary>
            Renders a <see cref="T:ZXing.Common.BitMatrix" /> to a <see cref="T:System.Windows.Media.Imaging.WriteableBitmap" />
            </summary>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.Foreground">
            <summary>
            Gets or sets the foreground color.
            </summary>
            <value>
            The foreground color.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.Background">
            <summary>
            Gets or sets the background color.
            </summary>
            <value>
            The background color.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.FontFamily">
            <summary>
            Gets or sets the font family.
            </summary>
            <value>
            The font family.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.FontSize">
            <summary>
            Gets or sets the size of the font.
            </summary>
            <value>
            The size of the font.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.FontStretch">
            <summary>
            Gets or sets the font stretch.
            </summary>
            <value>
            The font stretch.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.FontStyle">
            <summary>
            Gets or sets the font style.
            </summary>
            <value>
            The font style.
            </value>
        </member>
        <member name="P:ZXing.Rendering.WriteableBitmapRenderer.FontWeight">
            <summary>
            Gets or sets the font weight.
            </summary>
            <value>
            The font weight.
            </value>
        </member>
        <member name="M:ZXing.Rendering.WriteableBitmapRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ZXing.Rendering.WriteableBitmapRenderer"/> class.
            </summary>
        </member>
        <member name="M:ZXing.Rendering.WriteableBitmapRenderer.Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String)">
            <summary>
            Renders the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <param name="format">The format.</param>
            <param name="content">The content.</param>
            <returns></returns>
        </member>
        <member name="M:ZXing.Rendering.WriteableBitmapRenderer.Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String,ZXing.Common.EncodingOptions)">
            <summary>
            Renders the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <param name="format">The format.</param>
            <param name="content">The content.</param>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
    </members>
</doc>
